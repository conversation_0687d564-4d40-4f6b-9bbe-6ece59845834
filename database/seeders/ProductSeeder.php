<?php

namespace Database\Seeders;

use Carbon\Carbon;
use Illuminate\Support\Str;
use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class ProductSeeder extends Seeder
{
    /**
     * Run the database seeds.
     *
     */
    public function run(): void
    {
        $date = Carbon::now();
        DB::statement('SET foreign_key_checks = 0');
        DB::table('products')->truncate();

        // Ground Sirloin
        DB::table('products')->insert([
            'id' => 1,
            'title' => 'Ground Sirloin (Sample)',
            'slug' => Str::slug('Ground Sirloin (Sample)'),
            'cover_photo' => 'https://s3.amazonaws.com/grazecart/demo/sample-photos/gorund-sirloin.jpg',
            'cover_photo_thumbnail' => 'https://s3.amazonaws.com/grazecart/demo/sample-photos/gorund-sirloin.jpg',
            'unit_price' => 775,
            'unit_of_issue' => 'package',
            'unit_description' => '1lb / pkg',
            'weight' => 1.000,
            'inventory' => 10,
            'description' => 'A exclusive blend from the sirloin yielding a 90-95% lean product. Ground twice for a finer texture and great eating experience.',
            'created_at' => $date,
            'updated_at' => $date
        ]);

        // Add to collection
        DB::table('collection_product')->insert([
            'product_id' => 1,
            'collection_id' => 1,
            'sort_order' => 1
        ]);

        // Pork Chop
        DB::table('products')->insert([
            'id' => 2,
            'title' => 'Pork Chop (Sample)',
            'slug' => Str::slug('Pork Chop (Sample)'),
            'cover_photo' => 'https://s3.amazonaws.com/grazecart/demo/sample-photos/pork-chops.jpg',
            'cover_photo_thumbnail' => 'https://s3.amazonaws.com/grazecart/demo/sample-photos/pork-chops.jpg',
            'unit_price' => 995,
            'unit_of_issue' => 'weight',
            'unit_description' => '2 cuts - 1 to 1.5lb Avg/pkg',
            'weight' => 1.200,
            'inventory' => 10,
            'description' => 'Thicker bone-in chops cut from the loin or rib section.',
            'created_at' => $date,
            'updated_at' => $date
        ]);

        // Add to collection
        DB::table('collection_product')->insert([
            'product_id' => 2,
            'collection_id' => 3,
            'sort_order' => 1
        ]);

        // Eggs
        DB::table('products')->insert([
            'id' => 3,
            'title' => 'Dozen Large Eggs (Sample)',
            'slug' => Str::slug('Dozen Large Eggs (Sample)'),
            'cover_photo' => 'https://s3.amazonaws.com/grazecart/demo/sample-photos/eggs.jpg',
            'cover_photo_thumbnail' => 'https://s3.amazonaws.com/grazecart/demo/sample-photos/eggs.jpg',
            'unit_price' => 400,
            'unit_of_issue' => 'package',
            'unit_description' => '2 cuts - 1 to 1.5lb Avg/pkg',
            'weight' => 1.500,
            'inventory' => 10,
            'description' => 'From hens raised free-range without feeding drugs, antibiotics or GMO grains.',
            'created_at' => $date,
            'updated_at' => $date
        ]);

        // Add to collection
        DB::table('collection_product')->insert([
            'product_id' => 3,
            'collection_id' => 2,
            'sort_order' => 1
        ]);

        // Add to collection
        DB::table('collection_product')->insert([
            'product_id' => 3,
            'collection_id' => 4,
            'sort_order' => 1
        ]);
    }
}

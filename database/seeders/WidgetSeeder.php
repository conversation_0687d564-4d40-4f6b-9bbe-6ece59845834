<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use Illuminate\Support\Facades\DB;

class WidgetSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        $date = date('Y-m-d H:i:s');
        DB::table('widgets')->truncate();
        DB::table('widgets')->insert([
          [
            'page_id' => 1,
            'enabled' => 1,
            'title' => 'Image Banner',
            //'slug' => Str::slug('How It Works Header'),
            'template' => 'Banner',
            'content' => '',
            'settings' => json_encode([
              'alignment' => 'text-center',
              'header' => 'COMPANY ONE-LINER',
              'message' => 'Describe what you sell and how your customers can get it',
              'background' => 'https://s3.amazonaws.com/grazecart/newtrialtemplate/images/1642778247_61eace873f277.jpg',
              'background_overlay_color' => 'hsla(0, 0%, 0%, 0.3)',
              'cta_show' => true,
              'cta' => 'Shop Now',
              'cta_url' => '/store',
              'cta_2_show' => false,
              'cta_2' => 'Sign Up',
              'cta_2_url' => '/signup',
              'background_position' => '50% 50%',
              'background_attachment' => 'scroll',
              'background_repeat' => 'no-repeat',
              'background_size' => 'cover',
              'paddingTop' => 200,
              'paddingBottom' => 200,
              'paddingLeft' => 80,
              'paddingRight' => 80,
              'header_font_size' => 56,
              'header_line_height' => 1.25,
              'header_width' => 750,
              'header_text_color' => '#FFF',
              'header_letter_spacing' => '0.05em',
              'subheader_font_size' => 24,
              'subheader_line_height' => 1.25,
              'subheader_width' => '650',
              'subheader_letter_spacing' => 'normal',
              'subheader_text_color' => '#FFF',
              'background_height' => 1000,
              'match_image_height' => false,
              'inner_container_max_width' => 1600,
            ]),
            'sort' => '0',
            'created_at' => $date,
            'updated_at' => $date
          ],
          [
            'page_id' => 1,
            'enabled' => 1,
            'title' => 'Trust Statements',
            //'slug' => Str::slug('Trust Statements'),
            'template' => 'RichText',
            'content' => '<h3 class="ql-align-center">✔ Transparent&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;✔ Ethical&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;✔ Convenient</h3>',
            'settings' => json_encode(array (
                'layout_width' => 'full-width',
                'width' => '950',
                'paddingTop' => 20,
                'paddingBottom' => 4,
                'line_height' => 1.75,
                'background' => '#3d3d3d',
                'text_color' => '#ffffff',
                'link_color' => '#ffffff',
              )),
            'sort' => '0',
            'created_at' => $date,
            'updated_at' => $date
          ],
          [
            'page_id' => 1,
            'enabled' => true,
            'title' => 'What We Offer',
            //'slug' => Str::slug('What We Offer'),
            'template' => 'Offerings',
            'content' => '',
            'settings' => json_encode(array (
                'paddingTop' => 96,
                'paddingBottom' => 64,
                'header_show' => true,
                'header' => 'What We Offer',
                'background' => '--background_color',
                'text_color' => '--text_color',
                'link_color' => '--link_color',
                'items' => 
                array (
                  0 => 
                  array (
                    'id' => 1,
                    'caption' => 'BEEF',
                    'subcaption' => 'ETHICALLY RAISED',
                    'url' => '/store/beef',
                    'src' => 'https://s3.amazonaws.com/grazecart/newtrialtemplate/images/1642777894_61eacd262e3b6.jpg',
                    'visible' => true,
                    'show_caption' => true,
                    'click_action' => 'link',
                    'grid_span' => 3,
                    'height' => 'auto',
                    'width' => 'auto',
                  ),
                  1 => 
                  array (
                    'id' => 2,
                    'caption' => 'POULTRY',
                    'subcaption' => 'SMALL BATCH',
                    'url' => '/store/poultry',
                    'src' => 'https://s3.amazonaws.com/grazecart/newtrialtemplate/images/1642777754_61eacc9a945e7.jpg',
                    'visible' => true,
                    'show_caption' => true,
                    'click_action' => 'link',
                    'grid_span' => 3,
                    'height' => 'auto',
                    'width' => 'auto',
                  ),
                  2 => 
                  array (
                    'id' => 3,
                    'caption' => 'PORK',
                    'subcaption' => 'HERITAGE',
                    'url' => '/store/pork',
                    'src' => 'https://s3.amazonaws.com/grazecart/newtrialtemplate/images/1642777832_61eacce8792be.jpg',
                    'visible' => true,
                    'show_caption' => true,
                    'click_action' => 'link',
                    'grid_span' => 3,
                    'height' => 'auto',
                    'width' => 'auto',
                  ),
                ),
              )),
            'sort' => '0',
            'created_at' => $date,
            'updated_at' => $date
          ],
          [
            'page_id' => 1,
            'enabled' => 1,
            'title' => 'How It Works',
            //'slug' => Str::slug('How It Works Header'),
            'template' => 'HowItWorks',
            'content' => '',
            'settings' => json_encode([
              'layout_width' => 'full-width',
              'header' => 'How It Works',
              'cta' => 'Start Shopping',
              'cta_url' => '/store',
              'step_1_title' => 'Sign-up',
              'step_1_message' => 'Sign-up to browse products available for delivery to your area.',
              'step_2_title' => 'Shop',
              'step_2_message' => 'Fill your cart with your favorite fresh products.',
              'step_3_message' => 'Relax, gather your family and friends and eat with confidence.',
              'step_3_title' => 'Enjoy',
              'background' => '#f5f5f5',
              'step_1_icon' => 1,
              'step_2_icon' => 2,
              'step_3_icon' => 3,
              'paddingTop' => 64,
              'paddingBottom' => 64,
              'text_color' => '#3d3d3d',
              'header_show' => true,
              'cta_show' => true,
              'cta_2_show' => false,
              'cta_2' => 'Sign Up Now',
              'cta_2_url' => '/register',
              'link_color' => '--link_color',
              'icon_background' => '#e2e2e2',
              'icon_color' => '#3d3d3d',
              ]),
              'sort' => '3',
              'created_at' => $date,
              'updated_at' => $date
            ],
            [
              'page_id' => 3,
              'enabled' => 1,
              'title' => 'How It Works Header',
              //'slug' => Str::slug('How It Works Header'),
              'template' => 'RichText',
              'content' => '<h2 class="ql-align-center">How It Works</h2>',
              'settings' => json_encode(array (
                  'layout_width' => 'full-width',
                  'width' => 640,
                  'paddingTop' => 24,
                  'paddingBottom' => 1,
                  'line_height' => 1.75,
                  'background' => '--background_color',
                  'text_color' => '--text_color',
                  'link_color' => '--link_color',
              )),
              'sort' => '1',
              'created_at' => $date,
              'updated_at' => $date
            ],
            [
                'page_id' => 3,
                'enabled' => 1,
                'title' => 'How It Works',
                //'slug' => Str::slug('How It Works'),
                'template' => 'HowItWorks',
                'content' => '',
                'settings' => json_encode(array (
                    'paddingTop' => 24,
                    'paddingBottom' => 24,
                    'layout_width' => 'full-width',
                    'header_show' => false,
                    'header' => 'How It Works',
                    'cta_show' => false,
                    'cta' => 'Sign Up Now',
                    'cta_url' => '/register',
                    'cta_2_show' => false,
                    'cta_2' => 'Sign Up Now',
                    'cta_2_url' => '/register',
                    'step_1_icon' => '1',
                    'step_1_title' => 'You Shop',
                    'step_1_message' => 'Browse and order your family\'s favorite ethical foods, raised right!',
                    'step_2_icon' => '2',
                    'step_2_title' => 'We Deliver',
                    'step_2_message' => 'Our team carfully packs and delivers directly to you.',
                    'step_3_icon' => '3',
                    'step_3_message' => 'Gather the family and enjoy your new source of premium meat products!',
                    'step_3_title' => 'You Enjoy',
                    'background' => '--background_color',
                    'text_color' => '--text_color',
                    'link_color' => '--link_color',
                    'icon_background' => '#e2e2e2',
                    'icon_color' => '#3d3d3d',
                  )),
                'sort' => '1',
                'created_at' => $date,
                'updated_at' => $date
            ],
            [
                'page_id' => 3,
                'enabled' => 1,
                'title' => 'What We Offer',
                //'slug' => Str::slug('What We Offer'),
                'template' => 'Offerings',
                'content' => '',
                'settings' => json_encode(array (
                    'paddingTop' => 24,
                    'paddingBottom' => 64,
                    'header_show' => false,
                    'header' => 'What We Offer',
                    'background' => '--background_color',
                    'text_color' => '--text_color',
                    'link_color' => '--link_color',
                    'items' => 
                    array (
                      0 => 
                      array (
                        'id' => 1,
                        'caption' => 'BEEF',
                        'subcaption' => 'Ethically Raised',
                        'url' => '/store/beef',
                        'src' => 'https://s3.amazonaws.com/grazecart/newtrialtemplate/images/1642777894_61eacd262e3b6.jpg',
                        'visible' => true,
                        'show_caption' => true,
                        'click_action' => 'link',
                        'grid_span' => 3,
                        'height' => 'auto',
                        'width' => 'auto',
                      ),
                      1 => 
                      array (
                        'id' => 2,
                        'caption' => 'POULTRY',
                        'subcaption' => 'Small Batch',
                        'url' => '/store/poultry',
                        'src' => 'https://s3.amazonaws.com/grazecart/newtrialtemplate/images/1642777754_61eacc9a945e7.jpg',
                        'visible' => true,
                        'show_caption' => true,
                        'click_action' => 'link',
                        'grid_span' => 3,
                        'height' => 'auto',
                        'width' => 'auto',
                      ),
                      2 => 
                      array (
                        'id' => 3,
                        'caption' => 'PORK',
                        'subcaption' => 'Heritage',
                        'url' => '/store/pork',
                        'src' => 'https://s3.amazonaws.com/grazecart/newtrialtemplate/images/1642777832_61eacce8792be.jpg',
                        'visible' => true,
                        'show_caption' => true,
                        'click_action' => 'link',
                        'grid_span' => 3,
                        'height' => 'auto',
                        'width' => 'auto',
                      ),
                    ),
                  )),
                'sort' => '2',
                'created_at' => $date,
                'updated_at' => $date
            ],
            [
                'page_id' => 1,
                'enabled' => 1,
                'title' => 'Image Banner',
                //'slug' => Str::slug('Image Banner'),
                'template' => 'Banner',
                'content' => '',
                'settings' => json_encode(array (
                    'alignment' => 'text-center',
                    'header' => '',
                    'message' => '',
                    'background' => 'https://s3.amazonaws.com/grazecart/newtrialtemplate/images/1642778308_61eacec4d063a.jpg',
                    'background_overlay_color' => 'transparent',
                    'cta_show' => false,
                    'cta' => 'Shop Now',
                    'cta_url' => '/store',
                    'cta_2_show' => false,
                    'cta_2' => 'Sign Up',
                    'cta_2_url' => '/signup',
                    'background_position' => '50% 100%',
                    'background_attachment' => 'scroll',
                    'background_repeat' => 'no-repeat',
                    'background_size' => 'cover',
                    'paddingTop' => 75,
                    'paddingBottom' => 75,
                    'paddingLeft' => 80,
                    'paddingRight' => 80,
                    'header_font_size' => 48,
                    'header_line_height' => 1.25,
                    'header_width' => 608,
                    'header_text_color' => '#FFF',
                    'header_letter_spacing' => 'normal',
                    'subheader_font_size' => 24,
                    'subheader_line_height' => 1.25,
                    'subheader_width' => 608,
                    'subheader_letter_spacing' => 'normal',
                    'subheader_text_color' => '#FFF',
                    'background_height' => 1118,
                    'match_image_height' => false,
                    'inner_container_max_width' => 1600,
                  )),
                'sort' => '4',
                'created_at' => $date,
                'updated_at' => $date
            ],
            [
                'page_id' => 1,
                'enabled' => 1,
                'title' => 'Testimonials',
                //'slug' => Str::slug('Testimonials'),
                'template' => 'testimonials',
                'content' => '',
                'settings' => json_encode([
                    'items' => [
                      0 => [
                        'id' => 1,
                        'body' => 'Insert a positive review from a happy customer. Add a profile photo to make it more personable.',
                        'attribution' => 'Happy Jane - ⭐⭐⭐⭐⭐',
                      ],
                      1 => [
                        'id' => 2,
                        'body' => 'If you don\'t have any reviews yet, hopefully, GrazeCart will help you get a few soon 😉',
                        'attribution' => 'GrazeCart Team - ⭐⭐⭐⭐⭐',
                      ],
                      2 => [
                        'id' => 3,
                        'body' => 'Insert positive testimony from a raving fan! Remember you can always delete this section.',
                        'attribution' => 'Raving Mike - ⭐⭐⭐⭐⭐',
                      ],
                    ],
                    'paddingTop' => 64,
                    'paddingBottom' => 96,
                    'header_show' => true,
                    'header' => 'Customer Reviews!',
                    'text_color' => '#3d3d3d',
                    'testimonial_bg_color' => '#f5f5f5',
                ]),
                'sort' => '5',
                'created_at' => $date,
                'updated_at' => $date
            ],
            [
                'page_id' => 1,
                'enabled' => 1,
                'title' => 'Divider',
                //'slug' => Str::slug('Divider'),
                'template' => 'Divider',
                'content' => '',
                'settings' => json_encode([
                    'bg_color' => '#dbdbdb',
                    'height' => 16,
                    'width' => 'full',
                    'layout_width' => 'full-width',
                  ]),
                'sort' => '6',
                'created_at' => $date,
                'updated_at' => $date
            ],
            [
                'page_id' => 1,
                'enabled' => 1,
                'title' => 'Image',
                //'slug' => Str::slug('Image'),
                'template' => 'RichText',
                'content' => '<p><img src="https://s3.amazonaws.com/grazecart/newtrialtemplate/images/1642778313_61eacec9a4b68.jpg"></p>',
                'settings' => json_encode([
                    'layout_width' => 'half-width',
                    'width' => 640,
                    'paddingTop' => 64,
                    'paddingBottom' => 64,
                    'line_height' => 1.75,
                    'background' => '#eeeeee',
                    'text_color' => '--text_color',
                    'link_color' => '--link_color',
                  ]),
                'sort' => '7',
                'created_at' => $date,
                'updated_at' => $date
            ],
            [
                'page_id' => 1,
                'enabled' => 1,
                'title' => 'Story Text',
                //'slug' => Str::slug('Story Text'),
                'template' => 'RichText',
                'content' => '<h2>Our Story</h2><p>A short paragraph about what you do, and why it matters to your ideal customer.</p><p>Something like, "Hey, we\'re the [Business Name], and we started raising [Insert Product] because we were tired of a food system that disregards honest farming practices, for the sake of profit.</p><p>If that\'s what you\'re after too, then we invite you to join us in making a change!"</p><p><strong>Learn more about what we do</strong> <a href="/about-us" rel="noopener noreferrer"><strong>here!</strong></a></p><p><br></p>',
                'settings' => json_encode([
                    'layout_width' => 'half-width',
                    'width' => 640,
                    'paddingTop' => 84,
                    'paddingBottom' => 64,
                    'line_height' => 1.75,
                    'background' => '#eeeeee',
                    'text_color' => '--text_color',
                    'link_color' => '--link_color',
                  ]),
                'sort' => '8',
                'created_at' => $date,
                'updated_at' => $date
            ],
            [
              'page_id' => 0,
              'enabled' => 0,
              'title' => 'Contact Card',
              //'slug' => Str::slug('Story Text'),
              'template' => 'ContactDetails',
              'content' => '',
              'settings' => json_encode([
                'layout_width' => 'quarter-width',
                'alignment' => 'text-left',
              ]),
              'sort' => '3',
              'created_at' => $date,
              'updated_at' => $date
          ],
          [
            'page_id' => 0,
            'enabled' => 0,
            'title' => 'Newsletter Signup Form',
            //'slug' => Str::slug('Story Text'),
            'template' => 'Newsletter',
            'content' => '',
            'settings' => json_encode(array (
              'layout_width' => 'half-width',
              'list' => '1',
              'header' => 'Join Our Newsletter',
              'background' => '#ffffff',
              'fontColor' => '#3d3d3d',
            )),
            'sort' => '2',
            'created_at' => $date,
            'updated_at' => $date
          ],
          [
            'page_id' => 0,
            'enabled' => 0,
            'title' => 'Social Networks',
            //'slug' => Str::slug('Story Text'),
            'template' => 'SocialNetworks',
            'content' => '',
            'settings' => json_encode(array (
              'orientation' => 'horizontal',
              'layout_width' => 'quarter-width',
              'links' => 
              array (
                0 => 
                array (
                  'data' => 
                  array (
                    'name' => 'Facebook',
                    'icon' => 'fa-facebook',
                  ),
                  'edit' => true,
                  'title' => 'Link',
                  'url' => '/',
                ),
                1 => 
                array (
                  'edit' => true,
                  'data' => 
                  array (
                    'name' => 'Instagram',
                    'icon' => 'fa-instagram',
                  ),
                  'url' => '/',
                ),
              ),
              'heading' => 'Follow Us',
            )),
            'sort' => '1',
            'created_at' => $date,
            'updated_at' => $date
          ],
          [
            'page_id' => 2,
            'enabled' => 0,
            'title' => 'Text',
            //'slug' => Str::slug('Story Text'),
            'template' => 'RichText',
            'content' => '<h1 class="ql-align-center">Our Story</h1><p class="ql-align-center">This is some filler text.&nbsp;This is a great place to tell your customers about your story. Tell them how you got started, why you believe in what you are doing, and how you can make their lives better.</p><p class="ql-align-center">Need help? Take the <a href="https://www.5minutemarketingmakeover.com/">StoryBrand free course</a> on how to craft a website message that covers.</p>',
            'settings' => json_encode([
              'layout_width' => 'full-width',
              'width' => '750',
              'paddingTop' => 64,
              'paddingBottom' => 64,
              'line_height' => 1.75,
              'background' => '--background_color',
              'text_color' => '--text_color',
              'link_color' => '--link_color',
            ]),
            'sort' => '1',
            'created_at' => $date,
            'updated_at' => $date
          ],
        ]);
    }
}

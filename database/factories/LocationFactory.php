<?php

namespace Database\Factories;

use App\Models\Location;
use Illuminate\Database\Eloquent\Factories\Factory;

/**
 * @extends Factory<Location>
 */
class LocationFactory extends Factory
{
    public function definition(): array
    {
        return [
            'title' => $this->faker->word(),
            'subtitle' => $this->faker->sentence(),
            'slug' => $this->faker->slug(),
            'type' => $this->faker->randomElement(['market', 'restaurant', 'retail']),
            'description' => $this->faker->sentence(),
            'hours_of_operation' => '9AM-5PM',
            'cover_photo' => null,
            'street' => '123 ' . $this->faker->streetName(),
            'street_2' => 'Unit 1',
            'city' => $this->faker->city(),
            'state' => $this->faker->state(),
            'zip' => $this->faker->postcode(),
            'country' => 'USA',
            'lat' => $this->faker->latitude(),
            'lng' => $this->faker->longitude(),
            'marker_color' => null,
            'marker_icon' => null,
            'contact_name' => $this->faker->name(),
            'contact_phone' => $this->faker->phoneNumber(),
            'contact_email' => $this->faker->safeEmail(),
            'contact_website' => $this->faker->url(),
            'visible' => true,
        ];
    }
}

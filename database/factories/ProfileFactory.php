<?php

namespace Database\Factories;

use App\Models\User;
use Illuminate\Database\Eloquent\Factories\Factory;

class ProfileFactory extends Factory
{
    /**
     * Define the model's default state.
     */
    public function definition(): array
    {
        return [
            'position_title' => $this->faker->jobTitle(),
            'bio' => $this->faker->paragraph(),
            'facebook' => $this->faker->url(),
            'twitter' => $this->faker->url(),
            'linkedin' => $this->faker->url(),
            'photo_path' => $this->faker->imageUrl(),
            'user_id' => User::factory(),
        ];
    }
}

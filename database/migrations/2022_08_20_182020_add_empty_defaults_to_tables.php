<?php

use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE vendors
            MODIFY email VARCHAR(255) DEFAULT NULL,
            MODIFY phone VARCHAR(255) DEFAULT NULL,
            MODIFY website VARCHAR(255) DEFAULT NULL
        ');
    }

    public function down(): void
    {
        \Illuminate\Support\Facades\DB::statement('ALTER TABLE vendors 
            MODIFY email VARCHAR(255) NOT NULL, 
            MODIFY phone VARCHAR(255) NOT NULL, 
            MODIFY website VARCHAR(255) NOT NULL
        ');
    }
};

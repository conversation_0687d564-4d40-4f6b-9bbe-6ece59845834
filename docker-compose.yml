version: "3"
services:
  app:
    image: php:8.2-fpm
    container_name: app
    restart: always
    build:
      context: .
      dockerfile: dockerfiles/Dockerfile.app
    volumes:
      - ./:/var/www/html
    environment:
      - APP_ENV=local
    env_file:
      - docker.env
    networks:
      - laravel_network
    ports:
      - "80:80"

  app-test:
    image: php:8.2-fpm
    container_name: app-test
    restart: always
    build:
      context: .
      dockerfile: dockerfiles/Dockerfile.app
    volumes:
      - ./:/var/www/html
    environment:
      - APP_ENV=test
    env_file:
      - docker.env
    networks:
      - laravel_network
    ports:
      - "8080:80"

  mysql:
    image: mysql:5.7
    container_name: mysql
    platform: linux/x86_64
    command: --default-authentication-plugin=mysql_native_password
    restart: always
    env_file:
      - docker.env
    ports:
      - "3306:3306"
    volumes:
      - mysql-data:/var/lib/mysql
    networks:
      - laravel_network

  mysql-test:
    image: mysql:5.7
    container_name: mysql-test
    platform: linux/x86_64
    command: --default-authentication-plugin=mysql_native_password --max_connections=200
    restart: always
    env_file:
      - docker.env
    ports:
      - "3307:3306"
    volumes:
      - mysql-test-data:/var/lib/mysql
    networks:
      - laravel_network

  redis:
    image: redis:4.0-alpine
    container_name: redis
    ports:
      - 63799:6379
    networks:
      - laravel_network

  redis-test:
    image: redis:4.0-alpine
    container_name: redis-test
    ports:
      - 63798:6379
    networks:
      - laravel_network

volumes:
  mysql-data:
  mysql-test-data:
    driver_opts:
      type: tmpfs
      device: tmpfs

networks:
  laravel_network:
    driver: bridge

import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';
import nesting from '@tailwindcss/nesting';
import aspectRatio from '@tailwindcss/aspect-ratio';

/** @type {import('tailwindcss').Config} */
export default {
    prefix: 'tw-',
    important: '.tw-reset',
    content: [
        './resources/theme/resources/views/**/*.blade.php',
        './resources/theme/resources/assets/js/**/*.js',
        './resources/theme/resources/assets/js/**/*.vue'
    ],
    theme: {
        extend: {
            backgroundImage: {
                'inner-shadow-l': 'linear-gradient(to right, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 2%)',
                'inner-shadow-x': 'linear-gradient(to right, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 2%), linear-gradient(to left, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 2%)',
                'inner-shadow-r': 'linear-gradient(to left, rgba(0,0,0,0.1) 0%, rgba(0,0,0,0) 2%)'
            },

            boxShadow: {
                'inner-left': 'inset 3px 0 3px -3px rgb(0 0 0 / 0.05)',
                'inner-right': 'inset -3px 0 10px 0 rgb(0 0 0 / 0.05)'
            },

            colors: {
                'theme-brand-color': 'rgb(var(--brand-color) / <alpha-value>)',
                'theme-brand-color-inverted': 'rgb(var(--brand-color-inverted) / <alpha-value>)',
                'theme-background-color': 'rgb(var(--background-color) / <alpha-value>)',
                'theme-text-color': 'rgb(var(--text-color) / <alpha-value>)',
                'theme-link-color': 'rgb(var(--link-color) / <alpha-value>)',
                'theme-action-color': 'rgb(var(--action-color) / <alpha-value>)',
                'theme-action-color-inverted': 'rgb(var(--action-color-inverted) / <alpha-value>)',
                'theme-announcement-bar-bg-color': 'rgb(var(--announcement-bar-bg-color) / <alpha-value>)',
                'theme-announcement-bar-text-color': 'rgb(var(--announcement-bar-text-color) / <alpha-value>)',
                'theme-announcement-bar-link-color': 'rgb(var(--announcement-bar-link-color) / <alpha-value>)',
                'theme-header-bg-color': 'rgb(var(--header-bg-color) / <alpha-value>)',
                'theme-header-border-color': 'rgb(var(--header-border-color) / <alpha-value>)',
                'theme-main-navigation-bg-color': 'rgb(var(--main-navigation-bg-color) / <alpha-value>)',
                'theme-main-navigation-link-color': 'rgb(var(--main-navigation-link-color) / <alpha-value>)',
                'theme-main-navigation-link-color-hover': 'rgb(var(--main-navigation-link-color-hover) / <alpha-value>)',
                'theme-main-navigation-link-bg-color': 'rgb(var(--main-navigation-link-bg-color) / <alpha-value>)',
                'theme-auxiliary-bg-color': 'rgb(var(--auxiliary-bg-color) / <alpha-value>)',
                'theme-auxiliary-border-color': 'rgb(var(--auxiliary-border-color) / <alpha-value>)',
                'theme-auxiliary-link-color': 'rgb(var(--auxiliary-link-color) / <alpha-value>)',
                'theme-order-status-bg-color': 'rgb(var(--order-status-bg-color) / <alpha-value>)',
                'theme-order-status-color': 'rgb(var(--order-status-color) / <alpha-value>)',
                'theme-store-menu-bg_color': 'rgb(var(--store-menu-bg_color) / <alpha-value>)',
                'theme-store-menu-color': 'rgb(var(--store-menu-color) / <alpha-value>)',
                'theme-footer-bg-color': 'rgb(var(--footer-bg-color) / <alpha-value>)',
                'theme-footer-color': 'rgb(var(--footer-color) / <alpha-value>)',
                'theme-footer-link-color': 'rgb(var(--footer-link-color)  <alpha-value>)',

                'buttercup': {
                    '50': '#fdf9e9',
                    '100': '#fbf1c6',
                    '200': '#f7e191',
                    '300': '#f2ca52',
                    '400': '#ecaf1d',
                    '500': '#dd9915',
                    '600': '#be7610',
                    '700': '#985310',
                    '800': '#7e4315',
                    '900': '#6b3718',
                    '950': '#3e1b0a'
                },

                'chestnut-rose': {
                    '50': '#fdf3f3',
                    '100': '#fbe6e5',
                    '200': '#f8d1d0',
                    '300': '#f2b1af',
                    '400': '#e88481',
                    '500': '#d9534f',
                    '600': '#c73f3b',
                    '700': '#a7322e',
                    '800': '#8a2d2a',
                    '900': '#742a28',
                    '950': '#3e1211'
                },

                'keppel': {
                    '50': '#f3f7f2',
                    '100': '#e3edde',
                    '200': '#c7dac0',
                    '300': '#9dc095',
                    '400': '#7da875',
                    '500': '#4e8247',
                    '600': '#396734',
                    '700': '#2d522a',
                    '800': '#264223',
                    '900': '#1f371d',
                    '950': '#101e10'
                }
            },

            fontFamily: {
                'display': 'var(--display-font-family)',
                'body': 'var(--body-font-family)',
                'serif': 'var(--display-font-family)',
                'sans': 'var(--body-font-family)'
            }
        },

        aspectRatio: {
            auto: 'auto',
            square: '1 / 1',
            video: '16 / 9',
            1: '1',
            2: '2',
            3: '3',
            4: '4',
            5: '5',
            6: '6',
            7: '7',
            8: '8',
            9: '9',
            10: '10',
            11: '11',
            12: '12',
            13: '13',
            14: '14',
            15: '15',
            16: '16'
        }
    },
    safelist: [
        // Google Autocomplete classes - https://developers.google.com/maps/documentation/javascript/place-autocomplete#style-autocomplete
        'pac-container', 'pac-icon', 'pac-item', 'pac-item:hover', 'pac-item-selected', 'pac-item-query', 'pac-matched'
    ],
    corePlugins: {
        aspectRatio: false
    },
    plugins: [
        forms,
        typography,
        nesting,
        aspectRatio
    ]
};

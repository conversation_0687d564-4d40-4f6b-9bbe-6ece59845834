APP_ENV=local
ROOT_DOMAIN=sevensons.test
API_DOMAIN="api.${ROOT_DOMAIN}"
APP_URL="http://${ROOT_DOMAIN}"

APP_TIMEZONE=UTC
APP_LOCALE=en
APP_FALLBACK_LOCALE=en
APP_FAKER_LOCALE=en_US
APP_MAINTENANCE_DRIVER=file
APP_MAINTENANCE_STORE=database
BCRYPT_ROUNDS=12
APP_NAME=GrazeCart
VITE_APP_NAME="${APP_NAME}"
APP_DEBUG=true
APP_KEY=M4Kemd6WNYlMOAYRDhPomEQuq2jXLgpi

LOG_CHANNEL=stack
LOG_STACK=single
LOG_SLACK_WEBHOOK_URL=*****************************************************************************
LOG_DAILY_DAYS=7

DB_CONNECTION=mysql
DB_HOST=127.0.0.1
DB_DATABASE=seven_sons_farms
DB_USERNAME=root
DB_PASSWORD=password
DB_PORT=3357

HORIZON_PREFIX=grazecart:
HORIZON_SECRET=
HORIZON_PATH="/admin/horizon"

BROADCAST_CONNECTION=log

CACHE_STORE=redis
CACHE_PREFIX=grazecart

SESSION_DRIVER=redis
SESSION_CONNECTION=default
SESSION_ENCRYPT=false
SESSION_PATH=/
SESSION_DOMAIN=null

QUEUE_DRIVER=redis
QUEUE_CONNECTION=redis

REDIS_PORT=63799

MAIL_MAILER=smtp
MAIL_HOST=0.0.0.0
MAIL_PORT=1025
MAIL_USERNAME=null
MAIL_PASSWORD=null
MAIL_ENCRYPTION=
MAIL_FROM_ADDRESS=
MAIL_FROM_NAME=
MAIL_MARKETING_FROM_ADDRESS=
MAIL_NO_REPLY_ADDRESS=

GEOCODIO_KEY=
GEOCODERCA_KEY=

MAILGUN_DOMAIN=sandbox6232.mailgun.org
MAILGUN_MARKETING_DOMAIN=sandbox6232.mailgun.org
MAILGUN_SECRET=
MAILGUN_TESTING_ENDPOINT=http://bin.mailgun.net/0a78c2ce

S3_KEY=
S3_SECRET=
S3_BUCKET=grazecart-demo
S3_ROOT=demo
VITE_S3_BUCKET=grazecart-demo

FILE_UPLOAD_PREFIX=

STRIPE_SECRET=
STRIPE_KEY=
STRIPE_WEBHOOK_SECRET=
STRIPE_CLIENT_ID=
STRIPE_APPLICATION_FEE=
STRIPE_MANAGED=true

BUGSNAG_API_KEY=
VITE_BUGSNAG_JS_API_KEY=

VITE_TINYMCE_API_KEY=

SLACK_WEB_HOOK=
SLACK_DEFAULT_ROOM=grazecart-test

GOOGLE_GEOCODER_API_KEY=
GOOGLE_PLACES_JS_API_KEY=
GOOGLE_SHEETS_API_KEY=
GOOGLE_ANALYTICS_ID=
GOOGLE_ADS_ID=
INVENTORY_SPREADSHEET_ID=
INVENTORY_WORKSHEET_NAME=

DRIP_API_KEY=
DRIP_ACCOUNT_ID=

DEBUGBAR_ENABLED=false
TELESCOPE_ENABLED=false
TELESCOPE_PATH="/admin/telescope"

GRAVITY_ASSET_URL=https://assets.emergepay-sandbox.chargeitpro.com
GRAVITY_PAYMENTS_ENVIRONMENT_URL=https://api.emergepay-sandbox.chargeitpro.com/virtualterminal/v1
GRAVITY_PAYMENTS_OID=**********
GRAVITY_PAYMENTS_AUTH_TOKEN=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************************.jhIq5Gp6LP-uQDxM_x2SBgy3mR8x1ez5YNbK8pGVCns

TRIBE_PLAN_ID=price_1HTsUJH02VjJBzfMyx0eKYNH

FULL_ACCESS_DOMAINS=sev0618-02-19.test
BETA_ACCESS_DOMAINS=sev0618-02-19.test

TWILIO_AUTH_TOKEN=
TWILIO_ACCOUNT_SID=
TWILIO_FROM=

# optional
# TWILIO_ALPHA_SENDER=HELLO
# Set a number that call calls/messages should be routed to for debugging
# TWILIO_DEBUG_TO=
# Optional but recommended
# TWILIO_SMS_SERVICE_SID=MG0a0aaaaaa00aa00a00a000a00000a00a

CANNY_APP_ID=

NOVA_LICENSE_KEY=

SEGMENT_WRITE_KEY=

NOTIFICATIONS_TENANT_REGISTRATION=<EMAIL>,<EMAIL>,<EMAIL>

TOLSTOY_APP_KEY=

HOMEPAGE_FEATURED_BUNDLE_IDS=

SCOUT_DRIVER=database
SCOUT_PREFIX=sevensonsfarms:
SCOUT_QUEUE=false
SCOUT_IDENTIFY=false
MEILISEARCH_HOST=http://127.0.0.1:7700
MEILISEARCH_KEY=

FACEBOOK_PIXEL_ID=

SUBSCRIPTION_DEMAND_REPORT_RECIPIENTS=

CLOUDFRONT_DISTRIBUTION_URL=
VITE_CLOUDFRONT_DISTRIBUTION_URL=

ORDER_CREDIT_REPORT_RECIPIENTS=

NEW_STOREFRONT_ENABLED=false

META_ACCESS_TOKEN=
META_TEST_EVENT_CODE=

WELCOME_COUPON_CODE=WELCOME20

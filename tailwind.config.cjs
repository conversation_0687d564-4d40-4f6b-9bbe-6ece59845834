import forms from '@tailwindcss/forms';
import typography from '@tailwindcss/typography';
import aspectRatio from '@tailwindcss/aspect-ratio';

/** @type {import('tailwindcss').Config} */
export default {
    content: [
        './resources/views/**/*.blade.php',
        './resources/assets/js/admin/**/*.js',
        './resources/assets/js/admin/**/*.vue',
        './resources/assets/js/pos/**/*.js',
        './resources/assets/js/pos/**/*.vue'
    ],
    theme: {
        extend: {
            animation: {
                'pulse-slow': 'pulse 3s linear infinite'
            },

            colors: {
                'keppel': {
                    DEFAULT: '#7da875',
                    50: '#f2f7f1',
                    100: '#e6f0e2',
                    200: '#c2dbbf',
                    300: '#a4cfa1',
                    400: '#86c284',
                    500: '#68b567',
                    600: '#7da875',
                    700: '#5e9763',
                    800: '#4a7e4e',
                    900: '#3a643e'
                }
            }
        },

        aspectRatio: {
            auto: 'auto',
            square: '1 / 1',
            video: '16 / 9',
            1: '1',
            2: '2',
            3: '3',
            4: '4',
            5: '5',
            6: '6',
            7: '7',
            8: '8',
            9: '9',
            10: '10',
            11: '11',
            12: '12',
            13: '13',
            14: '14',
            15: '15',
            16: '16'
        }
    },
    corePlugins: {
        aspectRatio: false
    },
    plugins: [
        forms,
        typography,
        aspectRatio
    ]
};

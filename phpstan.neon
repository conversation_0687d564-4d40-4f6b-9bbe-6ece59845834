includes:
    - ./vendor/larastan/larastan/extension.neon

parameters:
    paths:
        - app

    # The level 9 is the highest level
    level: 5

    ignoreErrors:
        - '#Unsafe usage of new static#'
        - '#Call to an undefined method Illuminate\\Database\\Eloquent\\Builder::unconfirmed\(\)#'

    excludePaths:
        - ./*/*/FileToBeExcluded.php

    # try removing this section after laravel upgrade and replace with docblock: @extends JsonResource<\App\Model\Something>
    universalObjectCratesClasses:
        - Illuminate\Http\Resources\Json\JsonResource
        - Spatie\SchemalessAttributes\SchemalessAttributes
        - Stripe\StripeObject

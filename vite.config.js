import { defineConfig } from 'vite';
import laravel from 'laravel-vite-plugin';
import vue from '@vitejs/plugin-vue';

export default defineConfig({
    plugins: [
        laravel([
            'resources/assets/css/tailwind.css',
            'resources/assets/css/graze.css',
            'resources/assets/less/admin.less',

            'resources/theme/resources/assets/less/theme.less',
            'resources/theme/resources/assets/css/tailwind.css',
            'resources/theme/resources/assets/css/tailwind-full.css',
            'resources/theme/resources/assets/css/tailwind-scoped.css',
            'resources/theme/resources/assets/js/app.js',

            'resources/assets/js/admin/main.js',
            'resources/assets/js/admin/inertia-app.js',
            'app/PickupManager/js/pickupManager.js',
            'resources/assets/js/theme/theme.js',
            'resources/assets/js/theme/homepage.js',
            'resources/assets/js/theme/products/bundle.js',
            'resources/assets/js/theme/products/standard.js'
        ]),
        vue({
            template: {
                transformAssetUrls: {
                    base: null,
                    includeAbsolute: false
                }
            }
        })
    ],
    resolve: {
        alias: {
            vue: 'vue/dist/vue.esm-bundler.js'
        }
    }
});

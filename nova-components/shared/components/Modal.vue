<template>
    <teleport to="#modals">
        <TransitionRoot as="template" :show="open">
            <Dialog as="div" class="fixed z-40 inset-0 overflow-y-auto" :initialFocus="getActiveElement()" @close="$emit('close')">
                <div class="flex items-end justify-center min-h-screen text-center sm:block px-3 md:px-0 py-3 md:py-6">
                    <TransitionChild as="template"
                                     enter="ease-out duration-300"
                                     enter-from="opacity-0"
                                     enter-to="opacity-100"
                                     leave="ease-in duration-200"
                                     leave-from="opacity-100"
                                     leave-to="opacity-0"
                    >
                        <DialogOverlay class="fixed inset-0 bg-gray-500 opacity-75 transition-opacity" />
                    </TransitionChild>

                    <!-- This element is to trick the browser into centering the modal contents. -->
                    <span class="hidden sm:inline-block sm:align-middle sm:h-screen" aria-hidden="true">&#8203;</span>
                    <TransitionChild as="template"
                                     enter="ease-out duration-300"
                                     enter-from="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                                     enter-to="opacity-100 translate-y-0 sm:scale-100"
                                     leave="ease-in duration-200"
                                     leave-from="opacity-100 translate-y-0 sm:scale-100"
                                     leave-to="opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                    >
                        <div class="relative z-40 inline-block align-bottom bg-white rounded-lg text-left overflow-hidden shadow-xl transform transition-all sm:my-8 sm:align-middle sm:max-w-lg sm:w-full">
                            <slot></slot>
                        </div>
                    </TransitionChild>
                </div>
            </Dialog>
        </TransitionRoot>
    </teleport>
</template>

<script>
import { Dialog, DialogOverlay, DialogTitle, TransitionChild, TransitionRoot } from '@headlessui/vue'
export default {
    props: {
        open: {
            default: false
        }
    },

    emits: ['close'],

    components: {
        Dialog,
        DialogOverlay,
        DialogTitle,
        TransitionChild,
        TransitionRoot,
    },

    setup() {
        // fixes focus issue: See: https://github.com/tailwindlabs/headlessui/issues/825
        const getActiveElement = () => {
            return document.activeElement;
        }

        return {
            getActiveElement
        }
    }
}
</script>
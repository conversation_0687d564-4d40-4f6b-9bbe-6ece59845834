body {
    margin: 0px;
    padding: 0px;
    font-family: "Helvetica Neue", Helvetica, Roboto, Arial, sans-serif;
    font-size: 14px;
    color: #3d3d3d;
}

.invoice {
    page-break-after: always;
    break-after: always;
}

.text-right {
    text-align: right;
}

.text-center {
    text-align: center;
}


table {
    width: 100%;
}

a[href]::after {
    content: none;
}

h1 {
    font-size: 18px;
}

h2 {
    font-size: 1.1em;
    margin: 0px 0px 0.25em 0px;
}

h3 {
    margin: 0px;
}

ul {
    list-style-type: none;
    margin: 0px;
    padding: 0px;
}

hr {
    border: none;
    height: 1px;
    background-color: #dddddd;
    margin: 0px;
    padding: 0px;
}

.type {
    font-size: 2.2em;
    color: #5d5d5d;
    margin-bottom: 0.25em;
    margin-top: 0px;
    font-family: 'Helvetica Neue', Arial, Helvetica, sans-serif;
    font-weight: 300;
}

.customer-details ul {
    list-style-type: none;
    margin: 0px;
    padding: 0px;
}

.final-total {
    font-size: 1.3em;
    margin-top: 0.25em;
    padding-top: 0.25em;
    display: inline-block;
}

#header {
    width: 100%;
    margin-bottom: 0.5cm;
    background-color: #eee;
    overflow: hidden;
}

#items {
    width: 100%;
    margin: 2em 0px 0.5em 0px;
}

#items table {
    width: 100%;
    text-align: left;
    margin-bottom: 0.5em;
}

#items table thead tr th {
    color: #3d3d3d;
    font-size: 1em;
    text-transform: uppercase;
    padding-bottom: 0.25cm;
}

#items table tbody tr td {
    color: #3d3d3d;
    font-size: 12px;
}


.totals {
    margin-top: 30px;
    text-align: right;
}

.totals ul li {
    font-size: 1em;
}

.text-right {
    text-align: right;
}
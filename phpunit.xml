<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" backupGlobals="false" bootstrap="vendor/autoload.php" colors="true" processIsolation="false"
         stopOnFailure="false" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/10.5/phpunit.xsd" cacheDirectory=".phpunit.cache"
         backupStaticProperties="false">
    <coverage/>
    <testsuites>
        <testsuite name="Unit">
            <directory suffix="Test.php">./tests/Unit</directory>
        </testsuite>
        <testsuite name="Feature">
            <directory suffix="Test.php">./tests/Feature</directory>
        </testsuite>
    </testsuites>
    <php>
        <ini name="memory_limit" value="4G"/>
        <env name="APP_ENV" value="testing"/>
        <env name="BCRYPT_ROUNDS" value="4"/>
        <env name="DB_PORT" value="3359"/>
        <env name="CACHE_STORE" value="redis"/>
        <env name="MAIL_MAILER" value="array"/>
        <env name="MAIL_DRIVER" value="array"/>
        <env name="QUEUE_DRIVER" value="sync"/>
        <env name="QUEUE_CONNECTION" value="sync"/>
        <env name="SESSION_DRIVER" value="array"/>
        <!-- override any valid envs that arent needed for testing -->
        <env name="GEOCODIO_KEY" value=""/>
        <env name="GEOCODERCA_KEY" value=""/>
        <env name="MAIL_FROM_ADDRESS" value="<EMAIL>"/>
        <env name="MAIL_MARKETING_FROM_ADDRESS" value="<EMAIL>"/>
        <env name="MAILGUN_DOMAIN" value=""/>
        <env name="MAILGUN_MARKETING_DOMAIN" value=""/>
        <env name="MAILGUN_SECRET" value=""/>
        <env name="MAILGUN_TESTING_ENDPOINT" value=""/>
        <env name="S3_KEY" value=""/>
        <env name="S3_SECRET" value=""/>
        <env name="STRIPE_KEY" value=""/>
        <env name="STRIPE_SECRET" value=""/>
        <env name="STRIPE_CLIENT_ID" value=""/>
        <env name="BUGSNAG_API_KEY" value=""/>
        <env name="SLACK_WEB_HOOK" value=""/>
        <env name="GOOGLE_API_KEY" value=""/>
        <env name="GOOGLE_MAPS_API_KEY" value=""/>
        <env name="DRIP_API_KEY" value="dummykey"/>
        <env name="GRAVITY_PAYMENTS_AUTH_TOKEN" value=""/>
        <env name="TWILIO_AUTH_TOKEN" value=""/>
        <env name="TWILIO_ACCOUNT_SID" value=""/>
        <env name="SEGMENT_WRITE_KEY" value=""/>
    </php>
    <source>
        <include>
            <directory suffix=".php">./app</directory>
        </include>
    </source>
</phpunit>

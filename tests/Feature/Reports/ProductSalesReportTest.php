<?php

namespace Tests\Feature\Reports;

use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\Tag;
use App\Models\Vendor;
use App\Support\Enums\OrderStatus;
use Illuminate\Support\Collection;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProductSalesReportTest extends TenantTestCase
{
    #[Test]
    public function an_guest_cannot_fetch_the_product_sales_report(): void
    {
        $this->get(route('admin.reports.product-sales'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_fetch_the_product_sales_report(): void
    {
        $this->actingAsCustomer()
            ->get(route('admin.reports.product-sales'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_can_fetch_the_product_sales_report(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.reports.product-sales'))
            ->assertOk()
            ->assertViewIs('reports.product-sales.index')
            ->assertViewHas([
                'savedFilters',
                'appliedFilters',
                'appliedFilter',
                'results',
            ]);
    }

    #[Test]
    public function the_product_sales_report_includes_the_expected_items(): void
    {
        $order = Order::factory()->create(['confirmed' => true]);
        $item = OrderItem::factory()->create(['order_id' => $order->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.product-sales'))
            ->assertOk()
            ->assertViewHas([
                'results' => fn(Collection $arg) => $arg->isNotEmpty()
                    && $arg->contains(fn($arg_item) => $arg_item->id === $item->id)
            ]);
    }

    #[Test]
    public function the_product_sales_report_includes_the_expected_item_columns(): void
    {
        $order = Order::factory()->create(['confirmed' => true]);
        $vendor = Vendor::factory()->create(['title' => 'testvendor']);
        $product = Product::factory()->create(['title' => 'testtitle', 'sku' => 'testsku', 'custom_sort' => 'testsort', 'unit_description' => 'testdesc', 'item_cost' => 5, 'vendor_id' => $vendor->id]);
        $item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.product-sales'))
            ->assertOk()
            ->assertViewHas([
                'results' => fn(Collection $arg) => $arg->isNotEmpty()
                    && $arg->contains(function ($arg_item) use ($item, $product) {
                        return $arg_item->id === $item->id
                            && $arg_item->orders > 0
                            && $arg_item->item_title === $item->title
                            && $arg_item->product_id === $product->id
                            && $arg_item->on_order > 0
                            && $arg_item->total > 0
                            && $arg_item->weight > 0
                            && $arg_item->title === 'testtitle'
                            && $arg_item->unit_description === 'testdesc'
                            && $arg_item->sku === 'testsku'
                            && $arg_item->sort === 'testsort'
                            && $arg_item->item_cost === 500
                            && $arg_item->vendor === 'testvendor';
                    })
            ]);
    }

    #[Test]
    public function the_product_sales_report_does_not_include_unconfirmed_orders(): void
    {
        $order = Order::factory()->create(['confirmed' => false]);
        $item = OrderItem::factory()->create(['order_id' => $order->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.product-sales'))
            ->assertOk()
            ->assertViewHas([
                'results' => fn(Collection $arg) => $arg->doesntContain(fn($arg_item) => $arg_item->id === $item->id)
            ]);
    }

    #[Test]
    public function the_product_sales_report_does_not_include_canceled_orders(): void
    {
        $order = Order::factory()->create(['confirmed' => true, 'canceled' => true]);
        $item = OrderItem::factory()->create(['order_id' => $order->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.product-sales'))
            ->assertOk()
            ->assertViewHas([
                'results' => fn(Collection $arg) => $arg->doesntContain(fn($arg_item) => $arg_item->id === $item->id)
            ]);
    }

    #[Test]
    public function the_product_sales_report_can_filter_by_order_status(): void
    {
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'status_id' => OrderStatus::confirmed()]);
        $item_one = OrderItem::factory()->create(['order_id' => $order_one->id]);

        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'status_id' => OrderStatus::processing()]);
        $item_two = OrderItem::factory()->create(['order_id' => $order_two->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.product-sales', [
                'order_status' => [OrderStatus::processing(), OrderStatus::packed()]
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);
    }

    #[Test]
    public function the_product_sales_report_can_filter_by_pickup_date(): void
    {
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pickup_date' => today()->subDay()]);
        $item_one = OrderItem::factory()->create(['order_id' => $order_one->id]);

        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pickup_date' => today()->addDay()]);
        $item_two = OrderItem::factory()->create(['order_id' => $order_two->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.product-sales', [
                'pickup_date' => []
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->contains(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.product-sales', [
                'pickup_date' => [
                    'start' => today()->format('Y-m-d'),
                ]
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.product-sales', [
            'pickup_date' => [
                'end' => today()->format('Y-m-d')
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->contains(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.product-sales', [
            'pickup_date' => [
                'start' => today()->subDays(2)->format('Y-m-d'),
                'end' => today()->addDays(2)->format('Y-m-d')
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->contains(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.product-sales', [
            'pickup_date' => [
                'start' => today()->subDays(4)->format('Y-m-d'),
                'end' => today()->subDays(2)->format('Y-m-d')
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);
    }

    #[Test]
    public function the_product_sales_report_can_filter_by_confirmed_date(): void
    {
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => today()->subDay()]);
        $item_one = OrderItem::factory()->create(['order_id' => $order_one->id]);

        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'confirmed_date' => today()->addDay()]);
        $item_two = OrderItem::factory()->create(['order_id' => $order_two->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.product-sales', [
                'confirmed_date' => []
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->contains(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.product-sales', [
            'confirmed_date' => [
                'start' => today()->format('Y-m-d'),
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.product-sales', [
            'confirmed_date' => [
                'end' => today()->format('Y-m-d')
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->contains(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.product-sales', [
            'confirmed_date' => [
                'start' => today()->subDays(2)->format('Y-m-d'),
                'end' => today()->addDays(2)->format('Y-m-d')
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->contains(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);

        $this->get(route('admin.reports.product-sales', [
            'confirmed_date' => [
                'start' => today()->subDays(4)->format('Y-m-d'),
                'end' => today()->subDays(2)->format('Y-m-d')
            ]
        ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);
    }

    #[Test]
    public function the_product_sales_report_can_filter_by_collection(): void
    {
        $collection_one = \App\Models\Collection::factory()->create();
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false]);
        $item_one = OrderItem::factory()->create(['order_id' => $order_one->id]);
        $item_one->product->collections()->attach($collection_one);

        $collection_two = \App\Models\Collection::factory()->create();
        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false]);
        $item_two = OrderItem::factory()->create(['order_id' => $order_two->id]);
        $item_two->product->collections()->attach($collection_two);

        $this->actingAsAdmin()
            ->get(route('admin.reports.product-sales', [
                'collection_id' => $collection_two->id,
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);
    }

    #[Test]
    public function the_product_sales_report_includes_deleted_products_by_default(): void
    {
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false]);
        $item_one = OrderItem::factory()->create(['order_id' => $order_one->id]);
        $item_one->product->delete();

        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false]);
        $item_two = OrderItem::factory()->create(['order_id' => $order_two->id]);
        $item_two->product->delete();

        $this->actingAsAdmin()
            ->get(route('admin.reports.product-sales'))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->contains(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);
    }

    #[Test]
    public function the_product_sales_report_includes_deleted_products_when_filtering_by_a_product_filter(): void
    {
        $collection_one = \App\Models\Collection::factory()->create();
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false]);
        $item_one = OrderItem::factory()->create(['order_id' => $order_one->id]);
        $item_one->product->collections()->attach($collection_one);

        $collection_two = \App\Models\Collection::factory()->create();
        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false]);
        $item_two = OrderItem::factory()->create(['order_id' => $order_two->id]);
        $item_two->product->collections()->attach($collection_two);
        $item_two->product->delete();

        $this->actingAsAdmin()
            ->get(route('admin.reports.product-sales', [
                'collection_id' => $collection_two->id,
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id);
                }
            ]);
    }

    #[Test]
    public function the_report_can_filter_by_order_tags(): void
    {
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pickup_date' => today()->subDay()]);
        $item_one = OrderItem::factory()->create(['order_id' => $order_one->id]);
        $tag_one = Tag::factory()->create();
        $order_one->tags()->attach($tag_one);

        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pickup_date' => today()->subDay()]);
        $item_two = OrderItem::factory()->create(['order_id' => $order_two->id]);
        $tag_two = Tag::factory()->create();
        $order_two->tags()->attach($tag_two);

        $order_three = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pickup_date' => today()->subDay()]);
        $item_three = OrderItem::factory()->create(['order_id' => $order_three->id]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.product-sales', [
                'order_tags' => [],
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two, $item_three) {
                    return $arg->contains(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_three->id);
                }
            ]);


        $this->actingAsAdmin()
            ->get(route('admin.reports.product-sales', [
                'order_tags' => [$tag_one->id],
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two, $item_three) {
                    return $arg->contains(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_two->id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_three->id);
                }
            ]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.product-sales', [
                'order_tags' => [$tag_two->id],
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $arg) use ($item_one, $item_two, $item_three) {
                    return $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_one->id)
                        && $arg->contains(fn($arg_item) => $arg_item->id === $item_two->id)
                        && $arg->doesntContain(fn($arg_item) => $arg_item->id === $item_three->id);
                }
            ]);
    }
}

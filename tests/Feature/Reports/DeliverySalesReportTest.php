<?php

namespace Tests\Feature\Reports;

use App\Exports\DeliverySalesExport;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Tag;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Facades\Excel;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DeliverySalesReportTest extends TenantTestCase
{
    #[Test]
    public function an_guest_cannot_fetch_the_delivery_sales_report(): void
    {
        $this->get(route('admin.reports.delivery-sales'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function a_non_admin_cannot_fetch_the_delivery_sales_report(): void
    {
        $this->actingAsCustomer()
            ->get(route('admin.reports.delivery-sales'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_can_fetch_the_delivery_sales_report(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.reports.delivery-sales'))
            ->assertOk()
            ->assertViewIs('reports.delivery-sales.index')
            ->assertViewHas([
                'savedFilters',
                'appliedFilters',
                'appliedFilter',
                'results',
            ]);
    }

    #[Test]
    public function an_admin_can_fetch_the_delivery_sales_report_as_an_export(): void
    {
        Excel::fake();

        $this->actingAsAdmin()
            ->get(route('admin.reports.delivery-sales', ['export']))
            ->assertOk();

        Excel::matchByRegex();
        Excel::assertDownloaded('/delivery_sales_.+\.csv/', fn(DeliverySalesExport $export) => true);
    }

    #[Test]
    public function the_report_can_filter_by_order_tags(): void
    {
        $order_one = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pickup_date' => today()->subDay()]);
        OrderItem::factory()->create(['order_id' => $order_one->id, 'subtotal' => 250, 'weight' => 23]);
        $tag_one = Tag::factory()->create();
        $order_one->tags()->attach($tag_one);


        $order_two = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pickup_date' => today()->subDay()]);
        OrderItem::factory()->create(['order_id' => $order_two->id, 'subtotal' => 200, 'weight' => 2]);
        $tag_two = Tag::factory()->create();
        $order_two->tags()->attach($tag_two);

        $order_three = Order::factory()->create(['confirmed' => true, 'canceled' => false, 'pickup_date' => today()->subDay()]);
        OrderItem::factory()->create(['order_id' => $order_three->id, 'subtotal' => 150, 'weight' => 1]);


        $this->actingAsAdmin()
            ->get(route('admin.reports.delivery-sales', [
                'order_tags' => [],
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $results) use ($order_one, $order_two, $order_three, $tag_one, $tag_two) {
                    return $results->contains(fn($results_row) => $results_row->subtotal == '250' && $results_row->weight === 23.0)
                        && $results->contains(fn($results_row) => $results_row->subtotal == '200' && $results_row->weight === 2.0)
                        && $results->contains(fn($results_row) => $results_row->subtotal == '150' && $results_row->weight === 1.0);
                    }
            ]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.delivery-sales', [
                'order_tags' => [$tag_one->id],
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $results) use ($order_one, $order_two, $order_three, $tag_one, $tag_two) {
                    return $results->contains(fn($results_row) => $results_row->subtotal == '250' && $results_row->weight === 23.0)
                        && $results->doesntContain(fn($results_row) => $results_row->subtotal == '200' && $results_row->weight === 2.0)
                        && $results->doesntContain(fn($results_row) => $results_row->subtotal == '150' && $results_row->weight === 1.0);
                }
            ]);

        $this->actingAsAdmin()
            ->get(route('admin.reports.delivery-sales', [
                'order_tags' => [$tag_two->id],
            ]))
            ->assertOk()
            ->assertViewHas([
                'results' => function (Collection $results) use ($order_one, $order_two, $order_three, $tag_one, $tag_two) {
                    return $results->doesntContain(fn($results_row) => $results_row->subtotal == '250' && $results_row->weight === 23.0)
                        && $results->contains(fn($results_row) => $results_row->subtotal == '200' && $results_row->weight === 2.0)
                        && $results->doesntContain(fn($results_row) => $results_row->subtotal == '150' && $results_row->weight === 1.0);
                }
            ]);
    }
}
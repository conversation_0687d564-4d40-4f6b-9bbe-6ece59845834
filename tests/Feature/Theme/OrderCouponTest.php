<?php

namespace Tests\Feature\Theme;

use App\Models\Coupon;
use App\Models\Order;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class OrderCouponTest extends TenantTestCase
{
    #[Test]
    public function an_unauthenticated_user_cannot_fetch_order_coupons(): void
    {
        $order = Order::factory()->create();

        $this->get(route('theme.orders.coupons.index', compact('order')))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function a_customer_cannot_fetch_order_coupons_for_invalid_order(): void
    {
        $this->actingAsCustomer()
            ->get(route('theme.orders.coupons.index', ['order' => 123123123123]))
            ->assertRedirect();
    }

    #[Test]
    public function an_customer_cannot_fetch_order_coupons_for_another_customers_order(): void
    {
        $order = Order::factory()->create();

        $this->actingAsCustomer()
            ->get(route('theme.orders.coupons.index', compact('order')))
            ->assertRedirect();
    }

    #[Test]
    public function a_customer_can_fetch_order_coupons_for_their_order(): void
    {
        $order = Order::factory()->create();

        $coupon_one = Coupon::factory()->create();
        $coupon_two = Coupon::factory()->create();

        $order->applyCoupon($coupon_one);
        $order->applyCoupon($coupon_two);

        $this->actingAs($order->customer)
            ->get(route('theme.orders.coupons.index', compact('order')))
            ->assertOk()
            ->assertJsonFragment(['id' => $coupon_one->id])
            ->assertJsonFragment(['id' => $coupon_two->id]);
    }

    #[Test]
    public function an_unauthenticated_user_cannot_store_order_coupons(): void
    {
        $order = Order::factory()->create();

        $this->post(route('theme.orders.coupons.store', compact('order')))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function a_customer_cannot_store_order_coupons_for_invalid_order(): void
    {
        $this->actingAsCustomer()
            ->post(route('theme.orders.coupons.store', ['order' => 123123123123]))
            ->assertRedirect();
    }

    #[Test]
    public function an_customer_cannot_store_order_coupons_for_another_customers_order(): void
    {
        $order = Order::factory()->create();

        $this->actingAsCustomer()
            ->post(route('theme.orders.coupons.store', compact('order')))
            ->assertRedirect();
    }

    #[Test]
    public function it_validates_the_coupon_store_request(): void
    {
        $order = Order::factory()->create();

        $this->actingAs($order->customer)
            ->post(route('theme.orders.coupons.store', compact('order')))
            ->assertInvalid(['coupon_code' => 'The coupon code field is required.']);

        $this->post(route('theme.orders.coupons.store', compact('order')), [
                'coupon_code' => 123123123
            ])
            ->assertInvalid(['coupon_code' => 'The selected coupon code is invalid.']);
    }

    #[Test]
    public function it_stores_the_coupon_to_the_order(): void
    {
        $order = Order::factory()->create();
        $coupon = Coupon::factory()->create();

        $this->actingAs($order->customer)
            ->post(route('theme.orders.coupons.store', compact('order')), [
                'coupon_code' => $coupon->code
            ])
            ->assertOk()
            ->assertJsonStructure([
                'coupon',
                'summary',
                'tax',
                'order_discount',
                'fees_subtotal',
                'delivery_fee',
                'delivery_rate',
                'subtotal',
                'total',
            ]);

        $this->assertDatabaseHas('coupon_order', [
            'order_id' => $order->id,
            'coupon_id' => $coupon->id,
            'user_id' => $order->customer_id
        ]);
    }

    #[Test]
    public function an_unauthenticated_user_cannot_destroy_order_coupons(): void
    {
        $order = Order::factory()->create();
        $coupon = Coupon::factory()->create();

        $this->delete(route('theme.orders.coupons.destroy', compact('order', 'coupon')))
            ->assertRedirect(route('login'));
    }

    #[Test]
    public function a_customer_cannot_destroy_order_coupons_for_invalid_order(): void
    {
        $coupon = Coupon::factory()->create();

        $this->actingAsCustomer()
            ->delete(route('theme.orders.coupons.destroy', ['order' => 123123123123, 'coupon' => $coupon->id]))
            ->assertRedirect();
    }

    #[Test]
    public function a_customer_cannot_destroy_order_coupons_for_invalid_coupon(): void
    {
        $order = Order::factory()->create();

        $this->actingAsCustomer()
            ->delete(route('theme.orders.coupons.destroy', ['order' => $order->id, 'coupon' => 123123123123]))
            ->assertRedirect();
    }

    #[Test]
    public function an_customer_cannot_destroy_order_coupons_for_another_customers_order(): void
    {
        $order = Order::factory()->create();
        $coupon = Coupon::factory()->create();

        $this->actingAsCustomer()
            ->delete(route('theme.orders.coupons.destroy', compact('order', 'coupon')))
            ->assertRedirect();
    }

    #[Test]
    public function an_customer_can_destroy_order_coupons(): void
    {
        $order = Order::factory()->create();
        $coupon = Coupon::factory()->create();

        $order->discounts()->attach($coupon->id, ['user_id' => $order->customer_id, 'savings' => 1234]);

        $this->assertDatabaseHas('coupon_order', [
            'order_id' => $order->id,
            'coupon_id' => $coupon->id,
        ]);

        $this->actingAs($order->customer)
            ->delete(route('theme.orders.coupons.destroy', compact('order', 'coupon')))
            ->assertOk()
            ->assertJsonStructure([
                'summary',
                'tax',
                'order_discount',
                'fees_subtotal',
                'delivery_fee',
                'delivery_rate',
                'subtotal',
                'total',
            ]);

        $this->assertDatabaseMissing('coupon_order', [
            'order_id' => $order->id,
            'coupon_id' => $coupon->id,
        ]);
    }
}
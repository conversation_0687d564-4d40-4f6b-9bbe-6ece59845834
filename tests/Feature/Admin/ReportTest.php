<?php

namespace Tests\Feature\Admin;

use App\Models\Product;
use App\Models\Vendor;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ReportTest extends TenantTestCase
{
    #[Test]
    public function a_subscribed_admin_can_visit_the_stock_out_report_page(): void
    {
        $this->actingAsAdmin()
            ->get(route('admin.reports.stock-out'))
            ->assertOk();
    }

    #[Test]
    public function the_stock_out_report_only_shows_relevant_products_and_their_details(): void
    {
        $vendor = Vendor::factory()->create();

        Product::query()->delete();

        $stock_out_products = Product::factory()->count(2)
            ->create(['vendor_id' => $vendor->id, 'stock_out_inventory' => 3, 'inventory' => 0]);

        $other_products = Product::factory()->count(2)
            ->create(['vendor_id' => $vendor->id, 'stock_out_inventory' => 1, 'inventory' => 2]);

        $response = $this->actingAsAdmin()
            ->get(route('admin.reports.stock-out'))
            ->assertOk()
            ->assertSee("Showing 2 of 2 result(s)");

        $stock_out_products->each(function (Product $product) use ($vendor, $response) {
            $response->assertSee($product->title)
                ->assertSee($product->stock_out_inventory)
                ->assertSee($product->inventory)
                ->assertSee($vendor->title)
                ->assertDontSee("N/A", true);
        });

        $other_products->each(function (Product $product) use ($vendor, $response) {
            $response->assertDontSee($product->title);
        });
    }

    #[Test]
    public function the_stock_out_report_shows_products_that_do_not_have_a_vendor(): void
    {
        $stock_out_products = Product::factory()->count(2)
            ->create(['vendor_id' => null, 'stock_out_inventory' => 3, 'inventory' => 0]);

        $response = $this->actingAsAdmin()
            ->get(route('admin.reports.stock-out'))
            ->assertOk();

        $stock_out_products->each(function (Product $product) use ($response) {
            $response->assertSee($product->title)
                ->assertSee($product->stock_out_inventory)
                ->assertSee($product->inventory);
        });
    }

}
<?php

namespace Tests\Feature\Admin;

use App\Models\Pickup;
use App\Models\Product;
use App\Models\Schedule;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ScheduleTest extends TenantTestCase
{
    #[Test]
    public function a_non_admin_cannot_update_a_schedule(): void
    {
        $schedule = Schedule::factory()->create();

        $this->actingAsCustomer()
            ->put(route('admin.schedules.update', compact('schedule')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_cannot_update_a_non_existent_schedule(): void
    {
        $this->actingAsAdmin()
            ->put(route('admin.schedules.update', ['abc']))
            ->assertRedirect('/');
    }

    #[Test]
    public function it_validates_an_inactive_schedule_update_delivery_frequency_and_reorder_frequency_request(): void
    {
        $schedule = Schedule::factory()->create();

        $this->actingAsAdmin()
            ->put(route('admin.schedules.update', compact('schedule')), [
                'delivery_frequency' => 'abc',
                'reorder_frequency' => 'abc',
            ])
            ->assertInvalid(['delivery_frequency' => 'The delivery frequency field must be an integer.'])
            ->assertInvalid(['reorder_frequency' => 'The reorder frequency field must be an array.']);

        $this->put(route('admin.schedules.update', compact('schedule')), [
                'delivery_frequency' => 10,
                'reorder_frequency' => ['abc'],
            ])
            ->assertInvalid(['delivery_frequency' => 'The selected delivery frequency is invalid.'])
            ->assertInvalid(['reorder_frequency.0' => 'The reorder_frequency.0 field must be an integer.']);

        $this->put(route('admin.schedules.update', compact('schedule')), [
            'reorder_frequency' => [10],
        ])
            ->assertInvalid(['reorder_frequency.0' => 'The selected reorder_frequency.0 is invalid.']);
    }

    #[Test]
    public function it_validates_packed_deadline_on_update(): void
    {
        $schedule = Schedule::factory()->create();

        $this->actingAsAdmin()
            ->put(route('admin.schedules.update', compact('schedule')), [
                'pack_deadline_days_before' => 'abc',
                'pack_deadline_hours_before' => 'abc',
            ])
            ->assertInvalid(['pack_deadline_days_before' => 'The pack deadline days before field must be an integer.'])
            ->assertInvalid(['pack_deadline_hours_before' => 'The pack deadline hours before field must be an integer.']);

        $this->put(route('admin.schedules.update', compact('schedule')), [
                'pack_deadline_days_before' => -1,
                'pack_deadline_hours_before' => -1,
            ])
            ->assertInvalid(['pack_deadline_days_before' => 'The pack deadline days before field must be at least 0.'])
            ->assertInvalid(['pack_deadline_hours_before' => 'The pack deadline hours before field must be at least 0.']);

        $this->put(route('admin.schedules.update', compact('schedule')), [
                'pack_deadline_days_before' => 8,
                'pack_deadline_hours_before' => 24,
            ])
            ->assertInvalid(['pack_deadline_days_before' => 'The pack deadline days before field must not be greater than 7.'])
            ->assertInvalid(['pack_deadline_hours_before' => 'The pack deadline hours before field must not be greater than 23.']);
    }

    #[Test]
    public function it_updates_packed_deadline(): void
    {
        $schedule = Schedule::factory()->create([
            'pack_deadline_days_before' => 2,
            'pack_deadline_hours_before' => 12,
        ]);

        $this->actingAsAdmin()
            ->put(route('admin.schedules.update', compact('schedule')), [
                'pack_deadline_days_before' => 3,
                'pack_deadline_hours_before' => 20,
            ])
            ->assertSessionHasNoErrors();

        $this->assertDatabaseHas(Schedule::class, [
            'id' => $schedule->id,
            'pack_deadline_days_before' => 3,
            'pack_deadline_hours_before' => 20,
        ]);
    }

    #[Test]
    public function it_can_update_the_schedule_delivery_frequency_and_reorder_frequency_of_an_inactive_schedule(): void
    {
        $schedule = Schedule::factory()->create(['active' => false]);

        $this->actingAsAdmin()
            ->put(route('admin.schedules.update', compact('schedule')), [
                'delivery_frequency' => 7,
                'reorder_frequency' => [7,28],
            ])
            ->assertSessionHasNoErrors();

        $this->assertDatabaseHas(Schedule::class, [
            'id' => $schedule->id,
            'delivery_frequency' => 7,
            'reorder_frequency' => json_encode([7,28])
        ]);
    }

    #[Test]
    public function all_requests_to_show_page_are_redirected_to_the_edit_page(): void
    {
        $schedule = Schedule::factory()->create();

        $this->actingAsAdmin()
            ->get(route('admin.schedules.show', compact('schedule')))
            ->assertRedirect(route('admin.schedules.edit', compact('schedule')));
    }

    #[Test]
    public function a_non_admin_cannot_delete_a_schedule(): void
    {
        $schedule = Schedule::factory()->create();

        $this->actingAsCustomer()
            ->delete(route('admin.schedules.destroy', compact('schedule')))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_admin_cannot_delete_a_non_existent_schedule(): void
    {
        $this->actingAsAdmin()
            ->delete(route('admin.schedules.destroy', ['abc']))
            ->assertRedirect('/');
    }

    #[Test]
    public function an_admin_cannot_delete_a_schedule_currently_in_use_by_a_delivery_method(): void
    {
        $schedule = Schedule::factory()->create();

        $expected = Pickup::factory(2)->create(['schedule_id' => $schedule->id]);

        $unexpected = Pickup::factory()->create();

        $this->actingAsAdmin()
            ->delete(route('admin.schedules.destroy', compact('schedule')))
            ->assertRedirect('/')
            ->assertSessionHas('flash_notification', [
                'level' => 'error',
                'message' => 'The schedule cannot be deleted because it is currently in use by the following delivery methods: <br/><br/>' . $expected->pluck('title')->join('<br/>')
            ]);
    }

    #[Test]
    public function an_admin_cannot_delete_a_schedule_currently_in_use_by_a_product(): void
    {
        $schedule = Schedule::factory()->create();

        $expected = Product::factory(2)->create(['schedule_id' => $schedule->id]);

        $unexpected = Product::factory()->create();

        $this->actingAsAdmin()
            ->delete(route('admin.schedules.destroy', compact('schedule')))
            ->assertRedirect('/')
            ->assertSessionHas('flash_notification', [
                'level' => 'error',
                'message' => 'The schedule cannot be deleted because it is currently in use by the following products: <br/><br/>' . $expected->pluck('title')->join('<br/>')
            ]);
    }
}
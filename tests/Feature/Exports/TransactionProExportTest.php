<?php

namespace Tests\Feature\Exports;

use App\Exports\TransactionProExport;
use Illuminate\Support\Carbon;
use Maatwebsite\Excel\Facades\Excel;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class TransactionProExportTest extends TenantTestCase
{
    #[Test]
    public function an_unauthenticated_user_cannot_download_csv(): void
    {
        $this->put(route('admin.orders.bulk-update.export', ['export' => 'tap']), [
                'orders' => [1,2,3]
            ])
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_non_admin_user_cannot_download_csv(): void
    {
        $this->actingAsCustomer()
            ->put(route('admin.orders.bulk-update.export', ['export' => 'tap']), [
                'orders' => [1,2,3]
            ])
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_validates_the_request(): void
    {
        $this->actingAsAdmin()
            ->put(route('admin.orders.bulk-update.export', ['export' => 'tap']), [
                'type' => 'maybe',
                'orders' => [1,2,3]
            ])
            ->assertSessionHasErrors(['type' => 'The selected type is invalid.']);

        $this->actingAsAdmin()
            ->put(route('admin.orders.bulk-update.export', ['export' => 'tap']), [
                'type' => 'page',
                'orders' => []
            ])
            ->assertSessionHasErrors(['orders' => 'At least one order must be selected.']);
    }

    #[Test]
    public function it_can_generate_a_downloadable_csv_for_single_page(): void
    {
        Carbon::setTestNow(now());

        Excel::fake();

        $this->actingAsAdmin()
            ->put(route('admin.orders.bulk-update.export', ['export' => 'tap']), [
                'type' => 'page',
                'orders' => [1,2,3]
            ]);

        Excel::assertDownloaded('orders.csv', function(TransactionProExport $export){
                return $export->orderQuery->getBindings() === [1, 2, 3];
            }
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_generate_a_downloadable_csv_when_type_is_not_selected(): void
    {
        Carbon::setTestNow(now());

        Excel::fake();

        $this->actingAsAdmin()
            ->put(route('admin.orders.bulk-update.export', ['export' => 'tap']), [
                'type' => null,
                'orders' => [1,2,3]
            ]);

        Excel::assertDownloaded('orders.csv', function(TransactionProExport $export){
            return $export->orderQuery->getBindings() === [1, 2, 3];
        }
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_generate_a_downloadable_csv_for_all_pages(): void
    {
        Carbon::setTestNow(now());
        session(['orders-filtered' => [
            'subscription_status' => 'all',
            'confirmed' => '1',
            'order_status' => ['1','8'],
            'orderBy' => 'orders.confirmed_date',
            'sort' => 'desc',
        ]]);

        Excel::fake();

        $this->actingAsAdmin()
            ->put(route('admin.orders.bulk-update.export', ['export' => 'tap']), [
                'type' => 'all',
                'orders' => [1,2,3]
            ]);

        Excel::assertDownloaded('orders.csv', function(TransactionProExport $export) {
                return $export->orderQuery->getBindings() !== [1, 2, 3];
            }
        );

        Carbon::setTestNow();
    }
}
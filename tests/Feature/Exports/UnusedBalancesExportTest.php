<?php

namespace Tests\Feature\Exports;

use App\Exports\UnusedBalancesExport;
use Maatwebsite\Excel\Facades\Excel;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class UnusedBalancesExportTest extends TenantTestCase
{
    #[Test]
    public function an_unauthenticated_user_cannot_download_csv(): void
    {
        $this->get(route('admin.reports.unused-balances'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function an_non_admin_user_cannot_download_csv(): void
    {
        $this->actingAsCustomer()
            ->get(route('admin.reports.unused-balances'))
            ->assertRedirect(route('admin.login'));
    }

    #[Test]
    public function it_can_generate_a_downloadable_csv_for_authorized_users(): void
    {
        Excel::fake();

        $this->actingAsAdmin()
            ->get(route('admin.reports.unused-balances'))
            ->assertOk();

        Excel::assertDownloaded('unused_balances.csv', function(UnusedBalancesExport $export) {
            return true;
        });
    }
}

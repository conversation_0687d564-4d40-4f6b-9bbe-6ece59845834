<?php

namespace Tests\Feature\Livewire\Theme;

use App\Livewire\Theme\Offers\Subscription;
use App\Models\Cart;
use App\Models\Product;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SubscriptionTest extends TenantTestCase
{
    #[Test]
    public function renders_successfully_when_the_authenticated_user_has_subscription()
    {
        Livewire::test(Subscription::class, ['cart' => Cart::factory()->create()])
            ->assertStatus(200);
    }

    #[Test]
    public function it_can_start_a_subscription()
    {
        $cart = Cart::factory()->create();

        $cart->setCartAsOneTimePurchase();

        $this->assertFalse($cart->isRecurring());

        $product = Product::factory()->create();

        Livewire::test(Subscription::class, compact('cart'))
            ->set('selected_frequency', 7)
            ->set('selected_product_id', $product->id)
            ->call('startSubscription')
            ->assertRedirect(route('checkout.confirm.show'));

        $cart = $cart->fresh();
        $this->assertTrue($cart->isRecurring());
    }

    #[Test]
    public function it_can_ship_once()
    {
        $cart = Cart::factory()->create();
        $cart->setCartAsSubscriptionPurchase();

        $this->assertTrue($cart->isRecurring());

        Livewire::test(Subscription::class, compact('cart'))
            ->call('shipOnce')
            ->assertRedirect(route('checkout.confirm.show'));

        $cart = $cart->fresh();
        $this->assertFalse($cart->isRecurring());
    }
}


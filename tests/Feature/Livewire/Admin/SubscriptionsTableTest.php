<?php

namespace Tests\Feature\Livewire\Admin;

use App\Actions\Subscription\SyncSubscriptionDatetimes;
use App\Livewire\Admin\SubscriptionsTable;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use App\Models\User;
use Carbon\Carbon;
use Illuminate\Pagination\LengthAwarePaginator;
use Illuminate\Support\Collection;
use Livewire\Livewire;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class SubscriptionsTableTest extends LivewireTenantTestCase
{
    #[Test]
    public function the_component_can_render(): void
    {
        RecurringOrder::factory(2)->create();
        RecurringOrder::factory()->create(['deleted_at' => now()]);

        Livewire::test(SubscriptionsTable::class)
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 3)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 2);
    }

    #[Test]
    public function the_component_can_filter_by_status(): void
    {
        RecurringOrder::factory(2)->create();
        RecurringOrder::factory()->create(['deleted_at' => now()]);

        Livewire::test(SubscriptionsTable::class)
            ->set('statuses', ['canceled'])
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 3)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 1)
            ->set('statuses', ['active'])
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 3)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 2)
            ->set('statuses', ['active', 'canceled'])
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 3)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 3);
    }

    #[Test]
    public function the_component_can_filter_by_delivery_method(): void
    {
        $subscriptions = RecurringOrder::factory(2)->create();
        RecurringOrder::factory()->create(['deleted_at' => now()]);

        Livewire::test(SubscriptionsTable::class)
            ->set('delivery_methods', [$subscriptions->first()->fulfillment_id])
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 3)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 1)
            ->set('delivery_methods', $subscriptions->map->fulfillment_id->toArray())
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 3)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 2)
            ->set('delivery_methods', [129837192381])
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 3)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 0);
    }

    #[Test]
    public function the_component_can_filter_by_delivery_dates(): void
    {
        $subscriptions_one = RecurringOrder::factory(3)->create(['ready_at' => now()->addDays(2)]);
        $subscriptions_two = RecurringOrder::factory(1)->create(['ready_at' => now()->addDays(4)]);

        Livewire::test(SubscriptionsTable::class)
            ->assertStatus(200)
            ->set('delivery_start_date', now()->addDays(1)->format('Y-m-d'))
            ->set('delivery_end_date', now()->addDays(3)->format('Y-m-d'))
            ->assertStatus(200)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 3)
            ->set('delivery_start_date', now()->addDays(3)->format('Y-m-d'))
            ->set('delivery_end_date', now()->addDays(5)->format('Y-m-d'))
            ->assertStatus(200)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 1);
    }

    #[Test]
    public function the_component_can_filter_by_customer_name(): void
    {
        $customer_one = User::factory()->create(['first_name' => 'cus_one']);
        $subscription_one = RecurringOrder::factory()->create(['customer_id' => $customer_one->id]);

        $customer_two = User::factory()->create(['first_name' => 'cus_two']);
        $subscription_two = RecurringOrder::factory()->create(['customer_id' => $customer_two->id]);

        Livewire::test(SubscriptionsTable::class)
            ->set('customer_term', 'us_o')
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 2)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 1)
            ->set('customer_term', 'us_t')
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 2)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 1)
            ->set('customer_term', 'xxxxxxxx')
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 2)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 0);
    }

    #[Test]
    public function the_component_can_filter_by_customer_email(): void
    {
        $customer_one = User::factory()->create(['email' => '<EMAIL>']);
        $subscription_one = RecurringOrder::factory()->create(['customer_id' => $customer_one->id]);

        $customer_two = User::factory()->create(['email' => '<EMAIL>']);
        $subscription_two = RecurringOrder::factory()->create(['customer_id' => $customer_two->id]);

        Livewire::test(SubscriptionsTable::class)
            ->set('customer_term', 'one@test')
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 2)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 1)
            ->set('customer_term', 'two@test')
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 2)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 1)
            ->set('customer_term', 'xxxxxxxx')
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 2)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 0);
    }

    #[Test]
    public function the_component_can_filter_by_product_name(): void
    {
        $product_one = Product::factory()->create(['title' => 'prod_one']);
        $subscription_one = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_one->id]);

        $product_two = Product::factory()->create(['title' => 'prod_two']);
        $subscription_two = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_two->id, 'product_id' => $product_two->id]);

        Livewire::test(SubscriptionsTable::class)
            ->set('product_term', 'rod_o')
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 2)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 1)
            ->set('product_term', 'rod_t')
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 2)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 1)
            ->set('product_term', 'xxxxxxxx')
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 2)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 0);
    }

    #[Test]
    public function the_component_can_filter_by_product_collection_name(): void
    {
        $collection_one = \App\Models\Collection::factory()->create(['title' => 'coll_one']);
        $product_one = Product::factory()->create();
        $product_one->collections()->attach($collection_one->id);
        $subscription_one = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_one->id]);

        $collection_two = \App\Models\Collection::factory()->create(['title' => 'coll_two']);
        $product_two = Product::factory()->create();
        $product_two->collections()->attach($collection_two->id);
        $subscription_two = RecurringOrder::factory()->create();
        RecurringOrderItem::factory()->create(['order_id' => $subscription_two->id, 'product_id' => $product_two->id]);

        Livewire::test(SubscriptionsTable::class)
            ->set('product_term', 'oll_o')
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 2)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 1)
            ->set('product_term', 'oll_t')
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 2)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 1)
            ->set('product_term', 'xxxxxxxx')
            ->assertStatus(200)
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 2)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 0);
    }

    #[Test]
    public function the_component_can_filter_by_subscription_id(): void
    {
        $subscriptions = RecurringOrder::factory(2)->create();

        Livewire::test(SubscriptionsTable::class)
            ->set('subscription_ids', (string) $subscriptions->first()->id)
            ->assertStatus(200)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 1)
            ->set('subscription_ids', "{$subscriptions->first()->id},{$subscriptions->last()->id}")
            ->assertStatus(200)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 2)
            ->set('subscription_ids', '129837192381')
            ->assertStatus(200)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 0);
    }

    #[Test]
    function it_can_clear_filters(): void
    {
        $product_one = Product::factory()->create(['title' => 'prod_one']);
        $customer_one = User::factory()->create(['email' => '<EMAIL>']);
        $subscription_one = RecurringOrder::factory()->create(['customer_id' => $customer_one->id]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_one->id, 'product_id' => $product_one->id]);

        $product_two = Product::factory()->create(['title' => 'prod_two']);
        $customer_two = User::factory()->create(['email' => '<EMAIL>']);
        $subscription_two = RecurringOrder::factory()->create(['customer_id' => $customer_two->id]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_two->id, 'product_id' => $product_two->id]);

        $product_three = Product::factory()->create(['title' => 'prod_three']);
        $customer_three = User::factory()->create(['email' => '<EMAIL>']);
        $subscription_three = RecurringOrder::factory()->create(['customer_id' => $customer_three->id, 'deleted_at' => now()]);
        RecurringOrderItem::factory()->create(['order_id' => $subscription_three->id, 'product_id' => $product_three->id]);

        Livewire::test(SubscriptionsTable::class)
            ->set('customer_term', 'one@test')
            ->set('product_term', 'rod_o')
            ->set('delivery_methods', [$subscription_one->fulfillment_id])
            ->set('statuses', ['active'])
            ->assertHasNoErrors()
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 3)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 1)
            ->call('clearFilters')
            ->assertHasNoErrors()
            ->assertViewHas('available_delivery_methods', fn(Collection $arg) => $arg->count() === 3)
            ->assertViewHas('subscriptions', fn(LengthAwarePaginator $arg) => $arg->count() === 3);
    }

    #[Test]
    public function it_performs_delivery_date_bulk_action_events_for_all_matching_subscriptions(): void
    {
        $subscriptions = RecurringOrder::factory(2)->create(['ready_at' => now()->addDays(2)]);

        $new_date = now()->addDays(5);

        $this->mock(SyncSubscriptionDatetimes::class, function (MockInterface $mock) use ($subscriptions, $new_date) {
            foreach ($subscriptions as $subscription) {
                $mock->shouldReceive('handle')
                    ->with(
                        \Mockery::on(fn(RecurringOrder $arg) => $arg->id === $subscription->id),
                        \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $new_date->format('Y-m-d')),
                    );
            }

        });

        Livewire::test(SubscriptionsTable::class)
            ->assertStatus(200)
            ->set('page_size', 1)
            ->set('delivery_start_date', now()->addDays(1)->format('Y-m-d'))
            ->set('delivery_end_date', now()->addDays(3)->format('Y-m-d'))
            ->assertStatus(200)
            ->dispatch('bulk-action-selected', action: 'date', params: [
                'delivery_date' => $new_date->format('Y-m-d'),
                'bulk_selection_type' => 'all',
                'subscription_ids' => [],
            ]);
    }

    #[Test]
    public function it_performs_delivery_date_bulk_action_events_for_page_matching_subscriptions(): void
    {
        $subscriptions = RecurringOrder::factory(2)->create(['ready_at' => now()->addDays(2)]);

        $new_date = now()->addDays(5);

        $this->mock(SyncSubscriptionDatetimes::class, function (MockInterface $mock) use ($subscriptions, $new_date) {
            $mock->shouldReceive('handle')->once();
        });

        Livewire::test(SubscriptionsTable::class)
            ->assertStatus(200)
            ->set('page_size', 1)
            ->set('delivery_start_date', now()->addDays(1)->format('Y-m-d'))
            ->set('delivery_end_date', now()->addDays(3)->format('Y-m-d'))
            ->assertStatus(200)
            ->dispatch('bulk-action-selected', action: 'date', params: [
                'delivery_date' => $new_date->format('Y-m-d'),
                'bulk_selection_type' => 'page',
                'subscription_ids' => [],
            ]);
    }

    #[Test]
    public function it_performs_delivery_date_bulk_action_events_for_specific_subscription_ids(): void
    {
        $subscriptions = RecurringOrder::factory(2)->create(['ready_at' => now()->addDays(2)]);

        $new_date = now()->addDays(5);

        $this->mock(SyncSubscriptionDatetimes::class, function (MockInterface $mock) use ($subscriptions, $new_date) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    \Mockery::on(fn(RecurringOrder $arg) => $arg->id === $subscriptions->first()->id),
                    \Mockery::on(fn(Carbon $arg) => $arg->format('Y-m-d') === $new_date->format('Y-m-d')),
                );
        });

        Livewire::test(SubscriptionsTable::class)
            ->assertStatus(200)
            ->set('delivery_start_date', now()->addDays(1)->format('Y-m-d'))
            ->set('delivery_end_date', now()->addDays(3)->format('Y-m-d'))
            ->assertStatus(200)
            ->dispatch('bulk-action-selected', action: 'date', params: [
                'delivery_date' => $new_date->format('Y-m-d'),
                'bulk_selection_type' => null,
                'subscription_ids' => [$subscriptions->first()->id],
            ]);
    }

    #[Test]
    public function it_performs_product_removal_bulk_action_events_for_all_matching_subscriptions(): void
    {
        $product = Product::factory()->create();
        $subscriptions = RecurringOrder::factory(2)->create(['ready_at' => now()->addDays(2)]);
        foreach ($subscriptions as $subscription) {
            RecurringOrderItem::factory()->create([
                'order_id' => $subscription->id,
                'product_id' => $product->id
            ]);
            RecurringOrderItem::factory()->create(['order_id' => $subscription->id]);
        }

        Livewire::test(SubscriptionsTable::class)
            ->assertStatus(200)
            ->set('page_size', 1)
            ->set('delivery_start_date', now()->addDays(1)->format('Y-m-d'))
            ->set('delivery_end_date', now()->addDays(3)->format('Y-m-d'))
            ->assertStatus(200)
            ->dispatch('bulk-action-selected', action: 'product', params: [
                'product_action' => 'remove',
                'removed_product_id' => $product->id,
                'bulk_selection_type' => 'all',
                'subscription_ids' => [],
            ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[0]->id
        ]);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'order_id' => $subscriptions[0]->id,
            'product_id' => $product->id
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[1]->id
        ]);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'order_id' => $subscriptions[1]->id,
            'product_id' => $product->id
        ]);
    }

    #[Test]
    public function it_performs_product_removal_bulk_action_events_for_page_matching_subscriptions(): void
    {
        $product = Product::factory()->create();
        $subscriptions = RecurringOrder::factory(2)->create(['ready_at' => now()->addDays(2)]);
        foreach ($subscriptions as $subscription) {
            RecurringOrderItem::factory()->create([
                'order_id' => $subscription->id,
                'product_id' => $product->id
            ]);
            RecurringOrderItem::factory()->create(['order_id' => $subscription->id]);
        }

        Livewire::test(SubscriptionsTable::class)
            ->assertStatus(200)
            ->set('page_size', 1)
            ->set('delivery_start_date', now()->addDays(1)->format('Y-m-d'))
            ->set('delivery_end_date', now()->addDays(3)->format('Y-m-d'))
            ->assertStatus(200)
            ->dispatch('bulk-action-selected', action: 'product', params: [
                'product_action' => 'remove',
                'removed_product_id' => $product->id,
                'bulk_selection_type' => 'page',
                'subscription_ids' => [],
            ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[0]->id
        ]);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'order_id' => $subscriptions[0]->id,
            'product_id' => $product->id
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[1]->id
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[1]->id,
            'product_id' => $product->id
        ]);
    }

    #[Test]
    public function it_performs_product_removal_bulk_action_events_for_specific_subscription_ids(): void
    {
        $product = Product::factory()->create();
        $subscriptions = RecurringOrder::factory(2)->create(['ready_at' => now()->addDays(2)]);
        foreach ($subscriptions as $subscription) {
            RecurringOrderItem::factory()->create([
                'order_id' => $subscription->id,
                'product_id' => $product->id
            ]);
            RecurringOrderItem::factory()->create(['order_id' => $subscription->id]);
        }

        Livewire::test(SubscriptionsTable::class)
            ->assertStatus(200)
            ->set('page_size', 1)
            ->set('delivery_start_date', now()->addDays(1)->format('Y-m-d'))
            ->set('delivery_end_date', now()->addDays(3)->format('Y-m-d'))
            ->assertStatus(200)
            ->dispatch('bulk-action-selected', action: 'product', params: [
                'product_action' => 'remove',
                'removed_product_id' => $product->id,
                'bulk_selection_type' => null,
                'subscription_ids' => [$subscriptions[1]->id],
            ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[0]->id
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[0]->id,
            'product_id' => $product->id
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[1]->id
        ]);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'order_id' => $subscriptions[1]->id,
            'product_id' => $product->id
        ]);
    }

    #[Test]
    public function it_performs_product_replacement_bulk_action_events_for_all_matching_subscriptions(): void
    {
        $product = Product::factory()->create();
        $product_two = Product::factory()->create();
        $subscriptions = RecurringOrder::factory(2)->create(['ready_at' => now()->addDays(2)]);
        foreach ($subscriptions as $subscription) {
            RecurringOrderItem::factory()->create([
                'order_id' => $subscription->id,
                'product_id' => $product->id
            ]);
            RecurringOrderItem::factory()->create(['order_id' => $subscription->id]);
        }

        Livewire::test(SubscriptionsTable::class)
            ->assertStatus(200)
            ->set('page_size', 1)
            ->set('delivery_start_date', now()->addDays(1)->format('Y-m-d'))
            ->set('delivery_end_date', now()->addDays(3)->format('Y-m-d'))
            ->assertStatus(200)
            ->dispatch('bulk-action-selected', action: 'product', params: [
                'product_action' => 'replace',
                'removed_product_id' => $product->id,
                'replacement_product_id' => $product_two->id,
                'bulk_selection_type' => 'all',
                'subscription_ids' => [],
            ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[0]->id
        ]);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'order_id' => $subscriptions[0]->id,
            'product_id' => $product->id
        ]);
        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[0]->id,
            'product_id' => $product_two->id
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[1]->id
        ]);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'order_id' => $subscriptions[1]->id,
            'product_id' => $product->id
        ]);
        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[1]->id,
            'product_id' => $product_two->id
        ]);
    }

    #[Test]
    public function it_performs_product_replacement_bulk_action_events_for_page_matching_subscriptions(): void
    {
        Carbon::setTestNow(now());

        $product = Product::factory()->create();
        $replacement_product = Product::factory()->create();

        // this subscription will be on page one since it was "created more recently" by overriding the now() time
        $subscription_page_one = RecurringOrder::factory()->create(['ready_at' => now()->addDays(2)]);

        Carbon::setTestNow(now()->subDays(3));
        $subscription_page_two = RecurringOrder::factory()->create(['ready_at' => now()->addDays(2)]);
        Carbon::setTestNow();

        foreach ([$subscription_page_one, $subscription_page_two] as $subscription) {
            RecurringOrderItem::factory()->create([
                'order_id' => $subscription->id,
                'product_id' => $product->id
            ]);
            RecurringOrderItem::factory()->create(['order_id' => $subscription->id]);
        }

        Livewire::test(SubscriptionsTable::class)
            ->assertStatus(200)
            ->set('page_size', 1)
            ->set('delivery_start_date', now()->addDays(1)->format('Y-m-d'))
            ->set('delivery_end_date', now()->addDays(3)->format('Y-m-d'))
            ->assertStatus(200)
            ->assertSee($subscription_page_one->customer->full_name)
            ->assertDontSee($subscription_page_two->customer->full_name)
            ->dispatch('bulk-action-selected', action: 'product', params: [
                'product_action' => 'replace',
                'removed_product_id' => $product->id,
                'replacement_product_id' => $replacement_product->id,
                'bulk_selection_type' => 'page',
                'subscription_ids' => [],
            ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription_page_one->id
        ]);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'order_id' => $subscription_page_one->id,
            'product_id' => $product->id
        ]);
        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription_page_one->id,
            'product_id' => $replacement_product->id
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription_page_two->id
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscription_page_two->id,
            'product_id' => $product->id
        ]);
        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'order_id' => $subscription_page_two->id,
            'product_id' => $replacement_product->id
        ]);
    }

    #[Test]
    public function it_performs_product_replacement_bulk_action_events_for_specific_subscription_ids(): void
    {
        $product = Product::factory()->create();
        $product_two = Product::factory()->create();
        $subscriptions = RecurringOrder::factory(2)->create(['ready_at' => now()->addDays(2)]);
        foreach ($subscriptions as $subscription) {
            RecurringOrderItem::factory()->create([
                'order_id' => $subscription->id,
                'product_id' => $product->id
            ]);
            RecurringOrderItem::factory()->create(['order_id' => $subscription->id]);
        }

        Livewire::test(SubscriptionsTable::class)
            ->assertStatus(200)
            ->set('page_size', 1)
            ->set('delivery_start_date', now()->addDays(1)->format('Y-m-d'))
            ->set('delivery_end_date', now()->addDays(3)->format('Y-m-d'))
            ->assertStatus(200)
            ->dispatch('bulk-action-selected', action: 'product', params: [
                'product_action' => 'replace',
                'removed_product_id' => $product->id,
                'replacement_product_id' => $product_two->id,
                'bulk_selection_type' => null,
                'subscription_ids' => [$subscriptions[1]->id],
            ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[0]->id
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[0]->id,
            'product_id' => $product->id
        ]);
        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'order_id' => $subscriptions[0]->id,
            'product_id' => $product_two->id
        ]);

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[1]->id
        ]);

        $this->assertDatabaseMissing(RecurringOrderItem::class, [
            'order_id' => $subscriptions[1]->id,
            'product_id' => $product->id
        ]);
        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'order_id' => $subscriptions[1]->id,
            'product_id' => $product_two->id
        ]);
    }

    #[Test]
    public function it_does_not_perform_delivery_date_bulk_action_events_for_non_matching_subscriptions(): void
    {
        RecurringOrder::factory(1)->create(['ready_at' => now()->addDays(4)]);

        $new_date = now()->addDays(5);

        $this->mock(SyncSubscriptionDatetimes::class, function (MockInterface $mock) {
            $mock->shouldNotReceive('handle');
        });

        Livewire::test(SubscriptionsTable::class)
            ->assertStatus(200)
            ->set('delivery_start_date', now()->addDays(1)->format('Y-m-d'))
            ->set('delivery_end_date', now()->addDays(3)->format('Y-m-d'))
            ->assertStatus(200)
            ->dispatch('bulk-action-selected', action: 'date', params: [
                'delivery_date' => $new_date->format('Y-m-d'),
                'bulk_selection_type' => 'all',
                'subscription_ids' => [],
            ]);
    }
}

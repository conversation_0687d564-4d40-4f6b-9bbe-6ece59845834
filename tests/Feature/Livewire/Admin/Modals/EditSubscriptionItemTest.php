<?php

namespace Tests\Feature\Livewire\Admin\Modals;

use App\Livewire\Admin\Modals\EditSubscriptionItem;
use App\Models\Product;
use App\Models\RecurringOrderItem;
use Livewire\Livewire;
use PHPUnit\Framework\Attributes\Test;
use Tests\LivewireTenantTestCase;

class EditSubscriptionItemTest extends LivewireTenantTestCase
{
    #[Test]
    public function the_component_can_render(): void
    {
        $subscription_item = RecurringOrderItem::factory()->create();

        Livewire::test(EditSubscriptionItem::class)
            ->assertStatus(200)
            ->dispatch('open-modal-edit-subscription-item', $subscription_item->id)
            ->assertStatus(200);
    }

    #[Test]
    function product_id_is_validated_upon_submission(): void
    {
        $subscription_item = RecurringOrderItem::factory()->create();

        Livewire::test(EditSubscriptionItem::class)
            ->dispatch('open-modal-edit-subscription-item', $subscription_item->id)
            ->set('product_id', null)
            ->call('submit')
            ->assertHasErrors(['product_id' => 'required'])
            ->set('product_id', '9999999999')
            ->call('submit')
            ->assertHasErrors(['product_id' => 'exists']);;
    }

    #[Test]
    function quantity_is_validated_upon_submission(): void
    {
        $subscription_item = RecurringOrderItem::factory()->create();

        Livewire::test(EditSubscriptionItem::class)
            ->dispatch('open-modal-edit-subscription-item', $subscription_item->id)
            ->set('qty', 0)
            ->call('submit')
            ->assertHasErrors(['qty' => 'min:1']);
    }

    #[Test]
    function type_is_validated_upon_submission(): void
    {
        $subscription_item = RecurringOrderItem::factory()->create();

        Livewire::test(EditSubscriptionItem::class)
            ->dispatch('open-modal-edit-subscription-item', $subscription_item->id)
            ->set('type', '')
            ->call('submit')
            ->assertHasErrors(['type' => 'required'])
            ->set('type', 'something')
            ->call('submit')
            ->assertHasErrors(['type' => 'in']);
    }

    #[Test]
    function it_can_update_an_item_on_a_subscription(): void
    {
        $subscription_item = RecurringOrderItem::factory()->create();

        $product = Product::factory()->create();

        Livewire::test(EditSubscriptionItem::class)
            ->dispatch('open-modal-edit-subscription-item', $subscription_item->id)
            ->set('product_id', $product->id)
            ->set('qty', 5)
            ->set('type', 'addon')
            ->call('submit')
            ->assertHasNoErrors()
            
            ->assertDispatched('subscriptionUpdated');

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'id' => $subscription_item->id,
            'product_id' => $product->id,
            'qty' => 5,
            'type' => 'addon'
        ]);
    }

    #[Test]
    function it_can_close(): void
    {
        $subscription_item = RecurringOrderItem::factory()->create();

        Livewire::test(EditSubscriptionItem::class)
            ->dispatch('open-modal-edit-subscription-item', $subscription_item->id)
            ->call('close');

        $this->assertDatabaseHas(RecurringOrderItem::class, [
            'id' => $subscription_item->id,
        ]);
    }
}

<?php

namespace Tests;

abstract class LivewireTenantTestCase extends TenantTestCase
{
    protected function setUp(): void
    {
        parent::setUp();

        /**
         * With an initialized tenant, the storage path is scoped via the
         * Stancl\Tenancy\Bootstrappers\FilesystemTenancyBootstrapper::class bootstrapper
         * class. However, Livewire stores its compiled views in the unscoped storage/framework
         * directory. The line below resets the tenant scope to the root scope.
         */
        $this->app->useStoragePath(base_path('storage'));
    }
}

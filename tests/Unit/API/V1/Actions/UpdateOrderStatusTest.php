<?php

namespace Tests\Unit\API\V1\Actions;

use App\API\V1\Actions\UpdateOrderStatus;
use App\Integrations\Drip\Jobs\RecordFirstOrderReceived as RecordFirstOrderReceivedJob;
use App\Models\Order;
use App\Support\Enums\OrderStatus;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class UpdateOrderStatusTest extends TenantTestCase
{
    #[Test]
    public function it_performs_the_correct_action_when_triggered(): void
    {
        Bus::fake([RecordFirstOrderReceivedJob::class]);

        $order = Order::factory()->create();

        (new UpdateOrderStatus)->run($order, 'processing');
        $this->assertEquals(OrderStatus::processing(), $order->status_id);

        (new UpdateOrderStatus)->run($order, 'packed');
        $this->assertEquals(OrderStatus::packed(), $order->status_id);

        (new UpdateOrderStatus)->run($order, 'picked_up');
        $this->assertEquals(OrderStatus::pickedUp(), $order->status_id);

        (new UpdateOrderStatus)->run($order, 'out_for_delivery');
        $this->assertEquals(OrderStatus::outForDelivery(), $order->status_id);

        (new UpdateOrderStatus)->run($order, 'completed');
        $this->assertEquals(OrderStatus::completed(), $order->status_id);

        (new UpdateOrderStatus)->run($order, 'on_hold');
        $this->assertEquals(OrderStatus::onHold(), $order->status_id);

        (new UpdateOrderStatus)->run($order, 'canceled');
        $this->assertEquals(OrderStatus::canceled(), $order->status_id);

        (new UpdateOrderStatus)->run($order, 'pre_order');
        $this->assertEquals(OrderStatus::preOrder(), $order->status_id);
    }
}

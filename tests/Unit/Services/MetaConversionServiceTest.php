<?php

namespace Tests\Unit\Services;

use App\Models\Order;
use App\Models\RecurringOrder;
use App\Models\User;
use App\Services\MetaConversionService;
use Carbon\Carbon;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class MetaConversionServiceTest extends TestCase
{
    #[Test]
    public function it_doesnt_track_events_without_credentials()
    {
        Http::fake();

        Carbon::setTestNow($now = now());

        config(['services.facebook.pixel_id' => null, 'services.facebook.access_token' => null]);

        $user = User::factory()->create(['email' => '<EMAIL>']);

        app(MetaConversionService::class)->trackCompleteRegistration($user);

        Http::assertNothingSent();

        config(['services.facebook.pixel_id' => 'ABC123', 'services.facebook.access_token' => null]);

        app(MetaConversionService::class)->trackCompleteRegistration($user);

        Http::assertNothingSent();

        config(['services.facebook.pixel_id' => null, 'services.facebook.access_token' => 'ABC123']);

        app(MetaConversionService::class)->trackCompleteRegistration($user);

        Http::assertNothingSent();
    }

    #[Test]
    public function it_tracks_complete_registration_events()
    {
        Http::fake();

        Carbon::setTestNow($now = now());

        config(['services.facebook.pixel_id' => 'ABC123', 'services.facebook.access_token' => 'DEF456']);

        $user = User::factory()->create(['email' => '<EMAIL>']);

        app(MetaConversionService::class)->trackCompleteRegistration($user);


        Http::assertSent(function (Request $request) use ($user, $now) {
            $data = json_decode($request->data()[0]['contents'], true)[0];

            return $request->isMultipart()
                && $request->url() === 'https://graph.facebook.com/v23.0/ABC123/events?access_token=DEF456'
                && $data['event_name'] === 'CompleteRegistration'
                && $data['user_data']['em'] === hash('sha256', strtolower(trim($user->email)))
                && $data['event_time'] === $now->timestamp
                && $data['action_source'] === 'website';
        });

        Carbon::setTestNow();
    }

    #[Test]
    public function it_tracks_purchase_events()
    {
        Http::fake();

        Carbon::setTestNow($now = now());

        config(['services.facebook.pixel_id' => 'ABC123', 'services.facebook.access_token' => 'DEF456']);

        $user = User::factory()->create(['email' => '<EMAIL>']);
        $order = Order::factory()->make(['total' => 100, 'first_time_order' => true]);

        app(MetaConversionService::class)->trackPurchase($order);

        Http::assertSent(function ($request) use ($order, $now) {
            $data = json_decode($request->data()[0]['contents'], true)[0];

            return $request->isMultipart()
                && $request->url() === 'https://graph.facebook.com/v23.0/ABC123/events?access_token=DEF456'
                && $data['event_name'] === 'Purchase'
                && $data['user_data']['em'] === hash('sha256', strtolower(trim($order->customer->email)))
                && $data['custom_data']['value'] === $order->total
                && $data['custom_data']['currency'] === 'USD'
                && $data['custom_data']['first_time_order'] === 'Yes'
                && $data['custom_data']['delivery_method_id'] === $order->pickup_id
                && $data['custom_data']['delivery_method_name'] === $order->pickup->title
                && $data['event_time'] === $now->timestamp
                && $data['action_source'] === 'website';
        });

        Carbon::setTestNow();
    }

    #[Test]
    public function it_tracks_subscription_events()
    {
        Http::fake();

        Carbon::setTestNow($now = now());

        config(['services.facebook.pixel_id' => 'ABC123', 'services.facebook.access_token' => 'DEF456']);

        $user = User::factory()->create(['email' => '<EMAIL>']);
        $subscription = RecurringOrder::factory()->make(['subtotal' => 50.00]);

        app(MetaConversionService::class)->trackSubscription($subscription);

        Http::assertSent(function ($request) use ($subscription, $now) {
            $data = json_decode($request->data()[0]['contents'], true)[0];

            return $request->isMultipart()
                && $request->url() === 'https://graph.facebook.com/v23.0/ABC123/events?access_token=DEF456'
                && $data['event_name'] === 'Subscribe'
                && $data['user_data']['em'] === hash('sha256', strtolower(trim($subscription->customer->email)))
                && $data['custom_data']['value'] === $subscription->subtotal()
                && $data['custom_data']['currency'] === 'USD'
                && $data['custom_data']['reorder_frequency'] === $subscription->reorder_frequency
                && $data['custom_data']['delivery_method_id'] === $subscription->fulfillment_id
                && $data['custom_data']['delivery_method_name'] === $subscription->fulfillment->title
                && $data['event_time'] === $now->timestamp
                && $data['action_source'] === 'website';
        });

        Carbon::setTestNow();
    }
}

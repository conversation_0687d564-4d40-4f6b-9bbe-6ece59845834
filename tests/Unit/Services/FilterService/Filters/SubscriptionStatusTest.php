<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\SubscriptionStatus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class SubscriptionStatusTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_not_subscription_status(): void
    {
        $collection = (new SubscriptionStatus)->handle([$this->createRequest(['subscription_status' => 'inactive']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('subscription_status'));

        /** @var SubscriptionStatus $filter */
        $filter = $collection->get('subscription_status');

        $this->assertEquals('Order Type: ', $filter->label());
        $this->assertEquals('Inactive', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }

    #[Test]
    public function it_can_return_its_properties_for_subscription_status(): void
    {
        $collection = (new SubscriptionStatus)->handle([$this->createRequest(['subscription_status' => 'active']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('subscription_status'));

        /** @var SubscriptionStatus $filter */
        $filter = $collection->get('subscription_status');

        $this->assertEquals('Order Type: ', $filter->label());
        $this->assertEquals('Active', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}
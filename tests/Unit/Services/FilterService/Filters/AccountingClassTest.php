<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\AccountingClass;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class AccountingClassTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties(): void
    {
        $collection = (new AccountingClass)->handle([$this->createRequest(['accounting_class' => '123']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('accounting_class'));

        /** @var AccountingClass $filter */
        $filter = $collection->get('accounting_class');

        $this->assertEquals('Accounting Class:', $filter->label());
        $this->assertEquals('123', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());
    }
}
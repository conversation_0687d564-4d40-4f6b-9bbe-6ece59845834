<?php

namespace Tests\Unit\Services\FilterService\Filters;

use App\Services\FilterService\Filters\FirstTimeOrder;
use PHPUnit\Framework\Attributes\Test;
use Tests\TestCase;

class FirstTimeOrderTest extends TestCase
{
    use CreatesFakeRequest;

    #[Test]
    public function it_can_return_its_properties_for_first_time_order(): void
    {
        $collection = (new FirstTimeOrder)->handle([$this->createRequest(['first_time_order' => 'true']), collect()], function ($passable) {
            list($request, $collection) = $passable;
            return $collection;
        });

        $this->assertTrue($collection->has('first_time_order'));

        /** @var FirstTimeOrder $filter */
        $filter = $collection->get('first_time_order');

        $this->assertEquals('First Time Order', $filter->label());
        $this->assertEquals('', $filter->value());
        $this->assertEquals('http://test.test?foo=bar', $filter->removeFilterUrl());

    }
}
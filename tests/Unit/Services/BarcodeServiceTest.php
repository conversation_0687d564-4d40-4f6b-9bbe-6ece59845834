<?php

namespace Tests\Unit\Services;

use App\Services\BarcodeService;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class BarcodeServiceTest extends TenantTestCase
{
    #[Test]
    public function it_generates_a_barcode_as_an_svg(): void
    {
        $service = new BarcodeService;

        $this->assertStringContainsString(
            '<svg width="180" height="30" viewBox="0 0 180 30" version="1.1" xmlns="http://www.w3.org/2000/svg">',
            $service->generateAsSvg('123456789012')
        );
    }
}

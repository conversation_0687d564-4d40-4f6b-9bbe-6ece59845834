<?php

namespace Tests\Unit\Services;

use App\Models\Payment;
use App\Models\Setting;
use App\Services\SettingsService;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SettingServiceTest extends TenantTestCase
{
    #[Test]
    public function it_correctly_resolves_the_customerLocationWall(): void
    {
        Setting::flushCache();
        Setting::where(['key' => 'require_shopper_local'])->delete();
        $this->assertEquals('cart', app(SettingsService::class)->customerLocationWall());

        Setting::updateOrCreate(['key' => 'require_shopper_local'], ['value' => true]);
        $this->assertEquals('store', app(SettingsService::class)->customerLocationWall());

        Setting::updateOrCreate(['key' => 'require_shopper_local'], ['value' => false]);
        $this->assertEquals('cart', app(SettingsService::class)->customerLocationWall());

        Setting::updateOrCreate(['key' => 'customer_location_wall'], ['value' => 'off']);
        Setting::updateOrCreate(['key' => 'require_shopper_local'], ['value' => true]);
        // customer_location_wall settings takes precedence
        $this->assertEquals('off', app(SettingsService::class)->customerLocationWall());
    }

    #[Test]
    public function it_correctly_resolves_the_registrationWall(): void
    {
        Setting::flushCache();
        $this->assertEquals('cart', app(SettingsService::class)->registrationWall());

        Setting::updateOrCreate(['key' => 'require_visitors_have_account'], ['value' => '1']);
        $this->assertEquals('site', app(SettingsService::class)->registrationWall());
        Setting::where(['key' => 'require_visitors_have_account'])->delete();
        Setting::flushCache();

        Setting::updateOrCreate(['key' => 'prevent_guest_shopper'], ['value' => '1']);
        $this->assertEquals('store', app(SettingsService::class)->registrationWall());
        Setting::where(['key' => 'prevent_guest_shopper'])->delete();
        Setting::flushCache();

        Setting::updateOrCreate(['key' => 'product_price_gate'], ['value' => 'require_account']);
        $this->assertEquals('prices', app(SettingsService::class)->registrationWall());
        Setting::where(['key' => 'product_price_gate'])->delete();
        Setting::flushCache();

        Setting::updateOrCreate(['key' => 'registration_wall'], ['value' => 'checkout']);
        Setting::updateOrCreate(['key' => 'require_visitors_have_account'], ['value' => '1']);
        // registration_wall settings takes precedence
        $this->assertEquals('checkout', app(SettingsService::class)->registrationWall());
    }

    #[Test]
    public function it_correctly_resolves_preventsSiteGuests(): void
    {
        Setting::flushCache();
        $this->assertFalse(app(SettingsService::class)->preventsSiteGuests());

        Setting::updateOrCreate(['key' => 'require_visitors_have_account'], ['value' => '1']);
        $this->assertTrue(app(SettingsService::class)->preventsSiteGuests());
    }

    #[Test]
    public function it_correctly_resolves_preventsStoreGuests(): void
    {
        Setting::flushCache();
        $this->assertFalse(app(SettingsService::class)->preventsStoreGuests());

        Setting::updateOrCreate(['key' => 'require_visitors_have_account'], ['value' => '1']);
        $this->assertTrue(app(SettingsService::class)->preventsStoreGuests());
        Setting::where(['key' => 'require_visitors_have_account'])->delete();
        Setting::flushCache();

        Setting::updateOrCreate(['key' => 'prevent_guest_shopper'], ['value' => '1']);
        $this->assertTrue(app(SettingsService::class)->preventsStoreGuests());
        Setting::where(['key' => 'prevent_guest_shopper'])->delete();
        Setting::flushCache();
    }

    #[Test]
    public function it_correctly_resolves_requiresLocationOnStoreView(): void
    {
        Setting::flushCache();
        $this->assertFalse(app(SettingsService::class)->requiresLocationOnStoreView());

        Setting::updateOrCreate(['key' => 'require_shopper_local'], ['value' => '1']);
        $this->assertTrue(app(SettingsService::class)->requiresLocationOnStoreView());
    }

    #[Test]
    public function it_correctly_resolves_pricesRequireAccount(): void
    {
        Setting::flushCache();
        $this->assertFalse(app(SettingsService::class)->pricesRequireAccount());

        Setting::updateOrCreate(['key' => 'product_price_gate'], ['value' => 'require_account']);
        $this->assertTrue(app(SettingsService::class)->pricesRequireAccount());
    }

    #[Test]
    public function it_can_determine_if_multiple_coupons_per_order_are_disabled(): void
    {
        Setting::flushCache();
        $this->assertFalse(app(SettingsService::class)->preventsMultipleCouponsPerOrder());

        Setting::updateOrCreate(['key' => 'prevent_multiple_coupons'], ['value' => 0]);
        $this->assertFalse(app(SettingsService::class)->preventsMultipleCouponsPerOrder());

        Setting::updateOrCreate(['key' => 'prevent_multiple_coupons'], ['value' => 1]);
        $this->assertTrue(app(SettingsService::class)->preventsMultipleCouponsPerOrder());
    }

    #[Test]
    public function it_correctly_resolves_the_deadline_end_hour(): void
    {
        Setting::flushCache();
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 24]);
        $this->assertEquals(24, app(SettingsService::class)->deadlineHour());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 10]);
        $this->assertEquals(10, app(SettingsService::class)->deadlineHour());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 14]);
        $this->assertEquals(14, app(SettingsService::class)->deadlineHour());

        Setting::where(['key' => 'order_deadline_hour'])->delete();
        Setting::flushCache();
        $this->assertEquals(24, app(SettingsService::class)->deadlineHour());
    }

    #[Test]
    public function it_correctly_resolves_the_deadline_end_time(): void
    {
        Setting::flushCache();
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 24]);
        $this->assertEquals('23:59:59', app(SettingsService::class)->deadlineEndTime());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);
        $this->assertEquals('08:00:00', app(SettingsService::class)->deadlineEndTime());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 10]);
        $this->assertEquals('10:00:00', app(SettingsService::class)->deadlineEndTime());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 14]);
        $this->assertEquals('14:00:00', app(SettingsService::class)->deadlineEndTime());

        Setting::where(['key' => 'order_deadline_hour'])->delete();
        Setting::flushCache();
        $this->assertEquals('23:59:59', app(SettingsService::class)->deadlineEndTime());
    }

    #[Test]
    public function it_correctly_resolves_the_formatted_deadline_end_time(): void
    {
        Setting::flushCache();
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 24]);
        $this->assertEquals('11:59 PM', app(SettingsService::class)->formattedDeadlineEndTime());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 9]);
        $this->assertEquals('9:00 AM', app(SettingsService::class)->formattedDeadlineEndTime());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 12]);
        $this->assertEquals('12:00 PM', app(SettingsService::class)->formattedDeadlineEndTime());

        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 14]);
        $this->assertEquals('2:00 PM', app(SettingsService::class)->formattedDeadlineEndTime());

        Setting::where(['key' => 'order_deadline_hour'])->delete();
        Setting::flushCache();
        $this->assertEquals('11:59 PM', app(SettingsService::class)->formattedDeadlineEndTime());
    }

    #[Test]
    public function it_can_get_the_farm_country_settings(): void
    {
        Setting::where(['key' => 'farm_country'])->delete();
        Setting::flushCache();
        $this->assertEquals('USA', app(SettingsService::class)->farmCountry());

        Setting::updateOrCreate(['key' => 'farm_country'], ['value' => 'USA']);
        Setting::flushCache();
        $this->assertEquals('USA', app(SettingsService::class)->farmCountry());

        Setting::updateOrCreate(['key' => 'farm_country'], ['value' => 'Canada']);
        Setting::flushCache();
        $this->assertEquals('Canada', app(SettingsService::class)->farmCountry());
    }

    #[Test]
    public function it_can_get_default_parcel_count(): void
    {
        Setting::where(['key' => 'default_parcel_count'])->delete();
        Setting::flushCache();
        $this->assertEquals(1, app(SettingsService::class)->defaultParcelCount());

        Setting::updateOrCreate(['key' => 'default_parcel_count'], ['value' => 5]);
        Setting::flushCache();
        $this->assertEquals(5, app(SettingsService::class)->defaultParcelCount());

        Setting::updateOrCreate(['key' => 'default_parcel_count'], ['value' => 0]);
        Setting::flushCache();
        $this->assertEquals(0, app(SettingsService::class)->defaultParcelCount());
    }

    public function it_can_get_the_business_address()
    {
        Setting::updateOrCreate(['key' => 'farm_street'], ['value' => '123 Main']);
        Setting::updateOrCreate(['key' => 'farm_city'], ['value' => 'Test City']);
        Setting::updateOrCreate(['key' => 'farm_state'], ['value' => 'IA']);
        Setting::updateOrCreate(['key' => 'farm_zip'], ['value' => '12345']);
        Setting::updateOrCreate(['key' => 'farm_country'], ['value' => 'USA']);
        Setting::flushCache();

        $this->assertEquals([
            'street' => '123 Main',
            'city' => 'Test City',
            'state' => 'IA',
            'postal_code' => '12345',
            'country' => 'USA',
        ], app(SettingsService::class)->businessAddress());
    }

    #[Test]
    public function it_can_get_the_timezone(): void
    {
        Setting::where(['key' => 'timezone'])->delete();
        Setting::flushCache();
        $this->assertNull(app(SettingsService::class)->timezone());

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::flushCache();
        $this->assertEquals('America/Chicago', app(SettingsService::class)->timezone());
    }

    #[Test]
    public function it_can_get_the_cart_service(): void
    {
        Setting::where(['key' => 'cart_service'])->delete();
        Setting::flushCache();
        $this->assertEquals('order', app(SettingsService::class)->cartService());

        Setting::updateOrCreate(['key' => 'cart_service'], ['value' => 'database']);
        Setting::flushCache();
        $this->assertEquals('database', app(SettingsService::class)->cartService());
    }

    #[Test]
    public function it_can_get_the_require_term_agreement_setting(): void
    {
        Setting::where(['key' => 'require_checkout_agreement'])->delete();
        Setting::flushCache();
        $this->assertFalse(app(SettingsService::class)->requiresAgreementToTermsAtCheckout());

        Setting::updateOrCreate(['key' => 'require_checkout_agreement'], ['value' => 1]);
        Setting::flushCache();
        $this->assertTrue(app(SettingsService::class)->requiresAgreementToTermsAtCheckout());

        Setting::updateOrCreate(['key' => 'require_checkout_agreement'], ['value' => 0]);
        Setting::flushCache();
        $this->assertFalse(app(SettingsService::class)->requiresAgreementToTermsAtCheckout());
    }

    #[Test]
    public function it_can_get_the_order_modifications_setting(): void
    {
        Setting::where(['key' => 'ordering_mode'])->delete();
        Setting::flushCache();
        $this->assertFalse(app(SettingsService::class)->allowsOrderModification());

        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => 1]);
        Setting::flushCache();
        $this->assertTrue(app(SettingsService::class)->allowsOrderModification());

        Setting::updateOrCreate(['key' => 'ordering_mode'], ['value' => 0]);
        Setting::flushCache();
        $this->assertFalse(app(SettingsService::class)->allowsOrderModification());
    }

    #[Test]
    public function it_can_get_the_start_store_credit_balance(): void
    {
        Setting::where(['key' => 'user_registration_credit'])->delete();
        Setting::flushCache();
        $this->assertEquals(0, app(SettingsService::class)->startingStoreCreditBalance());

        Setting::updateOrCreate(['key' => 'user_registration_credit'], ['value' => '12.34']);
        Setting::flushCache();
        $this->assertEquals(1234, app(SettingsService::class)->startingStoreCreditBalance());
    }

    #[Test]
    public function it_can_get_the_new_customer_referral_bonus(): void
    {
        Setting::where(['key' => 'referral_payout'])->delete();
        Setting::flushCache();
        $this->assertEquals(0, app(SettingsService::class)->newCustomerReferralBonus());

        Setting::updateOrCreate(['key' => 'referral_payout'], ['value' => 1234]);
        Setting::flushCache();
        $this->assertEquals(1234, app(SettingsService::class)->newCustomerReferralBonus());
    }

    #[Test]
    public function it_can_get_the_existing_customer_referral_bonus(): void
    {
        Setting::where(['key' => 'referral_bonus'])->delete();
        Setting::flushCache();
        $this->assertEquals(0, app(SettingsService::class)->existingCustomerReferralBonus());

        Setting::updateOrCreate(['key' => 'referral_bonus'], ['value' => 1334]);
        Setting::flushCache();
        $this->assertEquals(1334, app(SettingsService::class)->existingCustomerReferralBonus());
    }

    #[Test]
    public function it_can_get_the_allow_po_box_shipping(): void
    {
        Setting::where(['key' => 'ship_to_pobox'])->delete();
        Setting::flushCache();
        $this->assertTrue(app(SettingsService::class)->allowsPOBoxShipping());

        Setting::updateOrCreate(['key' => 'ship_to_pobox'], ['value' => 1]);
        Setting::flushCache();
        $this->assertTrue(app(SettingsService::class)->allowsPOBoxShipping());

        Setting::updateOrCreate(['key' => 'ship_to_pobox'], ['value' => 0]);
        Setting::flushCache();
        $this->assertFalse(app(SettingsService::class)->allowsPOBoxShipping());
    }

    #[Test]
    public function it_knows_if_credit_cards_are_enabled(): void
    {
        Payment::where(['key' => 'card'])->delete();

        $this->assertFalse(app(SettingsService::class)->creditCardPaymentsAreEnabled());

        $payment = Payment::updateOrCreate(
            ['key' => 'card'],
            ['title' => 'Credit Card', 'instructions' => 'Credit Card', 'enabled' => false]
        );
        $payment->key = 'card';
        $payment->save();

        $this->assertFalse(app(SettingsService::class)->creditCardPaymentsAreEnabled());

        $payment->enabled = true;
        $payment->save();

        $this->assertTrue(app(SettingsService::class)->creditCardPaymentsAreEnabled());
    }

    #[Test]
    public function it_can_get_the_stripe_account_type(): void
    {
        Setting::where(['key' => 'stripe_account_type'])->delete();
        Setting::flushCache();
        $this->assertEquals('connect', app(SettingsService::class)->stripeAccountType());

        Setting::updateOrCreate(['key' => 'stripe_account_type'], ['value' => 'standard']);
        Setting::flushCache();
        $this->assertEquals('standard', app(SettingsService::class)->allowsPOBoxShipping());

        Setting::updateOrCreate(['key' => 'stripe_account_type'], ['value' => 'other']);
        Setting::flushCache();
        $this->assertEquals('other', app(SettingsService::class)->allowsPOBoxShipping());
    }

    #[Test]
    public function it_can_determine_if_its_stripe_standard(): void
    {
        Setting::where(['key' => 'stripe_account_type'])->delete();
        Setting::flushCache();
        $this->assertFalse(app(SettingsService::class)->isStandardStripeAccount());

        Setting::updateOrCreate(['key' => 'stripe_account_type'], ['value' => 'standard']);
        Setting::flushCache();
        $this->assertTrue(app(SettingsService::class)->isStandardStripeAccount());

        Setting::updateOrCreate(['key' => 'stripe_account_type'], ['value' => 'other']);
        Setting::flushCache();
        $this->assertFalse(app(SettingsService::class)->isStandardStripeAccount());
    }

    #[Test]
    public function it_can_get_the_stripe_secret_key(): void
    {
        config(['services.stripe.secret' => 'abc_123']);

        Setting::where(['key' => 'stripe_secret_key'])->delete();
        Setting::where(['key' => 'stripe_account_type'])->delete();
        Setting::flushCache();
        $this->assertEquals('abc_123', app(SettingsService::class)->stripeSecretKey());

        Setting::updateOrCreate(['key' => 'stripe_secret_key'], ['value' => 'other']);
        Setting::flushCache();
        $this->assertEquals('abc_123', app(SettingsService::class)->stripeSecretKey());

        Setting::updateOrCreate(['key' => 'stripe_account_type'], ['value' => 'standard']);
        Setting::updateOrCreate(['key' => 'stripe_secret_key'], ['value' => encrypt('standard_123')]);
        Setting::flushCache();
        $this->assertEquals('standard_123', app(SettingsService::class)->stripeSecretKey());
    }

    #[Test]
    public function it_can_get_the_stripe_public_key(): void
    {
        config(['services.stripe.key' => 'abc_123']);

        Setting::where(['key' => 'stripe_public_key'])->delete();
        Setting::where(['key' => 'stripe_account_type'])->delete();
        Setting::flushCache();
        $this->assertEquals('abc_123', app(SettingsService::class)->stripePublicKey());

        Setting::updateOrCreate(['key' => 'stripe_public_key'], ['value' => 'other']);
        Setting::flushCache();
        $this->assertEquals('abc_123', app(SettingsService::class)->stripePublicKey());

        Setting::updateOrCreate(['key' => 'stripe_account_type'], ['value' => 'standard']);
        Setting::updateOrCreate(['key' => 'stripe_public_key'], ['value' => encrypt('standard_123')]);
        Setting::flushCache();
        $this->assertEquals('standard_123', app(SettingsService::class)->stripePublicKey());
    }

    protected function setUp(): void
    {
        parent::setUp();
        Setting::where(['key' => 'stripe_account_type'])->delete();
        Setting::updateOrCreate(['key' => 'one_page_checkout'], ['value' => 0]);
        Setting::updateOrCreate(['key' => 'cart_service'], ['value' => 'order']);
    }
}

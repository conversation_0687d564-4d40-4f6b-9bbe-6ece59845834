<?php

namespace Tests\Unit\Repositories\Reports;

use App\Models\Order;
use App\Models\Pickup;
use App\Models\Schedule;
use App\Repositories\Reports\DeliverySalesDeductions;
use App\Support\Enums\OrderStatus;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class DeliverySalesDeductionsTest extends TenantTestCase
{
    #[Test]
    public function it_calculates_deduction_totals(): void
    {
        $pickup_one = Pickup::factory()->create();
        Order::factory()->create(['pickup_id' => $pickup_one->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 123, 'credit_applied' => 222, 'coupon_subtotal' => 333, 'tax' => 444, 'subscription_savings' => 555]);
        Order::factory()->create(['pickup_id' => $pickup_one->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 123, 'credit_applied' => 222, 'coupon_subtotal' => 333, 'tax' => 444, 'subscription_savings' => 555]);

        $pickup_two = Pickup::factory()->create();
        Order::factory()->create(['pickup_id' => $pickup_two->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 223, 'credit_applied' => 322, 'coupon_subtotal' => 433, 'tax' => 544, 'subscription_savings' => 655]);
        Order::factory()->create(['pickup_id' => $pickup_two->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 223, 'credit_applied' => 322, 'coupon_subtotal' => 433, 'tax' => 544, 'subscription_savings' => 655]);

        $results = (new DeliverySalesDeductions)->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup_one) {
            return $result->pickup_id === $pickup_one->id
                && $result->pickup_title === $pickup_one->title
                && $result->discount_total === '246'
                && $result->credit_total === '444'
                && $result->coupon_total === '666'
                && $result->sales_tax_total === '888'
                && $result->subscription_savings_total === '1110';
        }));

        $this->assertTrue($results->contains(function ($result) use ($pickup_two) {
            return $result->pickup_id === $pickup_two->id
                && $result->pickup_title === $pickup_two->title
                && $result->discount_total === '446'
                && $result->credit_total === '644'
                && $result->coupon_total === '866'
                && $result->sales_tax_total === '1088'
                && $result->subscription_savings_total === '1310';
        }));
    }

    #[Test]
    public function it_filters_out_non_confirmed_orders_by_default(): void
    {
        $pickup = Pickup::factory()->create();
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => false, 'canceled' => false, 'order_discount' => 123, 'credit_applied' => 222, 'coupon_subtotal' => 333, 'tax' => 444, 'subscription_savings' => 555]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => false, 'canceled' => false, 'order_discount' => 223, 'credit_applied' => 322, 'coupon_subtotal' => 433, 'tax' => 544, 'subscription_savings' => 655]);

        $results = (new DeliverySalesDeductions())->handle([]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_filters_out_canceled_orders_by_default(): void
    {
        $pickup = Pickup::factory()->create();
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => true, 'order_discount' => 123, 'credit_applied' => 222, 'coupon_subtotal' => 333, 'tax' => 444, 'subscription_savings' => 555]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => true, 'order_discount' => 223, 'credit_applied' => 322, 'coupon_subtotal' => 433, 'tax' => 544, 'subscription_savings' => 655]);

        $results = (new DeliverySalesDeductions())->handle([]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_can_filter_by_order_status(): void
    {
        $pickup = Pickup::factory()->create();
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 123, 'credit_applied' => 222, 'coupon_subtotal' => 333, 'tax' => 444, 'subscription_savings' => 555, 'status_id' => OrderStatus::confirmed()]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 223, 'credit_applied' => 322, 'coupon_subtotal' => 433, 'tax' => 544, 'subscription_savings' => 655, 'status_id' => OrderStatus::confirmed()]);

        $results = (new DeliverySalesDeductions())->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['order_status' => [OrderStatus::confirmed()]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['order_status' => [OrderStatus::canceled()]]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_can_filter_by_confirmed_date(): void
    {
        Carbon::setTestNow(now());

        $pickup = Pickup::factory()->create();

        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 123, 'credit_applied' => 222, 'coupon_subtotal' => 333, 'tax' => 444, 'subscription_savings' => 555, 'confirmed_date' => today()]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 223, 'credit_applied' => 322, 'coupon_subtotal' => 433, 'tax' => 544, 'subscription_savings' => 655, 'confirmed_date' => today()]);

        $results = (new DeliverySalesDeductions())->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['confirmed_date' => ['start' => today()]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['confirmed_date' => ['end' => today()]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['confirmed_date' => ['start' => today()->addDay()]]);

        $this->assertEmpty($results);

        $results = (new DeliverySalesDeductions())->handle(['confirmed_date' => ['end' => today()->subDay()]]);

        $this->assertEmpty($results);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_filter_by_pickup_date(): void
    {
        Carbon::setTestNow(now());

        $pickup = Pickup::factory()->create();

        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 123, 'credit_applied' => 222, 'coupon_subtotal' => 333, 'tax' => 444, 'subscription_savings' => 555, 'pickup_date' => today()]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 223, 'credit_applied' => 322, 'coupon_subtotal' => 433, 'tax' => 544, 'subscription_savings' => 655, 'pickup_date' => today()]);

        $results = (new DeliverySalesDeductions())->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['pickup_date' => ['start' => today()]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['pickup_date' => ['end' => today()]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['pickup_date' => ['start' => today()->addDay()]]);

        $this->assertEmpty($results);

        $results = (new DeliverySalesDeductions())->handle(['pickup_date' => ['end' => today()->subDay()]]);

        $this->assertEmpty($results);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_filter_by_first_time_orders_status(): void
    {
        $pickup = Pickup::factory()->create();

        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 123, 'credit_applied' => 222, 'coupon_subtotal' => 333, 'tax' => 444, 'subscription_savings' => 555,  'first_time_order' => true]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 223, 'credit_applied' => 322, 'coupon_subtotal' => 433, 'tax' => 544, 'subscription_savings' => 655,  'first_time_order' => true]);

        $results = (new DeliverySalesDeductions())->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['first_time_order' => true]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['first_time_order' => false]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_can_filter_by_fulfillment_error_status(): void
    {
        $pickup = Pickup::factory()->create();

        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 123, 'credit_applied' => 222, 'coupon_subtotal' => 333, 'tax' => 444, 'subscription_savings' => 555,  'fulfillment_error' => true]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 223, 'credit_applied' => 322, 'coupon_subtotal' => 433, 'tax' => 544, 'subscription_savings' => 655,  'fulfillment_error' => true]);

        $results = (new DeliverySalesDeductions())->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['fulfillment_error' => true]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['fulfillment_error' => false]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_can_filter_by_sales_channel(): void
    {
        $pickup = Pickup::factory()->create();

        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 123, 'credit_applied' => 222, 'coupon_subtotal' => 333, 'tax' => 444, 'subscription_savings' => 555, 'type_id' => 1]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 223, 'credit_applied' => 322, 'coupon_subtotal' => 433, 'tax' => 544, 'subscription_savings' => 655, 'type_id' => 1]);

        $results = (new DeliverySalesDeductions())->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['order_type_id' => [1]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['order_type_id' => [2]]);

        $this->assertEmpty($results);
    }

    #[Test]
    public function it_can_filter_by_schedule(): void
    {
        $schedule = Schedule::factory()->create();
        $other_schedule = Schedule::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);

        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 123, 'credit_applied' => 222, 'coupon_subtotal' => 333, 'tax' => 444, 'subscription_savings' => 555, 'type_id' => 1]);
        Order::factory()->create(['pickup_id' => $pickup->id, 'confirmed' => true, 'canceled' => false, 'order_discount' => 223, 'credit_applied' => 322, 'coupon_subtotal' => 433, 'tax' => 544, 'subscription_savings' => 655, 'type_id' => 1]);

        $results = (new DeliverySalesDeductions())->handle([]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['schedule_id' => [$schedule->id]]);

        $this->assertTrue($results->contains(function ($result) use ($pickup) {
            return $result->pickup_id === $pickup->id
                && $result->pickup_title === $pickup->title
                && $result->discount_total === '346'
                && $result->credit_total === '544'
                && $result->coupon_total === '766'
                && $result->sales_tax_total === '988'
                && $result->subscription_savings_total === '1210';
        }));

        $results = (new DeliverySalesDeductions())->handle(['schedule_id' => [$other_schedule->id]]);

        $this->assertEmpty($results);
    }
}

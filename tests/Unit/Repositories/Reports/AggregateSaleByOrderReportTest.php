<?php

namespace Tests\Unit\Repositories\Reports;

use App\Models\BundleProduct;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\PackingGroup;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use App\Models\Schedule;
use App\Models\User;
use App\Repositories\Reports\AggregateSaleByOrderReport;
use App\Support\Enums\OrderStatus;
use Carbon\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class AggregateSaleByOrderReportTest extends TenantTestCase
{
    private function createTestDataForOrder(array $orderAttributes = []): array
    {
        $packingGroup = PackingGroup::factory()->create();
        $schedule = Schedule::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $customer = User::factory()->create();
        $product = Product::factory()->create(['inventory_type' => $packingGroup->id]);

        $order = Order::factory()->create(array_merge([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
        ], $orderAttributes));

        $orderItem = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id
        ]);

        return compact('packingGroup', 'schedule', 'pickup', 'customer', 'product', 'order', 'orderItem');
    }

    #[Test]
    public function it_returns_billable_order_items_with_correct_data(): void
    {
        // Create test data
        $packingGroup = PackingGroup::factory()->create(['title' => 'Test Packing Group']);
        $schedule = Schedule::factory()->create(['title' => 'Test Schedule']);
        $pickup = Pickup::factory()->create([
            'title' => 'Test Pickup Location',
            'schedule_id' => $schedule->id
        ]);
        $customer = User::factory()->create([
            'first_name' => 'John',
            'last_name' => 'Doe',
            'email' => '<EMAIL>',
            'phone' => '555-1234'
        ]);
        $product = Product::factory()->create([
            'title' => 'Test Product',
            'sku' => 'TEST-SKU-001',
            'barcode' => 'TEST-BARCODE-001',
            'unit_of_issue' => 'package',
            'inventory_type' => $packingGroup->id,
            'accounting_class' => 'TEST-CLASS',
            'custom_sort' => 100,
            'is_bundle' => false
        ]);

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::confirmed(),
            'confirmed_date' => now(),
            'pickup_date' => now()->addDays(3),
            'payment_date' => now(),
            'customer_first_name' => $customer->first_name,
            'customer_last_name' => $customer->last_name,
            'customer_email' => $customer->email,
            'customer_phone' => $customer->phone,
            'blueprint_id' => null // One-time order
        ]);

        $orderItem = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $product->id,
            'title' => $product->title,
            'unit_price' => 1500, // $15.00
            'store_price' => 1800, // $18.00
            'qty' => 2,
            'fulfilled_qty' => 2,
            'weight' => 1.5,
            'subtotal' => 3000 // $30.00
        ]);

        $report = new AggregateSaleByOrderReport(collect([]));
        $results = $report->query();

        // Should contain at least one billable item
        $billableItem = $results->firstWhere('billable', 'Y');
        $this->assertNotNull($billableItem, 'Report should contain billable items');

        // Verify order data
        $this->assertEquals($order->id, $billableItem->order_id);
        $this->assertEquals('One Time', $billableItem->order_type);
        $this->assertEquals($orderItem->id, $billableItem->order_item_id);
        $this->assertEquals('Standard', $billableItem->product_type);

        // Verify product data
        $this->assertEquals('Y', $billableItem->billable);
        $this->assertEquals($product->barcode, $billableItem->product_barcode);
        $this->assertEquals($product->id, $billableItem->product_id);
        $this->assertEquals($product->sku, $billableItem->sku);
        $this->assertEquals($product->title, $billableItem->title);
        $this->assertEquals($product->unit_of_issue, $billableItem->unit_of_issue);
        $this->assertEquals(2, $billableItem->quantity);

        // Verify location and schedule data
        $this->assertEquals($pickup->title, $billableItem->location_name);
        $this->assertEquals($pickup->id, $billableItem->location_id);
        $this->assertEquals($schedule->title, $billableItem->schedule_name);
        $this->assertEquals($schedule->id, $billableItem->schedule_id);

        // Verify customer data
        $this->assertEquals($customer->id, $billableItem->customer_id);
        $this->assertEquals($customer->first_name, $billableItem->customer_first_name);
        $this->assertEquals($customer->last_name, $billableItem->customer_last_name);
        $this->assertEquals($customer->email, $billableItem->customer_email);
        $this->assertEquals($customer->phone, $billableItem->customer_phone);
    }

    #[Test]
    public function it_returns_non_billable_bundle_items_with_correct_data(): void
    {
        // Create test data
        $packingGroup = PackingGroup::factory()->create(['title' => 'Bundle Packing Group']);
        $schedule = Schedule::factory()->create(['title' => 'Bundle Schedule']);
        $pickup = Pickup::factory()->create([
            'title' => 'Bundle Pickup Location',
            'schedule_id' => $schedule->id
        ]);
        $customer = User::factory()->create([
            'first_name' => 'Jane',
            'last_name' => 'Smith',
            'email' => '<EMAIL>'
        ]);

        // Create bundle product
        $bundleProduct = Product::factory()->create([
            'title' => 'Test Bundle',
            'sku' => 'BUNDLE-001',
            'barcode' => 'BUNDLE-BARCODE-001',
            'is_bundle' => true,
            'inventory_type' => $packingGroup->id
        ]);

        // Create individual product that's part of the bundle
        $individualProduct = Product::factory()->create([
            'title' => 'Individual Product',
            'sku' => 'INDIVIDUAL-001',
            'barcode' => 'INDIVIDUAL-BARCODE-001',
            'unit_of_issue' => 'each',
            'inventory_type' => $packingGroup->id,
            'accounting_class' => 'INDIVIDUAL-CLASS',
            'custom_sort' => 200,
            'is_bundle' => false
        ]);

        // Create bundle relationship
        BundleProduct::factory()->create([
            'bundle_id' => $bundleProduct->id,
            'product_id' => $individualProduct->id,
            'qty' => 3
        ]);

        // Create recurring order first
        $recurringOrder = RecurringOrder::factory()->create([
            'customer_id' => $customer->id,
            'fulfillment_id' => $pickup->id,
            'schedule_id' => $schedule->id
        ]);

        $order = Order::factory()->create([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::confirmed(),
            'blueprint_id' => $recurringOrder->id // Subscription order
        ]);

        $bundleOrderItem = OrderItem::factory()->create([
            'order_id' => $order->id,
            'product_id' => $bundleProduct->id,
            'title' => $bundleProduct->title,
            'qty' => 1,
            'fulfilled_qty' => 1
        ]);

        $report = new AggregateSaleByOrderReport(collect([]));
        $results = $report->query();

        // Should contain billable bundle item
        $billableItem = $results->firstWhere(function ($item) use ($bundleOrderItem) {
            return $item->order_item_id == $bundleOrderItem->id && $item->billable === 'Y';
        });
        $this->assertNotNull($billableItem, 'Report should contain billable bundle item');
        $this->assertEquals('Bundle', $billableItem->product_type);
        $this->assertEquals('Subscription', $billableItem->order_type);

        // Should contain non-billable individual items from the bundle
        $nonBillableItem = $results->firstWhere(function ($item) use ($bundleOrderItem, $individualProduct) {
            return $item->order_item_id == $bundleOrderItem->id 
                && $item->billable === 'N' 
                && $item->product_id == $individualProduct->id;
        });
        $this->assertNotNull($nonBillableItem, 'Report should contain non-billable bundle component items');

        // Verify non-billable item data
        $this->assertEquals('N', $nonBillableItem->billable);
        $this->assertEquals($individualProduct->barcode, $nonBillableItem->product_barcode);
        $this->assertEquals($individualProduct->id, $nonBillableItem->product_id);
        $this->assertEquals($individualProduct->sku, $nonBillableItem->sku);
        $this->assertEquals($individualProduct->title, $nonBillableItem->title);
        $this->assertEquals(3, $nonBillableItem->quantity); // Bundle qty (1) * individual qty (3)
    }

    #[Test]
    public function it_can_filter_by_order_status(): void
    {
        // Create complete test data with all required relationships
        $packingGroup = PackingGroup::factory()->create();
        $schedule = Schedule::factory()->create();
        $pickup = Pickup::factory()->create(['schedule_id' => $schedule->id]);
        $customer = User::factory()->create();
        $product = Product::factory()->create(['inventory_type' => $packingGroup->id]);

        // Create orders with different statuses
        $confirmedOrder = Order::factory()->create([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::confirmed()
        ]);
        OrderItem::factory()->create([
            'order_id' => $confirmedOrder->id,
            'product_id' => $product->id
        ]);

        $packedOrder = Order::factory()->create([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::packed()
        ]);
        OrderItem::factory()->create([
            'order_id' => $packedOrder->id,
            'product_id' => $product->id
        ]);

        $canceledOrder = Order::factory()->create([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::canceled()
        ]);
        OrderItem::factory()->create([
            'order_id' => $canceledOrder->id,
            'product_id' => $product->id
        ]);

        $report = new AggregateSaleByOrderReport(collect([
            'order_status' => [OrderStatus::confirmed(), OrderStatus::packed()]
        ]));
        $results = $report->query();

        // Should contain confirmed and packed orders
        $this->assertTrue($results->contains('order_id', $confirmedOrder->id));
        $this->assertTrue($results->contains('order_id', $packedOrder->id));

        // Should not contain canceled order
        $this->assertFalse($results->contains('order_id', $canceledOrder->id));
    }

    #[Test]
    public function it_can_filter_by_confirmed_date_range(): void
    {
        Carbon::setTestNow('2024-01-15 12:00:00');

        // Create orders with different confirmed dates
        $dataInRange = $this->createTestDataForOrder(['confirmed_date' => '2024-01-10']);
        $dataOutOfRange = $this->createTestDataForOrder(['confirmed_date' => '2024-01-20']);

        $report = new AggregateSaleByOrderReport(collect([
            'confirmed_date' => [
                'start' => '2024-01-01',
                'end' => '2024-01-15'
            ]
        ]));
        $results = $report->query();

        // Should contain order within date range
        $this->assertTrue($results->contains('order_id', $dataInRange['order']->id));

        // Should not contain order outside date range
        $this->assertFalse($results->contains('order_id', $dataOutOfRange['order']->id));

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_filter_by_pickup_date_range(): void
    {
        Carbon::setTestNow('2024-02-15 12:00:00');

        // Create orders with different pickup dates
        $dataInRange = $this->createTestDataForOrder(['pickup_date' => '2024-02-10']);
        $dataOutOfRange = $this->createTestDataForOrder(['pickup_date' => '2024-02-20']);

        $report = new AggregateSaleByOrderReport(collect([
            'pickup_date' => [
                'start' => '2024-02-01',
                'end' => '2024-02-15'
            ]
        ]));
        $results = $report->query();

        // Should contain order within pickup date range
        $this->assertTrue($results->contains('order_id', $dataInRange['order']->id));

        // Should not contain order outside pickup date range
        $this->assertFalse($results->contains('order_id', $dataOutOfRange['order']->id));

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_filter_by_payment_date_range(): void
    {
        Carbon::setTestNow('2024-03-15 12:00:00');

        // Create orders with different payment dates
        $dataInRange = $this->createTestDataForOrder(['payment_date' => '2024-03-10']);
        $dataOutOfRange = $this->createTestDataForOrder(['payment_date' => '2024-03-20']);

        $report = new AggregateSaleByOrderReport(collect([
            'payment_date' => [
                'start' => '2024-03-01',
                'end' => '2024-03-15'
            ]
        ]));
        $results = $report->query();

        // Should contain order within payment date range
        $this->assertTrue($results->contains('order_id', $dataInRange['order']->id));

        // Should not contain order outside payment date range
        $this->assertFalse($results->contains('order_id', $dataOutOfRange['order']->id));

        Carbon::setTestNow();
    }

    #[Test]
    public function it_can_filter_by_order_type(): void
    {
        // Create one-time order
        $oneTimeData = $this->createTestDataForOrder(['type_id' => 1]);

        // Create farm store order
        $farmStoreData = $this->createTestDataForOrder(['type_id' => 4]);

        $report = new AggregateSaleByOrderReport(collect([
            'order_type_id' => [1] // Only one-time orders
        ]));
        $results = $report->query();

        // Should contain one-time order
        $this->assertTrue($results->contains('order_id', $oneTimeData['order']->id));

        // Should not contain farm store order
        $this->assertFalse($results->contains('order_id', $farmStoreData['order']->id));
    }

    #[Test]
    public function it_can_filter_by_pickup_location(): void
    {
        // Create different pickup locations
        $pickup1 = Pickup::factory()->create(['title' => 'Location 1']);
        $pickup2 = Pickup::factory()->create(['title' => 'Location 2']);

        $dataAtPickup1 = $this->createTestDataForOrder(['pickup_id' => $pickup1->id]);
        $dataAtPickup2 = $this->createTestDataForOrder(['pickup_id' => $pickup2->id]);

        $report = new AggregateSaleByOrderReport(collect([
            'pickup_id' => [$pickup1->id]
        ]));
        $results = $report->query();

        // Should contain order from pickup 1
        $this->assertTrue($results->contains('order_id', $dataAtPickup1['order']->id));

        // Should not contain order from pickup 2
        $this->assertFalse($results->contains('order_id', $dataAtPickup2['order']->id));
    }

    #[Test]
    public function it_can_filter_by_schedule(): void
    {
        // Create different schedules
        $schedule1 = Schedule::factory()->create(['title' => 'Schedule 1']);
        $schedule2 = Schedule::factory()->create(['title' => 'Schedule 2']);

        $orderOnSchedule1 = Order::factory()->create([
            'confirmed' => true,
            'canceled' => false,
            'schedule_id' => $schedule1->id
        ]);
        OrderItem::factory()->create(['order_id' => $orderOnSchedule1->id]);

        $orderOnSchedule2 = Order::factory()->create([
            'confirmed' => true,
            'canceled' => false,
            'schedule_id' => $schedule2->id
        ]);
        OrderItem::factory()->create(['order_id' => $orderOnSchedule2->id]);

        $report = new AggregateSaleByOrderReport(collect([
            'schedule_id' => [$schedule1->id]
        ]));
        $results = $report->query();

        // Should contain order from schedule 1
        $this->assertTrue($results->contains('order_id', $orderOnSchedule1->id));

        // Should not contain order from schedule 2
        $this->assertFalse($results->contains('order_id', $orderOnSchedule2->id));
    }

    #[Test]
    public function it_can_filter_by_customer_id(): void
    {
        $customer1 = User::factory()->create();
        $customer2 = User::factory()->create();

        $dataByCustomer1 = $this->createTestDataForOrder(['customer_id' => $customer1->id]);
        $dataByCustomer2 = $this->createTestDataForOrder(['customer_id' => $customer2->id]);

        $report = new AggregateSaleByOrderReport(collect([
            'customer' => $customer1->id
        ]));
        $results = $report->query();

        // Should contain order by customer 1
        $this->assertTrue($results->contains('order_id', $dataByCustomer1['order']->id));

        // Should not contain order by customer 2
        $this->assertFalse($results->contains('order_id', $dataByCustomer2['order']->id));
    }

    #[Test]
    public function it_can_filter_by_customer_email(): void
    {
        $customer1 = User::factory()->create(['email' => '<EMAIL>']);
        $customer2 = User::factory()->create(['email' => '<EMAIL>']);

        $dataByCustomer1 = $this->createTestDataForOrder([
            'customer_id' => $customer1->id,
            'customer_email' => $customer1->email
        ]);
        $dataByCustomer2 = $this->createTestDataForOrder([
            'customer_id' => $customer2->id,
            'customer_email' => $customer2->email
        ]);

        $report = new AggregateSaleByOrderReport(collect([
            'customer' => '<EMAIL>'
        ]));
        $results = $report->query();

        // Should contain order by customer 1
        $this->assertTrue($results->contains('order_id', $dataByCustomer1['order']->id));

        // Should not contain order by customer 2
        $this->assertFalse($results->contains('order_id', $dataByCustomer2['order']->id));
    }

    #[Test]
    public function it_excludes_unconfirmed_and_canceled_orders(): void
    {
        // Create confirmed order (should be included)
        $confirmedData = $this->createTestDataForOrder([
            'confirmed' => true,
            'canceled' => false
        ]);

        // Create unconfirmed order (should be excluded)
        $unconfirmedData = $this->createTestDataForOrder([
            'confirmed' => false,
            'canceled' => false
        ]);

        // Create canceled order (should be excluded)
        $canceledData = $this->createTestDataForOrder([
            'confirmed' => true,
            'canceled' => true
        ]);

        $report = new AggregateSaleByOrderReport(collect([]));
        $results = $report->query();

        // Should contain confirmed order
        $this->assertTrue($results->contains('order_id', $confirmedData['order']->id));

        // Should not contain unconfirmed order
        $this->assertFalse($results->contains('order_id', $unconfirmedData['order']->id));

        // Should not contain canceled order
        $this->assertFalse($results->contains('order_id', $canceledData['order']->id));
    }

    #[Test]
    public function it_returns_subscription_orders_with_correct_data(): void
    {
        // Create test data for subscription order
        $packingGroup = PackingGroup::factory()->create(['title' => 'Subscription Packing Group']);
        $schedule = Schedule::factory()->create(['title' => 'Subscription Schedule']);
        $pickup = Pickup::factory()->create([
            'title' => 'Subscription Pickup Location',
            'schedule_id' => $schedule->id
        ]);
        $customer = User::factory()->create([
            'first_name' => 'Subscription',
            'last_name' => 'Customer',
            'email' => '<EMAIL>',
            'phone' => '555-9999'
        ]);
        $product = Product::factory()->create([
            'title' => 'Subscription Product',
            'sku' => 'SUB-SKU-001',
            'barcode' => 'SUB-BARCODE-001',
            'unit_of_issue' => 'package',
            'inventory_type' => $packingGroup->id,
            'accounting_class' => 'SUB-CLASS',
            'custom_sort' => 300,
            'is_bundle' => false
        ]);

        // Create recurring order (subscription blueprint)
        $recurringOrder = RecurringOrder::factory()->create([
            'customer_id' => $customer->id,
            'fulfillment_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'reorder_frequency' => 7
        ]);

        // Create recurring order item
        RecurringOrderItem::factory()->create([
            'order_id' => $recurringOrder->id,
            'customer_id' => $customer->id,
            'product_id' => $product->id,
            'qty' => 3,
            'type' => 'recurring'
        ]);

        // Create actual order generated from the subscription
        $subscriptionOrder = Order::factory()->create([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::confirmed(),
            'confirmed_date' => now(),
            'pickup_date' => now()->addDays(3),
            'payment_date' => now(),
            'customer_first_name' => $customer->first_name,
            'customer_last_name' => $customer->last_name,
            'customer_email' => $customer->email,
            'customer_phone' => $customer->phone,
            'blueprint_id' => $recurringOrder->id // This makes it a subscription order
        ]);

        $subscriptionOrderItem = OrderItem::factory()->create([
            'order_id' => $subscriptionOrder->id,
            'product_id' => $product->id,
            'title' => $product->title,
            'unit_price' => 2000, // $20.00
            'store_price' => 2200, // $22.00
            'qty' => 3,
            'fulfilled_qty' => 3,
            'weight' => 2.0,
            'subtotal' => 6000 // $60.00
        ]);

        $report = new AggregateSaleByOrderReport(collect([]));
        $results = $report->query();

        // Should contain at least one billable item from subscription
        $billableItem = $results->firstWhere(function ($item) use ($subscriptionOrder) {
            return $item->order_id == $subscriptionOrder->id && $item->billable === 'Y';
        });
        $this->assertNotNull($billableItem, 'Report should contain billable subscription items');

        // Verify this is identified as a subscription order
        $this->assertEquals($subscriptionOrder->id, $billableItem->order_id);
        $this->assertEquals('Subscription', $billableItem->order_type); // Should show as "Subscription" because blueprint_id is set
        $this->assertEquals($subscriptionOrderItem->id, $billableItem->order_item_id);
        $this->assertEquals('Standard', $billableItem->product_type);

        // Verify product data
        $this->assertEquals('Y', $billableItem->billable);
        $this->assertEquals($product->barcode, $billableItem->product_barcode);
        $this->assertEquals($product->id, $billableItem->product_id);
        $this->assertEquals($product->sku, $billableItem->sku);
        $this->assertEquals($product->title, $billableItem->title);
        $this->assertEquals($product->unit_of_issue, $billableItem->unit_of_issue);
        $this->assertEquals(3, $billableItem->quantity);

        // Verify location and schedule data
        $this->assertEquals($pickup->title, $billableItem->location_name);
        $this->assertEquals($pickup->id, $billableItem->location_id);
        $this->assertEquals($schedule->title, $billableItem->schedule_name);
        $this->assertEquals($schedule->id, $billableItem->schedule_id);

        // Verify customer data
        $this->assertEquals($customer->id, $billableItem->customer_id);
        $this->assertEquals($customer->first_name, $billableItem->customer_first_name);
        $this->assertEquals($customer->last_name, $billableItem->customer_last_name);
        $this->assertEquals($customer->email, $billableItem->customer_email);
        $this->assertEquals($customer->phone, $billableItem->customer_phone);
    }

    #[Test]
    public function it_can_differentiate_between_one_time_and_subscription_orders(): void
    {
        $customer = User::factory()->create();
        $pickup = Pickup::factory()->create();
        $schedule = Schedule::factory()->create();
        $product = Product::factory()->create();

        // Create one-time order (no blueprint_id)
        $oneTimeOrder = Order::factory()->create([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
            'blueprint_id' => null // One-time order
        ]);
        OrderItem::factory()->create([
            'order_id' => $oneTimeOrder->id,
            'product_id' => $product->id
        ]);

        // Create subscription order (with blueprint_id)
        $recurringOrder = RecurringOrder::factory()->create([
            'customer_id' => $customer->id,
            'fulfillment_id' => $pickup->id,
            'schedule_id' => $schedule->id
        ]);
        $subscriptionOrder = Order::factory()->create([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
            'blueprint_id' => $recurringOrder->id // Subscription order
        ]);
        OrderItem::factory()->create([
            'order_id' => $subscriptionOrder->id,
            'product_id' => $product->id
        ]);

        $report = new AggregateSaleByOrderReport(collect([]));
        $results = $report->query();

        // Find one-time order item
        $oneTimeItem = $results->firstWhere('order_id', $oneTimeOrder->id);
        $this->assertNotNull($oneTimeItem, 'Report should contain one-time order');
        $this->assertEquals('One Time', $oneTimeItem->order_type);

        // Find subscription order item
        $subscriptionItem = $results->firstWhere('order_id', $subscriptionOrder->id);
        $this->assertNotNull($subscriptionItem, 'Report should contain subscription order');
        $this->assertEquals('Subscription', $subscriptionItem->order_type);
    }

    #[Test]
    public function it_handles_subscription_bundles_correctly(): void
    {
        // Create test data for subscription bundle
        $packingGroup = PackingGroup::factory()->create(['title' => 'Subscription Bundle Packing Group']);
        $schedule = Schedule::factory()->create(['title' => 'Subscription Bundle Schedule']);
        $pickup = Pickup::factory()->create([
            'title' => 'Subscription Bundle Pickup Location',
            'schedule_id' => $schedule->id
        ]);
        $customer = User::factory()->create([
            'first_name' => 'Bundle',
            'last_name' => 'Subscriber',
            'email' => '<EMAIL>'
        ]);

        // Create bundle product
        $bundleProduct = Product::factory()->create([
            'title' => 'Subscription Bundle',
            'sku' => 'SUB-BUNDLE-001',
            'barcode' => 'SUB-BUNDLE-BARCODE-001',
            'is_bundle' => true,
            'inventory_type' => $packingGroup->id
        ]);

        // Create individual product that's part of the bundle
        $individualProduct = Product::factory()->create([
            'title' => 'Subscription Individual Product',
            'sku' => 'SUB-INDIVIDUAL-001',
            'barcode' => 'SUB-INDIVIDUAL-BARCODE-001',
            'unit_of_issue' => 'each',
            'inventory_type' => $packingGroup->id,
            'accounting_class' => 'SUB-INDIVIDUAL-CLASS',
            'custom_sort' => 400,
            'is_bundle' => false
        ]);

        // Create bundle relationship
        BundleProduct::factory()->create([
            'bundle_id' => $bundleProduct->id,
            'product_id' => $individualProduct->id,
            'qty' => 2
        ]);

        // Create recurring order (subscription blueprint)
        $recurringOrder = RecurringOrder::factory()->create([
            'customer_id' => $customer->id,
            'fulfillment_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'reorder_frequency' => 14
        ]);

        // Create subscription order with bundle
        $subscriptionOrder = Order::factory()->create([
            'customer_id' => $customer->id,
            'pickup_id' => $pickup->id,
            'schedule_id' => $schedule->id,
            'confirmed' => true,
            'canceled' => false,
            'status_id' => OrderStatus::confirmed(),
            'blueprint_id' => $recurringOrder->id // Subscription order
        ]);

        $bundleOrderItem = OrderItem::factory()->create([
            'order_id' => $subscriptionOrder->id,
            'product_id' => $bundleProduct->id,
            'title' => $bundleProduct->title,
            'qty' => 1,
            'fulfilled_qty' => 1
        ]);

        $report = new AggregateSaleByOrderReport(collect([]));
        $results = $report->query();

        // Should contain billable bundle item
        $billableItem = $results->firstWhere(function ($item) use ($bundleOrderItem) {
            return $item->order_item_id == $bundleOrderItem->id && $item->billable === 'Y';
        });
        $this->assertNotNull($billableItem, 'Report should contain billable subscription bundle item');
        $this->assertEquals('Bundle', $billableItem->product_type);
        $this->assertEquals('Subscription', $billableItem->order_type);

        // Should contain non-billable individual items from the subscription bundle
        $nonBillableItem = $results->firstWhere(function ($item) use ($bundleOrderItem, $individualProduct) {
            return $item->order_item_id == $bundleOrderItem->id
                && $item->billable === 'N'
                && $item->product_id == $individualProduct->id;
        });
        $this->assertNotNull($nonBillableItem, 'Report should contain non-billable subscription bundle component items');

        // Verify non-billable item data
        $this->assertEquals('N', $nonBillableItem->billable);
        $this->assertEquals($individualProduct->barcode, $nonBillableItem->product_barcode);
        $this->assertEquals($individualProduct->id, $nonBillableItem->product_id);
        $this->assertEquals($individualProduct->sku, $nonBillableItem->sku);
        $this->assertEquals($individualProduct->title, $nonBillableItem->title);
        $this->assertEquals(2, $nonBillableItem->quantity); // Bundle qty (1) * individual qty (2)
    }

    #[Test]
    public function it_handles_negative_discount_calculations_correctly(): void
    {
        $data = $this->createTestDataForOrder();

        // This would previously cause MySQL "BIGINT UNSIGNED value is out of range" error
        $data['orderItem']->update([
            'unit_price' => 2500, // $25.00
            'store_price' => 2000, // $20.00 (lower than unit_price)
            'fulfilled_qty' => 2,
            'weight' => 1.5
        ]);

        $report = new AggregateSaleByOrderReport(collect([]));
        $results = $report->query();

        // Should not throw MySQL error and should return results
        $this->assertNotEmpty($results);

        // Find the billable item
        $billableItem = $results->firstWhere(function ($item) use ($data) {
            return $item->order_id == $data['order']->id && $item->billable === 'Y';
        });

        $this->assertNotNull($billableItem);

        // Verify discount calculations handle negative values correctly
        $this->assertEquals(-500, $billableItem->discount_per_unit);
        $this->assertEquals(-1000, $billableItem->total_discount);

        // Verify other calculations are still correct
        $this->assertEquals(2000, $billableItem->retail_price_per_unit);
        $this->assertEquals(4000, $billableItem->total_retail_price); // 2000 * 2
        $this->assertEquals(2500, $billableItem->billed_price_per_unit);
        $this->assertEquals(5000, $billableItem->total_billed_price); // 2500 * 2
    }

}
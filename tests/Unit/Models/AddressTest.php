<?php

namespace Tests\Unit\Models;

use App\Models\Address;
use MatanYadaev\EloquentSpatial\Objects\Point;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class AddressTest extends TenantTestCase
{
    #[Test]
    public function it_cast_point_to_point_type(): void
    {
        $address = Address::factory()->make();

        $this->assertInstanceOf(Point::class, $address->point);
    }
}

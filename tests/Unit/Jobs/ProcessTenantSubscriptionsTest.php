<?php

namespace Tests\Unit\Jobs;

use App\Jobs\HandleSubscriptionDeadlinesApproaching;
use App\Jobs\ProcessTenantSubscriptions;
use App\Jobs\SyncSubscriptionOrderTagsAtDeadline;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProcessTenantSubscriptionsTest extends TenantTestCase
{
    #[Test]
    public function it_queues_the_expected_jobs(): void
    {
        Bus::fake([
            SyncSubscriptionOrderTagsAtDeadline::class,
            HandleSubscriptionDeadlinesApproaching::class,
        ]);

        ProcessTenantSubscriptions::dispatch();

        Bus::assertDispatchedTimes(SyncSubscriptionOrderTagsAtDeadline::class, 1);
        Bus::assertDispatchedTimes(HandleSubscriptionDeadlinesApproaching::class, 1);
    }
}

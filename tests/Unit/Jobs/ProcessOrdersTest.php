<?php

namespace Tests\Unit\Jobs;

use App\Jobs\ProcessOrder;
use App\Jobs\ProcessOrders;
use App\Models\Order;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class ProcessOrdersTest extends TenantTestCase
{
    #[Test]
    public function it_processes_the_expected_orders(): void
    {
        Bus::fake([ProcessOrder::class]);

        $order_ids = Order::factory(2)
            ->create(['confirmed_date' => today(), 'confirmed' => true])
            ->pluck('id');

        (new ProcessOrders($order_ids->toArray()))->handle();

        foreach ($order_ids as $id) {
            Bus::assertDispatched(
                ProcessOrder::class,
                fn(ProcessOrder $job) => $job->order_id === $id
            );
        }
    }

    #[Test]
    public function it_does_not_process_invalid_orders(): void
    {
        Bus::fake([ProcessOrder::class]);

        (new ProcessOrders([12312312, 1203981]))->handle();

        Bus::assertNotDispatched(ProcessOrder::class);
    }

    #[Test]
    public function it_does_not_process_unconfirmed_orders(): void
    {
        Bus::fake([ProcessOrder::class]);

        $order_ids = Order::factory(2)
            ->create(['confirmed_date' => null, 'confirmed' => false])
            ->pluck('id')
            ->toArray();

        (new ProcessOrders($order_ids))->handle();

        Bus::assertNotDispatched(ProcessOrder::class);
    }
}

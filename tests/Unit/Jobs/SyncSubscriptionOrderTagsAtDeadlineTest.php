<?php

namespace Tests\Unit\Jobs;

use App\Jobs\SyncSubscriptionOrderTags;
use App\Jobs\SyncSubscriptionOrderTagsAtDeadline;
use App\Models\Order;
use App\Models\RecurringOrder;
use App\Models\Setting;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Bus;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class SyncSubscriptionOrderTagsAtDeadlineTest extends TenantTestCase
{
    #[Test]
    public function it_queues_the_expected_jobs(): void
    {
        $now = today()->setTime(8, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([SyncSubscriptionOrderTags::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);

        $recurring_order_one = RecurringOrder::factory()->create();
        $order_one = Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today(),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        $recurring_order_two = RecurringOrder::factory()->create();
        $order_two = Order::factory()->create([
            'blueprint_id' => $recurring_order_two->id,
            'deadline_date' => today()->subDay(),
            'pickup_date' => today()->addDays(1),
            'confirmed' => false
        ]);

        (new SyncSubscriptionOrderTagsAtDeadline)->handle();

        Bus::assertDispatchedTimes(SyncSubscriptionOrderTags::class, 1);

        Bus::assertDispatched(
            SyncSubscriptionOrderTags::class,
            fn(SyncSubscriptionOrderTags $job) => in_array($order_one->id, $job->order_ids)
        );

        Bus::assertDispatched(
            SyncSubscriptionOrderTags::class,
            fn(SyncSubscriptionOrderTags $job) => in_array($order_two->id, $job->order_ids)
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_queue_canceled_or_skipped_orders(): void
    {
        $now = today()->setTime(8, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([SyncSubscriptionOrderTags::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);

        $recurring_order = RecurringOrder::factory()->create();

        Order::factory()->create([
            'blueprint_id' => $recurring_order->id,
            'deadline_date' => today()->subDays(1),
            'pickup_date' => today(),
            'canceled' => true,
            'skipped_at' => null,
        ]);

        Order::factory()->create([
            'blueprint_id' => $recurring_order->id,
            'deadline_date' => today()->subDays(1),
            'pickup_date' => today(),
            'canceled' => false,
            'skipped_at' => now(),
        ]);

        (new SyncSubscriptionOrderTagsAtDeadline)->handle();

        Bus::assertNotDispatched(SyncSubscriptionOrderTags::class);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_queue_jobs_where_generation_date_has_not_been_reached_yet(): void
    {
        $now = today()->setTime(7, 59, 59);

        Carbon::setTestNow($now);

        Bus::fake([SyncSubscriptionOrderTags::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);

        $recurring_order = RecurringOrder::factory()->create();
        Order::factory()->create([
            'blueprint_id' => $recurring_order->id,
            'deadline_date' => today(),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        (new SyncSubscriptionOrderTagsAtDeadline)->handle();

        Bus::assertNotDispatched(SyncSubscriptionOrderTags::class);
    }

    #[Test]
    public function it_queues_the_expected_subscription_jobs_when_using_end_of_day_settings(): void
    {
        $now = today()->setTime(23, 59, 59);

        Carbon::setTestNow($now);

        Bus::fake([SyncSubscriptionOrderTags::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 24]);

        $recurring_order_one = RecurringOrder::factory()->create();
        $order_one = Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today(),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        $recurring_order_two = RecurringOrder::factory()->create();
        $order_two = Order::factory()->create([
            'blueprint_id' => $recurring_order_two->id,
            'deadline_date' => today()->subDay(),
            'pickup_date' => today()->addDays(1),
            'confirmed' => false
        ]);

        (new SyncSubscriptionOrderTagsAtDeadline)->handle();

        Bus::assertDispatchedTimes(SyncSubscriptionOrderTags::class, 1);

        Bus::assertDispatched(
            SyncSubscriptionOrderTags::class,
            fn(SyncSubscriptionOrderTags $job) => in_array($order_one->id, $job->order_ids)
        );

        Bus::assertDispatched(
            SyncSubscriptionOrderTags::class,
            fn(SyncSubscriptionOrderTags $job) => in_array($order_two->id, $job->order_ids)
        );

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_queue_the_expected_subscription_jobs_when_using_end_of_day_settings_and_deadline_has_not_passed(): void
    {
        $now = today()->setTime(23, 59, 58);

        Carbon::setTestNow($now);

        Bus::fake([SyncSubscriptionOrderTags::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 24]);

        $recurring_order_one = RecurringOrder::factory()->create();
        Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today(),
            'pickup_date' => today()->addDays(3),
            'confirmed' => false
        ]);

        $recurring_order_two = RecurringOrder::factory()->create();
        Order::factory()->create([
            'blueprint_id' => $recurring_order_two->id,
            'deadline_date' => today()->addDay(),
            'pickup_date' => today()->addDays(1),
            'confirmed' => false
        ]);

        (new SyncSubscriptionOrderTagsAtDeadline)->handle();

        Bus::assertNotDispatched(SyncSubscriptionOrderTags::class);
    }

    #[Test]
    public function it_does_not_queue_orders_with_passed_pickup_dates(): void
    {
        $now = today()->setTime(8, 00, 00);

        Carbon::setTestNow($now);

        Bus::fake([SyncSubscriptionOrderTags::class]);

        Setting::updateOrCreate(['key' => 'timezone'], ['value' => 'America/Chicago']);
        Setting::updateOrCreate(['key' => 'order_deadline_hour'], ['value' => 8]);

        $recurring_order_one = RecurringOrder::factory()->create();
        Order::factory()->create([
            'blueprint_id' => $recurring_order_one->id,
            'deadline_date' => today(),
            'pickup_date' => today()->subDays(3),
            'confirmed' => false
        ]);

        $recurring_order_two = RecurringOrder::factory()->create();
        Order::factory()->create([
            'blueprint_id' => $recurring_order_two->id,
            'deadline_date' => today()->subDay(),
            'pickup_date' => today()->subDays(1),
            'confirmed' => false
        ]);

        (new SyncSubscriptionOrderTagsAtDeadline)->handle();

        Bus::assertNotDispatched(SyncSubscriptionOrderTags::class);

        Carbon::setTestNow();
    }
}

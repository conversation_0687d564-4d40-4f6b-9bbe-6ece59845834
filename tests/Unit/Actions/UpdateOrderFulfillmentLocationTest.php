<?php

namespace Tests\Unit\Actions;

use App\Actions\Order\UpdateOrderFulfillmentLocation;
use App\Exceptions\ExclusivityException;
use App\Models\Date;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\ProductPrice;
use App\Models\ProductPriceGroup;
use App\Models\Schedule;
use App\Models\User;
use Illuminate\Support\Carbon;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class UpdateOrderFulfillmentLocationTest extends TenantTestCase
{
    #[Test]
    public function it_updates_upcoming_unconfirmed_order_details(): void
    {
        Carbon::setTestNow(now());

        $schedule = Schedule::factory()->create();

        $passed_date = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'order_start_date' => today()->subDays(2)->format('Y-m-d'),
            'order_end_date' => today()->subDay()->format('Y-m-d'),
            'pickup_date' => today()->subDay()->format('Y-m-d'),
            'active' => true
        ]);

        $expected_date = Date::factory()->create([
            'schedule_id' => $schedule->id,
            'order_start_date' => today()->subDay()->format('Y-m-d'),
            'order_end_date' => today()->addDays(5)->format('Y-m-d'),
            'pickup_date' => today()->addDays(5)->format('Y-m-d'),
            'active' => true
        ]);

        $old_pickup = Pickup::factory()->create(['schedule_id' => null]);
        $new_pickup = Pickup::factory()->create([
            'schedule_id' => $schedule->id,
            'tax_rate' => 5,
            'delivery_rate' => 6,
            'settings' => ['delivery_fee_type' => 2]
        ]);

        $user = User::factory()->create(['order_id' => 0, 'pickup_point' => $old_pickup->id]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'confirmed' => false, 'schedule_id' => 0]);

        (new UpdateOrderFulfillmentLocation)->handle($order, $new_pickup);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'schedule_id' => $schedule->id,
            'tax_rate' => "5",
            'delivery_rate' => 600,
            'delivery_fee_type' => 2,
            'pickup_date' => $expected_date->pickup_date->format('Y-m-d'),
            'original_pickup_date' => $expected_date->pickup_date->format('Y-m-d'),
            'deadline_date' => $expected_date->order_end_date->format('Y-m-d'),
        ]);

        Carbon::setTestNow(now());
    }

    #[Test]
    public function it_throws_exception_when_upcoming_unconfirmed_order_items_contain_an_excluded_product_from_new_location(): void
    {
        $old_pickup = Pickup::factory()->create(['schedule_id' => null]);

        $new_pickup = Pickup::factory()->create();
        $product = Product::factory()->create();
        $new_pickup->products()->attach($product->id);

        $user = User::factory()->create(['order_id' => 0, 'pickup_point' => $old_pickup->id]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'confirmed' => false, 'schedule_id' => 0]);
        $order_item = OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $this->expectException(ExclusivityException::class);
        $this->expectExceptionMessage('This product is currently not available at your location.');

        (new UpdateOrderFulfillmentLocation)->handle($order, $new_pickup);
    }

    #[Test]
    public function it_updates_order_item_pricing_for_an_upcoming_unconfirmed_order_when_new_pickup_has_a_price_group(): void
    {
        $old_pickup = Pickup::factory()->create(['schedule_id' => null]);

        $price_group = ProductPriceGroup::factory()->create(['type' => ProductPriceGroup::PERCENTAGE_DECREASE, 'amount' => 10]);
        $product = Product::factory()->create(['unit_price' => 100]);
        ProductPrice::factory()->create(['group_id' => $price_group->id, 'product_id' => $product->id]);

        $new_pickup = Pickup::factory()->create(['pricing_group_id' => $price_group->id]);

        $user = User::factory()->create(['order_id' => 0, 'pickup_point' => $old_pickup->id]);
        $order = Order::factory()->create(['customer_id' => $user->id, 'confirmed' => false, 'schedule_id' => 0, 'subtotal' => 10000]);
        $order_item = OrderItem::factory()->create(['order_id' => $order->id, 'qty' => 1, 'product_id' => $product->id, 'unit_price' => 10000]);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $order_item->id,
            'unit_price' => 10000
        ]);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'subtotal' => 10000
        ]);

        (new UpdateOrderFulfillmentLocation)->handle($order, $new_pickup);

        $this->assertDatabaseHas(OrderItem::class, [
            'id' => $order_item->id,
            'unit_price' => 9000
        ]);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'subtotal' => 9000
        ]);
    }
}
<?php

namespace Tests\Unit\Actions;

use App\Actions\Billing\ProcessOrderPayment;
use App\Actions\ProcessOrder;
use App\Events\Order\OrderWasPaid;
use App\Exceptions\InvalidPaymentMethodException;
use App\Exceptions\OrderChargeException;
use App\Mail\OrderProcessed;
use App\Models\Card;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\OrderPayment;
use App\Models\Payment;
use App\Models\Product;
use App\Models\Setting;
use App\Models\Tag;
use App\Models\User;
use App\Support\Enums\OrderStatus;
use Carbon\Carbon;
use Illuminate\Support\Facades\Event;
use Illuminate\Support\Facades\Mail;
use Mockery;
use Mockery\MockInterface;
use PHPUnit\Framework\Attributes\Test;
use Symfony\Component\HttpKernel\Exception\HttpException;
use Tests\TenantTestCase;

class ProcessOrderTest extends TenantTestCase
{
    #[Test]
    public function it_sets_status_to_configured_status(): void
    {
        Setting::updateOrCreate(['key' => 'process_order_status'], ['value' => OrderStatus::completed()]);

        $order = Order::factory()->create(['status_id' => OrderStatus::confirmed()]);

        (new ProcessOrder)->handle($order);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'status_id' => OrderStatus::completed()
        ]);
    }

    #[Test]
    public function it_does_not_change_status_when_process_order_status_is_not_configured(): void
    {
        Setting::where(['key' => 'process_order_status'])->delete();

        $order = Order::factory()->create(['status_id' => OrderStatus::confirmed()]);

        (new ProcessOrder)->handle($order);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'status_id' => OrderStatus::confirmed()
        ]);
    }

    #[Test]
    public function it_sets_staff_to_configured_user(): void
    {
        $staff_user = User::factory()->create();
        Setting::updateOrCreate(['key' => 'process_order_packed_by'], ['value' => $staff_user->id]);

        $order = Order::factory()->create(['staff_id' => '']);

        (new ProcessOrder)->handle($order);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'staff_id' => $staff_user->id
        ]);
    }

    #[Test]
    public function it_does_not_change_staff_when_process_order_packed_by_is_not_configured(): void
    {
        config(['tenant.settings' => ['process_order_packed_by' => null]]);

        $order = Order::factory()->create(['staff_id' => '']);

        (new ProcessOrder)->handle($order);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'staff_id' => ''
        ]);
    }

    #[Test]
    public function it_sets_processed_properties_on_order(): void
    {
        Carbon::setTestNow($now = now());

        $order = Order::factory()->create(['processed' => false, 'processed_date' => null]);

        (new ProcessOrder)->handle($order);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'processed' => true,
            'processed_date' => $now->format('Y-m-d')
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_removes_processing_tag_from_order_when_finished_processing(): void
    {
        $order = Order::factory()->create();

        $tag = Tag::factory()->create([
            'title' => 'Processing',
            'type' => Tag::type('order')
        ]);

        $order->tags()->attach($tag->id);
        $this->assertTrue($order->tags()->where('title', $tag->title)->exists());

        (new ProcessOrder)->handle($order);

        $this->assertFalse($order->tags()->where('title', $tag->title)->exists());
    }

    #[Test]
    public function it_removes_payment_failed_tag_from_order_when_finished_processing(): void
    {
        $order = Order::factory()->create();

        $tag = Tag::factory()->create([
            'title' => 'Payment Failed',
            'type' => Tag::type('order')
        ]);

        $order->tags()->attach($tag->id);
        $this->assertTrue($order->tags()->where('title', $tag->title)->exists());

        (new ProcessOrder)->handle($order);

        $this->assertFalse($order->tags()->where('title', $tag->title)->exists());
    }

    #[Test]
    public function it_dispatches_email_job_when_setting_is_enabled(): void
    {
        Mail::fake();

        Setting::updateOrCreate(['key' => 'process_order_email'], ['value' => true]);

        $order = Order::factory()->create();

        (new ProcessOrder)->handle($order);

        Mail::assertQueued(
            OrderProcessed::class,
            function (OrderProcessed $mail) use ($order) {
                return $mail->hasTo($order->customer_email)
                    && $mail->order_id === $order->id;
            }
        );
    }

    #[Test]
    public function it_does_not_dispatch_email_job_when_setting_is_disabled(): void
    {
        Mail::fake();

        Setting::updateOrCreate(['key' => 'process_order_email'], ['value' => false]);

        $order = Order::factory()->create();

        (new ProcessOrder)->handle($order);

        Mail::assertNotQueued(OrderProcessed::class);
    }

    #[Test]
    public function it_returns_expected_result_when_charge_card_is_disabled(): void
    {
        $order = Order::factory()->create();

        $result = (new ProcessOrder)->handle($order);

        $this->assertEquals(['payment' => null], $result);
    }

    #[Test]
    public function it_attempts_payment_without_user_card_when_charging_is_enabled(): void
    {
        Event::fake([OrderWasPaid::class]);

        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => 1]);

        $payment = Payment::where(['key' => 'card'])->first();
        $card = Card::factory()->create(['source_id' => 'abc_123']);
        $order = Order::factory()->create([
            'customer_id' => $card->user_id,
            'payment_id' => $payment->id,
            'payment_source_id' => null,
            'paid' => false,
            'confirmed' => true,
            'total' => 1000
        ]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $order_payment = OrderPayment::factory()->create();

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) use ($order, $order_payment) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn(Order $arg) => $arg->id === $order->id),
                    null,
                    null,
                    'Order processed'
                )
                ->andReturn($order_payment);
        });

        $result = (new ProcessOrder)->handle($order);

        $this->assertEquals(['payment' => $order_payment], $result);
    }

    #[Test]
    public function it_attempts_payment_without_user_card_when_not_found_when_charging_is_enabled(): void
    {
        Event::fake([OrderWasPaid::class]);

        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => 1]);

        $payment = Payment::where(['key' => 'card'])->first();
        $card = Card::factory()->create(['source_id' => 'abc_123']);
        $order = Order::factory()->create([
            'customer_id' => $card->user_id,
            'payment_id' => $payment->id,
            'payment_source_id' => 0,
            'paid' => false,
            'confirmed' => true,
            'total' => 1000
        ]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $order_payment = OrderPayment::factory()->create();

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) use ($order, $order_payment) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn(Order $arg) => $arg->id === $order->id),

                    null,
                    null,
                    'Order processed'
                )
                ->andReturn($order_payment);
        });

        $result = (new ProcessOrder)->handle($order);

        $this->assertEquals(['payment' => $order_payment], $result);
    }

    #[Test]
    public function it_does_not_add_paid_tag_after_unsuccessful_payment(): void
    {
        Event::fake([OrderWasPaid::class]);

        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => 1]);

        $payment = Payment::where(['key' => 'card'])->first();
        $card = Card::factory()->create(['source_id' => 'abc_123']);
        $order = Order::factory()->create([
            'customer_id' => $card->user_id,
            'payment_id' => $payment->id,
            'payment_source_id' => $card->id,
            'paid' => false,
            'confirmed' => true,
            'total' => 1000
        ]);

        $tag = Tag::factory()->create([
            'title' => 'Paid',
            'type' => Tag::type('order')
        ]);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) {
            $mock->shouldReceive('handle')
                ->andThrows(new OrderChargeException);
        });

        (new ProcessOrder)->handle($order);

        $this->assertFalse($order->tags()->where('title', $tag->title)->exists());
    }

    #[Test]
    public function it_add_payment_failed_tag_after_unsuccessful_payment(): void
    {
        Event::fake([OrderWasPaid::class]);

        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => 1]);

        $payment = Payment::where(['key' => 'card'])->first();
        $card = Card::factory()->create(['source_id' => 'abc_123']);
        $order = Order::factory()->create([
            'customer_id' => $card->user_id,
            'payment_id' => $payment->id,
            'payment_source_id' => $card->id,
            'paid' => false,
            'confirmed' => true,
            'total' => 1000
        ]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $tag = Tag::factory()->create([
            'title' => 'Payment Failed',
            'type' => Tag::type('order')
        ]);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) {
            $mock->shouldReceive('handle')
                ->andThrows(new OrderChargeException);
        });

        (new ProcessOrder)->handle($order);

        $this->assertTrue($order->tags()->where('title', $tag->title)->exists());
    }

    #[Test]
    public function it_removes_processing_tag_after_unsuccessful_payment(): void
    {
        Event::fake([OrderWasPaid::class]);

        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => 1]);

        $payment = Payment::where(['key' => 'card'])->first();
        $card = Card::factory()->create(['source_id' => 'abc_123']);
        $order = Order::factory()->create([
            'customer_id' => $card->user_id,
            'payment_id' => $payment->id,
            'payment_source_id' => $card->id,
            'paid' => false,
            'confirmed' => true,
            'total' => 1000
        ]);

        $tag = Tag::factory()->create([
            'title' => 'Processing',
            'type' => Tag::type('order')
        ]);

        $order->tags()->attach($tag->id);
        $this->assertTrue($order->tags()->where('title', $tag->title)->exists());

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) {
            $mock->shouldReceive('handle')
                ->andThrows(new OrderChargeException);
        });

        (new ProcessOrder)->handle($order);

        $this->assertFalse($order->tags()->where('title', $tag->title)->exists());
    }

    #[Test]
    public function it_returns_exception_response_after_failed_payment(): void
    {
        Event::fake([OrderWasPaid::class]);

        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => 1]);

        $payment = Payment::where(['key' => 'card'])->first();
        $card = Card::factory()->create(['source_id' => 'abc_123']);
        $order = Order::factory()->create([
            'customer_id' => $card->user_id,
            'payment_id' => $payment->id,
            'payment_source_id' => $card->id,
            'paid' => false,
            'confirmed' => true,
            'total' => 1000
        ]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) {
            $mock->shouldReceive('handle')
                ->andThrows(new OrderChargeException('some error'));
        });

        $result = (new ProcessOrder)->handle($order);

        $this->assertEquals([
            'exception' => new HttpException(400, 'some error<br>The order could not be processed.')
        ], $result);
    }

    #[Test]
    public function it_sets_status_after_failed_payment(): void
    {
        Setting::updateOrCreate(['key' => 'process_order_status'], ['value' => OrderStatus::completed()]);
        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => 1]);

        $payment = Payment::where(['key' => 'card'])->first();
        $card = Card::factory()->create(['source_id' => 'abc_123']);
        $order = Order::factory()->create([
            'customer_id' => $card->user_id,
            'payment_id' => $payment->id,
            'payment_source_id' => $card->id,
            'paid' => false,
            'confirmed' => true,
            'total' => 1000
        ]);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) {
            $mock->shouldReceive('handle')
                ->andThrows(new OrderChargeException);
        });

        (new ProcessOrder)->handle($order);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'status_id' => OrderStatus::completed()
        ]);
    }

    #[Test]
    public function it_sets_staff_id_after_failed_payment(): void
    {
        $staff_user = User::factory()->create();

        Setting::updateOrCreate(['key' => 'process_order_packed_by'], ['value' => $staff_user->id]);
        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => 1]);

        $payment = Payment::where(['key' => 'card'])->first();
        $card = Card::factory()->create(['source_id' => 'abc_123']);
        $order = Order::factory()->create([
            'customer_id' => $card->user_id,
            'payment_id' => $payment->id,
            'payment_source_id' => $card->id,
            'paid' => false,
            'confirmed' => true,
            'total' => 1000
        ]);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) {
            $mock->shouldReceive('handle')
                ->andThrows(new OrderChargeException);
        });

        (new ProcessOrder)->handle($order);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'staff_id' => $staff_user->id
        ]);
    }

    #[Test]
    public function it_does_not_sets_processed_properties_on_order_after_failed_payment(): void
    {
        Carbon::setTestNow($now = now());

        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => 1]);

        $payment = Payment::where(['key' => 'card'])->first();
        $card = Card::factory()->create(['source_id' => 'abc_123']);
        $order = Order::factory()->create([
            'customer_id' => $card->user_id,
            'payment_id' => $payment->id,
            'payment_source_id' => $card->id,
            'paid' => false,
            'confirmed' => true,
            'total' => 1000
        ]);
        $product = Product::factory()->create(['unit_price' => '10.00']);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) {
            $mock->shouldReceive('handle')
                ->andThrows(new OrderChargeException);
        });

        (new ProcessOrder)->handle($order);

        $this->assertDatabaseMissing(Order::class, [
            'id' => $order->id,
            'processed' => true,
            'processed_date' => $now->format('Y-m-d')
        ]);

        Carbon::setTestNow();
    }

    #[Test]
    public function it_does_not_dispatch_email_job_when_setting_is_enabled_after_failed_payment(): void
    {
        Mail::fake();

        Setting::updateOrCreate(['key' => 'process_order_email'], ['value' => true]);
        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => 1]);

        $payment = Payment::where(['key' => 'card'])->first();
        $card = Card::factory()->create(['source_id' => 'abc_123']);
        $order = Order::factory()->create([
            'customer_id' => $card->user_id,
            'payment_id' => $payment->id,
            'payment_source_id' => $card->id,
            'paid' => false,
            'confirmed' => true,
            'total' => 1000
        ]);

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) {
            $mock->shouldReceive('handle')
                ->andThrows(new OrderChargeException);
        });

        (new ProcessOrder)->handle($order);

        Mail::assertNotSent(OrderProcessed::class);
    }

    #[Test]
    public function it_recalculates_order_totals_before_processing_payments(): void
    {
        Event::fake([OrderWasPaid::class]);

        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => 1]);

        $payment = Payment::where(['key' => 'card'])->first();
        $card = Card::factory()->create(['source_id' => 'abc_123']);
        $order = Order::factory()->create([
            'customer_id' => $card->user_id,
            'payment_id' => $payment->id,
            'payment_source_id' => null,
            'paid' => false,
            'confirmed' => true,
            'total' => 1000,
            'credit_applied' => 0
        ]);

        $product = Product::factory()->create(['unit_price' => '10.00']);
        OrderItem::factory()->create(['order_id' => $order->id, 'product_id' => $product->id]);

        $order->customer->credit = 200;
        $order->customer->save();

        $order_payment = OrderPayment::factory()->create();

        $this->mock(ProcessOrderPayment::class, function (MockInterface $mock) use ($order, $order_payment) {
            $mock->shouldReceive('handle')
                ->once()
                ->with(
                    Mockery::on(fn(Order $arg) => $arg->id === $order->id),
                    null,
                    null,
                    'Order processed'
                )
                ->andReturn($order_payment);
        });

        (new ProcessOrder)->handle($order);

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'credit_applied' => 200
        ]);

        $this->assertDatabaseHas(User::class, [
            'id' => $order->customer_id,
            'credit' => 0
        ]);
    }

    #[Test]
    public function it_does_not_process_order_when_no_payment_is_selected(): void
    {
        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => 1]);

        $order = Order::factory()->create(['payment_id' => 0, 'status_id' => OrderStatus::confirmed()]);

        $result = (new ProcessOrder)->handle($order);

        $this->assertInstanceOf(InvalidPaymentMethodException::class, $result['exception']);
        $this->assertEquals('Payment method is invalid.', $result['exception']->getMessage());

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'status_id' => OrderStatus::confirmed()
        ]);

        $this->assertTrue($order->tags()->where([
            'title' => 'Payment Failed',
            'type' => Tag::type('order')
        ])->exists());
    }

    #[Test]
    public function it_does_not_process_order_when_selected_payment_method_is_disabled(): void
    {
        Setting::updateOrCreate(['key' => 'process_order_charge'], ['value' => 1]);

        $payment = Payment::factory()->create(['enabled' => 0]);

        $order = Order::factory()->create(['payment_id' => $payment->id, 'status_id' => OrderStatus::confirmed()]);

        $result = (new ProcessOrder)->handle($order);

        $this->assertInstanceOf(InvalidPaymentMethodException::class, $result['exception']);
        $this->assertEquals('Payment method is invalid.', $result['exception']->getMessage());

        $this->assertDatabaseHas(Order::class, [
            'id' => $order->id,
            'status_id' => OrderStatus::confirmed()
        ]);

        $this->assertTrue($order->tags()->where([
            'title' => 'Payment Failed',
            'type' => Tag::type('order')
        ])->exists());
    }

    protected function setUp(): void
    {
        parent::setUp();

        $payment = Payment::where(['key' => 'card'])->first()
            ?? Payment::factory()->create(['key' => 'card']);
        
        $payment->enabled = true;
        $payment->save();
    }
}

<?php

namespace Tests\Unit\Console\Commands;

use App\Models\Price;
use App\Models\Product;
use App\Support\Enums\ProductType;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class MigrateMultipacksToPriceTiers extends TenantTestCase
{
    #[Test]
    public function it_migrates_multipacks_to_price_tiers_correctly(): void
    {
        $base_product = Product::factory()->create([
            'type_id' => ProductType::STANDARD->value,
            'title' => 'Base Product',
            'unit_price' => 12.00,
            'sale_unit_price' => 11.00,
        ]);

        $mutlipack_product_1 = Product::factory()->create([
            'title' => '4 PK Base Product',
            'unit_price' => 10.00,
            'sale_unit_price' => 9.00,
        ]);

        $mutlipack_product_2 = Product::factory()->create([
            'title' => '10 PK Base Product',
            'unit_price' => 6.00,
            'sale_unit_price' => 5.00,
        ]);

        $mutlipack_product_1->bundle()->attach($base_product->id, ['qty' => 4]);
        $mutlipack_product_2->bundle()->attach($base_product->id, ['qty' => 10]);

        $this->assertDatabaseHas('bundle_product', [
            'product_id' => $base_product->id,
            'bundle_id' => $mutlipack_product_1->id,
            'qty' => 4,
        ]);

        $this->assertDatabaseHas('bundle_product', [
            'product_id' => $base_product->id,
            'bundle_id' => $mutlipack_product_2->id,
            'qty' => 10,
        ]);

        $this->artisan('app:migrate-multipacks-to-price-tiers');

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $base_product->id,
            'quantity' => 1,
            'unit_price' => 1200,
            'sale_unit_price' => 1100,
        ]);

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $base_product->id,
            'quantity' => 4,
            'unit_price' => 1140, // ((1200 * 4) * 0.95 / 4),
            'sale_unit_price' => 1045 // ((1100 * 4) * 0.95 / 4),
        ]);

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $base_product->id,
            'quantity' => 10,
            'unit_price' => 1140, // ((1200 * 10) * 0.95 / 10)
            'sale_unit_price' => 1045 // ((1100 * 10) * 0.95 / 10)
        ]);
    }

    #[Test]
    public function it_doesnt_migrate_non_standard_product_types(): void
    {
        $base_product = Product::factory()->create([
            'type_id' => ProductType::PREORDER->value,
            'title' => 'Base Product',
            'unit_price' => 12.00,
            'sale_unit_price' => 11.00,
        ]);

        $mutlipack_product = Product::factory()->create([
            'title' => '4 PK Base Product',
            'unit_price' => 10.00,
            'sale_unit_price' => 9.00,
        ]);

        $mutlipack_product->bundle()->attach($base_product->id, ['qty' => 4]);

        $this->assertDatabaseHas('bundle_product', [
            'product_id' => $base_product->id,
            'bundle_id' => $mutlipack_product->id,
            'qty' => 4,
        ]);

        $this->artisan('app:migrate-multipacks-to-price-tiers');

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $base_product->id,
            'quantity' => 1,
            'unit_price' => 1200,
            'sale_unit_price' => 1100,
        ]);

        $this->assertDatabaseMissing(Price::class, [
            'product_id' => $base_product->id,
            'quantity' => 4,
            'unit_price' => 1140, // ((1200 * 4) * 0.95 / 4),
            'sale_unit_price' => 1045 // ((1100 * 4) * 0.95 / 4),
        ]);
    }

    #[Test]
    public function it_doesnt_migrate_base_products_with_zero_unit_price(): void
    {
        $base_product = Product::factory()->create([
            'type_id' => ProductType::STANDARD->value,
            'title' => 'Base Product',
            'unit_price' => 0,
            'sale_unit_price' => 11.00,
        ]);

        $mutlipack_product = Product::factory()->create([
            'title' => '4 PK Base Product',
            'unit_price' => 10.00,
            'sale_unit_price' => 9.00,
        ]);

        $mutlipack_product->bundle()->attach($base_product->id, ['qty' => 4]);

        $this->assertDatabaseHas('bundle_product', [
            'product_id' => $base_product->id,
            'bundle_id' => $mutlipack_product->id,
            'qty' => 4,
        ]);

        $this->artisan('app:migrate-multipacks-to-price-tiers');

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $base_product->id,
            'quantity' => 1,
            'unit_price' => 0,
            'sale_unit_price' => 1100,
        ]);

        $this->assertDatabaseMissing(Price::class, [
            'product_id' => $base_product->id,
            'quantity' => 4,
            'unit_price' => 0, // ((0 * 4) * 0.95 / 4),
            'sale_unit_price' => 1045 // ((1100 * 4) * 0.95 / 4),
        ]);
    }

    #[Test]
    public function it_doesnt_migrate_base_products_with_zero_sale_unit_price(): void
    {
        $base_product = Product::factory()->create([
            'type_id' => ProductType::STANDARD->value,
            'title' => 'Base Product',
            'unit_price' => 12.00,
            'sale_unit_price' => 0,
        ]);

        $mutlipack_product = Product::factory()->create([
            'title' => '4 PK Base Product',
            'unit_price' => 10.00,
            'sale_unit_price' => 9.00,
        ]);

        $mutlipack_product->bundle()->attach($base_product->id, ['qty' => 4]);

        $this->assertDatabaseHas('bundle_product', [
            'product_id' => $base_product->id,
            'bundle_id' => $mutlipack_product->id,
            'qty' => 4,
        ]);

        $this->artisan('app:migrate-multipacks-to-price-tiers');

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $base_product->id,
            'quantity' => 1,
            'unit_price' => 1200,
            'sale_unit_price' => 0,
        ]);

        $this->assertDatabaseMissing(Price::class, [
            'product_id' => $base_product->id,
            'quantity' => 4,
            'unit_price' => 1140, // ((1200 * 4) * 0.95 / 4),
            'sale_unit_price' => 0 // ((0 * 4) * 0.95 / 4),
        ]);
    }

    #[Test]
    public function it_doesnt_migrate_base_products_with_non_matching_multipack_title(): void
    {
        $base_product = Product::factory()->create([
            'type_id' => ProductType::STANDARD->value,
            'title' => 'Base Product',
            'unit_price' => 12.00,
            'sale_unit_price' => 11.00,
        ]);

        $mutlipack_product = Product::factory()->create([
            'title' => '4 pack Base Product',
            'unit_price' => 10.00,
            'sale_unit_price' => 9.00,
        ]);

        $mutlipack_product->bundle()->attach($base_product->id, ['qty' => 4]);

        $this->assertDatabaseHas('bundle_product', [
            'product_id' => $base_product->id,
            'bundle_id' => $mutlipack_product->id,
            'qty' => 4,
        ]);

        $this->artisan('app:migrate-multipacks-to-price-tiers');

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $base_product->id,
            'quantity' => 1,
            'unit_price' => 1200,
            'sale_unit_price' => 1100,
        ]);

        $this->assertDatabaseMissing(Price::class, [
            'product_id' => $base_product->id,
            'quantity' => 4,
            'unit_price' => 1140, // ((1200 * 4) * 0.95 / 4),
            'sale_unit_price' => 1045 // ((1100 * 4) * 0.95 / 4),
        ]);
    }

    #[Test]
    public function it_doesnt_migrate_base_products_that_are_deleted(): void
    {
        $base_product = Product::factory()->create([
            'type_id' => ProductType::STANDARD->value,
            'title' => 'Base Product',
            'unit_price' => 12.00,
            'sale_unit_price' => 11.00,
            'deleted_at' => now(),
        ]);

        $mutlipack_product = Product::factory()->create([
            'title' => '4 pack Base Product',
            'unit_price' => 10.00,
            'sale_unit_price' => 9.00,
        ]);

        $mutlipack_product->bundle()->attach($base_product->id, ['qty' => 4]);

        $this->assertDatabaseHas('bundle_product', [
            'product_id' => $base_product->id,
            'bundle_id' => $mutlipack_product->id,
            'qty' => 4,
        ]);

        $this->artisan('app:migrate-multipacks-to-price-tiers');

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $base_product->id,
            'quantity' => 1,
            'unit_price' => 1200,
            'sale_unit_price' => 1100,
        ]);

        $this->assertDatabaseMissing(Price::class, [
            'product_id' => $base_product->id,
            'quantity' => 4,
            'unit_price' => 1140, // ((1200 * 4) * 0.95 / 4),
            'sale_unit_price' => 1045 // ((1100 * 4) * 0.95 / 4),
        ]);
    }

    #[Test]
    public function it_doesnt_migrate_bundle_products_that_are_deleted(): void
    {
        $base_product = Product::factory()->create([
            'type_id' => ProductType::STANDARD->value,
            'title' => 'Base Product',
            'unit_price' => 12.00,
            'sale_unit_price' => 11.00,
        ]);

        $mutlipack_product = Product::factory()->create([
            'title' => '4 pack Base Product',
            'unit_price' => 10.00,
            'sale_unit_price' => 9.00,
            'deleted_at' => now(),
        ]);

        $mutlipack_product->bundle()->attach($base_product->id, ['qty' => 4]);

        $this->assertDatabaseHas('bundle_product', [
            'product_id' => $base_product->id,
            'bundle_id' => $mutlipack_product->id,
            'qty' => 4,
        ]);

        $this->artisan('app:migrate-multipacks-to-price-tiers');

        $this->assertDatabaseHas(Price::class, [
            'product_id' => $base_product->id,
            'quantity' => 1,
            'unit_price' => 1200,
            'sale_unit_price' => 1100,
        ]);

        $this->assertDatabaseMissing(Price::class, [
            'product_id' => $base_product->id,
            'quantity' => 4,
            'unit_price' => 1140, // ((1200 * 4) * 0.95 / 4),
            'sale_unit_price' => 1045 // ((1100 * 4) * 0.95 / 4),
        ]);
    }

    #[Test]
    public function it_removes_variant_relation_when_migrating_to_price_tiers(): void
    {
        $base_product = Product::factory()->create([
            'type_id' => ProductType::STANDARD->value,
            'title' => 'Base Product',
            'unit_price' => 12.00,
            'sale_unit_price' => 11.00,
        ]);

        $mutlipack_product_1 = Product::factory()->create([
            'title' => '4 PK Base Product',
            'unit_price' => 10.00,
            'sale_unit_price' => 9.00,
        ]);

        $mutlipack_product_2 = Product::factory()->create([
            'title' => '10 PK Base Product',
            'unit_price' => 6.00,
            'sale_unit_price' => 5.00,
        ]);

        $other_product = Product::factory()->create([
            'title' => 'Some other thing',
            'unit_price' => 6.00,
            'sale_unit_price' => 5.00,
        ]);

        $mutlipack_product_1->bundle()->attach($base_product->id, ['qty' => 4]);
        $mutlipack_product_2->bundle()->attach($base_product->id, ['qty' => 10]);
        $other_product->bundle()->attach($base_product->id, ['qty' => 20]);

        $base_product->variants()->attach($mutlipack_product_1->id);
        $base_product->variants()->attach($mutlipack_product_2->id);
        $base_product->variants()->attach($other_product->id);

        $this->assertDatabaseHas('product_variants', [
            'product_id' => $base_product->id,
            'variant_id' => $mutlipack_product_1->id,
        ]);

        $this->assertDatabaseHas('product_variants', [
            'product_id' => $base_product->id,
            'variant_id' => $mutlipack_product_2->id,
        ]);

        $this->assertDatabaseHas('product_variants', [
            'product_id' => $base_product->id,
            'variant_id' => $other_product->id,
        ]);

        $this->artisan('app:migrate-multipacks-to-price-tiers');

        $this->assertDatabaseMissing('product_variants', [
            'product_id' => $base_product->id,
            'variant_id' => $mutlipack_product_1->id,
        ]);

        $this->assertDatabaseMissing('product_variants', [
            'product_id' => $base_product->id,
            'variant_id' => $mutlipack_product_2->id,
        ]);

        $this->assertDatabaseHas('product_variants', [
            'product_id' => $base_product->id,
            'variant_id' => $other_product->id,
        ]);
    }
}

<?php

namespace Tests\Unit\Geocoding;

use App\Exceptions\NoGeocodeResultsException;
use App\Models\Setting;
use App\Services\Geocoding\GeocodedAddress;
use App\Services\Geocoding\Geocodio;
use GuzzleHttp\Promise\PromiseInterface;
use Illuminate\Http\Client\Request;
use Illuminate\Support\Facades\Http;
use PHPUnit\Framework\Attributes\Test;
use Tests\TenantTestCase;

class GeocodioTest extends TenantTestCase
{
    #[Test]
    public function it_can_geocode_an_address_string_with_configured_country_setting(): void
    {
        Http::fake([ 'api.geocod.io/*' => $this->geocodeResult() ]);

        config(['services.geocodio.key' => 'some-api-key']);
        Setting::updateOrCreate(['key' => 'farm_country'],['value'  => 'country-name']);

        $address_string = ' 123 Fake St, Fakeville, FK, 12345 ';

        $result = (new Geocodio)->fromAddress($address_string);

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            '12345',
            'country response',
            0.9
        ), $result);

        Http::assertSent(function (Request $request) use ($address_string) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://api.geocod.io/v1.7/geocode' &&
                $request['q'] == trim($address_string) &&
                $request['country'] == 'country-name' &&
                $request['api_key'] == 'some-api-key';
        });
    }

    #[Test]
    public function it_can_geocode_an_address_string_with_default_country_setting(): void
    {
        Http::fake([ 'api.geocod.io/*' => $this->geocodeResult() ]);

        config(['services.geocodio.key' => 'some-api-key']);
        Setting::where('key', 'farm_country')->delete();
        Setting::flushCache();

        $address_string = ' 123 Fake St, Fakeville, FK, 12345 ';

        $result = (new Geocodio)->fromAddress($address_string);

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            '12345',
            'country response',
            0.9
        ), $result);

        Http::assertSent(function (Request $request) use ($address_string) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://api.geocod.io/v1.7/geocode' &&
                $request['q'] == trim($address_string) &&
                $request['country'] == 'USA' &&
                $request['api_key'] == 'some-api-key';
        });
    }

    #[Test]
    public function it_can_geocode_an_address_array_with_configured_country_setting(): void
    {
        Http::fake([ 'api.geocod.io/*' => $this->geocodeResult() ]);

        config(['services.geocodio.key' => 'some-api-key']);
        Setting::updateOrCreate(['key' => 'farm_country'],['value'  => 'country-name']);

        $result = (new Geocodio)->fromAddressParts([
            'street' => ' 123 Fake St ',
            'city' => ' Fakeville ',
            'state' => ' FK ',
            'zip' => ' 12345 '
        ]);

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            '12345',
            'country response',
            0.9
        ), $result);

        Http::assertSent(function (Request $request) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://api.geocod.io/v1.7/geocode' &&
                $request['q'] == '123 Fake St, Fakeville, FK, 12345' &&
                $request['country'] == 'country-name' &&
                $request['api_key'] == 'some-api-key';
        });
    }

    #[Test]
    public function it_can_geocode_an_address_array_with_default_country_setting(): void
    {
        Http::fake([ 'api.geocod.io/*' => $this->geocodeResult() ]);

        config(['services.geocodio.key' => 'some-api-key']);
        Setting::where('key', 'farm_country')->delete();
        Setting::flushCache();

        $result = (new Geocodio)->fromAddressParts([
            'street' => ' 123 Fake St ',
            'city' => ' Fakeville ',
            'state' => ' FK ',
            'zip' => ' 12345 '
        ]);

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            '12345',
            'country response',
            0.9
        ), $result);

        Http::assertSent(function (Request $request) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://api.geocod.io/v1.7/geocode' &&
                $request['q'] == '123 Fake St, Fakeville, FK, 12345' &&
                $request['country'] == 'USA' &&
                $request['api_key'] == 'some-api-key';
        });
    }

    #[Test]
    public function it_can_geocode_an_zipcode_with_configured_country_setting(): void
    {
        Http::fake([ 'api.geocod.io/*' => $this->geocodeResult() ]);

        config(['services.geocodio.key' => 'some-api-key']);
        Setting::updateOrCreate(['key' => 'farm_country'],['value'  => 'country-name']);

        $result = (new Geocodio)->fromZipcode(' 12345 ');

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            '12345',
            'country response',
            0.9
        ), $result);

        Http::assertSent(function (Request $request) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://api.geocod.io/v1.7/geocode' &&
                $request['postal_code'] == '12345' &&
                $request['country'] == 'country-name' &&
                $request['api_key'] == 'some-api-key';
        });
    }

    #[Test]
    public function it_can_geocode_an_zipcode_with_default_country_setting(): void
    {
        Http::fake([ 'api.geocod.io/*' => $this->geocodeResult() ]);

        config(['services.geocodio.key' => 'some-api-key']);
        Setting::where('key', 'farm_country')->delete();
        Setting::flushCache();

        $result = (new Geocodio)->fromZipcode(' 12345 ');

        $this->assertEquals(new GeocodedAddress(
            123.45,
            67.89,
            'Fakeville',
            'FK',
            '12345',
            'country response',
            0.9
        ), $result);

        Http::assertSent(function (Request $request) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://api.geocod.io/v1.7/geocode' &&
                $request['postal_code'] == '12345' &&
                $request['country'] == 'USA' &&
                $request['api_key'] == 'some-api-key';
        });
    }

    #[Test]
    public function it_throws_an_exception_when_geocoding_an_address_string_that_returns_no_results(): void
    {
        Http::fake([ 'api.geocod.io/*' => $this->geocodeEmptyResult() ]);

        config(['services.geocodio.key' => 'some-api-key']);

        $address_string = ' 123 Fake St, Fakeville, FK, 12345 ';

        $this->expectException(NoGeocodeResultsException::class);

        (new Geocodio)->fromAddress($address_string);

        Http::assertSent(function (Request $request) use ($address_string) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://api.geocod.io/v1.7/geocode' &&
                $request['q'] == trim($address_string) &&
                $request['country'] == 'USA' &&
                $request['api_key'] == 'some-api-key';
        });
    }

    #[Test]
    public function it_throws_a_no_result_exception_when_a_bad_request_exception_is_thrown(): void
    {
        Http::fake([
            '*' =>  Http::response(['error' => ['description' => 'Test was an error.']], 400)
        ]);

        config(['services.geocodio.key' => 'some-api-key']);

        $address_string = ' 123 Fake St, Fakeville, FK, 12345 ';

        $this->expectException(NoGeocodeResultsException::class);

        (new Geocodio)->fromAddress($address_string);

        Http::assertSentCount(1);

        Http::assertSent(function (Request $request) use ($address_string) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://api.geocod.io/v1.7/geocode' &&
                $request['q'] == trim($address_string) &&
                $request['country'] == 'USA' &&
                $request['api_key'] == 'some-api-key';
        });
    }

    #[Test]
    public function it_throws_a_no_result_exception_when_a_unprocessable_request_exception_is_thrown(): void
    {
        Http::fake([
            '*' =>  Http::response(['error' => ['description' => 'Test was an error.']], 422)
        ]);

        config(['services.geocodio.key' => 'some-api-key']);

        $address_string = ' 123 Fake St, Fakeville, FK, 12345 ';

        $this->expectException(NoGeocodeResultsException::class);

        (new Geocodio)->fromAddress($address_string);

        Http::assertSentCount(1);

        Http::assertSent(function (Request $request) use ($address_string) {
            return $request->method() && 'GET' &&
                $request->url() && 'https://api.geocod.io/v1.7/geocode' &&
                $request['q'] == trim($address_string) &&
                $request['country'] == 'USA' &&
                $request['api_key'] == 'some-api-key';
        });
    }


    private function geocodeResult($accuracy = 0.9): PromiseInterface
    {
        return Http::response([
            'results' => [
                [
                    'location' => [
                        'lat' => 123.45,
                        'lng' => 67.89
                    ],
                    'address_components' => [
                        'city' => 'Fakeville',
                        'state' => 'FK',
                        'zip' => '12345',
                        'country' => 'country response'
                    ],
                    'accuracy' => $accuracy
                ]
            ]
        ]);
    }

    private function geocodeEmptyResult(): PromiseInterface
    {
        return Http::response(['results' => []]);
    }
}
/vendor
/node_modules
/data.ms
.env
.env.test
.DS_Store


public/build
public/hot
public/uploads
public/client-files
public/themes
public/theme_assets
public/mix-manifest.json
public/js/chunks
public/pickup_manager

# Individual compiled assets.
public/js/coupon.js
public/js/main.js
public/js/main.js.LICENSE.txt
public/js/admin/inertia-app.js
public/js/admin/inertia-app.js.LICENSE.txt
*/node_modules
*.map

docker.env
docker/
docker-compose.override.yml

# script generated files for theme
resources/theme/public/css/tailwind-full.css
resources/theme/public/css/tailwind-scoped.css
resources/theme/public/css/tailwind.css
resources/theme/public/css/theme.css
resources/theme/public/js/app.js
resources/theme/public/js/manifest.js
resources/theme/public/js/vendor.js
resources/theme/public/js/vendor.js.LICENSE.txt
resources/theme/public/mix-manifest.json


### PhpStorm ###
# Covers JetBrains IDEs: IntelliJ, RubyMine, PhpStorm, AppCode, PyCharm

*.iml

## Directory-based project format:
.idea/
# if you remove the above rule, at least ignore the following:

# User-specific stuff:
# .idea/workspace.xml
# .idea/tasks.xml
# .idea/dictionaries

# Sensitive or high-churn files:
# .idea/dataSources.ids
# .idea/dataSources.xml
# .idea/sqlDataSources.xml
# .idea/dynamic.xml
# .idea/uiDesigner.xml

# Gradle:
# .idea/gradle.xml
# .idea/libraries

# Mongo Explorer plugin:
# .idea/mongoSettings.xml

## File-based project format:
*.ipr
*.iws

## Plugin-specific files:

# IntelliJ
/out/

# mpeltonen/sbt-idea plugin
.idea_modules/

# JIRA plugin
atlassian-ide-plugin.xml

# Crashlytics plugin (for Android Studio and IntelliJ)
com_crashlytics_export_strings.xml
crashlytics.properties
crashlytics-build.properties
npm-debug.log
/laradock
/.vscode/sftp.json
/.vscode/launch.json
/.vscode/settings.json
public/pickup_manager/js/pickup_manager.js
public/css/graze.css
public/css/admin.css

docker/
/docker.env
/public/js/theme.js
/public/css/page-editor.css
/public/css/tailwind.css
#/public/css/vendors.css
#/public/js/vendor.js
/.phpunit.result.cache
/.envault.json

composer
auth.json

.phpunit.cache/test-results
/test-results/
/playwright-report/
/blob-report/
/playwright/.cache/
/playwright/.auth

supervisord.log
supervisord.pid

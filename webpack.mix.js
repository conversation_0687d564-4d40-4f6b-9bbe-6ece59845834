const mix = require('laravel-mix');
const webpack = require('webpack');

const { BugsnagSourceMapUploaderPlugin } = require('webpack-bugsnag-plugins');

mix.scripts(
    [
        'resources/assets/js/admin/vendor/jquery.js',
        'resources/assets/js/admin/vendor/bootstrap-dropdown.min.js',
        'resources/assets/js/admin/vendor/moment.js',
        'resources/assets/js/admin/vendor/daterangepicker.js',
        'resources/assets/js/admin/vendor/redactor_3/redactor.min.js',
        'resources/assets/js/admin/vendor/redactor_3/limiter.min.js',
        'resources/assets/js/admin/vendor/redactor_3/counter.min.js',
        'resources/assets/js/admin/utilities.js'
    ],
    'public/js/vendor.js'
);

mix.styles(
    [
        'resources/assets/css/redactor_3.css',
        'resources/assets/css/spectrum.css',
        'resources/assets/css/daterangepicker.css',
        'resources/assets/css/select2.css',
        'node_modules/balloon-css/balloon.min.css'
    ],
    'public/css/vendors.css'
);

export default {
  install: (app, s3Bucket, cloudFrontUrl) => {
    // inject a globally available $s3ToCloudfront() method
    app.config.globalProperties.$s3ToCloudfront = (s3Url) => {
      if (!s3Bucket || !cloudFrontUrl || !s3Url) {
        return s3Url;
      }

      // replace the root s3 path with the root cloudfront path
      // using `key` as the path
      return s3Url.replace(
        `https://s3.amazonaws.com/${s3Bucket}/`,
        `${cloudFrontUrl}/`
      )
    }
  }
}

// import Swiper bundle with all modules installed
import Swiper from 'swiper/bundle';

// import styles bundle
import 'swiper/css/bundle';
import 'swiper/css/pagination';
import '../../../css/bundle-related-products-swiper.css';

const relatedProductsSwiper = new Swiper('#related-products-swiper', {
    effect: 'cards', // Use the 'cards' effect for stacking layers
    grabCursor: true, // Show the grabbing cursor for better UX
    cardsEffect: {
        rotate: false, // Disable rotation to keep it clean
        perSlideOffset: 10, // Offset distance between the layers
        perSlideRotate: 0, // Set rotation to 0 to avoid rotating slides
        depth: 300 // Adjust the depth perception of the stacked slides
    },
    loop: false, // Enable looping to keep the effect continuous
    slidesPerView: 1,// Only show one slide at a time,
    pagination: {
        el: '#related-products-pagination'
    },
    navigation: {
        nextEl: '#related-products-swiper-button-next',
        prevEl: '#related-products-swiper-button-prev'
    }
});




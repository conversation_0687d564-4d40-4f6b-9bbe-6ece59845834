import { Alpine, Livewire } from '../../../../vendor/livewire/livewire/dist/livewire.esm';
import intersect from '@alpinejs/intersect';
import { createPopper } from '@popperjs/core';
import googleAutocomplete from '../modules/googleAutocomplete.js';
import topNavigation from '../modules/topNavigation.js';
import annotated from '../modules/annotated.js';
import { v4 as uuidv4 } from 'uuid';
import { findIndex } from 'lodash';
import tracksProductViews from '../modules/tracksProductViews.js';
import categoryProductList from '../modules/categoryProductList.js';
import resize from '@alpinejs/resize';
import JSConfetti from 'js-confetti';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '../../../../tailwind.theme.config.cjs';

Alpine.data('googleAutocomplete', googleAutocomplete);
Alpine.data('topNavigation', topNavigation);
Alpine.data('annotated', annotated);
Alpine.data('tracksProductViews', tracksProductViews);
Alpine.data('categoryProductList', categoryProductList);

Alpine.data('popper', (buttonSelector, tooltipSelector) => ({
    open: false,
    close() {
        this.open = false;
    },
    init() {
        createPopper(
            document.querySelector(buttonSelector),
            document.querySelector(tooltipSelector)
        );
    }
}));

Alpine.data('legacyModal', () => ({
    open: false,

    close() {
        this.open = false;
    },

    init() {
        window.addEventListener('legacy-modal-opened', e => {
            if (e.detail.id === this.$el.id) {
                this.open = true;
            }
        });
    }
}));

Alpine.data('storefrontNotifications', () => ({
    notifications: [],

    add(notification) {
        const id = uuidv4();

        this.notifications.push({
            id: id,
            visible: false,
            level: notification.level ?? 'success', // 'success', 'info', 'warning', 'error
            title: notification.title ?? 'Success!',
            message: notification.message ?? 'The action completed.'
        });

        setTimeout(() => this.show(id), 50);
        setTimeout(() => this.hide(id), 3050);
    },

    show(id) {
        const index = findIndex(this.notifications, { id });
        if (index === -1) return;
        this.notifications[index].visible = true;
    },

    hide(id) {
        const index = findIndex(this.notifications, { id });
        if (index === -1) return;
        this.notifications[index].visible = false;
    }
}));

Alpine.plugin(intersect);
Alpine.plugin(resize);

Livewire.start();

// this resolves a bug with the Mobile Safari UI/WKWebView. Bug was logging many reports in Bugsnag
// https://stackoverflow.com/questions/72720387/cant-find-variable-autofillcallbackhandler
window._AutofillCallbackHandler = window._AutofillCallbackHandler || function() {
};

// hide the zendesk widget on page load
var zendeskInterval = setInterval(function() {
    if (typeof zE !== 'undefined') {
        clearInterval(zendeskInterval);
        zE('webWidget', 'hide');

        zE('webWidget:on', 'close', function() {
            zE('webWidget', 'hide');
        });
    }
}, 100);

window.openZendeskWidget = function() {
    if (typeof zE !== 'undefined') {
        zE('webWidget', 'show');
        zE('webWidget', 'open');
    }
};

// Event dispatched by Livewire and/or Alpine
window.addEventListener('item-added-to-cart', event => {
    if (typeof fbq !== 'undefined') {
        fbq('track', 'AddToCart', {
            content_type: 'product',
            content_ids: [event.detail.item.product.id],
            content_name: event.detail.item.product.title,
            value: event.detail.item.product.price,
            currency: 'USD'
        });
    }

    if (typeof gtag === 'function') {
        gtag('event', 'add_to_cart', {
            currency: 'USD',
            value: (event.detail.item.product.price) / 100,
            items: [
                {
                    item_id: event.detail.item.product.id,
                    item_name: event.detail.item.product.title,
                    index: 0,
                    item_category: event.detail.metadata?.item_category ?? null,
                    item_category2: event.detail.metadata?.item_category2 ?? null,
                    item_list_id: event.detail.metadata?.item_list_id ?? null,
                    item_list_name: event.detail.metadata?.item_list_name ?? null,
                    price: event.detail.item.product.price / 100,
                    quantity: event.detail.item.quantity
                }
            ]
        });
    }
});

const themeConfig = resolveConfig(tailwindConfig);

window.loadConfetti = elementId => {
    const canvas = document.getElementById(elementId);
    new JSConfetti({ canvas }).addConfetti({
        confettiColors: [
            themeConfig.theme.colors['buttercup'][400],
            themeConfig.theme.colors['buttercup'][500],
            themeConfig.theme.colors['buttercup'][600],
            themeConfig.theme.colors['chestnut-rose'][400],
            themeConfig.theme.colors['chestnut-rose'][500],
            themeConfig.theme.colors['chestnut-rose'][600],
            themeConfig.theme.colors['keppel'][400],
            themeConfig.theme.colors['keppel'][500],
            themeConfig.theme.colors['keppel'][600]
        ]
    });

};

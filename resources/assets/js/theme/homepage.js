// import Swiper bundle with all modules installed
import Swiper from 'swiper/bundle';

// import styles bundle
import 'swiper/css/bundle';
import resolveConfig from 'tailwindcss/resolveConfig';
import tailwindConfig from '../../../../tailwind.theme.config.cjs';
import 'swiper/css/pagination';
import '../../css/hompage-category-swiper.css';

const config = resolveConfig(tailwindConfig);
const breakpoints = config.theme.screens;

const smallBreakpoint = parseInt(breakpoints.sm.replace('px', ''));
const largeBreakpoint = parseInt(breakpoints.lg.replace('px', ''));
const xlBreakpoint = parseInt(breakpoints.xl.replace('px', ''));

const categorySwiper = new Swiper('#category-swiper', {
    // configure Swiper to use modules
    slidesPerView: 1 + .15,
    slidesPerGroup: 1,
    spaceBetween: 10,
    speed: 450,

    pagination: {
        el: '#category-swiper-pagination'
    },

    breakpoints: {
        [smallBreakpoint]: {
            slidesPerView: 2.15,
            slidesPerGroup: 2,
            spaceBetween: 20
        },
        [largeBreakpoint]: {
            slidesPerView: 3.15,
            slidesPerGroup: 3,
            spaceBetween: 30
        }
    }
});

const categoryNext = document.querySelector('#category-swiper-button-next');
categoryNext.addEventListener('click', () => {
    categorySwiper.slideNext();
});

const categoryPrev = document.querySelector('#category-swiper-button-prev');
categoryPrev.addEventListener('click', () => {
    categorySwiper.slidePrev();
});


const bundleSwiper = new Swiper('#bundle-swiper', {
    effect: 'cards', // Use the 'cards' effect for stacking layers
    grabCursor: true, // Show the grabbing cursor for better UX
    cardsEffect: {
        rotate: false, // Disable rotation to keep it clean
        perSlideOffset: 10, // Offset distance between the layers
        perSlideRotate: 0, // Set rotation to 0 to avoid rotating slides
        depth: 300 // Adjust the depth perception of the stacked slides
    },
    loop: false, // Enable looping to keep the effect continuous
    slidesPerView: 1,// Only show one slide at a time,
    pagination: {
        el: '#bundle-swiper-pagination'
    },
    navigation: {
        nextEl: '#bundle-swiper-button-next',
        prevEl: '#bundle-swiper-button-prev'
    }
});




import moment from 'moment';

export default (startFieldId, endFieldId) => ({
    setRangeToToday() {
        this.$dispatch('date-selected', { id: startFieldId, date: moment().startOf('day') });
        this.$dispatch('date-selected', { id: endFieldId, date: moment().endOf('day') });
    },

    setRangeToYesterday() {
        this.$dispatch('date-selected', { id: startFieldId, date: moment().subtract(1, 'day').startOf('day') });
        this.$dispatch('date-selected', { id: endFieldId, date: moment().subtract(1, 'day').endOf('day') });
    },

    setRangeToThisWeek(startWeekOnMonday = false) {
        this.$dispatch('date-selected', { id: startFieldId, date: moment().startOf('week').add(startWeekOnMonday ? 1 : 0, 'day') });
        this.$dispatch('date-selected', { id: endFieldId, date: moment().endOf('week').add(startWeekOnMonday ? 1 : 0, 'day') });
    },

    setRangeToThisWeekLastYear(startWeekOnMonday = false) {
        this.$dispatch('date-selected', { id: startFieldId, date: moment().subtract(1, 'year').startOf('week').add(startWeekOnMonday ? 1 : 0, 'day') });
        this.$dispatch('date-selected', { id: endFieldId, date: moment().subtract(1, 'year').endOf('week').add(startWeekOnMonday ? 1 : 0, 'day') });
    },

    setRangeToThisMonth() {
        this.$dispatch('date-selected', { id: startFieldId, date: moment().startOf('month') });
        this.$dispatch('date-selected', { id: endFieldId, date: moment().endOf('month') });
    },

    setRangeToThisMonthLastYear() {
        this.$dispatch('date-selected', { id: startFieldId, date: moment().subtract(1, 'year').startOf('month') });
        this.$dispatch('date-selected', { id: endFieldId, date: moment().subtract(1, 'year').endOf('month') });
    },

    setRangeToLastMonth() {
        this.$dispatch('date-selected', { id: startFieldId, date: moment().subtract(1, 'month').startOf('month') });
        this.$dispatch('date-selected', { id: endFieldId, date: moment().subtract(1, 'month').endOf('month') });
    },

    setRangeToLastMonthLastYear() {
        this.$dispatch('date-selected', { id: startFieldId, date: moment().subtract(1, 'month').subtract(1, 'year').startOf('month') });
        this.$dispatch('date-selected', { id: endFieldId, date: moment().subtract(1, 'month').subtract(1, 'year').endOf('month') });
    },

    setRangeToThisYear() {
        this.$dispatch('date-selected', { id: startFieldId, date: moment().startOf('year') });
        this.$dispatch('date-selected', { id: endFieldId, date: moment().endOf('year') });
    },

    setRangeToLastYear() {
        this.$dispatch('date-selected', { id: startFieldId, date: moment().subtract(1, 'year').startOf('year') });
        this.$dispatch('date-selected', { id: endFieldId, date: moment().subtract(1, 'year').endOf('year') });
    },

    setRangeToNextSevenDays() {
        this.$dispatch('date-selected', { id: startFieldId, date: moment() });
        this.$dispatch('date-selected', { id: endFieldId, date: moment().add(7, 'days') });
    },

    setRangeToLastSevenDays() {
        this.$dispatch('date-selected', { id: startFieldId, date: moment().subtract(7, 'day').startOf('day') });
        this.$dispatch('date-selected', { id: endFieldId, date: moment().subtract(1, 'day').endOf('day') });
    },

    setRangeToNextFourteenDays() {
        this.$dispatch('date-selected', { id: startFieldId, date: moment() });
        this.$dispatch('date-selected', { id: endFieldId, date: moment().add(14, 'days') });
    },

    setRangeToNextThirtyDays() {
        this.$dispatch('date-selected', { id: startFieldId, date: moment() });
        this.$dispatch('date-selected', { id: endFieldId, date: moment().add(30, 'days') });
    },

    setRangeToLastThirtyDays() {
        this.$dispatch('date-selected', { id: startFieldId, date: moment().subtract(30, 'day').startOf('day') });
        this.$dispatch('date-selected', { id: endFieldId, date: moment().subtract(1, 'day').endOf('day') });
    },

    clearDateRange() {
        this.$dispatch('date-selected', { id: startFieldId, date: null });
        this.$dispatch('date-selected', { id: endFieldId, date: null });
    },

    clearDate(fieldId) {
        this.$dispatch('date-selected', { id: fieldId, date: null });
    }
})

import Pikaday from 'pikaday';
import moment from 'moment';

export default dateFormat => ({
    pickerInput: null,

    inputValue: null,

    init() {
        this.pickerInput = new Pikaday({
            format: dateFormat,
            field: this.$refs.dateInput,
            onSelect: date => this.updateInputValue(date)
        });

        if (this.$refs.dateInput.value) {
            this.updateInputValue(moment(this.$refs.dateInput.value, dateFormat).toDate());
        }
    },

    dateSelected(event) {
        if (event.detail.id !== this.$refs.dateInput.id) return;

        const selectedDate = event.detail.date;

        if (selectedDate === null) {
            this.updateInputValue(null);
            return this.pickerInput.clear();
        }

        this.updateInputValue(selectedDate);
        this.pickerInput.setMoment(moment(selectedDate));
    },

    updateInputValue(date) {
        this.inputValue = date;
        this.$refs.dateInput.value = date ? moment(date).format(dateFormat) : '';
    }
})

export default () => ({
    card: null,

    init() {
        var elements = stripe.elements();

        this.card = elements.create('card', {
            style: {
                base: {
                    fontSmoothing: 'antialiased',
                    fontSize: '18px',
                    '::placeholder': {
                        color: '#888'
                    }
                },
                invalid: {
                    color: '#fa755a',
                    iconColor: '#fa755a'
                }
            }
        });

        this.card.mount('#card-element');

        this.card.addEventListener('change', function(event) {
            var displayError = document.getElementById('card-errors');
            if (event.error) {
                displayError.textContent = event.error.message;
            } else {
                displayError.textContent = '';
            }
        });
    },

    submit(event) {
        event.preventDefault();

        if (document.getElementById('cardholder_name').value == '') {
            document.getElementById('cardholder_name').focus();
            return alert('Cardholder Name is required');
        }

        const submitButton = document.getElementById('payment-form-button');
        const submitButtonText = submitButton.innerHTML;
        submitButton.disabled = true;
        submitButton.innerHTML = 'Saving card...';

        stripe.createToken(this.card, {
            name: document.getElementById('cardholder_name').value
        })
            .then((result) => {
                if (result.error) {
                    // Inform the user if there was an error
                    const errorElement = document.getElementById('card-errors');
                    submitButton.disabled = false;
                    submitButton.innerHTML = submitButtonText;
                    errorElement.textContent = result.error.message;
                    return;
                }
                
                // Insert the token ID into the form so it gets submitted to the server
                var form = document.getElementById('payment-form');
                var hiddenInput = document.createElement('input');
                hiddenInput.setAttribute('type', 'hidden');
                hiddenInput.setAttribute('name', 'token');
                hiddenInput.setAttribute('value', result.token.id);
                form.appendChild(hiddenInput);

                // Submit the form
                form.submit();
            });
    }
});



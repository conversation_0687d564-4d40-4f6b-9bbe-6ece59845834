export const perUnitWeightLabel = weightUOM => weightUOM === 'pounds' ? 'per lb' : 'per kg'

export const weightLabel = weightUOM => weightUOM === 'pounds' ? 'lbs' : 'kgs'

export const isPricedByWeight = product => product.unit_of_issue === 'weight'

export const isTaxable = product => !!product.taxable

export const originalProductPrice = product => product.unit_price

export const productPrice = product => {
    if (product.group_price) return product.group_price;

    return !!product.sale ? product.sale_unit_price : originalProductPrice(product)
}

export const itemSubtotal = item => isPricedByWeight(item.product)
    ? Math.round(item.weight * productPrice(item.product))
    : Math.round(item.quantity * productPrice(item.product))

export const itemTaxTotal = (item, taxRate) => isTaxable(item.product)
    ? Math.round(taxRate * itemSubtotal(item))
    : 0

export const itemsSubtotal = items => items.reduce((previousValue, item) => previousValue + itemSubtotal(item), 0)

export const locationFeesTotal = fees => fees.reduce((previousValue, fee) => previousValue + fee.amount, 0)

export const locationFeesTaxTotal = (fees, taxRate) =>  {
    return Math.round(
        fees.filter(fee => Boolean(fee.taxable))
            .reduce((previousValue, fee) => previousValue + Math.round(fee.amount * taxRate), 0)
    )
}

export const cartSubtotal = cart => cart.items.reduce((previousValue, item) => previousValue + itemSubtotal(item), 0)
export const cartTaxableSubtotal = cart => cart.items
    .filter(item => isTaxable(item.product))
    .reduce((previousValue, item) => previousValue + itemSubtotal(item), 0)

export const itemWeightTotal = items => items.reduce((previousValue, item) => previousValue + item.weight, 0)

export const cartTaxTotal = (cartSubtotal, taxRate) => Math.round(cartSubtotal * taxRate)

export const cartDiscountTotal = (cart) => cart.discount_amount

export const cartTotal = (cart, taxRate) => {
    const subtotal = cartSubtotal(cart);
    const taxTotal = cartTaxTotal(cartTaxableSubtotal(cart), taxRate)
    return subtotal + taxTotal - cartDiscountTotal(cart)
}

export const deliveryFeeTax = (deliveryMethod, items, taxRate) => Math.round(deliveryFee(deliveryMethod, items) * taxRate)

export const deliveryFee = (deliveryMethod, items) => {
    if ( ! deliveryMethod || deliveryMethod.delivery_rate === 0) return 0;

    let rate = deliveryMethod.delivery_rate

    if (parseInt(deliveryMethod.settings.delivery_fee_type) === 1) { // variable by weight
        rate = rate * itemWeightTotal(items)
    }

    if ( ! Boolean(deliveryMethod.apply_limit) || itemsSubtotal(items) < deliveryMethod.delivery_total_threshold) {
        return rate;
    }

    return deliveryMethod.delivery_fee_cap
}

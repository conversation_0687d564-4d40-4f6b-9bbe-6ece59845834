import { Loader } from '@googlemaps/js-api-loader';

export default (googleApi<PERSON>ey, inputField) => ({
    places: null,

    async init() {
        this.$watch('open', value => {
            if (value) this.setupAutocompleteInput();
        });
    },

    async setupAutocompleteInput() {
        if (this.places !== null) {
            return this.createInput();
        }

        this.places = await new Loader({
            apiKey: googleApiKey,
            version: 'weekly'
        }).importLibrary('places');

        this.createInput();
    },

    createInput() {
        const autocompleteInput = new this.places.Autocomplete(inputField, { fields: ['address_components', 'geometry'] });

        autocompleteInput.addListener('place_changed', () => {

            let street = '';
            let city = '';
            let state = '';
            let postal_code = '';

            autocompleteInput.getPlace()
                .address_components
                .forEach(component => {
                    if (component.types.includes('street_number')) {
                        street = component.long_name;
                    }
                    if (component.types.includes('route')) {
                        street += ' ' + component.long_name;
                    }
                    if (component.types.includes('locality')) {
                        city = component.long_name;
                    }
                    if (component.types.includes('administrative_area_level_1')) {
                        state = component.short_name;
                    }
                    if (component.types.includes('postal_code')) {
                        postal_code = component.long_name;
                    }
                });

            this.$dispatch('google-place-changed', {
                street,
                city,
                state,
                postal_code
            });
        });
    }
});

import { Alpine, Livewire } from '../../../../vendor/livewire/livewire/dist/livewire.esm';
import { createApp, defineAsyncComponent } from 'vue';
import mitt from 'mitt';
import Sortable from 'sortablejs';
import axios from 'axios';
import bugsnagVuePlugin from '../bugsnag-vue';
// used for product select
import { createPopper } from '@popperjs/core';
import googleAutocomplete from '../modules/googleAutocomplete.js';
import datePicker from '../modules/datePicker.js';
import dateRange from '../modules/dateRange.js';
import colorPicker from '../modules/colorPicker.js';
import 'leaflet/dist/leaflet.css';
import { LIcon, LMap, LMarker, LTileLayer, LTooltip } from '@vue-leaflet/vue-leaflet';
import { findIndex } from 'lodash';
import { v4 as uuidv4 } from 'uuid';
import stripeForm from '../modules/stripeForm.js';
import 'livewire-sortable';
import s3ToCloudfront from '../s3ToCloudfront.js';

Alpine.data('googleAutocomplete', googleAutocomplete);
Alpine.data('datePicker', datePicker);
Alpine.data('dateRange', dateRange);
Alpine.data('colorPicker', colorPicker);
Alpine.data('stripeForm', stripeForm);

Alpine.data('adminNotifications', () => ({

    notifications: [],

    add(notification) {
        const id = uuidv4();

        this.notifications.push({
            id: id,
            visible: false,
            level: notification.level ?? 'success', // 'success', 'info', 'warning', 'error
            title: notification.title ?? 'Success!',
            message: notification.message ?? 'The action completed.'
        });

        setTimeout(() => this.show(id), 50);
        setTimeout(() => this.hide(id), 3050);
    },

    show(id) {
        const index = findIndex(this.notifications, { id });
        if (index === -1) return;
        this.notifications[index].visible = true;
    },

    hide(id) {
        const index = findIndex(this.notifications, { id });
        if (index === -1) return;
        this.notifications[index].visible = false;
    }
}));


window.axios = axios;
window.axios.defaults.headers.common = {
    'X-Requested-With': 'XMLHttpRequest'
};

window.createPopper = createPopper;

const eventHub = mitt();
window.eventHub = eventHub;

const Tags = defineAsyncComponent(() =>
    import('./components/Tags.vue')
);
const Bundle = defineAsyncComponent(() =>
    import('./components/Bundle.vue')
);
const Detours = defineAsyncComponent(() =>
    import('./components/Detours/Detours.vue')
);
const Coupon = defineAsyncComponent(() =>
    import('./components/Coupon/Coupon.vue')
);
const PageEditor = defineAsyncComponent(() =>
    import('./components/PageEditor/PageEditor.vue')
);
const TinyEditor = defineAsyncComponent(() =>
    import('./components/TinyMCE.vue')
);
const CoverPhoto = defineAsyncComponent(() =>
    import('./components/MediaManager/CoverPhoto.vue')
);
const PickupFees = defineAsyncComponent(() =>
    import('./components/PickupFees.vue')
);
const TextEditor = defineAsyncComponent(() =>
    import('./components/TextEditor.vue')
);
const QuillEditor = defineAsyncComponent(() =>
    import('./components/QuillEditor.vue')
);
const MenuManager = defineAsyncComponent(() =>
    import('./components/MenuManager/MenuItemList.vue')
);
const Ingredients = defineAsyncComponent(() =>
    import('./components/Ingredients.vue')
);
const Collections = defineAsyncComponent(() =>
    import('./components/Collections.vue')
);
const TeamManager = defineAsyncComponent(() =>
    import('./components/TeamManager/TeamManager.vue')
);
const ThemeEditor = defineAsyncComponent(() =>
    import('./components/ThemeEditor/ThemeEditor.vue')
);
const MediaManager = defineAsyncComponent(() =>
    import('./components/MediaManager/MediaManager.vue')
);
const MediaBrowser = defineAsyncComponent(() =>
    import('./components/MediaBrowser/MediaBrowser.vue')
);
const OrderManager = defineAsyncComponent(() =>
    import('./components/OrderManager/OrderManager.vue')
);
const ProductPrice = defineAsyncComponent(() =>
    import('./components/ProductPrice/ProductPrice.vue')
);
const ProductSelect = defineAsyncComponent(() =>
    import('./components/ProductSelect.vue')
);
const ProductSearchSelect = defineAsyncComponent(() =>
    import('./components/ProductSearchSelect.vue')
);
const ProductVariants = defineAsyncComponent(() =>
    import('./components/ProductVariants/ProductVariants.vue')
);
const PriceGroups = defineAsyncComponent(() =>
    import('./components/ProductPrice/PriceGroups.vue')
);
const ProposalsMap = defineAsyncComponent(() =>
    import('./components/ProposalsMap/ProposalsMap.vue')
);

const CustomerCredit = defineAsyncComponent(() =>
    import('./components/CustomerCredit/CustomerCredit.vue')
);
const CreateDeliveryZone = defineAsyncComponent(() =>
    import('./components/Logistics/Delivery/CreateDeliveryZone.vue')
);
const EditDeliveryZone = defineAsyncComponent(() =>
    import('./components/Logistics/Delivery/EditDeliveryZone.vue')
);
const OrderNotifications = defineAsyncComponent(() =>
    import('./components/OrderNotifications/OrderNotifications.vue')
);
const CollectionsManager = defineAsyncComponent(() =>
    import('./components/CollectionsManager/CollectionsManager.vue')
);
const CanceledOrderManager = defineAsyncComponent(() =>
    import('./components/OrderManager/CanceledOrderManager.vue')
);
const EmailTemplateEditor = defineAsyncComponent(() =>
    import('./components/EmailTemplateEditor/EmailTemplateEditor.vue')
);
const SystemNotification = defineAsyncComponent(() =>
    import('./components/SystemNotification.vue')
);
const SelectList = defineAsyncComponent(() =>
    import('./components/SelectList.vue')
);
const InstantSlug = defineAsyncComponent(() =>
    import('./components/InstantSlug.vue')
);
const SubscriptionChoices = defineAsyncComponent(() =>
    import('./components/SubscriptionChoices.vue')
);
const DashboardWidgets = defineAsyncComponent(() =>
    import('./components/Dashboard/DashboardWidgets.vue')
);
const BulkSelector = defineAsyncComponent(() =>
    import('./components/BulkSelector.vue')
);

const app = createApp({
    created() {
        window.eventHub.on(
            'showProgressBar',
            () => $('#progressBar').addClass('show-progress-bar')
        );

        window.eventHub.on(
            'hideProgressBar',
            () => $('#progressBar').removeClass('show-progress-bar')
        );

        window.eventHub.on('showModal', this.showModal);
    },

    mounted() {
        document.addEventListener('keydown', (e) => {
            if (e.keyCode == 83 && e.altKey) {
                this.togglePanel('filterPanel');
            }
        });
    },

    components: {
        Tags,
        Bundle,
        Detours,
        Coupon,
        PageEditor,
        TinyEditor,
        CoverPhoto,
        PickupFees,
        TextEditor,
        QuillEditor,
        MenuManager,
        Ingredients,
        Collections,
        TeamManager,
        ThemeEditor,
        MediaManager,
        MediaBrowser,
        OrderManager,
        ProductPrice,
        ProductSelect,
        ProductSearchSelect,
        ProductVariants,
        PriceGroups,
        ProposalsMap,
        CustomerCredit,
        CreateDeliveryZone,
        EditDeliveryZone,
        OrderNotifications,
        CollectionsManager,
        CanceledOrderManager,
        EmailTemplateEditor,
        SystemNotification,
        SelectList,
        InstantSlug,
        SubscriptionChoices,
        DashboardWidgets,
        BulkSelector,
        LMap, LTileLayer, LTooltip, LMarker, LIcon
    },

    methods: {
        toggleChecked: function(checkedItem, e) {
            let checked = e.target.checked;
            let index = this.checked.indexOf(checkedItem);
            if (checked) {
                this.checked.push(checkedItem);
            } else {
                this.checked.splice(index, 1);
            }
        },

        toggleBulkCheckSelector: function(selector, type) {
            $(selector).prop('checked', type !== null);
            this.pageChecked = type !== null;
            this.allChecked = type === 'all';
            $('#selectAllType').val(type === null ? '' : type);
        },

        checkAll: function(selector, e) {
            if (e.target.checked) {
                $(selector).prop('checked', true);
                this.pageChecked = true;
                this.allChecked = false;
            } else {
                $(selector).prop('checked', false);
                this.pageChecked = false;
                this.allChecked = false;

            }
        },

        checkAllToggle: function(selector) {
            this.pageChecked = !this.pageChecked;
            this.allChecked = false;

            if (this.pageChecked) {
                $(selector).prop('checked', true);
            } else {
                $(selector).prop('checked', false);
            }
        },

        displayInlineErrors: function(error) {
            let self = this;

            self.validate = {};

            if (error.response.status === 422 && typeof error.response.data == 'object') {
                let self = this;

                $.each(error.response.data, function(index, value) {
                    self.validate[index] = value[0];
                });
            }
        },
        findLabel: function(name) {
            let labels = document.getElementsByTagName('label');
            for (var i = 0; i < labels.length; i++) {
                if (labels[i].htmlFor == name) return labels[i];
            }
        },
        changePriceUnit: function(value, weightUOM) {
            if (value === 'weight') {
                this.priceUnitMessage = 'Price/' + weightUOM + '.';
            } else {
                this.priceUnitMessage = 'Price';
            }
        },
        toggleNavigation: function(id, className) {
            let el = document.getElementById(id);
            el.classList.toggle(className ? className : 'navigation-items--show');
            let isToggled = el.classList.contains(
                className ? className : 'navigation-items--show'
            );
            if (isToggled) {
                document.cookie =
                    'navigation_toggled=true; expires=Sun, 1 Jan 2222 00:00:00 UTC; path=/';
            } else {
                document.cookie =
                    'navigation_toggled=; expires=Sun, 1 Jan 2222 00:00:00 UTC; path=/';
            }
        },

        toggleClass: function(id, className) {
            $('#' + id).toggleClass(className);
        },

        removeUrlParameter: function(parameter) {
            var url = window.location.href;
            var urlParts = url.split('?');
            if (urlParts.length >= 2) {
// Get first part, and remove from array
                var urlBase = urlParts.shift();

// Join it back up
                var queryString = urlParts.join('?');

                var prefix = encodeURIComponent(parameter) + '=';
                var parts = queryString.split(/[&;]/g);

// Reverse iteration as may be destructive
                for (var i = parts.length; i-- > 0;) {
// Idiom for string.startsWith
                    if (parts[i].lastIndexOf(prefix, 0) !== -1) {
                        parts.splice(i, 1);
                    }
                }

                url = urlBase + '?' + parts.join('&');
            }

            return (location.href = url);
        }
    },

    data() {
        return {
            selected: [],
            checked: [],
            pageChecked: false,
            allChecked: false,
            validate: {},
            priceUnitMessage: 'Price'
        };
    },

    computed: {
        disableButtons() {
            return !(this.pageChecked || this.checked.length);
        },

        selectPageButtonsEnabled() {
            return this.pageChecked || this.checked.length > 0;
        },

        selectAllButtonsEnabled() {
            return this.allChecked;
        }
    }
});

app.use(s3ToCloudfront, import.meta.env.VITE_S3_BUCKET, import.meta.env.VITE_CLOUDFRONT_DISTRIBUTION_URL);

app.directive('sortable', {
    mounted: function(el, binding) {
        new Sortable(el, binding.value || {});
    }
});

app.directive('click-outside', {
    mounted(el, binding, vnode) {
        el.clickOutsideEvent = function(event) {
            if (!(el === event.target || el.contains(event.target))) {
                binding.value(event, el);
            }
        };
        document.body.addEventListener('click', el.clickOutsideEvent);
    },
    unmounted(el) {
        document.body.removeEventListener('click', el.clickOutsideEvent);
    }
});

app.config.globalProperties.$filters = {
    cents(value) {
        value = value / 100;
        return value.toFixed(2);
    },

    currency(value) {
        return '$' + value;
    },

    weight(value) {
        value = value / 1;
        return value.toFixed(2) + weightUOM + '.';
    }
};

app.mixin({
    methods: {
        exportFiltered: function(formId) {
            let form = document.getElementById(formId);
            let hiddenInput = document.createElement('input');
            hiddenInput.setAttribute('type', 'hidden');
            hiddenInput.setAttribute('name', 'export');
            hiddenInput.setAttribute('value', true);
            form.appendChild(hiddenInput);
            form.submit();
            form.removeChild(hiddenInput);
        },

        clearField: function(id) {
            document.getElementById(id).value = '';
        },

        togglePanel: function(panelId) {
            let panel = $('#' + panelId);
            if (panel.hasClass('filterPanel-enter')) {
                this.hidePanel(panelId);
            } else {
                this.showPanel(panelId);
            }
        },

        showPanel: function(panelId) {
            let that = this;
            let panel = $('#' + panelId);
            eventHub.emit(panelId + ':open');
            panel.addClass('filterPanel-enter');
            $(document.body).addClass('panel-open');
            $('.panel-open').on(
                'click',
                function(e) {
                    if (e.target.nodeName == 'BODY') {
                        that.hidePanel(panelId);
                    }
                }.bind(this)
            );
            document.addEventListener('keydown', (e) => {
                if (e.keyCode == 27) {
                    this.hidePanel(panelId);
                }
            });
            setTimeout(function() {
                $('.autofocus')
                    .first()
                    .focus();
            }, 200);
        },

        hidePanel: function(panelId, e) {
            let panel = $('#' + panelId);
            eventHub.emit(panelId + ':closed');
            if (e == undefined || e.target.classList.contains('filterPanel-mask')) {
                panel.removeClass('filterPanel-enter');
                $(document.body).removeClass('panel-open');
            }
        },

        toggleDropdown: function(e) {
            let element = $(e.target);
            element.closest('.dropdown').toggleClass('open');
        },

        showModal: function(modalId) {
            eventHub.emit(modalId + ':opened');
            $(document.body).addClass('modal-open');
            let modal = $('#' + modalId);
            modal.addClass('gc-modal-show').outerWidth();
            modal.addClass('gc-modal-enter');
            let firstInput = modal.find('.form-control')[0];
            if (firstInput) {
                firstInput.focus();
            }
        },

        hideModal: function(modalId) {
            eventHub.emit(modalId + ':closed');
            $(document.body).removeClass('modal-open');
            let modal = $('#' + modalId);
            modal.removeClass('gc-modal-enter');
            setTimeout(function() {
                modal.removeClass('gc-modal-show');
            }, 300);
        },

        event: function(eventName, params) {
            eventHub.emit(eventName, params);
        },

        submitForm: function(formId, e) {
            let form = document.getElementById(formId);

            if (window.submittingForm !== undefined) {
                window.submittingForm = true;
            }

            if (e !== undefined) {
                var button = e.target;
                button.disabled = true;
            }

            if (form.nodeName === 'FORM') {
                eventHub.emit('showProgressBar');
                form.submit();
            }

            return false;
        },

        async submitProductImportForm(formId, e) {
            let form = document.getElementById(formId);

            if (window.submittingForm !== undefined) {
                window.submittingForm = true;
            }

            if (e !== undefined) {
                var button = e.target;
                button.disabled = true;
            }

            if (form.nodeName === 'FORM') {
                eventHub.emit('showProgressBar');

                let request = () => new Promise(resolve => form.submit());
                request();
                window.submittingForm = false;
                this.hideModal('importProductModal');
                eventHub.emit('hideProgressBar');
            }

            return false;
        },

        submitFormAsync: function(formId, e) {
            let form = document.getElementById(formId);
            if (!form) return false;

            if (e != undefined) {
                var button = e.target;
                button.disabled = true;
            }

            eventHub.emit('showProgressBar');

            let formData = new FormData(form);
            axios[form.method](form.action, formData)
                .then(function(response) {

                    if (response.data.redirect) {
                        return (location.href = response.data.redirect);
                    }

                    if (response.data.message) {
                        eventHub.emit('notify', {
                            level: 'info',
                            message: response.data.message
                        });
                        eventHub.emit('hideProgressBar');
                        button.disabled = false;
                        return false;
                    }

                    location.reload();

                    if (window.submittingForm !== undefined) {
                        window.submittingForm = false;
                    }
                })
                .catch(function(error) {
                    if (button != undefined) {
                        button.disabled = false;
                    }
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                });
            return false;
        }
    }
});

app.use(bugsnagVuePlugin)
    .mount('#app');

window.onload = () => {
    let wrapper = document.getElementById('wrapper');
    if (wrapper) {
        wrapper.scrollTop = localStorage.getItem('last_scroll');
    }
};

window.onunload = () => {
    let wrapper = document.getElementById('wrapper');
    if (wrapper) {
        localStorage.setItem('last_scroll', wrapper.scrollTop);
    }
};

window.toggleAttributeOn = function(elementSelector, attribute) {
    let element = document.querySelector(elementSelector);
    if (element) {
        element.toggleAttribute(attribute);
    }
};

document.addEventListener('DOMContentLoaded', function(event) {
    $('.select2').select2({ closeOnSelect: false });

    $('.select2--products').select2({
        theme: 'graze',
        closeOnSelect: true,
        ajax: {
            url: '/api/products',
            dataType: 'json',
            data: params => ({ products: params.term }),
            processResults: data => ({
                results: data.map(item => ({ id: item.id, text: item.title }))
            })
        }
    });

    $('.select2--products-multiple').select2({
        closeOnSelect: false,
        ajax: {
            url: '/api/products',
            data: function(params) {
                var query = {
                    products: params.term
                };

                return query;
            },
            dataType: 'json',
            processResults: function(data) {
                return {
                    results: data.map(item => {
                        return { id: item.id, text: item.title };
                    })
                };
            }
        }
    });
});

Livewire.start();

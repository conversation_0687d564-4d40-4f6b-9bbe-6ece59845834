const showModal = modalId => {
    eventHub.emit(modalId + ":opened");
    $(document.body).addClass("modal-open");
    let modal = $("#" + modalId);
    modal.addClass("gc-modal-show").outerWidth();
    modal.addClass("gc-modal-enter");
    let firstInput = modal.find(".form-control")[0];
    if (firstInput) {
        firstInput.focus();
    }
}

const hideModal = modalId => {
    eventHub.emit(modalId + ":closed");
    $(document.body).removeClass("modal-open");
    let modal = $("#" + modalId);
    modal.removeClass("gc-modal-enter");
    setTimeout(function() {
        modal.removeClass("gc-modal-show");
    }, 300);
}

export { showModal, hideModal }
<template>
	<div class="settingsPanel">
		<div class="settingsPanel__body">
			<ul class="emailTemplateEditor__navigation tabsList">
	            <li :class="{'active': activeView == 'BlocksToolbar'}"
	            @click="$emit('changeView', 'BlocksToolbar')">
	                <a href="#">Content</a>
	            </li>
	            <li :class="{'active': activeView == 'TemplateSettings'}" @click="$emit('changeView', 'TemplateSettings')">
	                <a href="#">Design</a>
	            </li>
	        </ul>
			<table class="table table-bordered  table-striped table-settings">
				<tbody>
					<tr>
			         	<td>
			         	    <h2>Email subject</h2>
			         	    <p>The subject of the email that will be seen by all recipients.</p>
			         	</td>
			         	<td>
			         	    <input 
			         	    	type="text" 
			         	    	v-model="template.subject" 
			         	    	class="form-control"
			         	    >
			         	</td>
			         </tr>
			         <tr>
			         	<td>
			         	    <h2>Template name</h2>
			         	    <p>Your name for this template.</p>
			         	</td>
			         	<td>
			         	    <input type="text" v-model="template.title" class="form-control">
			         	</td>
			         </tr>
			         <tr>
			         	<td>
			         	    <h2>Background color</h2>
			         	</td>
			         	<td>
			         	    <input type="text" v-model="template.settings.backgroundColor" class="form-control colorpicker--modal" id="backgroundColor">
			         	</td>
			         </tr>
			         <tr>
			         	<td>
			         	    <h2>Font</h2>
			         	</td>
			         	<td>
			         		<select v-model="template.settings.fontFamily" class="form-control">
			         			<option value="Arial">Arial</option>
			         			<option value="Arial Black">Arial Black</option>
			         			<option value="Tahoma">Tahoma</option>
			         			<option value="Trebuchet MS">Trebuchet MS</option>
			         			<option value="Verdana">Verdana</option>
			         			<option vlaue="Courier">Courier</option>
			         			<option vlaue="Georgia">Georgia</option>
			         			<option vlaue="Times">Times</option>
			         		</select>
			         	</td>
			         </tr>
			         <tr>
			         	<td>
			         	    <h2>From Name</h2>
			         	    <p>Personalize who this email is coming from. Leave blank to default to your farm name.</p>
			         	</td>
			         	<td>
			         	    <input 
			         	    	type="text" 
			         	    	v-model="template.from_name" 
			         	    	class="form-control"
			         	    >
			         	</td>
			         </tr>
			         <tr>
			         	<td>
			         	    <h2>Track clicks with Google Analytics</h2>
			         	    <p>Track this email as a campaign.</p>
			         	</td>
			         	<td>
			         	    <div class="checkbox">
						    	<label>
						    		<input 
						    			type="checkbox" 
						    			v-model="template.settings.track_links" 
						    			> Enabled
						        </label>
						    </div>
			         	</td>
			         </tr>
			         <tr v-show="template.settings.track_links">
			         	<td>
			         	    <h2>Campaign name</h2>
			         	    <p>The campaign name used in Google Analytics.</p>
			         	</td>
			         	<td>
			         	    <input type="text" v-model="template.settings.campaign_name" class="form-control">
			         	</td>
			         </tr>
			         <tr v-show="template.settings.track_links">
			         	<td>
			         	    <h2>Campaign content</h2>
			         	    <p>Differentiate emails under the same campaign.</p>
			         	</td>
			         	<td>
			         	    <input type="text" v-model="template.settings.campaign_content" class="form-control">
			         	</td>
			         </tr>
				</tbody>
			</table>
		</div>
		<div class="settingsPanel__footer flex align-items-m">
	        <button 
	            type="button" 
	            class="btn btn-action flex-item push-right" 
	            @click="$emit('saveTemplate', template)"
	        >Save</button>
    	</div>
	</div>
</template>

<script>
	export default {
		props: ['template','active-view', 'views'],

        emits: ['changeView', 'saveTemplate'],

		mounted: function() {

			if(this.template.settings.track_links == undefined)
			{
				this.template.settings = Object.assign({}, this.template.settings, { track_links: false });
			}

			let self = this;
            this.$nextTick(function() {
                $("#backgroundColor").spectrum({
                    preferredFormat: "hex",
                    showInput: true,
                    allowEmpty: true,
                    change: function(color) {
                        self.template.settings.backgroundColor = color ? color.toHexString() : 'transparent';
                    }
                });
            });
		}
	}
</script>
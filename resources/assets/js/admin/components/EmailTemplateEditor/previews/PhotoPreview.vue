<template>
	<div :style="{
			paddingTop: block.settings.paddingTop+'px', 
			paddingBottom: block.settings.paddingBottom+'px',
			paddingLeft: block.settings.paddingSide+'px',
			paddingRight: block.settings.paddingSide+'px',
			textAlign: block.settings.alignment,
		}"
	>
		<img 
			:src="block.settings.url" 
			:alt="block.settings.altText"
		>
		<div v-show="!block.settings.url">Click here to add a photo</div>
	</div>		
</template>

<script>
	export default {
		props: ['block']
	}
</script>
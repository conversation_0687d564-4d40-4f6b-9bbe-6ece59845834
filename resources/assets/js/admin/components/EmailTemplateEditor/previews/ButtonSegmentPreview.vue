<template>
<div>
	<div style="text-align: center;" :style="{
			paddingTop: block.settings.paddingTop+'px', 
			paddingBottom: block.settings.paddingBottom+'px',
		}">
		<a :href="block.settings.url" style="box-sizing: border-box; line-height: 100%; mso-line-height-rule: exactly; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; text-decoration: none; border-radius: 3px; border: solid 2px transparent; display: inline-block; min-width: 200px; max-width: 100%; padding: 15px;" :style="{
			backgroundColor: block.settings.backgroundColor,
			color: block.settings.textColor,
			borderColor: block.settings.borderColor
		}">{{ block.settings.message }}</a>
	</div>
</div>	
</template>

<script>
	export default {
		props: ['block'],
	}
</script>
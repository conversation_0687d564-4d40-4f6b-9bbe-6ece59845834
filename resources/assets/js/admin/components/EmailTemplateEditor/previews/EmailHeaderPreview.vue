<template>
	<div style="line-height: 100%; mso-line-height-rule: exactly; -ms-text-size-adjust: 100%; -webkit-text-size-adjust: 100%; padding-left: 30px; padding-right: 30px;" :style="{
			paddingTop: block.settings.paddingTop+'px', 
			paddingBottom: block.settings.paddingBottom+'px',
			textAlign: block.settings.alignment,
			backgroundColor: block.settings.backgroundColor,
			color: block.settings.textColor
		}"
	>
		<img v-show="block.settings.logoUrl" :src="block.settings.logoUrl" :style="{height: block.settings.logoHeight+'px'}" :height="block.settings.logoHeight" :alt="block.settings.logoAltText" style="border: none; max-width: 100%;">
		<h1 v-show="block.settings.message" style="margin: 0; padding: 15px 0; font-weight: normal; line-height: 1.25;" :style="{fontSize: block.settings.fontSize}">{{ block.settings.message }}</h1>
	</div>		
</template>

<script>
	export default {
		props: ['block']
	}
</script>
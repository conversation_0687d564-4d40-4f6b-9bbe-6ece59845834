<template>
<div class="settingsPanel">
    <div class="settingsPanel__body">
        <div class="settingsPanel__heading">
            HTML
        </div>
        <textarea v-model="block.content" class="form-control" rows="20" style="height: calc(100% - 50px);"></textarea>
    </div>

    <div class="settingsPanel__footer flex align-items-m">
        <button 
            type="button" 
            class="btn btn-action flex-item push-right" 
            @click="save(block)"
        >Save &amp; Close</button>
    </div>
</div>    
</template>

<script>
	export default {
		props: ['block'],

        emits: ['saveBlock'],

        inheritAttrs: false,

        methods: {
            save: function(block) {
                this.$emit('saveBlock', block);
            }
        }
	}
</script>
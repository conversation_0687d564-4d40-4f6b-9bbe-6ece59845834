<template>
    <div class="settingsPanel">
        <div class="settingsPanel__body">

            <div class="settingsPanel__heading">
                Photo
            </div>

            <ul class="tabsList">
                <li :class="{'active': activeTab == 'content'}" @click="activeTab = 'content'">
                    <a href="#">Photo</a>
                </li>
                <li :class="{'active': activeTab == 'appearance'}" @click="activeTab = 'appearance'">
                    <a href="#">Settings</a>
                </li>
            </ul>

            <div v-show="activeTab == 'content'" class="linkListWidget__linkTab">
                <cover-photo 
                    :src="block.settings.url" 
                    message="Add Photo" 
                    @input="updatePhoto"
                ></cover-photo>
            </div>
    
            <div v-show="activeTab == 'appearance'" class="linkListWidget__linkTab">
                <table class="table table-bordered table-striped table-settings">
                    <tbody>
                        <tr>
                            <td>
                                <h2>Alt Text</h2>
                                <p>The text to display if the image does not load.</p>
                            </td>
                            <td>
                                <input type="text" class="form-control" v-model="block.settings.altText">
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <h2>Link</h2>
                                <p>The web address this image links to when clicked</p>
                            </td>
                            <td>
                                <input type="text" v-model="block.settings.link_url" class="form-control">
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <h2>Alignment</h2>
                            </td>
                            <td>
                                <select v-model="block.settings.alignment" class="form-control">
                                    <option value="left">Left</option>
                                    <option value="center">Center</option>
                                    <option value="right">Right</option>
                                </select>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <h2>Padding</h2>
                                <p>The top and bottom padding of the content block.</p>
                            </td>
                            <td>
                                <div class="form-group">
                                    <label>Top: <input type="text" class="hiddenInput" v-model="block.settings.paddingTop"> px</label>
                                        <input 
                                            type="range" 
                                            min="0" 
                                            max="160" 
                                            v-model="block.settings.paddingTop" 
                                        >
                                </div>
                                <div class="form-group">
                                    <label>Bottom: <input type="text" class="hiddenInput" v-model="block.settings.paddingBottom"> px</label>
                                        <input 
                                            type="range" 
                                            min="0" 
                                            max="160" 
                                            v-model="block.settings.paddingBottom" 
                                        >
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <h2>Edge-to-Edge</h2>
                                <p>Allow wide images to span the full width of the email body.</p> 
                            </td>
                            <td>
                                <label class="radio-inline">
                                  <input type="radio" name="edgeToEdge" id="edgeToEdgeNo" value="30" v-model="block.settings.paddingSide"> No
                                </label>
                                <label class="radio-inline">
                                  <input type="radio" name="edgeToEdge" id="edgeToEdgeYes" value="0" v-model="block.settings.paddingSide"> Yes
                                </label>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <div class="settingsPanel__footer flex align-items-m">
            <button 
                type="button" 
                class="btn btn-action flex-item push-right" 
                @click="save(block)"
            >Save &amp; Close</button>
        </div>
    </div>    
</template>

<script>
    import CoverPhoto from '../../MediaManager/CoverPhoto.vue'
	export default {
		props: ['block'],

        emits: ['saveBlock'],

        inheritAttrs: false,

        components: {
            CoverPhoto
        },

        data: function()
        {
            return {
                activeTab: 'content',
            }
        },         

        methods: {
            updatePhoto: function(photo) {
                this.block.settings.url = photo.path;
            },

            save: function(block) {
                this.$emit('saveBlock', block);
            }
        }
	}
</script>
<template>
<div class="settingsPanel">
    <div class="settingsPanel__body">
        <div class="settingsPanel__heading">
            Button
        </div>
        
        <table class="table table-bordered table-striped table-settings">
            <tbody>
                <tr>
                    <td>
                        <h2>Text</h2>
                    </td>
                    <td>
                        <input type="text" class="form-control" v-model="block.settings.message">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>URL</h2>
                    </td>
                    <td>
                        <input type="text" class="form-control" v-model="block.settings.url">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Background Color</h2>
                    </td>
                    <td>
                        <input type="text" v-model="backgroundColor" class="form-control colorpicker--modal" id="backgroundColor">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Text Color</h2>
                    </td>
                    <td>
                        <input type="text" v-model="block.settings.textColor" class="form-control colorpicker--modal" id="textColor">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Border Color</h2>
                    </td>
                    <td>
                        <input type="text" v-model="block.settings.borderColor" class="form-control colorpicker--modal" id="borderColor">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Padding</h2>
                        <p>The top and bottom padding of the content block.</p>
                    </td>
                    <td>
                        <div class="form-group">
                        <label>Top: <input type="text" class="hiddenInput" v-model="block.settings.paddingTop"> px</label>
                            <input 
                                type="range" 
                                min="0" 
                                max="160" 
                                v-model="block.settings.paddingTop" 
                            >
                        </div>
                        <div class="form-group">
                        <label>Bottom: <input type="text" class="hiddenInput" v-model="block.settings.paddingBottom"> px</label>
                            <input 
                                type="range" 
                                min="0" 
                                max="160" 
                                v-model="block.settings.paddingBottom" 
                            >
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="settingsPanel__footer flex align-items-m">
        <button 
            type="button" 
            class="btn btn-action flex-item push-right" 
            @click="save(block)"
        >Save &amp; Close</button>
    </div>
</div>    
</template>

<script>
	export default {
		props: ['block','palette'],

        emits: ['saveBlock'],

        inheritAttrs: false,

        created: function() {
            this.backgroundColor = this.block.settings.backgroundColor;
        },

        mounted: function() {
            let self = this;
            this.$nextTick(function() {
                $("#backgroundColor").spectrum({
                    preferredFormat: "hex",
                    showInput: true,
                    allowEmpty: true,
                    showPalette: true,
                    palette: self.palette,
                    change: function(color) {
                        self.block.settings.backgroundColor = color ? color.toHexString() : 'transparent';
                    }
                });

                $("#textColor").spectrum({
                    preferredFormat: "hex",
                    showInput: true,
                    allowEmpty: true,
                    showPalette: true,
                    palette: self.palette,
                    change: function(color) {
                        self.block.settings.textColor = color ? color.toHexString() : 'transparent';
                    }
                });

                $("#borderColor").spectrum({
                    preferredFormat: "hex",
                    showInput: true,
                    allowEmpty: true,
                    showPalette: true,
                    palette: self.palette,
                    change: function(color) {
                        self.block.settings.borderColor = color ? color.toHexString() : 'transparent';
                    }
                });
            });
        },

        methods: {
            changed: function() {
            },

            save: function(block) {
                this.$emit('saveBlock', block);
            }
        }
	}
</script>
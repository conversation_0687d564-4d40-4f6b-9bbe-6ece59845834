<template>
<div class="settingsPanel">
    <div class="settingsPanel__body">
        <div class="settingsPanel__heading">
            Header
        </div>

        <table class="table table-bordered table-striped table-settings">
            <tbody>
                <tr>
                    <td>
                        <h2>Logo</h2>
                        <p>Add your farm logo.</p>
                    </td>
                    <td>
                        <cover-photo :src="block.settings.logoUrl" message="Add Logo" @input="updateLogo"></cover-photo>
                        <div class="form-group" v-show="block.settings.logoUrl" style="margin-top: 12px;">
                            <label>Alt Text</label>
                            <input type="text" class="form-control" v-model="block.settings.logoAltText">
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Logo Height</h2>
                    </td>
                    <td>
                        <div class="form-group">
                        <label> 
                        <input type="text" class="hiddenInput" v-model="block.settings.logoHeight"> px</label>
                            <input 
                                type="range" 
                                min="50" 
                                max="250" 
                                v-model="block.settings.logoHeight" 
                            >
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Message</h2>
                    </td>
                    <td>
                        <input type="text" v-model="block.settings.message" class="form-control">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Font Size</h2>
                    </td>
                    <td>
                        <select 
                        class="form-control" 
                        :value="block.settings.fontSize ? block.settings.fontSize : '20px'" 
                        v-model="block.settings.fontSize"
                    >
                        <option value="16px">16px</option>
                        <option value="18px">18px</option>
                        <option value="20px">20px</option>
                        <option value="22px">22px</option>
                        <option value="24px">24px</option>
                        <option value="26px">26px</option>
                        <option value="28px">28px</option>
                        <option value="30px">30px</option>
                        <option value="32px">32px</option>
                        <option value="34px">34px</option>
                        <option value="36px">36px</option>
                        <option value="38px">38px</option>
                        <option value="40px">40px</option>
                        <option value="42px">42px</option>
                        <option value="44px">44px</option>
                        <option value="46px">46px</option>
                        <option value="48px">48px</option>
                        <option value="50px">50px</option>
                    </select>
                    </td>    
                </tr>
                <tr>
                    <td>
                        <h2>Background Color</h2>
                    </td>
                    <td>
                        <input type="text" v-model="block.settings.backgroundColor" class="form-control" id="backgroundColor">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Text Color</h2>
                    </td>
                    <td>
                        <input type="text" v-model="block.settings.textColor" class="form-control" id="textColor">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Alignment</h2>
                    </td>
                    <td>
                        <select v-model="block.settings.alignment" class="form-control">
                            <option value="left">Left</option>
                            <option value="center">Center</option>
                            <option value="right">Right</option>
                        </select>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Padding</h2>
                        <p>The top and bottom padding of the content block.</p>
                    </td>
                    <td>
                        <div class="form-group">
                        <label>Top: <input type="text" class="hiddenInput" v-model="block.settings.paddingTop"> px</label>
                            <input 
                                type="range" 
                                min="0" 
                                max="160" 
                                v-model="block.settings.paddingTop" 
                            >
                        </div>
                        <div class="form-group">
                        <label>Bottom: <input type="text" class="hiddenInput" v-model="block.settings.paddingBottom"> px</label>
                            <input 
                                type="range" 
                                min="0" 
                                max="160" 
                                v-model="block.settings.paddingBottom" 
                            >
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>    
    <div class="settingsPanel__footer flex align-items-m">
        <button 
            type="button" 
            class="btn btn-action flex-item push-right" 
            @click="save(block)"
        >Save &amp; Close</button>
    </div>
</div>
</template>

<script>
    import CoverPhoto from '../../MediaManager/CoverPhoto.vue'
	export default {

		props: ['block', 'palette'],

        emits: ['saveBlock'],

        inheritAttrs: false,

        components: {
            CoverPhoto
        },

        mounted: function() {
            let self = this;
            this.$nextTick(function() {

                $("#backgroundColor").spectrum({
                    preferredFormat: "hex",
                    showInput: true,
                    allowEmpty: true,
                    showPalette: true,
                    palette: self.palette,
                    change: function(color) {
                        self.block.settings.backgroundColor = color ? color.toHexString() : 'transparent';
                    }
                });

                $("#textColor").spectrum({
                    preferredFormat: "hex",
                    showInput: true,
                    allowEmpty: true,
                    showPalette: true,
                    palette: self.palette,
                    change: function(color) {
                        self.block.settings.textColor = color ? color.toHexString() : 'transparent';
                    }
                });
            });
        },

        data: function()
        {
            return {
                activeTab: 'content'
            }
        },

        methods: {
            updateLogo: function(logo) {
                this.block.settings.logoUrl = logo.path;
            },

            save: function(block) {
                this.$emit('saveBlock', block);
            }
        }
	}
</script>

<style>
    .coverPhoto__image {
        max-height: 125px;
        width: auto;
    }
</style>
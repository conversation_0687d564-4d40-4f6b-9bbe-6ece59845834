<template>
<div class="settingsPanel">
    <div class="settingsPanel__body">
        <div class="settingsPanel__heading">
            Divider
        </div>
        <table class="table table-bordered table-striped table-settings">
            <tbody>
                <tr>
                    <td>
                        <h2>Height</h2>
                    </td>
                    <td>
                        <div class="form-group">
                            <label><input type="text" class="hiddenInput" v-model="block.settings.height">px</label>
                                <input 
                                    type="range" 
                                    min="2" 
                                    max="30" 
                                    v-model="block.settings.height" 
                                >
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Color</h2>
                    </td>
                    <td>
                        <input type="text" v-model="block.settings.backgroundColor" class="form-control colorpicker--modal" id="backgroundColor">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Padding</h2>
                        <p>The top and bottom padding of the content block.</p>
                    </td>
                    <td>
                        <div class="form-group">
                            <label>Top: <input type="text" class="hiddenInput" v-model="block.settings.paddingTop"> px</label>
                                <input 
                                    type="range" 
                                    min="0" 
                                    max="160" 
                                    v-model="block.settings.paddingTop" 
                                >
                        </div>
                        <div class="form-group">
                            <label>Bottom: <input type="text" class="hiddenInput" v-model="block.settings.paddingBottom"> px</label>
                                <input 
                                    type="range" 
                                    min="0" 
                                    max="160" 
                                    v-model="block.settings.paddingBottom" 
                                >
                        </div>
                    </td>
                    <tr>
                        <td>
                            <h2>Edge-to-Edge</h2>
                        </td>
                        <td>
                            <label class="radio-inline">
                              <input type="radio" name="edgeToEdge" id="edgeToEdgeNo" value="30" v-model="block.settings.paddingSide"> No
                            </label>
                            <label class="radio-inline">
                              <input type="radio" name="edgeToEdge" id="edgeToEdgeYes" value="0" v-model="block.settings.paddingSide"> Yes
                            </label>
                        </td>
                    </tr>
                </tr>
            </tbody>
        </table>
    </div>

    <div class="settingsPanel__footer flex align-items-m">
        <button 
            type="button" 
            class="btn btn-action flex-item push-right" 
            @click="save(block)"
        >Save &amp; Close</button>
    </div>
</div>    
</template>

<script>
	export default {
		props: ['block', 'palette'],

        emits: ['saveBlock'],

        inheritAttrs: false,

        mounted: function() {
            let self = this;
            this.$nextTick(function() {
                $("#backgroundColor").spectrum({
                    preferredFormat: "hex",
                    showInput: true,
                    allowEmpty: true,
                    showPalette: true,
                    palette: self.palette,
                    change: function(color) {
                        self.block.settings.backgroundColor = color ? color.toHexString() : 'transparent';
                    }
                });
            });
        },

        methods: {
            save: function(block) {
                this.$emit('saveBlock', block);
            }
        }
	}
</script>
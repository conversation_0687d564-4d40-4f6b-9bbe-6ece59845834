<template>
    <div class="settingsPanel">
        <div class="settingsPanel__body">

            <div class="settingsPanel__heading">
                Text
            </div>

            <div class="settingsPanel__header">
                <ul class="tabsList">
                    <li :class="{'active': activeTab === 'content'}" @click="activeTab = 'content'">
                        <a href="#">Text</a>
                    </li>
                    <li :class="{'active': activeTab === 'appearance'}" @click="activeTab = 'appearance'">
                        <a href="#">Settings</a>
                    </li>
                </ul>
            </div>

            <div v-show="activeTab === 'content'" class="linkListWidget__linkTab">
                <tiny-editor :content="block.content" ref="tinyMCE" @update="update"></tiny-editor>
            </div>

            <div v-show="activeTab === 'appearance'" class="linkListWidget__linkTab">
                <table class="table table-bordered table-striped table-settings">
                    <tbody>
                        <tr>
                            <td>
                                <h2>Padding</h2>
                                <p>The top and bottom padding of the content block.</p>
                            </td>
                            <td>
                                <div class="form-group">
                                <label>Top: <input type="text" class="hiddenInput" v-model="block.settings.paddingTop"> px</label>
                                    <input 
                                        type="range" 
                                        min="0" 
                                        max="160" 
                                        v-model="block.settings.paddingTop" 
                                    >
                                </div>
                                <div class="form-group">
                                <label>Bottom: <input type="text" class="hiddenInput" v-model="block.settings.paddingBottom"> px</label>
                                    <input 
                                        type="range" 
                                        min="0" 
                                        max="160" 
                                        v-model="block.settings.paddingBottom" 
                                    >
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div class="settingsPanel__footer flex align-items-m">
            <button 
                type="button" 
                class="btn btn-action flex-item push-right" 
                @click="save(block)"
            >Save &amp; Close</button>
        </div>  
    </div>    
</template>

<script>
    import TinyEditor from '../../TinyMCE.vue'
	export default {

		props: ['block'],

        emits: ['saveBlock'],

        inheritAttrs: false,

        components: {
            TinyEditor
        },

        unmounted: function()
        {
            $('#pageBodyEditor').redactor('core.destroy');
        },

        data: function()
        {
            return {
                activeTab: 'content',
            }
        },

        watch: {
            'block': function(block) {
                this.$refs.tinyMCE.setContent(this.block.content);
            }
        },

        methods: {
            save: function(block) {
                this.$emit('saveBlock', block);
            },

            update: function(content) {
                this.block.content = content;
            }
        }
	}
</script>
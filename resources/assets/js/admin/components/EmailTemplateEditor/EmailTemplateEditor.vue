<template>
<div class="emailTemplateEditor">
    <div class="emailTemplateEditor__header toolbar">
        <div class="toolbar__breadcrumbs">
            <ol class="breadcrumb breadcrumb-toolbar">
                <li><a href="/admin/templates">Templates</a></li>
                <li>{{ template.title }}</li>
            </ol>
        </div>

        <div class="toolbar__buttons">
            <a href="/admin/templates" class="btn btn-light"><i class="fa fa-arrow-circle-o-left"></i> Exit</a>
            <a :href="'/admin/templates/' + template.id" target="_blank" class="btn btn-light"><i class="fa fa-eye"></i> Preview</a>
            <a href="#" class="btn btn-light" @click="showModal('sendTestEmailModal')"><i class="fa fa-envelope"></i> Send Test</a>
            <button type="button" class="btn btn-light" @click="showModal('publishTemplateModal')">
                <i class="fa fa-check-circle" :class="{'text-warning': template.needs_published, 'text-primary': !template.needs_published}"></i> Publish
            </button>
            <div class="dropdown" style="display: inline-block;">
                <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="fa fa-gear"></i> Actions <i class="fa fa-caret-down"></i>
                </button>
                <ul class="dropdown-menu pull-right">
                    <li><a href="#" @click="showModal('deleteTemplateModal')">Delete</a></li>
                </ul>
            </div>
        </div>

    </div>
    <div class="emailTemplateEditor__body">

        <div class="emailTemplateEditor__toolbarContainer">
            <component 
                :is="getBlock(activeView)" 
                :template="template" 
                :activeView="activeView" 
                :block="activeBlock" 
                :palette="palette" 
                @changeView="changeView"
                @saveBlock="updateBlock"
                @addBlock="addBlock"
                @saveTemplate="updateTemplate"
            ></component>   
        </div>

        <div 
            class="emailTemplateEditor__previewContainer" 
            :style="{'backgroundColor': template.settings.backgroundColor, 'fontFamily': template.settings.fontFamily}"
        >
            <ul 
                id="segmentList" 
                class="emailTemplateEditor__segmentList" 
                v-sortable="options" 
            >
                <li v-for="block in blocks" class="emailTemplateEditor__segmentListItem" @click.stop="editBlock(block)" :key="block.id">
                    <div class="emailTemplateEditor__segmentContainer">
                        <div class="emailTemplateEditor__segmentHeaderContainer" :class="{'emailTemplateEditor__segmentHeaderContainer--active': activeBlock === block}">
                            <div class="emailTemplateEditor__segmentHeader">
                                <span class="draghandle" title="Drag to reorder"><i class="fa fa-th"></i></span>
                                <span><a href="#" @click.stop="editBlock(block)"><i class="fa fa-pencil-square"></i></a></span>
                                <span><a href="#" @click.stop="duplicateBlock(block)"><i class="fa fa-clone"></i></a></span>
                                <span><a href="#" @click.stop="deleteBlock(block)"><i class="fa fa-trash"></i></a></span>
                            </div>    
                        </div>
                        <component :is="getPreview(block.view)" :block="block"></component>
                    </div> 
                </li>
                <li v-if="!blocks.length" class="emailTemplateEditor__emptySegmentList">
                    Drop Content Blocks Here.
                </li>
            </ul>
        </div>
    </div>

    <div class="gc-modal gc-modal-mask" id="publishTemplateModal" @click="hideModal('publishTemplateModal')">
        <div class="gc-modal-wrapper">
            <div class="gc-modal-container" @click.stop>
                
                <div class="gc-modal-header">
                    Publish Template
                </div>
    
                <div class="gc-modal-body">
                    Do you want to publish this template and make it live?
                </div>
    
                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('publishTemplateModal')">Cancel</button>
                    <button type="button" class="btn btn-action" @click="publish(template)">Publish</button>
                </div>
            </div>
        </div>
    </div>

    <div class="gc-modal gc-modal-mask" id="sendTestEmailModal" @click="hideModal('sendTestEmailModal')">
        <div class="gc-modal-wrapper">
            <div class="gc-modal-container" @click.stop>
                
                <div class="gc-modal-header">
                    Send Test Email
                </div>
    
                <div class="gc-modal-body">
                    <label for="emails">Send this preview to any active admin of your account. Separate multiple emails with a comma.</label>
                    <input type="text" name="emails" class="form-control" id="emails" v-model="recipients">
                </div>
    
                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('sendTestEmailModal')">Cancel</button>
                    <button type="submit" class="btn btn-action" @click="sendTestEmail(recipients)">Send Test Email</button>
                </div>
            </div>
        </div>
    </div>

    <div class="gc-modal gc-modal-mask" id="deleteTemplateModal" @click="hideModal('deleteTemplateModal')">
        <div class="gc-modal-wrapper">
            <div class="gc-modal-container" @click.stop>
                <div class="gc-modal-header">
                Delete this email template?
                </div>
                <div class="gc-modal-body">
                    Are you sure you want to delete this email template?
                </div>
                <div class="gc-modal-footer">
                    <button class="btn btn-alt" @click="hideModal('deleteTemplateModal')">Cancel</button>
                    <button class="btn btn-danger" @click="destroyTemplate()">Delete</button>
                </div>
            </div>
        </div>
    </div>

    <div class="gc-modal gc-modal-mask" id="deleteBlockModal" @click="hideModal('deleteBlockModal')">
        <div class="gc-modal-wrapper">
            <div class="gc-modal-container" @click.stop>
                <div class="gc-modal-header">
                Delete this content block?
                </div>
                <div class="gc-modal-body">
                    Delete this content block?
                </div>
                <div class="gc-modal-footer">
                    <button class="btn btn-alt" @click="cancelDelete()">Cancel</button>
                    <button class="btn btn-danger" @click="destroyBlock()">Delete</button>
                </div>
            </div>
        </div>
    </div>

</div>
</template>

<script>
    import TemplateSettings from './TemplateSettings.vue';
    import BlocksToolbar from './BlocksToolbar.vue';

    // Blocks
    import HtmlCode from './blocks/HtmlCode.vue';
    import RichText from './blocks/RichText.vue';
    import Photo from './blocks/Photo.vue';
    import EmailHeader from './blocks/Header.vue';
    import ButtonSegment from './blocks/Button.vue';
    import DividerSegment from './blocks/Divider.vue';

    // Previews
    import RichTextPreview from './previews/RichTextPreview.vue';
    import HtmlCodePreview from './previews/HtmlCodePreview.vue';
    import PhotoPreview from './previews/PhotoPreview.vue';
    import EmailHeaderPreview from './previews/EmailHeaderPreview.vue';
    import ButtonSegmentPreview from './previews/ButtonSegmentPreview.vue';
    import DividerSegmentPreview from './previews/DividerPreview.vue';
	export default {

		components: {
            TemplateSettings,
            BlocksToolbar,
            HtmlCode,
            Photo,
            ButtonSegment,
            DividerSegment,
            EmailHeader,
            RichText,
            RichTextPreview,
            HtmlCodePreview,
            PhotoPreview,
            EmailHeaderPreview,
            ButtonSegmentPreview,
            DividerSegmentPreview,
    	},

        props: {
            id: {
                type: String,
                required: true
            },
            emails: {
                type: String
            }
        },

        created: function() {
            this.getTemplate();
            this.getSettings();
            this.getTheme();
            this.recipients = this.emails;
        },

		mounted: function()
		{
            window.addEventListener("beforeunload", function (event) {
                if(this.template.needs_published)
                {
                    event.returnValue = "Changes have to been published yet.";
                }
            }.bind(this));
		},

    	data: function() {
    		return {
                template: {
                    subject: '',
                    title: '',
                    needs_published: false,
                    settings: {
                        backgroundColor: '#FFF',
                        fontFamily: 'Arial',
                        track_links: false,
                        campaign_name: '',
                        campaign_content: ''
                    }
                },
                recipients: '',
                saved: true,
    			blocks: [],
                settings: [],
                theme: null,
                palette: [],
                activeView: 'BlocksToolbar',
    			activeBlock: '',
                blockToDelete: null,
                options: { 
                    onUpdate: this.updateSort, 
                    onAdd: this.dropBlock,
                    handle: '.draghandle', 
                    animation: 150,
                    group: {
                        name:'segments',
                        pull: false,
                        put: true
                    } 
                }
    		}
    	},

    	methods: {
            getBlock: function(block) {
                if(block == 'HTML') {
                    return 'HtmlCode';
                }
                if(block == 'Image') {
                    return 'Photo';
                }
                return block;
            },

            getPreview: function(view) {
                if(view == 'HTML') {
                    return 'HtmlCodePreview';
                }
                if(view == 'Image') {
                    return 'PhotoPreview';
                }
                return view+'Preview';
            },

            changeView: function(view) {
                this.activeView = view;
            },

            getTemplate: function() {
                axios.get('/api/templates/'+this.id).then(function(response) {
                    this.template = response.data;
                    this.getBlocks();
                }.bind(this)).catch(function(error) {

                })
            },

    		getBlocks: function()
    		{
	            axios.get('/api/templates/'+this.id+'/segments')
	                .then(function(response) {
	                    this.blocks = response.data;
	                }.bind(this))
	                .catch(function(error) {
	                });
    		},

            addBlock: function(blockType) {
                this.storeBlock({
                    'template_id': this.id,
                    'title': blockType,
                    'view': blockType,
                    'content': '',
                    'sort': this.blocks.length,
                    'settings': {}
                });
            },

            dropBlock: function(event) {
                let segmentList = document.getElementById("segmentList");
                if(event.item.parentNode == segmentList) {
                    segmentList.removeChild(event.item);
                    let segmentType = event.item.getAttribute('data-id');
                    this.storeBlock({
                        'template_id': this.id,
                        'title': segmentType,
                        'view': segmentType,
                        'content': '',
                        'sort': event.newIndex,
                        'settings': {}
                    });
                }
                this.updateTemplate();
            },

            duplicateBlock: function(block) {
                this.template.needs_published = true;
                eventHub.emit('showProgressBar');
                axios.post('/api/templates/'+this.id+'/duplicated-segments', block)
                    .then(function(response) {
                        eventHub.emit('hideProgressBar');
                        this.blocks.splice(response.data.segment.sort, 0, response.data.segment);
                        this.editBlock(response.data.segment);
                        this.updateSort();
                    }.bind(this))
                    .catch(function(error) {
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('error', error);
                    }.bind(this));
            },

            storeBlock: function(block)
            {
                this.template.needs_published = true;
                eventHub.emit('showProgressBar');
                axios.post('/api/templates/'+this.id+'/segments', block)
                    .then(function(response) {
                        eventHub.emit('hideProgressBar');
                        this.blocks.splice(response.data.segment.sort, 0, response.data.segment);
                        this.editBlock(response.data.segment);
                        this.updateSort();
                    }.bind(this))
                    .catch(function(error) {
                        eventHub.emit('hideProgressBar');
                        eventHub('error', error);
                    }.bind(this));
            },

    		editBlock: function(block)
    		{
    			this.activeBlock = block;
                this.activeView = block.view;
    		},

    		updateBlock: function(block)
    		{
                this.template.needs_published = true;
                eventHub.emit('showProgressBar');
	            axios.put('/api/templates/'+this.id+'/segments/'+block.id, block)
	                .then(function(response) {
                        eventHub.emit('hideProgressBar');
                        this.activeBlock = '';
                        this.activeView = 'BlocksToolbar';
	                }.bind(this))
	                .catch(function(error) {
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('error', error);
	                }.bind(this));
    		},

    		deleteBlock: function(block)
    		{
                this.blockToDelete = block;
                this.showModal('deleteBlockModal');
    		},

            
            destroyBlock: function(block) {
                this.template.needs_published = true;
                eventHub.emit('showProgressBar');
                axios.delete('/api/templates/'+this.id+'/segments/'+this.blockToDelete.id)
                    .then(function(response) {
                        this.hideModal('deleteBlockModal');
                        this.activeBlock = '';
                        this.blockToDelete = null;
                        this.activeView = 'BlocksToolbar';
                        this.getBlocks();
                        eventHub.emit('hideProgressBar');
                    }.bind(this))
                    .catch(function(error) {
                        this.hideModal('deleteBlockModal');
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('error', error);
                    }.bind(this));
            },

            cancelDelete: function() {
                this.blockToDelete = null;
                this.hideModal('deleteBlockModal');
            },

    		updateSort: function(event)
			{
                this.template.needs_published = true;
                eventHub.emit('showProgressBar');
                if(event != undefined) {
                    this.blocks.splice(event.newIndex, 0, this.blocks.splice(event.oldIndex, 1)[0]);
                }
                
                var payload = {};
                for(var i = 0; i < this.blocks.length; i++) {
                   this.blocks[i].sort = i;
                   payload[this.blocks[i].id] = i;
                }

                axios.put('/api/templates/'+this.id+'/sorted-segments', payload)
                    .then(function(response) {
                        // this.getSegments();
                        eventHub.emit('hideProgressBar');
                    }.bind(this))
                    .catch(function(error) {
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('error', error);
                    });

                this.updateTemplate();
			},

            updateTemplate: function() {
                this.template.needs_published = true;
                eventHub.emit('showProgressBar');
                axios.put('/api/templates/'+this.id, this.template).then(function(response) {
                    eventHub.emit('hideProgressBar');
                }.bind(this))
                .catch(function(error){
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                })
            },

            destroyTemplate: function() {
                this.template.needs_published = true;
                eventHub.emit('showProgressBar');
                axios.delete('/api/templates/'+this.id).then(function(response) {
                    eventHub.emit('hideProgressBar');
                    return location.href = '/admin/templates';
                }.bind(this))
                .catch(function(error){
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                })
            },

            publish: function(template) {
                for(let i = 0; i < this.blocks.length; i++) {
                    this.updateBlock(this.blocks[i]);
                }
                this.updateTemplate();

                eventHub.emit('showProgressBar');
                axios.put('/api/templates/'+this.id, {'publish': true}).then(function(response) {
                    this.hideModal('publishTemplateModal');
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('notify', {level: 'info', message: response.data});
                    this.template.needs_published = false;
                }.bind(this)).catch(function(error){
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                }.bind(this))
            },

            sendTestEmail: function(recipients) {
                eventHub.emit('showProgressBar');
                axios.post('/admin/templates/'+this.template.id+'/preview', {
                    'emails': recipients
                }).then(function(response) {
                    this.hideModal('sendTestEmailModal');
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('notify', {level: 'info', message: response.data});
                }.bind(this)).catch(function(error) {
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                }.bind(this))
            },

            getSettings: function()
            {
                axios.get('/api/settings')
                    .then(function(response) {
                        this.settings = response.data;
                    }.bind(this))
                    .catch(function(error) {
                    });
            },

            getTheme: function() {
                axios.get('/api/theme')
                    .then(function(response) {
                        this.theme = response.data;
                        this.palette = [
                        this.theme.settings.brand_color, 
                        this.theme.settings.action_color, 
                        '#FFFFFF','#EEEEEE','#3D3D3D'
                    ];
                    }.bind(this))
                    .catch(function(error) {
                    });
            }
    	}
	}
</script>
<template>
	<div class="settingsPanel">
	    <div class="settingsPanel__body">
	    	<ul class="emailTemplateEditor__navigation tabsList">
	            <li :class="{'active': activeView == 'BlocksToolbar'}" @click="$emit('changeView', 'BlocksToolbar')">
	                <a href="#">Content</a>
	            </li>
	            <li :class="{'active': activeView == 'TemplateSettings'}" @click="$emit('changeView', 'TemplateSettings')">
	                <a href="#">Design</a>
	            </li>
	        </ul>
	        <ul class="emailTemplateEditor__blocksContainer" v-sortable="options">
				<li data-id="RichText" class="emailTemplateEditor__block btn" @click="addBlock('RichText')">
					<div>
						<i class="fa fa-align-left fs-3"></i> Text
					</div>
				</li>
				<li data-id="EmailHeader" class="emailTemplateEditor__block btn" @click="addBlock('EmailHeader')">
					<div>
						<i class="fa fa-window-maximize fs-3"></i> Header
					</div>
				</li>
				<li data-id="ButtonSegment" class="emailTemplateEditor__block btn" @click="addBlock('ButtonSegment')">
					<div>
						<i class="fa fa-square fs-3"></i> Button
					</div>
				</li>
				<li data-id="Photo" class="emailTemplateEditor__block btn" @click="addBlock('Photo')">
					<div>
						<i class="fa fa-picture-o fs-3"></i> Image
					</div>
				</li>
				<li data-id="DividerSegment" class="emailTemplateEditor__block btn" @click="addBlock('DividerSegment')">
					<div>
						<i class="fa fa-minus fs-3"></i> Divider
					</div>
				</li>
				<li data-id="HtmlCode" class="emailTemplateEditor__block btn" @click="addBlock('HtmlCode')">
					<div>
						<i class="fa fa-code fs-3"></i> HTML
					</div>
				</li>
			</ul>
		</div>

		<div class="settingsPanel__footer flex align-items-m justify-center">
			<span class="flex-item">Drag-and-drop Content Blocks to design your email.</span>
		</div>
	</div>
</template>

<script>
	export default {

		props: ['template','active-view','recipients'],

        emits: ['changeView', 'addBlock'],

		data: function() {
			return {
				view: 'content',
				options: { 
					sort: false,
                    group: {
                        name:'segments',
                        pull: 'clone',
                        put: false
                    } 
                }
			}
		},

		methods: {

			addBlock: function(block) {
				this.$emit('addBlock', block)
			},

			changeView: function(view) {
				this.$emit('changeView', view)
			}
        }
	}
</script>
<template>
    <div>
        <slot :from-string="fromString" :initial-slug="initialSlug" :slug="slug" :get-slug="getSlug" :flash-on-change="flashOnChange"></slot>
    </div>
</template>

<script>
import axios from 'axios';
import _ from 'lodash';

export default {
    name: "InstantSlug",
    props: {
        /**
         * The string from the backend that will initially be displayed in the fromString input (usually title of entry).
         */
        initialFromString: {
            type: String,
            required: false
        },
        /**
         * The slug from the backend that will initially be displayed in the slug input.
         */
        initialSlug: {
            type: String,
            required: false
        },
        /**
         * The name of the backend model for which a slug is being generated.
         */
        model: {
            type: String,
            required: true
        },
        /**
         * The name of the backend field in the model where the slug is stored.
         */
        databaseField: {
            type: String,
            required: true
        },
        /**
         * The database primary key of the entry in the backend model array specified by the model prop.
         */
        entryId: {
            type: String,
            required: true
        }
        // TODO: get this.debounceDelay to work as the debounce delay (see getSlug() below)
        // so that the parent can modify the typing debounce delay.
        // I think there's a proboem with the "this" binding.
        // debounceDelay: {
        //   type: Number,
        //   required: false,
        //   default: 1000
        // }
    },
    data: function() {
        return {
            /**
             * The string (probably the title of the page) that will be used to determine the slug for the page.
             */
            fromString: "",
            /**
             * The slug that is currenlty displayed in the slug input field
             */
            slug: "",
            /**
             * A boolean that will be changed rapidly to be used as the value to determine whether or not
             * a class or style that changes the background color of the slug intput field is on.
             * Ex.: <input :class="{ 'bg-primary': flashOnChange, 'text-white': flashOnChange }" type="text" name="slug" v-model="slug" class="form-control background-color-text-color-transition">
             */
            flashOnChange: false
        }
    },

    mounted: function() {
        this.fromString = this.initialFromString;
        this.slug = this.initialSlug;
    },

    methods: {
        flash: function() {
            this.flashOnChange = true;
            setTimeout(() => {
                this.flashOnChange = false
            }, 300)
        },
        getSlug: _.debounce(function(fromString) {
            // must update title in data of InstantSlug so that the title doesn't get wiped out when the component updates
            // TODO: consider changing this functionality because with just the right timing of typing, I believe the user's last keystroke can be deleted
            this.fromString = fromString;

            // make API call to admin API to get the slug based on the fromString given
            axios({
                method: 'post',
                url: '/admin/api/sluggable/create-slug',
                data: {
                    fromString: fromString,
                    model: this.model,
                    databaseField: this.databaseField,
                    entryId: this.entryId
                }
            })
                .then(response => {
                    this.slug = response.data.slug;
                    this.flash();
                })
                .catch(e => {
                });
        }, 1000) // TODO: get this.debounceDelay working here (see prop above)
    }
}
</script>


<template>
<div>
    <ul class="tabsList">
        <li :class="{'active': activeTab == 'items'}" @click="activeTab = 'items'">
            <a href="#">Photo Items</a>
        </li>
        <li :class="{'active': activeTab == 'appearance'}" @click="activeTab = 'appearance'">
            <a href="#">Appearance</a>
        </li>
    </ul>
    <div v-show="activeTab == 'items'">
    	<div class="photoGrid__listContainer pt-md">

    		<div class="photoGrid__itemListContainer">
		        <ul class="photoGrid__list" v-sortable="{ onUpdate: updateSort, animation: 150 }">
		            <li 
		                v-for="item in widget.settings.items" 
		                class="photoGrid__listItem" 
                        :key="itemIndex(item)"
		            >
		            <img 
                                :src="item.src ? item.src : this.placeholder" 
                                :alt="item.caption" class="photoGrid__imagePreview photoGrid__imagePreview--square" 
                                @click="activeItem = item" 
                                :class="{'photoGrid__imagePreview--active': activeItem == item}"
                            >
                        <div class="photoGrid__caption">{{ item.caption }}</div> 
		            </li>
		        </ul>
	        </div>

	       <div class="photoGrid__itemDetailsContainer" v-if="activeItem">
	        	<div class="photoGrid__linkInputs">
	            	<div class="form-group">
	            		<label>Photo</label>
	            		<div class="photoPreview">
	            		<cover-photo 
	            			:src="activeItem.src" 
                            @input="updatePhoto"
	            		></cover-photo>
	            		</div>
					</div>            			
	                <div class="form-group">
	                    <label>Main Caption</label>
	                    <input type="text" v-model="activeItem.caption" class="form-control">
	                </div>
	                <div class="form-group">
	                    <label>Tagline <small>(Goes above main caption in smaller font)</small></label>
	                    <input type="text" v-model="activeItem.subcaption" class="form-control">
	                </div>
	                <div class="form-group">
	                    <label>URL</label>
	                    <input type="text" v-model="activeItem.url" class="form-control">
	                </div>
	            </div>
                <div class="form-group text-right">
                        <a href="#" @click.prevent="removeLink(activeItem)" class="btn btn-sm btn-danger"><i class="fa fa-trash-o fa-lg"></i> Remove</a>
                </div>
	            <div class="photoGrid__linkInputs">
	            	<p>Select a photo item to edit, or add another one.</p>
	            </div>
	        </div>
        </div>
        <div class="form-group">
        	<button class="btn btn-light" @click="addLink()"><i class="fa fa-plus"></i> Add Item</button>
        </div>	
    </div>

    <div v-show="activeTab == 'appearance'">
    	<table class="table table-bordered  table-striped table-settings">
        <tbody>
        <tr>
            <td>
                <h2>Heading text</h2>
                <p>Optional heading that will appear above the photo grid.</p>
            </td>
            <td>
                <div class="form-group">
                    <input type="text" v-model="widget.settings.heading" class="form-control">
                </div>  
            </td>
        </tr>
        <tr>
	        <td>
	            <h2>Background Color</h2>
	            <p>The background color of the content block.</p>
	        </td>
	        <td>
	            <input type="text" v-model="widget.settings.background" class="form-control colorpicker--modal" id="backgroundColor">
	        </td>
	    </tr>
        <tr>
        <td>
            <h2>Padding</h2>
            <p>The top and bottom padding of the content block.</p>
        </td>
        <td>
            <div class="form-group">
            <label>Top: <input type="text" class="hiddenInput" v-model="widget.settings.paddingTop"> px</label>
                <input 
                    type="range" 
                    min="0" 
                    max="60" 
                    v-model="widget.settings.paddingTop"
                >
            </div>
            <div class="form-group">
            <label>Bottom: <input type="text" class="hiddenInput" v-model="widget.settings.paddingBottom"> px</label>
                <input 
                    type="range" 
                    min="0" 
                    max="60" 
                    v-model="widget.settings.paddingBottom"
                >
            </div>
        </td>
    </tr>
        <tr>
            <td>
                <h2>Limit Visibility</h2>
                <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
            </td>
            <td>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                    </label>
                </div>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                    </label>
                </div>
            </td>
        </tr>
        </tbody>
        </table>
    </div>     
</div>    
</template>

<script>
	import CoverPhoto from '../MediaManager/CoverPhoto.vue';
	export default {
		props: ['widget', 'create'],

		components: {
			CoverPhoto
		},

		created: function()
		{
			if(this.create)
            {
                this.widget.title = 'Photo Grid'
                this.widget.template = 'PhotoGrid'
                this.widget.settings = {
                    paddingTop: 60,
                    paddingBottom: 60,
                    items: [
                        {
                            'id': this.itemId,
                            'edit': true,
                            'caption': 'New Photo Item', 
                            'subcaption': '', 
                            'url': this.linkUrl,
                            'src': this.placeholder
                        }
                    ],
                }
            }
		},

        mounted: function() {
            let self = this;
            $("#backgroundColor").spectrum({
                preferredFormat: "hex",
                showInput: true,
                allowEmpty: true,
                appendTo: $('#widgetModal'),
                change: function(color) {
                    self.widget.settings.background = color.toHexString();
                }
            });
        },

        data: function()
        {
            return {
                linkLabel: 'Beef',
                linkUrl: '/',
                items: [],
                activeTab: 'items',
                activeItem: null,
                placeholder: 'https://s3.amazonaws.com/grazecart/stockphotos/photo-grid-placeholder.jpg'
            }
        },

        computed: {
            itemId: function() {
                if(this.widget.settings.items) {
                    return this.widget.settings.items.length;
                }
                else {
                    return 0;
                }
            }
        },

        methods: {
            itemIndex: function(item) {
                return Math.floor((Math.random() * 10000) + 1);
            },

            addLink: function()
            {
            	let newItem = {
                    'caption': 'New Photo Item '+this.widget.settings.items.length, 
                    'subcaption': '', 
                    'url': this.linkUrl,
                    'src': this.placeholder
                }

                this.widget.settings.items.push(newItem)

                this.activeItem = newItem
            },

            removeLink: function(link)
            {
                if(this.widget.settings.items.length > 1)
                {
                    this.activeItem = null
                    var index = this.widget.settings.items.indexOf(link); 
                    this.widget.settings.items.splice(index, 1)
                }
            },

            updatePhoto(photo) {
                this.activeItem.src = photo.path;
            },

            updateSort: function(event)
            {
                this.widget.settings.items.splice(event.newIndex, 0, this.widget.settings.items.splice(event.oldIndex, 1)[0]);
            }
        }
	}
</script>

<style>
.photoPreview img {
	max-height: 75px;
}
.photoGrid__listContainer {
    display: flex;
}

.photoGrid__itemListContainer {
    flex: 1 1 auto;
    display: inline-block;
    vertical-align: top;
    padding: 0;
    padding-right: 15px;
}

.photoGrid__itemDetailsContainer {
    flex: 1 0 auto;
    display: inline-block;
    vertical-align: top;
    padding-left: 15px;
    border-left: solid 1px #EEE;
    min-width: 50%;
}

.photoGrid__list {
    padding: 0;
    text-align: center;
    display: flex;
    justify-content: center;
    flex-wrap: wrap;
    max-height: 700px;
    overflow: auto;
}

.photoGrid__listItem {
    flex: 0 0 auto;
    vertical-align: top;
    display: inline-block;
    width: 150px;
    padding: 0;
    margin: 0 30px 30px 30px;
    text-align: center;
    border: solid 2px transparent;
}

.photoGrid__listItem--active {
    border-color: hsl(165, 55%, 45%);
}

.photoGrid__imagePreview {
    max-width: 150px;
    border: solid 2px transparent;
    transition: all ease-in-out 0.2s;
    border-radius: 50%;
}

.photoGrid__imagePreview--square {
    border-radius: 6px;
    max-height: auto;
}

.photoGrid__imagePreview:hover {
    border-color: hsl(165, 55%, 45%);
    cursor: pointer;
}

.photoGrid__imagePreview--active {
    border-color: hsl(165, 55%, 45%);
}

.photoGrid__caption {
    margin: 6px 0;
    text-align: center;
}
</style>
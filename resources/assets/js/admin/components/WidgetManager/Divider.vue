<template>
<div>
	<table class="table table-bordered  table-striped table-settings">
    <tbody>
    <tr>
        <td>
            <h2>Height: {{ widget.settings.height }}px</h2>
        </td>
        <td>
            <input type="range" class="" min="1" max="60" v-model="widget.settings.height">
        </td>
    </tr>
    <tr>
        <td>
            <h2>Divider Color</h2>
        </td>
        <td>
            <input type="text" v-model="widget.settings.bg_color" class="form-control colorpicker--modal" id="bg_color">
        </td>
    </tr>
    <tr>
        <td>
            <h2>Divider Width</h2>
        </td>
        <td>
            <select class="form-control" v-model="widget.settings.width">
                <option value="full">Full Screen</option>
                <option value="half">Half Page</option>
                <option value="page">Full Page</option>
            </select>
        </td>
    </tr>
    <tr>
        <td>
            <h2>Columns</h2>
            <p>How much horizontal space this block will take up. For example a 2 column layout would be 2 "Half" blocks.</p>
        </td>
        <td>
            <div class="form-group">
                <select class="form-control" v-model="widget.settings.layout_width">
                    <option value="full-width">Full</option>
                    <option value="half-width">Half</option>
                </select>   
            </div>  
        </td>
    </tr>
    <tr>
        <td>
            <h2>Limit Visibility</h2>
            <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
        </td>
        <td>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                </label>
            </div>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                </label>
            </div>
        </td>
    </tr>
    </tbody>
    </table>
</div>    
</template>

<script>
	export default {
		props: ['widget', 'create'],

		created: function()
		{
			if(this.create)
            {
                this.widget.title = 'Divider'
                this.widget.template = 'Divider'
                this.widget.settings = {
                    'bg_color': '#EEE',
                    'height': '1',
                    'width': 'full',
                    'layout_width': 'full-width'
                }
            }
		},

        mounted: function() {
            let self = this;
            $("#bg_color").spectrum({
                preferredFormat: "hex",
                showInput: true,
                allowEmpty: true,
                appendTo: $('#widgetModal'),
                change: function(color) {
                    self.widget.settings.bg_color = color.toHexString();
                }
            });
        }
	}
</script>
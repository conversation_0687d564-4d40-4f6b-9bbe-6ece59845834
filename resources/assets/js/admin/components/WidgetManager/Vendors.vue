<template>
<div>
    <table class="table table-bordered  table-striped table-settings">
    <tbody>
    <tr>
        <td>
            <h2>Sort Order</h2>
        </td>
        <td>
            <select v-model="widget.settings.sort" class="form-control">
                <option value="asc">A-Z</option>
                <option value="desc">Z-A</option>
            </select>
        </td>
    </tr>
    <tr>
        <td>
            <h2>Columns</h2>
            <p>How much horizontal space this block will take up. For example a 2 column layout would be 2 "Half" blocks.</p>
        </td>
        <td>
            <div class="form-group">
                <select class="form-control" v-model="widget.settings.layout_width">
                    <option value="full-width">Full</option>
                    <option value="half-width">Half</option>
                </select>   
            </div>  
        </td>
    </tr>
    <tr>
        <td>
            <h2>Limit Visibility</h2>
            <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
        </td>
        <td>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                </label>
            </div>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                </label>
            </div>
        </td>
    </tr>
    </tbody>
</table>
</div>
</template>

<script>
    export default {
        props: ['widget', 'create'],

        created: function()
        {
            if(this.create)
            {
                this.widget.title = 'Vendors'
                this.widget.template = 'Vendors'
                this.widget.settings = {
                    sort: 'asc'
                }
            }
        }
    }
</script>
<template>
<div>
    <ul class="tabsList">
        <li :class="{'active': activeTab == 'content'}" @click="activeTab = 'content'">
            <a href="#">HTML</a>
        </li>
        <li :class="{'active': activeTab == 'appearance'}" @click="activeTab = 'appearance'">
            <a href="#">Appearance</a>
        </li>
    </ul>
    <div v-show="activeTab == 'content'" class="linkListWidget__linkTab">
		<textarea class="form-control" rows="20" v-model="widget.content" placeholder="Enter your HTML code here."></textarea>
    </div>
    
    <div v-show="activeTab == 'appearance'" class="linkListWidget__linkTab">
        <table class="table table-bordered  table-striped table-settings">
            <tbody>
            <tr>
                <td>
                    <h2>Columns</h2>
                    <p>How much horizontal space this block will take up. For example a 2 column layout would be 2 "Half" blocks.</p>
                </td>
                <td>
                    <div class="form-group">
                        <select class="form-control" v-model="widget.settings.layout_width">
                            <option value="full-width">Full</option>
                            <option value="half-width">Half</option>
                        </select>   
                    </div>  
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Limit Visibility</h2>
                    <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
                </td>
                <td>
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                        </label>
                    </div>
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                        </label>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
</div>    
</template>

<script>
	export default {
		props: ['widget', 'create'],

		created: function()
		{
			if(this.create)
            {
                this.widget.title = 'HTML'
                this.widget.template = 'HTML'
                this.widget.settings = {
                    layout_width: 'full-width'
                }
                this.widget.content = ''
            }
		},

        data: function()
        {
            return {
                activeTab: 'content',
            }
        },
	}
</script>
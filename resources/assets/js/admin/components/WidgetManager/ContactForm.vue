<template>
<div>
	<table class="table table-bordered  table-striped table-settings">
    <tbody>
    <tr>
        <td>
            <h2>Heading Text</h2>
            <p>The heading tht will appear above the form. Just leave this blank to hide it.</p>
        </td>
        <td>
            <input type="text" v-model="widget.settings.header" class="form-control">
        </td>
    </tr>
    <tr>
        <td>
            <h2>Padding</h2>
            <p>The top and bottom padding of the content block.</p>
        </td>
        <td>
            <div class="form-group">
            <label>Top: <input type="text" class="hiddenInput" v-model="widget.settings.paddingTop"> px</label>
                <input 
                    type="range" 
                    min="0" 
                    max="60" 
                    v-model="widget.settings.paddingTop"
                >
            </div>
            <div class="form-group">
            <label>Bottom: <input type="text" class="hiddenInput" v-model="widget.settings.paddingBottom"> px</label>
                <input 
                    type="range" 
                    min="0" 
                    max="60" 
                    v-model="widget.settings.paddingBottom"
                >
            </div>
        </td>
    </tr>
    <tr>
        <td>
            <h2>Width</h2>
            <p>The maximum width of the content block.</p>
        </td>
        <td>
        <label>Width: <input type="text" class="hiddenInput" v-model="widget.settings.width"> px</label>
            <input 
                type="range" 
                min="455" 
                max="1366" 
                v-model="widget.settings.width"
            >
        </td>
    </tr>
    <tr>
        <td>
            <h2>Block Alignment</h2>
            <p>How the content block will be aligned to the page.</p>
        </td>
        <td>
            <div class="form-group">
                <select 
                    class="form-control" 
                    v-model="widget.settings.block_alignment" 
                >
                    <option value="block-center">Centered</option>
                    <option value="block-left">Left</option>
                    <option value="block-right">Right</option>
                </select>   
            </div>  
        </td>
    </tr>
    <tr>
        <td>
            <h2>Columns</h2>
            <p>How much horizontal space this block will take up. For example a 2 column layout would be 2 "Half" blocks.</p>
        </td>
        <td>
            <div class="form-group">
                <select 
                    class="form-control" 
                    v-model="widget.settings.layout_width"
                >
                    <option value="full-width">Full</option>
                    <option value="half-width">Half</option>
                </select>
            </div>  
        </td>
    </tr>
    <tr>
        <td>
            <h2>Limit Visibility</h2>
            <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
        </td>
        <td>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                </label>
            </div>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                </label>
            </div>
        </td>
    </tr>
    </tbody>
    </table>
</div>    
</template>

<script>
	export default {
		props: ['widget', 'create'],

		created: function()
		{
			if(this.create)
            {
                this.widget.title = 'Contact Form'
                this.widget.template = 'ContactForm'
                this.widget.settings = {
                    layout_width: 'full-width',
                    width: 683,
                    paddingTop: 60,
                    paddingBottom: 60,
                    block_alignment: 'block-center'
                }
                this.widget.content = ''
            }          
		}
	}
</script>
<template>
<div>
	<table class="table table-bordered  table-striped table-settings">
    <tbody>
    <tr>
        <td>
            <h2>Heading Text</h2>
            <p>A bold heading for introducing a section of your page.</p>
        </td>
        <td>
            <input type="text" v-model="widget.settings.header" class="form-control">
        </td>
    </tr>
    <tr>
        <td>
            <h2>Subheading text</h2>
            <p>This will appear below the heading and works great for providing a short intoduction.</p>
        </td>
        <td>
            <input type="text" v-model="widget.settings.subheader" class="form-control">
        </td>
    </tr>
    <tr>
        <td>
            <h2>Alignment</h2>
        </td>
        <td>
            <select v-model="widget.settings.alignment" class="form-control">
                <option value="text-left">Left</option>
                <option value="text-center">Center</option>
                <option value="text-right">Right</option>
            </select>
        </td>
    </tr>
    <tr>
        <td>
            <h2>Background Color</h2>
            <p>The background color of the content block.</p>
        </td>
        <td>
            <input type="text" v-model="widget.settings.background" class="form-control" id="backgroundColor">
        </td>
    </tr>
    <tr>
        <td>
            <h2>Text Color</h2>
            <p>The text color of the content block.</p>
        </td>
        <td>
            <select class="form-control" v-model="widget.settings.color">
                <option value="#3d3d3d">Dark</option>
                <option value="#FFFFFF">Light</option>
            </select>
        </td>
    </tr>
    <tr>
        <td>
            <h2>Padding</h2>
            <p>The top and bottom padding of the content block.</p>
        </td>
        <td>
            <div class="form-group">
                <label>Top: <input type="text" class="hiddenInput" v-model="widget.settings.paddingTop"> px</label>
                <input 
                    type="range" 
                    min="0" 
                    max="60" 
                    v-model="widget.settings.paddingTop" 
                >
            </div>
            <div class="form-group">
                <label>Bottom: <input type="text" class="hiddenInput" v-model="widget.settings.paddingBottom"> px</label>
                <input 
                    type="range" 
                    min="0" 
                    max="60" 
                    v-model="widget.settings.paddingBottom" 
                >
            </div>
        </td>
    </tr>
    <tr>
        <td>
            <h2>Columns</h2>
            <p>How much horizontal space this block will take up. For example a 2 column layout would be 2 "Half" blocks.</p>
        </td>
        <td>
            <div class="form-group">
                <select 
                    class="form-control" 
                    string
                    v-model="widget.settings.layout_width" 
                >
                    <option value="full-width">Full</option>
                    <option value="half-width">Half</option>
                </select>   
            </div>  
        </td>
    </tr>
    <tr>
        <td>
            <h2>Limit Visibility</h2>
            <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
        </td>
        <td>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                </label>
            </div>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                </label>
            </div>
        </td>
    </tr>
    </tbody>
</table>
</div>
</template>

<script>
    export default {
        props: ['widget', 'create'],

        created: function()
        {
            if(this.create)
            {
                this.widget.title = 'Text Header'
                this.widget.template = 'TextHeader'
                this.widget.settings = {
                    'header': 'Page Title',
                    'subheader': 'A short description of the page',
                    'alignment': 'text-left',
                    'background': '#FFFFFF',
                    'color': '#3d3d3d',
                    'paddingTop': 60,
                    'paddingBottom': 0,
                    'layout_width': 'full-width'
                }
            }
        },

        mounted: function() {
            let self = this;
            $("#backgroundColor").spectrum({
                preferredFormat: "hex",
                showInput: true,
                allowEmpty: true,
                appendTo: $('#widgetModal'),
                change: function(color) {
                    self.widget.settings.background = color.toHexString();
                }
            });
        }
    }
</script>
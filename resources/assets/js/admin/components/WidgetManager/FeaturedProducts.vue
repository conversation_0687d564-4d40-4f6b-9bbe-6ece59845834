<template>
<div>
	<table class="table table-bordered  table-striped table-settings">
    <tbody>
    <tr>
        <td>
            <h2>Products to Display</h2>
            <p>Display products from the selected collection.</p>
        </td>
        <td>
            <select v-model="widget.settings.collection" class="form-control">
                <option :value="null" disabled>Choose a product collection</option>
                <option v-for="collection in collections" :value="collection.id">{{ collection.title }}</option>
            </select>
        </td>
    </tr>
    <tr>
        <td>
            <h2>Product Count</h2>
            <p>The maximum number of products that will be displayed at one time.</p>
        </td>
        <td>
            <input type="text" v-model="widget.settings.count" class="form-control">
        </td>
    </tr>

    <tr>
        <td>
            <h2>Header Text</h2>
            <p>This will display above the products. Leave blank if you wish to hide.</p>
        </td>
        <td>
            <input type="text" v-model="widget.settings.header" class="form-control">
        </td>
    </tr>

    <tr>
        <td>
            <h2>Background Color</h2>
            <p>The background color of the content block.</p>
        </td>
        <td>
            <input type="text" v-model="widget.settings.background" class="form-control colorpicker--modal" id="backgroundColor">
        </td>
    </tr>
    <tr>
        <td>
            <h2>Layout</h2>
            <p>Select which layout style to display the products in.</p>
        </td>
        <td>
            <div class="form-group">
                <select 
                    class="form-control" 
                    v-model="widget.settings.layout_style"
                >
                    <option :value="null">Default</option>
                    <option value="style2">Comparison</option>
                </select>   
            </div>  
        </td>
    </tr>
    <tr v-show="widget.settings.layout_style == 'style2'">
        <td>
            <h2>Action Button</h2>
            <p>What should the call to action button do?</p>
        </td>
        <td>
            <select class="form-control" v-model="widget.settings.action_event">
                <option value="show_details">Show Details Page</option>
                <option value="add_to_cart">Add to Cart</option>
            </select>    
        </td>
    </tr>
    <tr v-show="widget.settings.layout_style == 'style2'">
        <td>
            <h2>Action Text</h2>
            <p>What should the call to action button say?</p>
        </td>
        <td>
            <input type="text" v-model="widget.settings.cta_text" class="form-control" placeholder="Buy Now">
        </td>
    </tr>
    <tr>
        <td>
            <h2>Columns</h2>
            <p>How much horizontal space this block will take up. For example a 2 column layout would be 2 "Half" blocks.</p>
        </td>
        <td>
            <div class="form-group">
                <select class="form-control" v-model="widget.settings.layout_width">
                    <option value="full-width">Full</option>
                    <option value="half-width">Half</option>
                </select>   
            </div>  
        </td>
    </tr>
    <tr>
        <td>
            <h2>Limit Visibility</h2>
            <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
        </td>
        <td>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                </label>
            </div>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                </label>
            </div>
        </td>
    </tr>
    </tbody>
</table>
</div>
</template>

<script>
	export default {
		props: ['widget', 'create'],

        props: {
            widget: {
                type: Object,
            },
            create: {
                type: Boolean
            }
        },

		created: function()
		{
            if(this.create)
            {
                this.widget.title = 'Products Feed'
                this.widget.template = 'FeaturedProducts'
                this.widget.settings = {
                    'count': 3,
                    'collection': null,
                    'header': 'Featured Products',
                    'background': '#FFF',
                    'layout_width': 'full-width',
                    'layout_style': 'style1'
                }
            }

            this.getCollections()
		},

        mounted: function() {
            let self = this;
            $("#backgroundColor").spectrum({
                preferredFormat: "hex",
                showInput: true,
                allowEmpty: true,
                appendTo: $('#widgetModal'),
                change: function(color) {
                    self.widget.settings.background = color.toHexString();
                }
            });
        },

        data: function()
        {
            return {
                'collections': ''
            }
        },

        methods: {
            'getCollections': function()
            {
                axios.get('/api/collections').then(function(response) {
                    this.collections = response.data;
                }.bind(this)).catch(function(error) {

                });
            }
        }
	}
</script>
<template>
<div>
<ul class="tabsList">
      <li :class="{'active': activeTab == 'appearance'}" @click="activeTab = 'appearance'">
        <a href="#">Appearance</a>
    </li>
    <li :class="{'active': activeTab == 'step1'}" @click="activeTab = 'step1'">
        <a href="#">Step 1</a>
    </li>
    <li :class="{'active': activeTab == 'step2'}" @click="activeTab = 'step2'">
        <a href="#">Step 2</a>
    </li>
    <li :class="{'active': activeTab == 'step3'}" @click="activeTab = 'step3'">
        <a href="#">Step 3</a>
    </li>
    </ul>
    <div v-show="activeTab == 'appearance'" class="linkListWidget__linkTab">
        <table class="table table-bordered  table-striped table-settings">
            <tbody>
                <tr>
                    <td>
                        <h2>Header</h2>
                        <p>Optional. Will display above the steps. Leave blank to hide.</p>
                    </td>
                    <td>
                        <input type="text" v-model="widget.settings.header" class="form-control">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Call To Action Text</h2>
                        <p>Optional. Will display above the steps. Leave blank to hide.</p>
                    </td>
                    <td>
                        <input type="text" v-model="widget.settings.cta" class="form-control">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Call To Action Url</h2>
                        <p>Optional. Will display above the steps. Leave blank to hide.</p>
                    </td>
                    <td>
                        <input type="text" v-model="widget.settings.cta_url" class="form-control">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Background Color</h2>
                        <p>The background color of the content block.</p>
                    </td>
                    <td>
                        <input type="text" v-model="widget.settings.background">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Columns</h2>
                        <p>How much horizontal space this block will take up. For example a 2 column layout would be 2 "Half" blocks.</p>
                    </td>
                    <td>
                        <div class="form-group">
                            <select class="form-control" v-model="widget.settings.layout_width">
                                <option value="full-width">Full</option>
                                <option value="half-width">Half</option>
                            </select>   
                        </div>  
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Limit Visibility</h2>
                        <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
                    </td>
                    <td>
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                            </label>
                        </div>
                        <div class="checkbox">
                            <label>
                                <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                            </label>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table> 
    </div>
    <div v-show="activeTab == 'step1'" class="linkListWidget__linkTab">
        <table class="table table-bordered  table-striped table-settings">
            <tbody>
                <tr>
                    <td>
                        <h2>Step 1 Icon</h2>
                    </td>
                    <td>
                        <input type="text" v-model="widget.settings.step_1_icon" class="form-control" placeholder="1">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Step 1 Title</h2>
                    </td>
                    <td>
                        <input type="text" v-model="widget.settings.step_1_title" class="form-control">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Step 1 Message</h2>
                    </td>
                    <td>
                        <textarea v-model="widget.settings.step_1_message" class="form-control" rows="5"></textarea>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <div v-show="activeTab == 'step2'" class="linkListWidget__linkTab">
        <table class="table table-bordered  table-striped table-settings">
            <tbody>
                <tr>
                    <td>
                        <h2>Step 2 Icon</h2>
                        <small>Copy & Paste HTML from <a href="http://fontawesome.io/icon/map-marker/">Font Awesome</a> to get icons.</small>
                    </td>
                    <td>
                        <input type="text" v-model="widget.settings.step_2_icon" class="form-control" placeholder="2">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Step 2 Title</h2>
                    </td>
                    <td>
                        <input type="text" v-model="widget.settings.step_2_title" class="form-control">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Step 2 Message</h2>
                    </td>
                    <td>
                        <textarea v-model="widget.settings.step_2_message" class="form-control" rows="5"></textarea>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
    <div v-show="activeTab == 'step3'" class="linkListWidget__linkTab">
        <table class="table table-bordered  table-striped table-settings">
            <tbody>
                <tr>
                    <td>
                        <h2>Step 3 Icon</h2>
                        <small>Copy & Paste HTML from <a href="http://fontawesome.io/icon/map-marker/">Font Awesome</a> to get icons.</small>
                    </td>
                    <td>
                        <input type="text" v-model="widget.settings.step_3_icon" class="form-control" placeholder="3">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Step 3 Title</h2>
                    </td>
                    <td>
                        <input type="text" v-model="widget.settings.step_3_title" class="form-control">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Step 3 Message</h2>
                    </td>
                    <td>
                        <textarea v-model="widget.settings.step_3_message" class="form-control" rows="5"></textarea>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>
</template>

<script>
	export default {
		props: ['widget', 'create'],

		created: function()
		{
            if(this.create)
            {
                this.widget.title = 'How It Works'
                this.widget.template = 'HowItWorks'
                this.widget.settings = {
                    'layout_width': 'full-width',
                    'header': 'How It Works',
                    'cta': 'Sign Up Now',
                    'cta_url': '/register',
                    'step_1_title': 'Sign Up',
                    'step_1_message': 'Create your free account and choose the pickup location closest to you. This is where we will meet you with your order.',
                    'step_2_title': 'Shop',
                    'step_2_message': 'Browse through our ever growing selection of pasture-raised products. Add the products you like to your order.',
                    'step_3_message': 'Meet us to pick up your order on your scheduled pickup date and time. We look forward to seeing you!',
                    'step_3_title': 'Pick Up'
                }
            }
		},

        mounted: function() {
            let self = this;
            $("#backgroundColor").spectrum({
                preferredFormat: "hex",
                showInput: true,
                allowEmpty: true,
                appendTo: $('#widgetModal'),
                change: function(color) {
                    self.widget.settings.background = color.toHexString();
                }
            });
        },

        data: function()
        {
            return {
                activeTab: 'appearance',
            }
        },
	}
</script>

<style>
.fontAwesome {
    font-family: FontAwesome;
}
</style>
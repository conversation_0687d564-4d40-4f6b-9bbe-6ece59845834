<template>
<div>
    <ul class="tabsList">
        <li :class="{'active': activeTab == 'appearance'}" @click="activeTab = 'appearance'">
            <a href="#">Appearance</a>
        </li>
        <li :class="{'active': activeTab == 'message'}" @click="activeTab = 'message'">
            <a href="#">Message</a>
        </li>
        <li :class="{'active': activeTab == 'links'}" @click="activeTab = 'links'">
            <a href="#">Calls To Action</a>
        </li>
    </ul>
    <div v-show="activeTab == 'appearance'" class="linkListWidget__linkTab">
        <table class="table table-bordered  table-striped table-settings">
            <tbody>
            <tr>
                <td>
                    <h2>Background Photo</h2>
                </td>
                <td>
                    <cover-photo :src="widget.settings.background" @input="updatePhoto"></cover-photo>
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Text Alignment</h2>
                </td>
                <td>
                    <select v-model="widget.settings.alignment" class="form-control">
                        <option value="text-left">Left</option>
                        <option value="text-center">Center</option>
                        <option value="text-right">Right</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Text Color</h2>
                </td>
                <td>
                    <select class="form-control" v-model="widget.settings.color">
                        <option value="light">Light</option>
                        <option value="dark">Dark</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Background Position</h2>
                </td>
                <td>
                    <select v-model="widget.settings.background_position" class="form-control">
                        <option value="left top">Left Top</option>
                        <option value="left center">Left Center</option>
                        <option value="left bottom">Left Bottom</option>
                        <option value="right top">Right Top</option>
                        <option value="right center">Right Center</option>
                        <option value="right bottom">Right Bottom</option>
                        <option value="center top">Center Top</option>
                        <option value="center center">Center Center</option>
                        <option value="center botom">Center Bottom</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Background Attachment</h2>
                </td>
                <td>
                    <select v-model="widget.settings.background_attachment" class="form-control">
                        <option value="scroll">Normal</option>
                        <option value="fixed">Move While Scrolling</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Background Size</h2>
                </td>
                <td>
                    <select v-model="widget.settings.background_size" class="form-control">
                        <option value="auto">Normal</option>
                        <option value="cover">Fill banner</option>
                        <option value="contain">Contain inside banner</option>
                    </select>
                </td>
            </tr>
            <tr v-show="widget.settings.background_size != 'cover'">
                <td>
                    <h2>Background Repeat</h2>
                </td>
                <td>
                    <select v-model="widget.settings.background_repeat" class="form-control">
                        <option value="no-repeat">No-Repeat</option>
                        <option value="repeat">Repeat</option>
                    </select>
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Padding</h2>
                    <p>Control the height of the banner with top and bottom padding.</p>
                </td>
                <td>
                    <div class="form-group">
                    <label>Top <span id="paddingTopDisplay">{{ widget.settings.paddingTop }}px</span></label>
                        <input 
                            type="range" 
                            min="0" 
                            max="300" 
                            v-model="widget.settings.paddingTop"
                        >
                    </div>
                    <div class="form-group">
                    <label>Bottom <span id="paddingBottomDisplay">{{ widget.settings.paddingBottom }}px</span></label>
                        <input 
                            type="range" 
                            min="0" 
                            max="300" 
                            v-model="widget.settings.paddingBottom"
                        >
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Heading Font Size</h2>
                </td>
                <td>
                    <div class="form-group">
                    <label>{{ widget.settings.header_font_size }}px</label>
                        <input 
                            type="range" 
                            class="" 
                            min="40" 
                            max="80"  
                            v-model="widget.settings.header_font_size"
                        >
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Limit Visibility</h2>
                    <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
                </td>
                <td>
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                        </label>
                    </div>
                    <div class="checkbox">
                        <label>
                            <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                        </label>
                    </div>
                </td>
            </tr>
            </tbody>
        </table>
    </div>
    <div v-show="activeTab == 'message'" class="linkListWidget__linkTab">
    <table class="table table-bordered  table-striped table-settings">
        <tbody>
            <tr>
                <td>
                    <h2>Heading</h2>
                    <p>Your main message. Leave blank to hide.</p>
                </td>
                <td>
                    <textarea
                        v-model="widget.settings.header"
                        class="form-control"
                    ></textarea>
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Message</h2>
                    <p>Appears in smaller text below the heading. Leave blank to hide.</p>
                </td>
                <td>
                    <textarea v-model="widget.settings.message" class="form-control"></textarea>
                </td>
            </tr>
        </tbody>
    </table>        
    </div>
    <div v-show="activeTab == 'links'" class="linkListWidget__linkTab">
    <table class="table table-bordered  table-striped table-settings">
        <tbody>
            <tr>
                <td>Show Call To Action</td>
                <td>
                    <div class="radio">
                        <label class="radio-inline">
                            <input type="radio" :value="true" v-model="widget.settings.cta_show"> Yes
                        </label>
                        <label class="radio-inline">
                            <input type="radio" :value="false" v-model="widget.settings.cta_show"> No
                      </label>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Call To Action Text</h2>
                </td>
                <td>
                    <input type="text" v-model="widget.settings.cta" :disabled="!widget.settings.cta_show" class="form-control">
                </td>
            </tr>

            <tr>
                <td>
                    <h2>Call To Action Url</h2>
                </td>
                <td>
                    <input type="text" v-model="widget.settings.cta_url" :disabled="!widget.settings.cta_show" class="form-control">
                </td>
            </tr>
        </tbody>
    </table>

    <table class="table table-bordered  table-striped table-settings">
        <tbody>
            <tr>
                <td>Show Secondary Call To Action</td>
                <td>
                    <div class="radio">
                      <label class="radio-inline">
                        <input type="radio" :value="true" v-model="widget.settings.cta_2_show"> Yes
                        </label>
                      <label class="radio-inline">
                        <input type="radio" :value="false" v-model="widget.settings.cta_2_show"> No
                      </label>
                    </div>
                </td>
            </tr>
            <tr>
                <td>
                    <h2>Secondary Call To Action Text</h2>
                </td>
                <td>
                    <input type="text" v-model="widget.settings.cta_2" :disabled="!widget.settings.cta_2_show" class="form-control">
                </td>
            </tr>

            <tr>
                <td>
                    <h2>Secondary Call To Action Url</h2>
                </td>
                <td>
                    <input type="text" v-model="widget.settings.cta_2_url" :disabled="!widget.settings.cta_2_show" class="form-control">
                </td>
            </tr>
        </tbody>
    </table>
    </div>
</div>
</template>

<script>
    import CoverPhoto from '../MediaManager/CoverPhoto.vue'
	export default {

        components: {
            CoverPhoto,
        },

		props: {
                widget: {
                    type: Object
                },
                page: {
                    type: String
                },
                create: {
                    type: Boolean
                }
            },

		created: function()
		{
            if(this.create)
            {
                this.widget.title = 'Banner'
                this.widget.template = 'Banner'
                this.widget.settings = {
                    'alignment': 'text-center',
                    'header': 'From Our Farm to Your Table',
                    'message': 'Pasture-raised foods delivered to your neighborhood',
                    'background': 'https://s3.amazonaws.com/grazecart/stockphotos/banner.jpg',
                    'color': 'light',
                    'cta_show': false,
                    'cta': 'Shop Now',
                    'cta_url': '/store',
                    'cta_2_show': false,
                    'cta_2': 'Sign Up',
                    'cta_2_url': '/signup',
                    'background_position': 'center center',
                    'background_attachment': 'scroll',
                    'background_repeat': 'no-repeat',
                    'background_size': 'cover',
                    'paddingTop': '80',
                    'paddingBottom': '80',
                    'header_font_size': 40
                }
            }

            if(this.widget.settings.cta)
            {
                this.$set(this.widget.settings, 'cta_show', true);
            }
		},

        watch: {
            'widget.settings.cta_show': function(value)
            {
                if(!value)
                {
                    this.widget.settings.cta = ''
                    this.widget.settings.cta_url = ''
                }
                else
                {
                    if(!this.widget.settings.cta)
                    {
                        this.widget.settings.cta = 'Sign Up'
                        this.widget.settings.cta_url = '/register'
                    }
                }
            },
            'widget.settings.cta_2_show': function(value)
            {
                if(!value)
                {
                    this.widget.settings.cta_2 = ''
                    this.widget.settings.cta_2_url = ''
                }
                else
                {
                    this.widget.settings.cta_2 = 'Shop Now'
                    this.widget.settings.cta_2_url = '/store'
                }
            }
        },

        data: function()
        {
            return {
                activeTab: 'appearance',
            }
        },

        methods: {
            updatePhoto: function(photo) {
                this.widget.settings.background = photo.path;
            }
        }
	}
</script>
<template>
    <div>
        <ul class="tabsList">
            <li :class="{'active': activeTab == 'content'}" @click="activeTab = 'content'">
                <a href="#">Content</a>
            </li>
            <li :class="{'active': activeTab == 'appearance'}" @click="activeTab = 'appearance'">
                <a href="#">Appearance</a>
            </li>
        </ul>
        <div v-show="activeTab == 'content'" class="linkListWidget__linkTab">
            <text-editor :content="widget.content" :offset="0" name="pageBody" @input="updateContent"></text-editor>
        </div>

        <div v-show="activeTab == 'appearance'" class="linkListWidget__linkTab">
            <table class="table table-bordered  table-striped table-settings">
                <tbody>
                <tr>
                    <td>
                        <h2>Width</h2>
                        <p>The maximum width of the text. Make your text comfortable to read by ensuring a single line of text is not too long. A good default
                            is 683px.</p>
                    </td>
                    <td>
                        <label>{{ widget.settings.width }}px</label>
                        <input
                            v-model="widget.settings.width"
                            max="1366"
                            min="455"
                            type="range"
                        >
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Background Color</h2>
                        <p>The background color of the content block.</p>
                    </td>
                    <td>
                        <input v-model="widget.settings.background" class="form-control colorpicker--modal" type="text">
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Padding</h2>
                        <p>The top and bottom padding of the content block.</p>
                    </td>
                    <td>
                        <div class="form-group">
                            <label>Top: <input v-model="widget.settings.paddingTop" class="hiddenInput" type="text"> px</label>
                            <input
                                v-model="widget.settings.paddingTop"
                                max="60"
                                min="0"
                                type="range"
                            >
                        </div>
                        <div class="form-group">
                            <label>Bottom: <input v-model="widget.settings.paddingBottom" class="hiddenInput" type="text"> px</label>
                            <input
                                v-model="widget.settings.paddingBottom"
                                max="60"
                                min="0"
                                type="range"
                            >
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Columns</h2>
                        <p>How much horizontal space this block will take up. For example a 2 column layout would be 2 "Half" blocks.</p>
                    </td>
                    <td>
                        <div class="form-group">
                            <select v-model="widget.settings.layout_width" class="form-control">
                                <option value="full-width">Full</option>
                                <option value="half-width">Half</option>
                            </select>
                        </div>
                    </td>
                </tr>
                <tr>
                    <td>
                        <h2>Limit Visibility</h2>
                        <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
                    </td>
                    <td>
                        <div class="checkbox">
                            <label>
                                <input v-model="widget.settings.show_when_auth" type="checkbox"> Only show when signed in
                            </label>
                        </div>
                        <div class="checkbox">
                            <label>
                                <input v-model="widget.settings.show_when_guest" type="checkbox">Only show when <strong>not</strong> signed in
                            </label>
                        </div>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    </div>
</template>

<script>
import TextEditor from '../TextEditor.vue';

export default {
    props: ['widget', 'create'],

    components: {
        TextEditor
    },

    created: function() {
        if (this.create) {
            this.widget.title = 'Text';
            this.widget.template = 'RichText';
            this.widget.content = '',
                this.widget.settings = {
                    'layout_width': 'full-width',
                    'width': 683,
                    'paddingTop': 60,
                    'paddingBottom': 60
                };
        }
    },

    mounted: function() {
        console.log('hello');
        let self = this;
        $('.colorpicker--modal').spectrum({
            preferredFormat: 'hex',
            showInput: true,
            allowEmpty: true,
            appendTo: $('#widgetModal'),
            change: function(color) {
                self.widget.settings.background = color.toHexString();
            }
        });
    },

    data: function() {
        return {
            activeTab: 'content'
        };
    },

    unmounted: function() {
        $('#pageBodyEditor').redactor('core.destroy');
    },

    methods: {
        updateContent: function(content) {
            this.widget.content = content;
        }
    },

    events: {
        createWidget: function() {
            let redactor = $('#pageBodyEditor').redactor('core.object');
            this.widget.content = redactor.code.get();
        },
        updateWidget: function() {
            let redactor = $('#pageBodyEditor').redactor('core.object');
            this.widget.content = redactor.code.get();
        }
    }
};
</script>

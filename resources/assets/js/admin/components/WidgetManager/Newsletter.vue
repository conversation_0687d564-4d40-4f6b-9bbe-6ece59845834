<template>
<div>
	<table class="table table-bordered  table-striped table-settings">
    <tbody>
    <tr>
        <td>
            <h2>Heading</h2>
        </td>
        <td>
            <input type="text" v-model="widget.settings.header" class="form-control">
        </td>
    </tr>
    <tr>
        <td>
            <h2>Background Color</h2>
        </td>
        <td>
            <input type="text" v-model="widget.settings.background" class="form-control colorpicker--modal" id="backgroundColor">
        </td>
    </tr>
    <tr>
        <td>
            <h2>Text Color</h2>
        </td>
        <td>
            <select class="form-control" v-model="widget.settings.fontColor">
                <option value="#3d3d3d">Dark</option>
                <option value="#FFFFFF">Light</option>
            </select>
        </td>
    </tr>
    <tr>
        <td>
            <h2>Limit Visibility</h2>
            <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
        </td>
        <td>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                </label>
            </div>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                </label>
            </div>
        </td>
    </tr>
    <tr>
        <td>
            <h2>Columns</h2>
            <p>How much horizontal space this block will take up. For example a 2 column layout would be 2 "Half" blocks.</p>
        </td>
        <td>
            <div class="form-group">
                <select class="form-control" v-model="widget.settings.layout_width">
                    <option value="full-width">Full</option>
                    <option value="half-width">Half</option>
                </select>   
            </div>  
        </td>
    </tr>
    </tbody>
</table>
</div>
</template>

<script>
    export default {
        props: ['widget', 'create'],

        created: function()
        {
            if(this.create)
            {
                this.widget.title = 'Newsletter Signup Form'
                this.widget.template = 'Newsletter'
                this.widget.settings = {
                    'layout_width': 'full-width',
                    'list': '1',
                    'header': 'Join Our Newsletter',
                    'background': 'transparent',
                    'fontColor': '#3d3d3d'
                }
            }
        },

        mounted: function() {
            let self = this;
            $("#backgroundColor").spectrum({
                preferredFormat: "hex",
                showInput: true,
                allowEmpty: true,
                appendTo: $('#widgetModal'),
                change: function(color) {
                    self.widget.settings.background = color.toHexString();
                }
            });
        }
    }
</script>
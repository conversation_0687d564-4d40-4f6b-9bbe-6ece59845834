<template>
<div>
	<table class="table table-bordered  table-striped table-settings">
    <tbody>
    <tr>
        <td>
            <h2>Title</h2>
            <p>The text will appear in the button.</p>
        </td>
        <td>
            <div class="form-group">
                <input type="text" v-model="widget.settings.title" class="form-control"> 
            </div>  
        </td>
    </tr>
    <tr>
        <td>
            <h2>URL</h2>
            <p>To link to external sites be sure to preceed your url with <em>"http://"</em>. Otherwise all urls are relative your domain.</p>
        </td>
        <td>
            <div class="form-group">
                <input type="text" v-model="widget.settings.url" class="form-control"> 
            </div>  
        </td>
    </tr>
    <tr>
        <td>
            <h2>Style</h2>
            <p>The color style of the button. This will reflect what is set under your theme color settings.</p>
        </td>
        <td>
            <div class="form-group">
                <select class="form-control" v-model="widget.settings.style">
                    <option value="btn-action">Action</option>
                    <option value="btn-brand">Brand</option>
                </select>   
            </div>  
        </td>
    </tr>
    <tr>
        <td>
            <h2>Background Color</h2>
            <p>The background color of the content block.</p>
        </td>
        <td>
            <input type="text" v-model="widget.settings.background" class="form-control colorpicker--modal" id="backgroundColor">
        </td>
    </tr>
    <tr>
        <td>
            <h2>Padding</h2>
            <p>The top and bottom padding of the content block.</p>
        </td>
        <td>
            <div class="form-group">
            <label>Top: <input type="text" class="hiddenInput" v-model="widget.settings.paddingTop"> px</label>
                <input 
                    type="range" 
                    min="0" 
                    max="60" 
                    v-model="widget.settings.paddingTop"
                >
            </div>
            <div class="form-group">
            <label>Bottom: <input type="text" class="hiddenInput" v-model="widget.settings.paddingBottom"> px</label>
                <input 
                    type="range" 
                    min="0" 
                    max="60" 
                    v-model="widget.settings.paddingBottom"
                >
            </div>
        </td>
    </tr>
    <tr>
        <td>
            <h2>Limit Visibility</h2>
            <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
        </td>
        <td>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                </label>
            </div>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                </label>
            </div>
        </td>
    </tr>
    </tbody>
    </table>
</div>    
</template>

<script>
	export default {
		props: ['widget', 'create'],

		created: function()
		{
			if(this.create)
            {
                this.widget.title = 'Button'
                this.widget.template = 'CtaButton'
                this.widget.settings = {
                    url: '/',
                    title: 'My Button',
                    style: 'btn-action',
                    paddingTop: 60,
                    paddingBottom: 60
                }
                this.widget.content = ''
            }
		},

        mounted: function() {
            let self = this;
            $("#backgroundColor").spectrum({
                preferredFormat: "hex",
                showInput: true,
                allowEmpty: true,
                appendTo: $('#widgetModal'),
                change: function(color) {
                    self.widget.settings.background = color.toHexString();
                }
            });
        }
	}
</script>
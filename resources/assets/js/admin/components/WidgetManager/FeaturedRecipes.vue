<template>
<div>
	<table class="table table-bordered  table-striped table-settings">
    <tbody>
    <tr>
        <td>
            <h2>Recipe Source</h2>
            <p>Control which recipes are show based on their assigned tags.</p>
        </td>
        <td>
            <select v-model="widget.settings.source" class="form-control">
                <option value="most_recent">Show most recently published</option>
                <option v-for="tag in tags" :value="tag.id">{{ tag.title }}</option>
            </select>
        </td>
    </tr>
    <tr>
        <td>
            <h2>Recipe Count</h2>
            <p>The maximum number of recipes that will be displayed at one time.</p>
        </td>
        <td>
            <input type="text" v-model="widget.settings.count" class="form-control">
        </td>
    </tr>

    <tr>
        <td>
            <h2>Header Text</h2>
            <p>This will display above the recipes. Leave this blank if you wish to hide.</p>
        </td>
        <td>
            <input type="text" v-model="widget.settings.header" class="form-control">
        </td>
    </tr>

    <tr>
        <td>
            <h2>Background Color</h2>
            <p>The background color of the content block.</p>
        </td>
        <td>
            <input type="text" v-model="widget.settings.background" class="form-control colorpicker--modal" id="backgroundColor">
        </td>
    </tr>
    <tr>
        <td>
            <h2>Limit Visibility</h2>
            <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
        </td>
        <td>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                </label>
            </div>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                </label>
            </div>
        </td>
    </tr>
    <tr>
        <td>
            <h2>Columns</h2>
            <p>How much horizontal space this block will take up. For example a 2 column layout would be 2 "Half" blocks.</p>
        </td>
        <td>
            <div class="form-group">
                <select class="form-control" v-model="widget.settings.layout_width">
                    <option value="full-width">Full</option>
                    <option value="half-width">Half</option>
                </select>   
            </div>  
        </td>
    </tr>
    </tbody>
</table>
</div>
</template>

<script>
    export default {
        props: ['widget', 'create'],

        created: function()
        {
            if(this.create)
            {
                this.widget.title = 'Recipes Feed'
                this.widget.template = 'FeaturedRecipes'
                this.widget.settings = {
                    'count': 3,
                    'source': 'most_recent',
                    'header': 'Featured Recipes',
                    'background': '#FFF',
                    'layout_width': 'full-width'
                }
            }

            this.getTags()
        },

        mounted: function() {
            let self = this;
            $("#backgroundColor").spectrum({
                preferredFormat: "hex",
                showInput: true,
                allowEmpty: true,
                appendTo: $('#widgetModal'),
                change: function(color) {
                    self.widget.settings.background = color.toHexString();
                }
            });
        },

        data: function()
        {
            return {
                'tags': ''
            }
        },

        methods: {
            'getTags': function()
            {

                axios.get('/api/tags/recipe').then(function(response) {
                    this.tags = response.data;
                }.bind(this)).catch(function(error) {

                });
            }
        }
    }
</script>
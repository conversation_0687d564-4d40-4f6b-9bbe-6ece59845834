<template>
    <div
        @click.stop
        v-click-outside="close"
    >
        <div class="panel">
            <div class="panel-heading">
                <p class="m-0">Product Variants</p>
                <p class="m-0 mt-1 font-normal text-gray-500 text-xs">Add your variants below. Pre-order products cannot be added as variants.</p>
            </div>
            <div class="panel-body" style="overflow: visible">
                <div class="select">
                    <input
                        type="text"
                        v-model="q"
                        @focus="showResults = true"
                        @keyup="fetchProducts"
                        class="form-control"
                        autocomplete="off"
                        placeholder="Search for products to add"
                        tabindex="1"
                    />
                    <ul class="select-results" v-if="showResults && q.length">
                        <li v-if="products.length === 0">
                            <b>No results found</b>
                        </li>
                        <li v-else v-for="product in products" :key="product.id" :class="[product.type_id === 4 ? 'bg-gray-100' : '']">
                            <span
                                @click="toggleVariant(product, $event)"
                                @keypress.enter="toggleVariant(product, $event)"
                                role="button"
                                tabindex="1"
                            >
                                <i :class="['fa fa-lg', product.active ? 'fa-check-circle-o' : 'fa-circle-o']"></i>
                                <span :class="[product.type_id === 4 ? 'cursor-not-allowed' : 'cursor-pointer']">
                                    <span class="inline-block" v-text="product.title"></span>
                                    <span v-if="product.type_id === 4" class="inline-block italic">&nbsp;(Unavailable)</span>
                                </span>
                            </span>
                        </li>
                    </ul>
                </div>
                <div>
                    <span v-if="!variants.length" class="text-xs">This product does not have any variants.</span>
                    <transition-group
                        v-else
                        tag="ul"
                        class="draggable-list"
                        name="fade"
                        v-sortable="options"
                    >
                        <li v-for="variant in variants" :key="variant.id" class="list-group-item">
                            <span class="fa draghandle" title="Drag to reorder"></span>
                            <a :href="`/admin/products/${variant.id}/edit`" v-text="variant.title"></a>
                            <button type="button" @click="removeVariant(variant, $event)" class="pull-right btn btn-alt btn-sm pa-0">
                                <i class="fa fa-times"></i>
                            </button>
                        </li>
                    </transition-group>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: ["id", "model"],

    data: function () {
        return {
            showResults: false,
            q: "",
            products: [],
            variants: [],
            options: {
                onUpdate: this.updateSort,
                handle: ".draghandle",
                animation: 150,
                group: {
                    name: "segments",
                    pull: false,
                    put: true,
                },
            },
        };
    },

    created() {
        this.fetchVariants();
    },

    methods: {
        fetchProducts() {
            if (this.q.length <= 0) {
                return this.products = [];
            }

            axios.get('/api/products', {
                params: { products: this.q, limit: 15 }
            })
                .then(({ data }) => {
                    this.products = data;
                })
                .catch(error => {});
        },

        fetchVariants() {
            axios.get(`/admin/products/${this.id}/variants`)
                .then(({ data }) => {
                    this.variants = data;
                })
                .catch(error => {});
        },

        toggleVariant(variant, e) {
            e.stopPropagation();
            e.preventDefault();

            if (variant.type_id === 4) return;

            variant.active = true;

            axios.post(`/admin/products/${this.id}/variants`, {
                variant_id: variant.id
            })
                .then(() => {
                    this.fetchVariants();
                    this.showResults = false;
                })
                .catch(error => {});
        },

        removeVariant(variant, e) {
            variant.active = false;

            axios.delete(`/admin/products/${this.id}/variants/${variant.id}`)
                .then(() => {
                    this.fetchVariants();
                    this.showResults = false;
                })
                .catch(error => {});
        },

        close() {
            this.showResults = false;
        },

        updateSort(event) {
            eventHub.emit("showProgressBar");

            this.variants.splice(event.newIndex, 0, this.variants.splice(event.oldIndex, 1)[0])

            eventHub.emit("hideProgressBar");

            const variants = this.variants.map((variant, idx) => ({
                product_id: this.id,
                variant_id: variant.id,
                sort:  idx + 1,
                title: variant.title
            }))

            axios.put(`/admin/products/${this.id}/variants`, {
                    action: 'update_sort', sortable_item: variants
                })
                .then(() => {
                    this.showResults = false;
                })
                .catch(error => {});
        },
    },
};
</script>

<style scoped>
.variantBody {
    overflow: visible !important;
}
.list-group-item {
    display: flex;
    align-items: center;
    justify-content: space-between;
}
.fa-times {
    color: #dddd;
}
.fa-times:hover {
    color: #ff0000;
}
</style>
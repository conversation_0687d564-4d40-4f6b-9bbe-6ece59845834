<template>
	<select 
		class="form-control" 
		tabindex="1" 
		:name="name"
        :value="modelValue"
        @change=" $emit('update:modelValue', $event.target.value)"
    >
		<option value="">Select an option</option>
		<option v-for="(value, key) in options" :key="key" :value="key" v-text="value"></option>
	</select>
</template>

<script>
	export default {
		props: ['name', 'modelValue', 'url'],

        emits: ['update:modelValue'],

        created() {
			this.getOptions();
		},

		data() {
			return {
				options: { 0: 'Loading options...' },
			}
		},

		methods: {
			getOptions() {
	            axios.get(this.url)
	                .then(response => this.options = response.data)
	                .catch(error => this.options = { 0: 'No results' });
			},
		}
	}
</script>
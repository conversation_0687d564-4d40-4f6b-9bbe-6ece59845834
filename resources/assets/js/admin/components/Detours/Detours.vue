<template>
    <div>
        <div class="bg-white p-4 sm:p-6 lg:p-8">
            <div class="sm:flex sm:items-center">
                <div class="sm:flex-auto">
                    <h1 class="text-base font-semibold text-gray-900">Redirects</h1>
                    <p class="mt-2 text-sm text-gray-700">A list of redirects on the site, including their response status.</p>
                </div>
                <div class="mt-4 sm:ml-16 sm:mt-0 sm:flex-none">
                    <button class="block rounded-md bg-keppel-600 px-3 py-2 text-center text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600" onclick="eventHub.emit('detours:add')" type="button">Add redirect</button>
                </div>
            </div>
            <div class="mt-8 flow-root">
                <div class="-mx-4 -my-2 overflow-x-auto sm:-mx-6 lg:-mx-8">
                    <div class="inline-block min-w-full py-2 align-middle sm:px-6 lg:px-8">
                        <table class="min-w-full divide-y divide-gray-300">
                            <thead>
                            <tr>
                                <th class="py-3.5 pl-4 pr-3 text-left text-sm font-semibold text-gray-900 sm:pl-0" scope="col">Redirect</th>
                                <th class="px-3 py-3.5 text-left text-sm font-semibold text-gray-900" scope="col">Status</th>
                                <th class="relative py-3.5 pl-3 pr-4 sm:pr-0" scope="col">
                                    <span class="sr-only">Edit</span>
                                </th>
                            </tr>
                            </thead>
                            <tbody class="divide-y divide-gray-200 bg-white">
                            <tr v-for="detour in detours">
                                <td class="whitespace-nowrap py-5 pl-4 pr-3 text-sm sm:pl-0">
                                    <div class="flex items-center">
                                        <div class="h-11 w-11 flex items-end justify-center flex-shrink-0">
                                            <svg class="size-6" fill="none" stroke="currentColor" stroke-width="1.5" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                                                <path d="m16.49 12 3.75 3.75m0 0-3.75 3.75m3.75-3.75H3.74V4.499" stroke-linecap="round" stroke-linejoin="round"/>
                                            </svg>
                                        </div>
                                        <div class="ml-4">
                                            <div class="font-medium text-gray-900">{{ detour.from_url }}</div>
                                            <div class="mt-1 text-gray-500">{{ detour.to_url }}</div>
                                        </div>
                                    </div>
                                </td>

                                <td class="whitespace-nowrap px-3 py-5 text-sm text-gray-500">
                                    <span v-if="detour.status_code == 301" class="inline-flex items-center rounded-md bg-green-50 px-2 py-1 text-xs font-medium text-green-700 ring-1 ring-inset ring-green-600/20" v-text="detour.status_code"></span>
                                    <span v-if="detour.status_code == 302" class="inline-flex items-center rounded-md bg-yellow-50 px-2 py-1 text-xs font-medium text-yellow-700 ring-1 ring-inset ring-yellow-600/20" v-text="detour.status_code"></span>
                                </td>
                                <td class="relative whitespace-nowrap py-5 pl-3 pr-4 text-right text-sm font-medium sm:pr-0">
                                    <!--                                <a href="#" class="text-keppel-600 hover:text-keppel-900">Edit<span class="sr-only">, Lindsay Walton</span></a>-->
                                    <button class="btn btn-alt btn-sm" @click="edit(detour)">Edit</button>
                                    <button class="btn btn-alt btn-sm" @click="remove(detour)">Delete</button>
                                </td>
                            </tr>

                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <div id="createRedirectModal" class="gc-modal gc-modal-mask" @click="hideModal('createRedirectModal')">
            <div class="gc-modal-wrapper">
                <div class="gc-modal-container" @click.stop>

                    <div class="gc-modal-header">
                        Create Redirect
                    </div>

                    <div class="gc-modal-body">
                        <div class="form-group">
                            <label>Origin URL</label>
                            <input v-model="detour.from_url" class="form-control" type="text">
                        </div>
                        <div class="form-group">
                            <label>Destination URL</label>
                            <input v-model="detour.to_url" class="form-control" type="text">
                        </div>
                        <div class="form-group">
                            <label>Status Code</label>
                            <select
                                v-model="detour.status_code"
                                class="form-control"
                            >
                                <option value="301">301 (Permanent)</option>
                                <option value="302">302 (Temporary)</option>
                            </select>
                        </div>
                    </div>

                    <div class="gc-modal-footer">
                        <button class="btn btn-alt" type="button" @click="hideModal('createRedirectModal')">Cancel</button>
                        <button
                            :disabled="!detour.from_url || !detour.to_url || detour.from_url == detour.to_url"
                            class="btn btn-action"
                            type="button"
                            @click="store(detour)"
                        >Create Redirect
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div id="editRedirectModal" class="gc-modal gc-modal-mask" @click="hideModal('editRedirectModal')">
            <div class="gc-modal-wrapper">
                <div class="gc-modal-container" @click.stop>
                    <div class="gc-modal-header">
                        Edit Redirect
                    </div>

                    <div class="gc-modal-body">
                        <div class="form-group">
                            <label>Origin URL</label>
                            <input v-model="detour.from_url" class="form-control" type="text">
                        </div>
                        <div class="form-group">
                            <label>Destination URL</label>
                            <input v-model="detour.to_url" class="form-control" type="text">
                        </div>
                        <div class="form-group">
                            <label>Status Code</label>
                            <select
                                v-model="detour.status_code"
                                class="form-control"
                            >
                                <option value="301">301 (Permanent)</option>
                                <option value="302">302 (Temporary)</option>
                            </select>
                        </div>
                    </div>

                    <div class="gc-modal-footer">
                        <button class="btn btn-alt" type="button" @click="hideModal('editRedirectModal')">Cancel</button>
                        <button
                            :disabled="!detour.from_url || !detour.to_url || detour.from_url == detour.to_url"
                            class="btn btn-action"
                            type="button"
                            @click="update(detour)"
                        >Save
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div id="deleteRedirectModal" class="gc-modal gc-modal-mask" @click="hideModal('deleteRedirectModal')">
            <div class="gc-modal-wrapper">
                <div class="gc-modal-container" @click.stop>

                    <div class="gc-modal-header">
                        Delete Redirect
                    </div>

                    <div class="gc-modal-body">
                        Are you sure you want to delete this redirect?
                    </div>

                    <div class="gc-modal-footer">
                        <button class="btn btn-alt" type="button" @click="hideModal('deleteRedirectModal')">Cancel</button>
                        <button class="btn btn-danger" type="button" @click="destroy(detour)">Delete Redirect</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    created() {
        eventHub.on('detours:add', this.create);
        eventHub.on('editRedirectModal:closed', () => this.detour = {});

        this.fetchRedirects();
    },

    data: () => ({
        detours: [],
        detour: {}
    }),

    methods: {
        fetchRedirects() {
            axios.get('/api/detours')
                .then(({ data }) => {
                    this.detours = data;
                })
                .catch(error => {
                });
        },

        create() {
            this.detour = {
                'id': null,
                'from_url': '',
                'to_url': '',
                'status_code': 301
            };

            this.showModal('createRedirectModal');
        },

        store(detour) {
            eventHub.emit('showProgressBar');

            axios.post('/api/detours', detour)
                .then(() => {
                    eventHub.emit('hideProgressBar');
                    this.hideModal('createRedirectModal');
                    this.fetchRedirects();
                }).catch(error => {
                eventHub.emit('hideProgressBar');
                eventHub.emit('error', error);
            });
        },

        edit(detour) {
            this.detour = detour;
            this.showModal('editRedirectModal');
        },

        update(detour) {
            eventHub.emit('showProgressBar');

            axios.put(`/api/detours/${detour.id}`, detour)
                .then(() => {
                    this.hideModal('editRedirectModal');
                    this.fetchRedirects();

                    eventHub.emit('hideProgressBar');
                    eventHub.emit('notify', { level: 'info', message: 'Changes Saved' });
                })
                .catch(error => {
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                });
        },

        remove(detour) {
            this.detour = detour;
            this.showModal('deleteRedirectModal');
        },

        destroy(detour) {
            eventHub.emit('showProgressBar');
            axios.delete(`/api/detours/${detour.id}`, detour)
                .then(() => {
                    this.detour = {};
                    this.hideModal('deleteRedirectModal');
                    eventHub.emit('hideProgressBar');
                    this.fetchRedirects();
                })
                .catch(error => {
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                });
        }
    }
};
</script>

<template>
    <div>
        <div class="gc-modal gc-modal-mask" id="orderEventsModal" @click="hideModal('orderEventsModal')">
            <div class="gc-modal-wrapper">
                <div class="gc-modal-container gc-modal-container--large" @click.stop>

                    <div class="gc-modal-header">
                        Order Events
                    </div>

                    <div class="gc-modal-body">
                        <table class="table">
                            <tbody>
                            <tr v-for="event in events" :key="event.id">
                                <td><span class="label label-light" :class="event.event_id"></span></td>
                                <td>{{ event.description}}</td>
                                <td>
                                    <span v-if="event.user" :title="event.user.email">{{ event.user.first_name }} {{ event.user.last_name }}</span>
                                    <span v-else title="User deleted" class="italic">User deleted</span>
                                </td>
                                <td>{{ dayjs(event.created_at).format('YYYY-MM-DD hh:mm:ss A') }}</td>
                            </tr>
                            <tr v-if="!events.length">
                                <td>There are no events recorder for this order.</td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="gc-modal-footer">
                        <button type="button" class="btn btn-alt" @click="hideModal('orderEventsModal')">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import dayjs from "dayjs";

export default {
    props: {
        order: { required: true }
    },

    created() {
        eventHub.on('orderEventsModal:opened', this.fetch);
    },

    data() {
        return {
            events: [],
        }
    },

    methods: {
        dayjs,
        fetch() {
            axios.get(`/api/orders/${this.order.id}/events`)
                .then(({ data }) => {
                    this.events = data;
                })
                .catch(error => {
                    eventHub.emit('error', error);
                })
        }
    }
}
</script>
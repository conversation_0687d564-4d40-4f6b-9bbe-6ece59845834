<template>
	<div class="orderFees" id="orderFees">
		<div class="panel">
			<div class="panel-heading flex align-items-m">
				<div class="flex-item">Fees</div>
				<div class="flex-item push-right">
					<a href="#" class="btn btn-sm btn-alt" @click.prevent="addFee = !addFee"><i class="fa fa-plus-circle fa-lg"></i> Add Fee</a>
				</div>
			</div>
			<div class="panel-body pr-0 pl-0">
				<table class="table table-striped table-compact table-full">
					<thead>
						<tr>
							<th>Name</th>
							<th>Qty.</th>
							<th>Amount</th>
							<th></th>
							<th colspan="100%">Subtotal</th>
						</tr>	
					</thead>
					<tbody>
						<transition name="fade">
						<tr v-if="addFee">
							<td>
								<input type="text" v-model="newFee.title" class="form-control" placeholder="Handling fee" autofocus>
							</td>
							<td>
								<input type="number" min="0" v-model="newFee.qty" class="form-control input-sm" placeholder="Qty.">
							</td>
							<td>
								<input type="number" min="0" v-model="newFee.amount" class="form-control input-sm" placeholder="Amount">
							</td>
							<td colspan="100%">
								<button 
									type="button" 
									class="btn btn-light" 
									:class="{'btn-action': newFee.title.length > 0 && newFee.amount.length > 0 }"
									:disabled="newFee.title.length === 0 || newFee.amount.length === 0"
									@click="add()"
								>Add Fee</button>
								<button type="button" class="btn btn-alt" @click="addFee = false">Cancel</button>
							</td>
						</tr>
						</transition>
			    		<tr v-for="fee in order.fees" :key="fee.id">
					        <td>
					            <a href="#" @click.prevent="editFee(fee)">
					            	{{ fee.title }}
					            </a> 
					        </td>
					        <td>{{ fee.qty }}</td>
					        <td>&#36;{{ fee.amount_formatted }}<small>{{ fee.type_formatted }}</small></td>
					        <td></td>
					        <td>&#36;{{ fee.subtotal_formatted }}</td>
					        <td class="text-right">
					        	<button type="button" class="btn btn-alt btn-sm" @click="editFee(fee)">Edit</button>
					        	<button type="button" class="btn btn-alt btn-sm" @click="removeFee(fee)">Delete</button>
					        </td>
					    </tr>

					    <tr>
					        <td>
					        	<a href="#" @click.prevent="editDeliveryFee()">
					        		Delivery Fee
					        	</a>
					        </td>
					        <td>1</td>
					        <td>&#36;{{ order.delivery_rate_formatted }}<small v-show="order.delivery_fee_type == 1">/{{ weightuom }}.</small></td>
					        <td>
					            Order Weight: <strong>{{ order.weight }}</strong> {{ weightuom }}
					        </td>
					        <td>&#36;{{ order.delivery_fee_formatted }}</td>
					        <td class="text-right">
					        	<button type="button" class="btn btn-alt btn-sm" @click="editDeliveryFee()">Edit</button>
					        </td>
			    		</tr>
		    		</tbody>
				</table>
			</div>
		</div>		

		<div class="gc-modal gc-modal-mask" id="editDeliveryFeeModal" @click="hideModal('editDeliveryFeeModal')">
			<div class="gc-modal-wrapper">
				<div class="gc-modal-container" @click.stop>
					
					<div class="gc-modal-header">
						Edit Delivery Fee
					</div>
		
					<div class="gc-modal-body">
						<div class="flex align-items-b">
							<div class="flex-item-fill mr-md">
			                    <label>Delivery Fee Type</label>
			                    <select v-model="order.delivery_fee_type" class="form-control">
			                    	<option value="1">Price Per Pound</option>
			                    	<option value="2">Flat Fee</option>
			                    </select>
			                </div>
							<div class="flex-item-fill mr-md">
			                    <label>Delivery Fee</label>
			                    <div class="input-group">
                                    <div class="input-group-addon">$</div>
			                    	<input type="text" class="form-control" v-model="order.delivery_rate_formatted" />
			                    </div>	
			                </div>
			            </div>    
					</div>
		
					<div class="gc-modal-footer">
						<button type="button" class="btn btn-alt" @click="hideModal('editDeliveryFeeModal')">Cancel</button>
		                <button type="button" class="btn btn-action" @click="updateDeliveryFee()">Save</button>
					</div>
				</div>
			</div>
		</div>

		<div class="gc-modal gc-modal-mask" id="deleteFeeModal" @click="hideModal('deleteFeeModal')">
			<div class="gc-modal-wrapper">
				<div class="gc-modal-container" @click.stop>
					
					<div class="gc-modal-header">
						Delete Fee
					</div>
		
					<div class="gc-modal-body">
						Would you like to delete <strong>{{ fee.title }} </strong>?
					</div>
		
					<div class="gc-modal-footer">
						<button type="button" class="btn btn-alt" @click="hideModal('deleteFeeModal')">Cancel</button>
		                <button type="button" class="btn btn-danger" @click="destroyFee()">Delete Fee</button>
					</div>
				</div>
			</div>
		</div>

		<div class="gc-modal gc-modal-mask" id="editFeeModal" @click="hideModal('editFeeModal')">
			<div class="gc-modal-wrapper">
				<div class="gc-modal-container" @click.stop>
					
					<div class="gc-modal-header">
						Edit Fee
					</div>
		
					<div class="gc-modal-body">
						<div class="form-group">
		                    <label for="title">Title</label>
		                    <input type="text" name="title" class="form-control" v-model="fee.title"/>
		                </div>

		                <div class="form-group">
		                    <label for="qty">Qty</label>
		                    <input type="text" name="qty" class="form-control" v-model="fee.qty"/>
		                </div>

		                <div class="form-group">
		                    <label for="amount">Amount</label>
		                    <input type="text" name="amount" class="form-control" v-model="fee.amount_formatted"/>
		                </div>
					</div>
		
					<div class="gc-modal-footer">
						<button type="button" class="btn btn-alt" @click="hideModal('editFeeModal')">Cancel</button>
		                <button type="button" class="btn btn-action" @click="updateFee()">Save</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
	export default {
		props: ['order', 'weightuom'],

        emits: ['updateTotals', 'error', 'showProgressBar'],

		data: function()
		{
			return {
				fee: {},
				newFee: {
					title: '',
					qty: 1,
					amount: ''
				},
				addFee: false
			}
		},

		methods: {
			add: function() {
				axios.post('/admin/orders/'+this.order.id+'/fees', this.newFee)
                    .then(function(response) {
                        this.$emit('updateTotals', response.data);
                    }.bind(this))
                    .catch(function(error) {
                        eventHub.emit('error', error);
                        this.$emit('error', error);
                    }.bind(this))
			},

			editFee: function(fee) {
	            this.fee = fee
	            this.showModal('editFeeModal');
	        },

	        removeFee: function(fee) {
	        	this.fee = fee;
	            this.showModal('deleteFeeModal');
	        },

	        editDeliveryFee: function()
	        {
	            this.showModal('editDeliveryFeeModal');
	        },

			updateFee: function() {
				eventHub.emit('showProgressBar');
	            var url = '/admin/orders/'+this.order.id+'/fees/'+this.fee.id;
	            var payload = {
	                title: this.fee.title,
	                qty: this.fee.qty,
	                amount: this.fee.amount_formatted
	            };
	            axios.put(url, payload)
	                .then(function(response) {
	                    eventHub.emit('hideProgressBar');
	                    this.fee = response.data.fee;
	                    this.$emit('updateTotals', response.data);
	                    this.hideModal('editFeeModal');
	                }.bind(this))
	                .catch(function(error) {
	                    eventHub.emit('hideProgressBar');
	                    eventHub.emit('error', error);
	                }.bind(this));
	        },

	        updateDeliveryFee: function() {
	            eventHub.emit('showProgressBar');
	            var url = '/admin/orders/'+this.order.id;
	            var payload = {
	            	delivery_fee_type: this.order.delivery_fee_type,
	            	delivery_rate: this.order.delivery_rate_formatted
	            };
	            axios.put(url, payload)
	                .then(function(response) {
	                	this.hideModal('editDeliveryFeeModal');
	                    eventHub.emit('hideProgressBar');
	                    this.$emit('updateTotals', response.data);
	                }.bind(this))
	                .catch(function(error) {
	                    eventHub.emit('hideProgressBar');
	                    eventHub.emit('error', error);
	                }.bind(this));
	        },

	        destroyFee: function() {
	            eventHub.emit('showProgressBar');
	            var url = '/admin/orders/'+this.order.id+'/fees/'+this.fee.id;
	            axios.delete(url)
	                .then(function(response) {
	                	this.hideModal('deleteFeeModal');
	                	eventHub.emit('hideProgressBar');
	                    this.$emit('updateTotals', response.data);
	                }.bind(this))
	                .catch(function(error) {
	                    eventHub.emit('hideProgressBar');
	                    eventHub.emit('error', error);
	                }.bind(this));
	        },
		}
	}
</script>
<template>
    <tr>
        <td>
            <v-select
                :options="products"
                @search="getProducts"
                v-model="selectedProduct"
                label="title"
                ref="addItemSelect"
            >
                <template v-slot:no-options>
                    <span>Search for a product to add...</span>
                </template>
            </v-select>
        </td>
        <td>
            <input
                type="number"
                v-model="qty"
                class="form-control hide-spinners input-sm"
                placeholder="Qty."
                tabindex="0"
                @keyup.enter="addLineItem"
                ref="qtyInput"
            >
        </td>
        <td colspan="100%">
            <button
                type="button"
                class="btn btn-primary"
                :class="{'btn-action': product}"
                @mouseup="addLineItem"
                @keyup.enter="addLineItem"
                :disabled="!product"
                tabindex="0"
            >Add Item</button>
            <button
                type="button"
                class="btn btn-alt"
                @click="toggleNewLineItem"
            >Cancel</button>
        </td>
    </tr>
</template>

<script>
	import vSelect from 'vue-select';
    import 'vue-select/dist/vue-select.css';

    export default {

        components: {
            vSelect
        },

        emits: ['save', 'toggle'],

        mounted: function() {
            this.focusSelect();
        },

        data: function() {
            return {
                products: [],
                selectedProduct: null,
                product: null,
                qty: '',
            }
        },

        methods: {
            getProducts: function(query) {
                this.products = [{ title: 'Searching...' }]
                axios.get('/api/products', { params: { q: query } }).then(function(response) {
                    this.products = response.data;
                }.bind(this), function(response) {
                });
            },

            selected: function(product) {
                this.product = product;
                setTimeout(function() {
                    this.$refs.qtyInput.focus();
                }.bind(this), 100);
            },

            addLineItem: function() {
                if (!this.product) return;
                this.$emit('save', {
                    product: this.product.id,
                    qty: this.qty
                });

                this.product = null;
                this.qty = 1;
                this.focusSelect();
            },

            focusSelect: function() {
                this.$refs.addItemSelect.$refs.search.focus();
            },

            toggleNewLineItem: function() {
                this.$emit('toggle');
            }
        },

        watch: {
            selectedProduct(product) {
                this.selected(product)
            }
        }
    }
</script>

<style>
.productSelect {
	position: relative;
}

.productSelect-list {
	list-style-type: none;
	margin: 0 0 10px 0;
	padding: 0;
	position: absolute;
	z-index: 1030;
	top: 36px;
	left: 0;
	width: 100%;
	max-height: 290px;
	overflow: scroll;
	border: solid 1px #DDD;
	box-shadow: 0 6px 12px rgba(0,0,0,.175);
}

.productSelect-list > li {
	display: block;
	padding: 8px 6px;
	border-top: solid 1px #DDD;
	border-bottom: none;
	background-color: #FFF;
	color: #34B393;
}

.productSelect-list > li > a:focus {
	background-color: #34B393;
	color: #FFF;
}

.productSelect-list > li > a:hover {
	background-color: #34B393;
	color: #FFF;
	cursor: pointer;
}

.productSelect-list > li:first-of-type {
	border-top: none;
}

.productSelect-list > li:last-of-type {
	border-bottom: none;
}
</style>
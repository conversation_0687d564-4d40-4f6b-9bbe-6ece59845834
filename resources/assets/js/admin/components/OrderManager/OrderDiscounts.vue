<template>
	<div class="discounts" id="orderDiscounts">
		<div class="panel panel-default">
			<div class="panel-heading flex align-items-m">
				<div class="flex-item">Coupons</div>
				<div class="flex-item push-right">
					<a href="#" class="btn btn-sm btn-alt" @click.prevent="addDiscount = !addDiscount"><i class="fa fa-plus-circle fa-lg"></i> Add Coupon</a>
				</div>
			</div>
			<div class="panel-body pr-0 pl-0">
				<table class="table table-compact table-full">
				<thead>
					<tr>
						<th>Coupon Code</th>
						<th>Description</th>
						<th colspan="100%">Applied Savings</th>
					</tr>	
				</thead>
				<tbody>
					<transition name="fade">
					<tr v-if="addDiscount">
						<td>
							<input type="text" v-model="code" class="form-control" autofocus>
						</td>
						<td colspan="100%">
							<button 
								type="button" 
								class="btn btn-light" 
								:class="{'btn-action': code.length}"
								:disabled="!code.length" 
								@click="add(code)"
							>Add Coupon</button>
							<button type="button" class="btn btn-alt" @click="addDiscount = !addDiscount">Cancel</button>
						</td>
					</tr>
					</transition>
					<tr v-for="discount in order.discounts">
						<td>{{ discount.code }}</td>
						<td>{{ discount.description }}</td>
						<td>{{ $filters.cents(discount.pivot.savings) }}</td>
						<td class="text-right">
							<button class="btn btn-sm btn-alt" @click="remove(discount)">
							Delete</button>
						</td>	
					</tr>
				</tbody>
				</table>
				<div class="pa-md" v-if="order.discounts == undefined || !order.discounts.length">No coupons applied.</div>
			</div>
		</div>

		<div class="gc-modal gc-modal-mask" id="deleteCouponModal" @click="hideModal('deleteCouponModal')">
			<div class="gc-modal-wrapper">
				<div class="gc-modal-container" @click.stop>
					
					<div class="gc-modal-header">
						Delete Coupon
					</div>
		
					<div class="gc-modal-body">
						Would you like to delete <strong>{{ discount.code }} </strong>?
					</div>
		
					<div class="gc-modal-footer">
						<button type="button" class="btn btn-alt" @click="hideModal('deleteCouponModal')">Cancel</button>
		                <button type="button" class="btn btn-danger" @click="destroy(discount)">Delete Coupon</button>
					</div>
				</div>
			</div>
		</div>	
	</div>
</template>

<script>
	export default {

		props: ['order'],

        emits: ['updateTotals'],

		data: function() {
			return {
				discount: {},
				code: '',
				addDiscount: false,
			}
		},

		methods: {
			add: function(code) {
				axios.post('/admin/orders/'+this.order.id+'/discounts', {code: code}).then(function(response) {
					this.$emit('updateTotals', response.data);
					this.code = '';
				}.bind(this)).catch(function(error) {
					eventHub.emit('error', error);
				}.bind(this))
			},

			remove: function(discount) {
				this.discount = discount;
				this.showModal('deleteCouponModal');
			},

			destroy: function(discount) {
				axios.delete('/admin/orders/'+this.order.id+'/discounts/'+discount.id).then(function(response) {
					this.hideModal('deleteCouponModal');
					this.$emit('updateTotals', response.data);
				}.bind(this)).catch(function(error) {
					this.hideModal('deleteCouponModal');
					eventHub.emit('error', error);
				}.bind(this))
			},
		}
	}
</script>
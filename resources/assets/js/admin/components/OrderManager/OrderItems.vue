<template>
<div>
	<div class="gc-modal gc-modal-mask" id="editItemModal" @click="cancel('editItemModal')">
		<div class="gc-modal-wrapper">
			<div class="gc-modal-container" @click.stop>
				<div class="gc-modal-header">
					Edit Item
				</div>
				<div v-if="item" class="gc-modal-body">
					<div class="form-group">
	                    <label for="title">Title</label>
	                    <input type="text" name="title" class="form-control" v-model="item.title"/>
	                </div>
	                <div class="form-group">
	                    <label for="qty" class="hint--bounce hint--top" :data-hint="'Original Qty: ' + item.original_qty">Qty</label>
	                    <input type="number" min="1" name="qty" class="form-control" :disabled="item.type_id === 2" v-model="item.qty"/>
                        <p v-if="item.type_id === 2" class="m-0 mt-1 text-gray-500">Gift card quantities cannot be updated</p>
	                </div>

	                <div v-show="item.type_id === 1">
		                <div class="checkbox form-group">
		                    <label>
		                        <input type="checkbox" v-model="estimateWeight" name="estimate_weight"> Estimate weight from quantity.
		                    </label>
		                </div>

		                <div class="form-group" v-if="!estimateWeight">
		                    <label for="weight" class="hint--bounce hint--top" :data-hint="'Original Weight: ' + item.original_weight">Weight</label>
		                    <input type="text" name="weight" class="form-control" v-model="item.weight"/>
		                </div>
		            </div>

	                <div class="form-group">
	                    <label for="unit_price" class="hint--bounce hint--top" :data-hint="'Original Price: ' + item.original_price_formatted">
	                    	{{ item.type_id === 1 ? 'Price' : 'Amount' }}
	                    </label>
	                    <div class="input-group">
	                        <div class="input-group-addon">$</div>
	                    <input type="text" name="unit_price" class="form-control" v-model="item.unit_price_formatted" />
	                    </div>
	                </div>

	                <div v-show="item.type_id !== 2">
		                <hr>
		                <div class="flex align-items-m">
		                    <div class="flex-item-fill mr-sm">
		                        <label for="discount">Discount</label>
		                        <div class="input-group">
		                        <div class="input-group-addon">{{ item.discount_type === 'fixed' ? '$' : '%' }}</div>
		                        <input type="text" name="discount" class="form-control" v-model="item.discount_formatted" />
		                        </div>
		                    </div>

		                    <div class="flex-item-fill">
		                        <label for="discount_type">Discount Type</label>
		                        <select name="discount_type" v-model="item.discount_type" class="form-control">
		                            <option value="fixed">$</option>
		                            <option value="percent">%</option>
		                        </select>
		                    </div>
		                </div>
	            	</div>
				</div>

				<div class="gc-modal-footer">
					<button type="button" class="btn btn-alt" @click="cancel('editItemModal')">Cancel</button>
	                <button type="button" class="btn btn-action" @click="updateItem">Save</button>
				</div>
			</div>
		</div>
	</div>

	<div class="gc-modal gc-modal-mask" id="deleteItemModal" @click="cancel('deleteItemModal')">
        <div class="gc-modal-wrapper">
            <div v-if="item" class="gc-modal-container" @click.stop>
                <div class="gc-modal-header">
                    Delete Item
                </div>
                <div class="gc-modal-body">
                    Are you sure you want to delete <strong>{{ item.title }}</strong>?
                </div>
                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="cancel('deleteItemModal')">Cancel</button>
                    <button type="button" class="btn btn-danger" @click="destroyItem">Yes, Delete</button>
                </div>
            </div>
        </div>
    </div>

	<div class="panel panel-default">
		<div class="panel-heading flex align-items-m">
        	<div class="flex-item">Items</div>
        	<a href="#" class="btn btn-sm btn-alt flex-item push-right" tabindex="1" @click.prevent="toggleNewLineItem">
                <i class="fa fa-plus-circle fa-lg"></i> Add Item
            </a>
        </div>
    	<div class="panel-body pr-0 pl-0">
			<table class="table table-compact table-striped table-full">
		    	<thead>
				    <tr>
				        <th>Name</th>
				        <th style="width:50px;">Qty.</th>
				        <th>Price</th>
				        <th>Weight</th>
				        <th colspan="100%">Subtotal</th>
				    </tr>
		    	</thead>
		    	<tbody>
		    		<tr
		    			is="vue:add-line-item"
		    			v-if="showNewLineItem"
		    			@save="addItem"
		    			@toggle="toggleNewLineItem"
		    		></tr>

			    	<tr v-for="item in items" :key="item.id">
			    		<td>
                            <ul class="tags" v-if="item.stock_status === 'short'">
                                <li class="tag">
                                    <span class="tagTitle">
                                        Short &nbsp;
                                    </span>
                                </li>
                            </ul>
                            <ul class="tags" v-if="item.stock_status === 'out'">
                                <li class="tag">
                                    <span class="tagTitle">
                                        Out Of Stock &nbsp;
                                    </span>
                                </li>
                            </ul>
			    			<a href="#" @click.prevent="editItem(item)" v-text="item.title"></a>
                            <span class="text-gray-medium fs-sm pl-sm">{{ item.sku }}</span>
                            <a v-if="item.code"
                               :href="`/admin/gift-cards/${item.product_id}/codes/${item.code}`"
                               class="mt-1 block text-gray-500 hover:text-keppel-500 fs-sm"
                               v-text="item.code"
                            ></a>
                            <button v-if="item.type_id === 2 && !item.code"
                                    @click.prevent="issueGiftCardCode(item)"
                                    type="button"
                                    class="mt-1 inline-flex items-center rounded border border-gray-300 bg-white px-2.5 py-1.5 text-xs font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-keppel-500 focus:ring-offset-2"
                            >Issue Code</button>
                            <div></div>
                            <ul v-if="showTypes" class="tags mt-1">
                                <li class="tag">
                                    <span class="text-gray-medium fs-sm pl-sm px-2 py-1 bg-gray-100 text-gray-500 rounded-md" v-text="item.type"></span>
                                </li>
                            </ul>
<!--                            <p v-if="showTypes" class="mt-1 inline-block text-gray-medium fs-sm pl-sm px-2 py-1 bg-gray-100 text-gray-500 rounded-md" v-text="item.type"></p>-->
			    		</td>
			    		<td style="width:100px;">
                            <span v-if="item.stock_status === 'short' || item.stock_status === 'out'">
                                {{ item.fulfilled_qty }} of {{ item.qty }}
                            </span>
                            <span v-else>
                            {{ item.qty }}
                            </span>
                        </td>

			    		<td>&#36;{{ item.unit_price_formatted }} <small>{{ item.unit_of_issue_formatted }}</small></td>

			    		<td v-if="item.unit_of_issue === 'weight'">
			    			<input type="number"
		                   		v-model="item.weight"
		                   		class="form-control input-sm hide-spinners"
		                   		step='0.001'
		                   		tabindex="1"
		                   		@change="updateWeight(item)"
		                    >
			    		</td>

			    		<td v-else>
			    			<input type="text"
		                   		style="max-width: 100px;"
		                   		class="form-control"
		                   		v-model="item.weight"
		                   		disabled
		                    >
			    		</td>

			    		<td>&#36;{{ item.subtotal_formatted}}</td>

			    		<td>
			    			<button type="button" class="btn btn-sm btn-alt" @click="editItem(item)">Edit</button>
			    			<button type="button" class="btn btn-sm btn-alt" @click="removeItem(item)">Delete</button>
			    		</td>
			    	</tr>
		    	</tbody>
		    </table>
		    <div class="pa-md" v-if="items.length === 0">This order has no items.</div>
		</div>
	</div>
</div>
</template>


<script>
import AddLineItem from './AddLineItem.vue';
import { showModal, hideModal } from "../../shared/useModal";
import { ref } from "vue";

export default {
    props: {
        id: {
            type: String,
            required: true
        },
        showTypes: {
            type: Boolean,
            default: false
        }
    },

    emits: ['updateTotals'],

    components: {
        AddLineItem
    },

    setup(props, { emit }) {
        const items = ref([])
        const item = ref(null)
        const estimateWeight = ref(false)
        const showNewLineItem = ref(false)

        const toggleNewLineItem = () => showNewLineItem.value = !showNewLineItem.value

        const cancel = modal => {
            hideModal(modal)
            item.value = null;
        }

        const getItems = () => {
            axios.get(`/api/orders/${props.id}/items`)
                .then(({ data }) => items.value = data)
                .catch(error => eventHub.emit("error", error));
        }

        const addItem = item => {
            eventHub.emit('showProgressBar');

            axios.post(`/admin/orders/${props.id}/items`, {
                'id': item.product,
                'qty': item.qty
            })
                .then(function(response) {
                    eventHub.emit('hideProgressBar');
                    emit('updateTotals', response.data);
                    getItems();
                })
                .catch(function(error) {
                    eventHub.emit('hideProgressBar')
                    eventHub.emit('error', error);
                });
        }

        const removeItem = itemToRemove => {
            item.value = itemToRemove;
            showModal('deleteItemModal');
        }

        const editItem = itemToEdit => {
            if (item.weight !== item.original_weight && item.unit_of_issue === 'weight') {
                estimateWeight.value = false;
            } else {
                estimateWeight.value = true;
            }

            item.value = itemToEdit
            showModal('editItemModal');
        }

        const updateWeight = item => {
            eventHub.emit('showProgressBar');

            axios.put(`/admin/orders/${props.id}/items/${item.id}/weights`,{
                weight: item.weight
            })
                .then(function(response) {
                    eventHub.emit('hideProgressBar')

                    if (response.data.weight_difference) {
                        alert('The weight you entered is '+response.data.weight_difference+' pounds different then what it was before. Are you sure this is correct?')
                    }

                    item.subtotal_formatted = response.data.item.subtotal_formatted;
                    emit('updateTotals', response.data)
                })
                .catch(function(error) {
                    eventHub.emit('hideProgressBar')
                    eventHub.emit('error', error);
                });
        }

        const issueGiftCardCode = item => {
            eventHub.emit('showProgressBar');

            axios.post(`/api/gift-card-codes`,{
                order_item_id: item.id,
            })
                .then(({ data }) => {
                    eventHub.emit('hideProgressBar')
                    item.code = data.code;
                })
                .catch(function(error) {
                    eventHub.emit('hideProgressBar')
                    eventHub.emit('error', error);
                });
        }

        const updateItem = () => {
            eventHub.emit('showProgressBar');

            const itemToUpdate = item.value;

            axios.patch(`/admin/orders/${props.id}/items/${itemToUpdate.id}`, {
                title: itemToUpdate.title,
                qty: itemToUpdate.qty, // gift card quantities cannot be changed
                weight: itemToUpdate.weight,
                unit_price: itemToUpdate.unit_price_formatted,
                discount: itemToUpdate.discount_formatted,
                discount_type: itemToUpdate.discount_type,
                estimate_weight: estimateWeight.value
            })
                .then(function(response) {
                    eventHub.emit('hideProgressBar');
                    emit('updateTotals', response.data);
                    hideModal('editItemModal');
                    item.value = null;
                    getItems();
                })
                .catch(function(error) {
                    eventHub.emit('error', error);
                    eventHub.emit('hideProgressBar');
                });
        }

        const destroyItem = () => {
            eventHub.emit('showProgressBar');

            axios.delete(`/admin/orders/${props.id}/items/${item.value.id}`)
                .then(function(response) {
                    eventHub.emit('hideProgressBar');
                    emit('updateTotals', response.data)
                    hideModal('deleteItemModal');
                    item.value = null;
                    $('.orderTotal').text(response.data.order_total)
                    getItems();
                })
                .catch(function(error) {
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                });
        }

        getItems()

        return {
            items,
            item,
            estimateWeight,
            showNewLineItem,
            addItem,
            updateItem,
            toggleNewLineItem,
            editItem,
            updateWeight,
            destroyItem,
            removeItem,
            cancel,
            issueGiftCardCode,
            showModal,
            hideModal
        }
    }
}
</script>

<template>
    <div id="orderPayments" class="orderPayments">
        <div class="panel">
            <div class="panel-heading flex align-items-m">
                <div class="flex-item">Payments</div>
            </div>
            <div class="panel-body pr-0 pl-0">
                <table class="table table-striped table-compact table-full">
                    <thead>
                    <tr>
                        <th class="text-right">Amount</th>
                        <th></th>
                        <th>Type</th>
                        <th>Description</th>
                        <th>Date</th>
                        <th></th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="payment in order.payments" :key="payment.id">
                        <td class="text-right" style="width: 75px;">
                            {{ $filters.currency($filters.cents(payment.amount)) }}
                        </td>
                        <td>
                            <a v-show="payment.amount_refunded > 0 && payment.amount_refunded < payment.amount"
                               class="label label-light ml-xs"
                               href="#"
                               @click.prevent="editCardRefund(payment)"
                            >Partially Refunded</a>
                            <a v-show="payment.amount_refunded > 0 && payment.amount_refunded == payment.amount"
                               class="label label-light ml-xs"
                               href="#"
                               @click.prevent="editCardRefund(payment)"
                            >Refunded</a>
                            <span v-show="payment.deleted_at" class="label label-light ml-xs">Deleted</span>
                        </td>
                        <td>{{ payment.payment_type }}</td>
                        <td style="max-width: 400px;">
                            <p class="m-0 text-sm text-gray-900">{{ payment.description }}</p>
                            <a :href="`${baseStripeUrl}/payments/${payment.payment_id}`" class="m-0 mt-2 text-xs text-keppel-600 hover:text-keppel-500" target="_blank">{{ payment.payment_id }}</a>
                        </td>
                        <td>{{ dayjs(payment.created_at).format('YYYY-MM-DD hh:mm:ss A') }}</td>
                        <td v-if="payment.payment_type_id == 1" class="text-right">
                            <button class="btn btn-alt btn-sm" type="button" @click="editPayment(payment)">Edit</button>
                            <button
                                v-show="payment.payment_type_id == 1"
                                class="btn btn-alt btn-sm"
                                style="min-width: 62px;"
                                type="button"
                                @click="deleteCashPayment(payment)"
                            >Delete
                            </button>
                        </td>
                        <td v-else class="text-right">
                            <button class="btn btn-alt btn-sm" type="button" @click="editPayment(payment)">Edit</button>
                            <button
                                v-show="payment.payment_type_id == 2"
                                class="btn btn-alt btn-sm"
                                style="min-width: 62px;"
                                type="button"
                                @click="editCardRefund(payment)"
                            >Refunds
                            </button>
                        </td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <div id="createCardPaymentModal" class="gc-modal gc-modal-mask" @click="hideModal('createCardPaymentModal')">
            <div class="gc-modal-wrapper">
                <div class="gc-modal-container" @click.stop>

                    <div class="gc-modal-header">
                        Add Payment
                    </div>

                    <div class="gc-modal-body">
                        <div v-if="order.total_due > 0">
                            <div class="form-group">
                                <label>Charge the customer's credit card</label>
                                <select v-model="cardPayment.payment_source_id" class="form-control">
                                    <option value="">Choose a payment source</option>
                                    <option
                                        v-for="paymentMethod in paymentMethods"
                                        :key="paymentMethod.id"
                                        :value="paymentMethod.id"
                                    >
                                        {{ paymentMethodLabel(paymentMethod) }}
                                    </option>
                                </select>
                            </div>

                            <div class="form-group">
                                <div class="radio">
                                    <label for="full_amount">
                                        <input id="full_amount" v-model="cardPayment.full_charge" :value="true" type="radio"> Charge full amount due: {{ $filters.currency($filters.cents(order.total_due)) }}
                                    </label>
                                </div>
                                <div class="radio">
                                    <label for="partial_amount">
                                        <input id="partial_amount" v-model="cardPayment.full_charge" :value="false" type="radio"> Charge partial amount
                                    </label>
                                </div>

                            </div>

                            <div v-if="!cardPayment.full_charge">
                                <div class="form-group">
                                    <label>Amount</label>
                                    <div class="input-group">
                                        <div class="input-group-addon">$</div>
                                        <input v-model="cardPayment.amount" class="form-control" min="0" type="number">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea v-model="cardPayment.description" class="form-control"></textarea>
                                </div>
                            </div>
                        </div>
                        <div v-else>
                            The order balance has already been paid.
                        </div>
                    </div>

                    <div v-if="paymentMethods.length" class="gc-modal-footer">
                        <button class="btn btn-alt" type="button" @click="hideModal('createCardPaymentModal')">Cancel</button>
                        <button
                            v-show="order.total_due > 0"
                            :disabled="processing"
                            class="btn btn-action"
                            type="button"
                            @click="storeCardPayment(cardPayment)"
                        >Charge Card
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <div id="createCashPaymentModal" class="gc-modal gc-modal-mask" @click="hideModal('createCashPaymentModal')">
            <div class="gc-modal-wrapper">
                <div class="gc-modal-container" @click.stop>

                    <div class="gc-modal-header">
                        Add Payment
                    </div>

                    <div class="gc-modal-body">
                        <div v-if="order.total_due > 0">

                            <p>Add a cash or check payment against the total due: {{ $filters.currency($filters.cents(order.total_due)) }}</p>

                            <div class="form-group">
                                <label>Payment Type</label>
                                <select v-model="cashPayment.payment_type" class="form-control">
                                    <option value="Check">Check</option>
                                    <option value="Cash">Cash</option>
                                    <option value="Credit Card (Offline)">Credit Card (Offline)</option>
                                </select>
                            </div>

                            <div class="form-group">
                                <div class="radio">
                                    <label for="full_amount_cash">
                                        <input id="full_amount_cash" v-model="cashPayment.full_charge" :value="true" type="radio"> Full amount due: {{ $filters.currency($filters.cents(order.total_due)) }}
                                    </label>
                                </div>
                                <div class="radio">
                                    <label for="partial_amount_cash">
                                        <input id="partial_amount_cash" v-model="cashPayment.full_charge" :value="false" type="radio"> Partial amount
                                    </label>
                                </div>
                            </div>

                            <div v-if="!cashPayment.full_charge">
                                <div class="form-group">
                                    <label>Amount</label>
                                    <div class="input-group">
                                        <div class="input-group-addon">$</div>
                                        <input v-model="cashPayment.amount" class="form-control" min="0" type="number">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>Description</label>
                                    <textarea v-model="cashPayment.description" class="form-control"></textarea>
                                </div>
                            </div>
                        </div>
                        <div v-else>
                            <p>The order balance has already been paid.</p>
                        </div>
                    </div>

                    <div class="gc-modal-footer">
                        <button class="btn btn-alt" type="button" @click="hideModal('createCashPaymentModal')">Cancel</button>
                        <button v-show="order.total_due > 0" :disabled="processing" class="btn btn-action" type="button" @click="storeCashPayment(cashPayment)">Add Payment</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="editPaymentModal" class="gc-modal gc-modal-mask" @click="hideModal('editPaymentModal')">
            <div class="gc-modal-wrapper">
                <div class="gc-modal-container" @click.stop>

                    <div class="gc-modal-header">
                        Edit Payment {{ $filters.currency($filters.cents(cashPayment.amount)) }}
                    </div>

                    <div class="gc-modal-body">
                        <div class="form-group">
                            <label>Description</label>
                            <textarea v-model="cashPayment.description" class="form-control"></textarea>
                        </div>

                        <div v-show="cashPayment.payment_type == 'Check' || cashPayment.payment_type == 'Cash'" class="form-group">
                            <label>Payment Type</label>
                            <select v-model="cashPayment.payment_type" class="form-control">
                                <option value="Check">Check</option>
                                <option value="Cash">Cash</option>
                            </select>
                        </div>
                    </div>

                    <div class="gc-modal-footer">
                        <button class="btn btn-alt" type="button" @click="hideModal('editPaymentModal')">Cancel</button>
                        <button class="btn btn-action" type="button" @click="updateCashPayment(cashPayment)">Update Payment</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="deletePaymentModal" class="gc-modal gc-modal-mask" @click="hideModal('deletePaymentModal')">
            <div class="gc-modal-wrapper">
                <div v-if="!cashPayment.deleted_at" class="gc-modal-container" @click.stop>

                    <div class="gc-modal-header">
                        Delete Payment
                    </div>

                    <div class="gc-modal-body">
                        <p>Delete {{ cashPayment.payment_type }} payment of {{ $filters.currency($filters.cents(cashPayment.amount)) }}?</p>
                    </div>

                    <div class="gc-modal-footer">
                        <button class="btn btn-alt" type="button" @click="hideModal('deletePaymentModal')">Cancel</button>
                        <button class="btn btn-danger" type="button" @click="destroyCashPayment(cashPayment)">Delete</button>
                    </div>
                </div>
                <div v-else class="gc-modal-container" @click.stop>

                    <div class="gc-modal-header">
                        Delete Payment
                    </div>

                    <div class="gc-modal-body">
                        <p>This payment was deleted on <strong>{{ cashPayment.deleted_at }}</strong></p>
                    </div>

                    <div class="gc-modal-footer">
                        <button class="btn btn-alt" type="button" @click="hideModal('deletePaymentModal')">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <div id="refundPaymentModal" class="gc-modal gc-modal-mask" @click="hideModal('refundPaymentModal')">
            <div class="gc-modal-wrapper">
                <div class="gc-modal-container" @click.stop>

                    <div class="gc-modal-header">
                        Refund Payment
                    </div>

                    <div class="gc-modal-body">
                        <div v-if="refund.payment.amount_refunded < refund.payment.amount">
                            <p>Refund this payment back on the customer's card?</p>
                            <div class="form-group">
                                <div class="radio">
                                    <label for="full_amount_refund">
                                        <input
                                            id="full_amount_refund"
                                            v-model="refund.full_refund"
                                            :value="true"
                                            type="radio"
                                        > Full amount: {{ $filters.currency($filters.cents((refund.payment.amount - refund.payment.amount_refunded))) }}
                                    </label>
                                </div>
                                <div class="radio">
                                    <label for="partial_amount_refund">
                                        <input id="partial_amount_refund" v-model="refund.full_refund" :value="false" type="radio"> Partial amount
                                    </label>
                                </div>
                            </div>

                            <div v-if="!refund.full_refund">
                                <div class="form-group">
                                    <label>Amount</label>
                                    <div class="input-group">
                                        <div class="input-group-addon">$</div>
                                        <input v-model="refund.amount" class="form-control" min="0" type="number">
                                    </div>
                                </div>

                                <div class="form-group">
                                    <label>Reason for Refund</label>
                                    <textarea v-model="refund.description" class="form-control"></textarea>
                                </div>
                            </div>
                        </div>
                        <div v-else>
                            <p>This payment has been fully refunded.</p>
                        </div>

                        <table class="table table-striped">
                            <tbody>
                            <tr v-for="refund in refund.payment.refunds">
                                <td>
                                    {{ $filters.currency($filters.cents(refund.amount)) }} refunded
                                    <div class="fs-sm text-gray-6">{{ dayjs(refund.created_at).format('YYYY-MM-DD hh:mm:ss A') }}</div>
                                    <a :href="`${baseStripeUrl}/refunds/${refund.refund_id}`" class="m-0 mt-2 text-xs text-keppel-600 hover:text-keppel-500" target="_blank">{{ refund.refund_id }}</a>
                                    <div class="fs-sm text-gray-6">{{ refund.reason }}</div>
                                </td>
                            </tr>
                            </tbody>
                        </table>
                    </div>

                    <div class="gc-modal-footer">
                        <button class="btn btn-alt" type="button" @click="hideModal('refundPaymentModal')">Cancel</button>
                        <button
                            v-show="refund.payment.amount_refunded < refund.payment.amount"
                            :disabled="processing"
                            class="btn btn-danger"
                            type="button"
                            @click="refundCardPayment(refund)"
                        >Refund
                        </button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script>
import dayjs from 'dayjs';

export default {
    props: ['order'],

    emits: ['updateTotals', 'error'],

    created() {
        this.setDefaultCardPayment();
        this.setDefaultCashPayment();
        this.getPaymentMethods();

        eventHub.on('OrderPayments:createCardPayment', this.createCardPayment);
        eventHub.on('OrderPayments:createCashPayment', this.createCashPayment);
    },

    data() {
        return {
            cardPayment: {},
            cashPayment: {},
            refund: {
                payment: {},
                full_refund: true,
                description: null,
                amount: 0
            },
            processing: false,
            paymentMethods: []
        };
    },

    computed: {
        baseStripeUrl() {
            let url = `https://dashboard.stripe.com`;

            if (import.meta.env.VITE_APP_ENV !== 'production') {
                url = `${url}/test`;
            }

            return url;
        }
    },

    methods: {
        dayjs,
        setDefaultCardPayment() {
            this.cardPayment = {
                full_charge: true,
                amount: 0,
                description: '',
                payment_source_id: this.order.payment_source?.source_id
            };
        },

        setDefaultCashPayment() {
            this.cashPayment = {
                full_charge: true,
                amount: 0,
                description: '',
                payment_type: 'Check'
            };
        },

        getPaymentMethods() {
            axios.get(`/api/users/${this.order.customer_id}/payment-methods`)
                .then(({ data }) => {
                    this.paymentMethods = data;
                })
                .catch(error => {
                });
        },

        createCardPayment() {
            this.setDefaultCardPayment();
            this.showModal('createCardPaymentModal');
        },

        createCashPayment() {
            this.setDefaultCashPayment();
            this.showModal('createCashPaymentModal');
        },

        storeCardPayment(charge) {
            this.processing = true;
            eventHub.emit('showProgressBar');

            axios.post(`/admin/orders/${this.order.id}/card-payments`, charge)
                .then(({ data }) => {
                    this.$emit('updateTotals', data);
                    this.hideModal('createCardPaymentModal');
                    this.setDefaultCardPayment();
                    this.processing = false;
                    eventHub.emit('notify', { message: data.responseText, level: 'info' });
                    eventHub.emit('hideProgressBar');
                    location.reload();
                })
                .catch(error => {
                    eventHub.emit('error', error);
                    this.$emit('error', error);
                    this.processing = false;
                    eventHub.emit('hideProgressBar');
                });
        },

        editCardRefund(payment) {
            this.refund.payment = payment;
            this.showModal('refundPaymentModal');
        },

        refundCardPayment(refund) {
            this.processing = true;
            eventHub.emit('showProgressBar');

            axios.post(`/admin/orders/${this.order.id}/card-payments/${refund.payment.id}/refunds`, refund)
                .then(({ data }) => {
                    this.$emit('updateTotals', data);
                    this.hideModal('refundPaymentModal');
                    this.processing = false;
                    this.refund = {
                        payment: {},
                        full_refund: true,
                        description: null,
                        amount: 0
                    };
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('notify', { message: data.responseText, level: 'info' });
                })
                .catch(error => {
                    eventHub.emit('error', error);
                    this.$emit('error', error);
                    this.processing = false;
                    eventHub.emit('hideProgressBar');
                });
        },

        storeCashPayment(cashPayment) {
            this.processing = true;
            eventHub.emit('showProgressBar');

            axios.post(`/admin/orders/${this.order.id}/payments`, cashPayment)
                .then(({ data }) => {
                    this.$emit('updateTotals', data);
                    this.hideModal('createCashPaymentModal');
                    this.setDefaultCashPayment();
                    this.processing = false;
                    eventHub.emit('notify', { message: data.responseText, level: 'info' });
                    eventHub.emit('hideProgressBar');
                    location.reload();
                })
                .catch(error => {
                    eventHub.emit('error', error);
                    this.$emit('error', error);
                    this.processing = false;
                    eventHub.emit('hideProgressBar');
                });
        },

        editPayment(payment) {
            this.cashPayment = payment;
            this.showModal('editPaymentModal');
        },

        updateCashPayment(payment) {
            axios.put(`/admin/orders/${this.order.id}/payments/${payment.id}`, payment)
                .then(() => {
                    this.hideModal('editPaymentModal');
                })
                .catch(error => {
                    eventHub.emit('error', error);
                    this.$emit('error', error);
                    this.hideModal('editPaymentModal');
                });
        },

        deleteCashPayment(payment) {
            this.cashPayment = payment;
            this.showModal('deletePaymentModal');
        },

        destroyCashPayment(payment) {
            this.processing = true;
            eventHub.emit('showProgressBar');

            axios.delete(`/admin/orders/${this.order.id}/payments/${payment.id}`)
                .then(({ data }) => {
                    this.$emit('updateTotals', data);
                    this.hideModal('deletePaymentModal');
                    this.setDefaultCashPayment();
                    this.processing = false;
                    eventHub.emit('notify', { message: data.responseText, level: 'info' });
                    eventHub.emit('hideProgressBar');
                })
                .catch(error => {
                    eventHub.emit('error', error);
                    this.$emit('error', error);
                    this.processing = false;
                    eventHub.emit('hideProgressBar');
                });
        },

        paymentMethodLabel(paymentMethod) {
            let label = `${paymentMethod.brand.charAt(0).toUpperCase() + paymentMethod.brand.slice(1)} ending in ${paymentMethod.last_four}`;

            if (paymentMethod.default) {
                label += ' (Default)';
            }

            return label;
        }
    }
};
</script>

<template>
<div>
<div class="panel panel-default">
    <div class="panel-body">
        <order-items :order="order" :weightuom="weightuom"></order-items>
    </div>
</div>        

    <div class="row">
    	<div class="content mr-md">
    	    <div class="panel panel-default">
    	        <div class="panel-heading">
    	        <span class="hint--top hint--bounce" data-hint="Notes the customer left at checkout.">
    	        Customer's Notes
    	        </span>
    	        </div>
    	        <div class="panel-body">
    	            <textarea name="customer_notes" class="form-control" rows="10" v-model="order.customer_notes" readonly></textarea>
    	        </div>
    	    </div>
    	</div>

    	<div class="content mr-md">
    	    <div class="panel panel-default">
    	        <div class="panel-heading">
    	        <span class="hint--top hint--bounce" data-hint="Notes that will only appear in the order manager.">
    	        Packing List Notes
    	        </span>
    	        </div>
    	        <div class="panel-body">
    	            <textarea name="packing_notes" class="form-control" rows="10" v-model="order.packing_notes" readonly></textarea>
    	        </div>
    	    </div>
    	</div>

    	<div class="content">
    	    <div class="panel panel-default">
    	        <div class="panel-heading">
    	        <span class="hint--top hint--bounce" data-hint="Notes that appear on the invoice print out.">
    	        Invoice Notes
    	        </span>
    	        </div>
    	        <div class="panel-body">
    	            <textarea name="invoice_notes" class="form-control" rows="10" v-model="order.invoice_notes" readonly></textarea>
    	        </div>
    	    </div>
    	</div>
    </div>

    <order-events :order="order"></order-events>
</div>    
</template>

<script>
import OrderItems from './CanceledOrderItems.vue';
import OrderEvents from "./OrderEvents.vue";

export default {
    props: {
        orderId: {
            required: true,
        },

        weightuom: {
            required: true,
            type: String
        }
    },

    components: {
        OrderItems,
        OrderEvents
    },

    data() {
        return {
            order: {}
        }
    },

    created() {
        this.getOrder()
    },

    methods: {
        getOrder() {
            axios.get(`/api/order/${this.orderId}`)
                .then(({ data }) => this.order = data)
                .catch(error => { });
        }
    }
}
</script>

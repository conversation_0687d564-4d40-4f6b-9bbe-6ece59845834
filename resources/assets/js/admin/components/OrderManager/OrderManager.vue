<template>
    <div v-if="order">
        <div class="row">
            <div class="content">
                <div class="panel">
                    <div class="panel-body pt-lg pb-lg">
                        <div class="flex align-items-m flex-wrap">
                            <div class="flex-item-fill mr-sm">
                                <div class="form-group">
                                    <label for="status">Status</label>
                                    <order-status-select
                                        :excluded="[7]"
                                        :modelValue="order.status_id"
                                        @update:modelValue="updateStatus"
                                    ></order-status-select>
                                </div>
                            </div>

                            <div class="flex-item-fill mr-sm">
                                <div class="form-group">
                                    <label for="Packed By">Packed By</label>
                                    <user-select
                                        :modelValue="order.staff_id"
                                        type="staff"
                                        @update:modelValue="updatePackedBy"
                                    ></user-select>
                                </div>
                            </div>
                            <div class="flex-item-fill mr-sm">
                                <div class="form-group">
                                    <label for="containers">Parcels</label>
                                    <input
                                        v-model="order.containers"
                                        class="form-control"
                                        name="containers"
                                        tabindex="1"
                                        type="text"
                                        @change="updateOrder"
                                    />
                                </div>
                            </div>
                            <div class="flex-item-fill">
                                <div class="form-group">
                                    <label for="containers">Parcels (Dry/Fresh)</label>
                                    <input
                                        v-model="order.containers_2"
                                        class="form-control"
                                        name="containers_2"
                                        tabindex="1"
                                        type="text"
                                        @change="updateOrder"
                                    />
                                </div>
                            </div>
                        </div>

                        <div class="flex">
                            <div class="checkbox flex-item mr-md">
                                <label>
                                    <input
                                        v-model="order.flagged"
                                        type="checkbox"
                                        @change="updateOrder"
                                    />
                                    Flagged
                                </label>
                            </div>
                            <div class="checkbox flex-item mr-md">
                                <label>
                                    <input
                                        v-model="order.fulfillment_error"
                                        type="checkbox"
                                        @change="updateOrder"
                                    />
                                    Mispacked
                                </label>
                            </div>
                            <div class="checkbox flex-item">
                                <label class="checkbox">
                                    <input
                                        v-model="order.exported"
                                        type="checkbox"
                                        @change="updateOrder"
                                    />
                                    Exported
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <Tags v-if="enableTags" :id="order.id" model="orders"/>

                <order-items
                    :id="orderId"
                    :show-types="userRole === 6"
                    @updateTotals="updateTotals"
                ></order-items>
                <order-fees
                    :order="order"
                    :weightuom="weightuom"
                    @updateTotals="updateTotals"
                ></order-fees>
                <order-discounts
                    :order="order"
                    @updateTotals="updateTotals"
                ></order-discounts>
                <order-payments
                    :order="order"
                    @updateTotals="updateTotals"
                ></order-payments>
                <order-events :order="order"></order-events>
            </div>
            <div id="orderSummary" class="sidebar sidebar-right sidebar-1">
                <div class="panel panel-default">
                    <div class="panel-heading flex align-items-m">
                        <div class="flex-item">Order Summary</div>
                        <button
                            class="btn btn-sm btn-alt flex-item push-right"
                            type="button"
                            @click.prevent="refreshTotals()"
                        >
                            <i
                                :class="{ 'fa-spin': refreshing }"
                                class="far fa-sync-alt fa-lg"
                            ></i>
                        </button>
                    </div>
                    <div class="panel-body">
                        <table class="table table-summary">
                            <tr>
                                <td class="text-left">Items:</td>
                                <td class="text-right">&#36;{{ order.subtotal_formatted }}</td>
                            </tr>
                            <tr>
                                <td class="text-left">Fees:</td>
                                <td class="text-right">
                                    &#36;{{ order.fees_subtotal_formatted }}
                                </td>
                            </tr>
                            <tr>
                                <td class="text-left">Tax:</td>
                                <td class="text-right">&#36;{{ order.tax_formatted }}</td>
                            </tr>
                            <tr v-if="order.discounts && order.discounts.length">
                                <td class="text-left">Coupons:</td>
                                <td class="text-right">-{{ $filters.cents(order.coupon_subtotal) }}</td>
                            </tr>
                            <tr>
                                <td class="text-left">Discount:</td>
                                <td class="text-right">
                                    <div class="input-group input-group-inline">
                                        <span class="input-group-addon">&#36;</span>
                                        <input
                                            v-model="order.order_discount_formatted"
                                            :disabled="order.is_paid"
                                            class="form-control input-sm"
                                            min="0"
                                            type="number"
                                            @change="updateDiscount"
                                        />
                                    </div>
                                </td>
                            </tr>
                            <tr>
                                <td class="text-left">Credit:</td>
                                <td class="text-right">
                                    - &#36;{{ order.credit_applied_formatted }}
                                </td>
                            </tr>
                            <tr v-if="(order.is_recurring || order.blueprint_id !== null) && order.subscription_savings > 0">
                                <td class="text-left pb-sm pt-sm">
                                    <strong>Subscription Savings:</strong>
                                </td>
                                <td class="text-right">
                                    -&#36;{{ order.subscription_savings_formatted }}
                                </td>
                            </tr>
                            <tr></tr>
                            <tr>
                                <td class="text-left pb-sm pt-sm"><strong>Total:</strong></td>
                                <td class="text-right">&#36;{{ order.total_formatted }}</td>
                            </tr>
                            <tr>
                                <td class="text-left pb-sm pt-sm">
                                    <strong>Payments:</strong>
                                </td>
                                <td v-if="order.is_paid" class="text-right">
                                    -&#36;{{ order.total_formatted }}
                                </td>
                                <td v-else class="text-right">
                                    -&#36;{{ $filters.cents(order.payments_subtotal) }}
                                </td>
                            </tr>
                            <tr>
                                <td class="text-left pb-sm pt-sm text-danger">
                                    <strong>Total Due:</strong>
                                </td>
                                <td v-if="order.is_paid" class="text-right">
                                    &#36;{{ $filters.cents(0) }}
                                </td>
                                <td v-else class="text-right text-danger">
                                    &#36;{{ $filters.cents(order.total_due) }}
                                </td>
                            </tr>
                        </table>
                    </div>
                </div>
            </div>
        </div>
        <div class="row flex-wrap">
            <div class="content mr-md">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <span
                            class="hint--top hint--bounce"
                            data-hint="Notes the customer left at checkout."
                        >
                            Customer's Notes
                        </span>
                    </div>
                    <div class="panel-body">
            <textarea
                v-model="order.customer_notes"
                class="form-control readonly"
                name="customer_notes"
                readonly
                rows="10"
                @change="updateOrder"
            ></textarea>
                    </div>
                </div>
            </div>

            <div class="content mr-md">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <span
                            class="hint--top hint--bounce"
                            data-hint="Notes that will only appear in the order manager."
                        >
                            Packing List Notes
                        </span>
                    </div>
                    <div class="panel-body">
            <textarea
                v-model="order.packing_notes"
                class="form-control"
                name="packing_notes"
                rows="10"
                @change="updateOrder"
            ></textarea>
                    </div>
                </div>
            </div>

            <div class="content">
                <div class="panel panel-default">
                    <div class="panel-heading">
                        <span
                            class="hint--top hint--bounce"
                            data-hint="Notes that appear on the invoice print out."
                        >
                            Invoice Notes
                        </span>
                    </div>
                    <div class="panel-body">
            <textarea
                v-model="order.invoice_notes"
                class="form-control"
                name="invoice_notes"
                rows="10"
                @change="updateOrder"
            ></textarea>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import OrderItems from './OrderItems.vue';
import OrderFees from './OrderFees.vue';
import OrderPayments from './OrderPayments.vue';
import OrderEvents from './OrderEvents.vue';
import OrderDiscounts from './OrderDiscounts.vue';
import UserSelect from '../UserSelect.vue';
import OrderStatusSelect from '../OrderStatusSelect.vue';
import Tags from '../Tags.vue';

export default {
    props: ['orderId', 'enableTags', 'weightuom', 'userRole'],

    components: {
        OrderItems,
        OrderFees,
        OrderPayments,
        OrderEvents,
        OrderDiscounts,
        UserSelect,
        OrderStatusSelect,
        Tags
    },

    created() {
        this.getOrder();
    },

    mounted() {
        this.registerLivewireAlpineEvents();
    },

    data: () => ({
        order: null,
        showItems: true,
        showFees: true,
        showDiscounts: true,
        highlightFees: false,
        refreshing: false
    }),

    methods: {
        registerLivewireAlpineEvents() {
            window.addEventListener('order-updated', (event) => {
                this.getOrder();
            });

            window.addEventListener('card-order-payment-requested', (event) => {
                eventHub.emit('OrderPayments:createCardPayment');
            });

            window.addEventListener('cash-order-payment-requested', (event) => {
                eventHub.emit('OrderPayments:createCashPayment');
            });
        },

        getOrder() {
            eventHub.emit('showProgressBar');

            axios.get(`/api/order/${this.orderId}`)
                .then(({ data }) => {
                    this.order = data;
                    eventHub.emit('hideProgressBar');
                })
                .catch(error => {
                    eventHub.emit('error', error);
                    eventHub.emit('hideProgressBar');
                });
        },

        updateDiscount() {
            eventHub.emit('showProgressBar');

            axios.put(`/admin/orders/${this.order.id}`, {
                order_discount: this.order.order_discount_formatted
            })
                .then(({ data }) => {
                    eventHub.emit('hideProgressBar');
                    this.updateTotals(data);
                })
                .catch(error => {
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                });
        },

        updateCredit() {
            eventHub.emit('showProgressBar');

            axios.put(`/admin/orders/${this.order.id}`, {
                credit_applied: this.order.credit_applied_formatted
            })
                .then(({ data }) => {
                    eventHub.emit('hideProgressBar');
                    this.updateTotals(data);
                })
                .catch(error => {
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                });
        },

        updateOrder() {
            eventHub.emit('showProgressBar');

            axios.put(`/admin/orders/${this.orderId}`, {
                status_id: this.order.status_id,
                staff_id: this.order.staff_id,
                containers: this.order.containers,
                containers_2: this.order.containers_2,
                packing_notes: this.order.packing_notes,
                invoice_notes: this.order.invoice_notes,
                flagged: this.order.flagged,
                fulfillment_error: this.order.fulfillment_error,
                exported: this.order.exported
            })
                .then(() => {
                    eventHub.emit('hideProgressBar');
                })
                .catch(error => {
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                });
        },

        updateTotals(data) {
            this.order = data.order;
            $('.orderTotal').html(this.order.total_formatted);
        },

        updateStatus(status_id) {
            this.order.status_id = status_id;
            this.updateOrder();
        },

        updatePackedBy(user_id) {
            this.order.staff_id = user_id;
            this.updateOrder();
        },

        refreshTotals() {
            this.refreshing = true;

            axios.put(`/admin/orders/${this.order.id}`)
                .then(({ data }) => {
                    this.updateTotals(data);

                    setTimeout(() => {
                        this.refreshing = false;
                    }, 500);
                })
                .catch(error => {
                    eventHub.emit('error', error);
                    this.refreshing = false;
                });
        }
    }
};
</script>

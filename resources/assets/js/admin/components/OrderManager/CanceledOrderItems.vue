<template>
<div>
	<table class="table table-condensed table-striped ">
    	<thead>
		    <tr>
		        <th>Name</th>
		        <th>Qty.</th>
		        <th>Price</th>
		        <th>Weight</th>
		        <th>Subtotal</th>
		    </tr>
    	</thead>
    	<tbody>
	    	<tr v-for="item in order.items">
	    		<td>
	    			{{ item.title }}&nbsp;<small>({{ item.sku }})</small>
	    		</td>
	    		<td>{{ item.qty }}</td>

	    		<td>&#36;{{ item.unit_price_formatted }} <small>{{ item.unit_of_issue_formatted }}</small></td>

	    		<td>
	    			{{ item.weight }} {{ weightuom }}
	    		</td>

	    		<td>&#36;{{ item.subtotal_formatted}}</td>

	    	</tr>
    	</tbody>
    	<thead>
	        <tr>
	            <th colspan="100%"><br><b>Delivery & Fees</b></th>
	        </tr>
    	</thead>
    	<tbody>
    		<tr v-for="fee in order.fees">
		        <td>{{ fee.title }}</td>
		        <td>{{ fee.qty }}</td>
		        <td>&#36;{{ fee.amount_formatted }}<small>{{ fee.type_formatted }}</small></td>
		        <td></td>
		        <td>&#36;{{ fee.subtotal_formatted }}</td>
		    </tr>

		    <tr>
		        <td>Delivery Fee</td>
		        <td>1</td>
		        <td>&#36;{{ order.delivery_rate_formatted }}<small>/{{ weightuom }}.</small></td>
		        <td>
		            Order Weight: <strong>{{ order.weight }}</strong> {{ weightuom }}
		        </td>
		        <td>&#36;{{ order.delivery_fee_formatted }}</td>
    		</tr>
    		</tbody>
    		<tfoot>
			    <tr>
			        <td colspan="100%">
			            <ul class="order-totals text-right">
			                <li>
			                	Items: <input type="text" class="form-control" v-model="order.subtotal_formatted" readonly>
			                </li>
			                <li>
			                	Fees: <input type="text" class="form-control" v-model="order.fees_subtotal_formatted" readonly>
			                </li>
			                <li>
			                	Discount: <input type="text" class="form-control"
			                	v-model="order.order_discount_formatted" readonly
			                	>
			                </li>
			                <li>
			                	Tax: <input type="text" class="form-control" v-model="order.tax_formatted" readonly>
			                </li>
			                <li>
			                	Customer Credit: <input type="text" class="form-control" v-model="order.credit_applied_formatted" readonly>
			                </li>
			                <li>
			                	<span class="text-danger"><strong>Order Total: </strong></span> 
			                	<input type="text" class="form-control" v-model="order.total_formatted" readonly>
			                </li>
			            </ul>
			        </td>
			    </tr>
		    </tfoot>
    </table>
</div>    
</template>

<script>
	export default {
		props: {
            order: {
                required: true,
                type: Object
            },

            weightuom: {
                required: true,
                type: String
            }
        }
	}
</script>
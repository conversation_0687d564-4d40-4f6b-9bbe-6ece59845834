<template>
<div>
	<div class="panel panel-default">
	    <div class="panel-heading">
	        Tags
	    </div>
	    <div class="panel-body tagBody">
	        <div class="select">
	            <input 
	            	type="text" 
	            	class="form-control" 
	            	autocomplete="off" 
	            	@blur="q = ''" 
	            	@keyup.enter="addTag" 
	            	tabindex="2" v-model="q" 
	            	placeholder="add some tags..." 
	            >
	            <ul class="select-results" v-show="q.length">
	                <li v-if="!totalMatches">
	                    <span @mousedown="addTag"><i class="fa fa-circle-o"></i> Add "{{ q }}"</span>
	                </li>
	                <li v-for="tag in filteredTags" :key="tag.id">
	                    <span @mousedown="toggleTag(tag, $event)"><i class="fa fa-lg" :class="tag.active ? 'fa-check-circle-o' : 'fa-circle-o'"></i> {{ tag.title }}</span>
	                </li>
	            </ul>
	        </div>
	        <div>
	            <div v-show="!activeTags.length"><em>No tags assigned...</em></div>
                <ul class="tags">
	               <li class="tag" v-for="tag in tags" v-show="tag.active" :key="tag.id">
                        <span class="tagTitle">
                            <i class="fa fa-tag"></i> {{ tag.title }}
                        </span>
                        <a href="#" @click="removeTag(tag, $event)" class="tagClose">
                            <i class="fa fa-times fa-fw tag-close"></i>
                        </a>
                   </li>
                </ul>   
	        </div>
	    </div>
	</div>
</div>
</template>

<script>
export default {
    props: ['id','model'],

    data() {
        return {
            showResults: false,
            totalMatches: 0,
            q: '',
            tags: []
        }
    },

    created() {
        this.getTags();
    },

    computed: {
        activeTags() {
            return this.tags.filter(function(tag) {
                if (tag.active) {
                    return true;
                }
            });
        },

        filteredTags() {
            return this.tags.filter(tag => {
              return tag.title.toLowerCase().indexOf(this.q.toLowerCase()) !== -1
            })
        }
    },

    watch: {
        id: function(newVal, oldVal) {
            if (newVal !== oldVal) {
                this.getTags();
            }
        }
    },

    methods: {
        getTags() {
            axios.get(`/admin/${this.model}/${this.id}/tags`)
                .then(({ data }) => {
                    this.tags = data;
                })
                .catch((error) => {});
        },

        toggleTag(tag, e) {
            e.stopPropagation();
            e.preventDefault();

            if ( ! tag.id) return;

            tag.active
                ? this.removeTag(tag)
                : this.assignTag(tag);
        },

        addTag(e) {
            if(e) {
                e.preventDefault;
            }

            if (this.q.length === 0) return;

            eventHub.emit('showProgressBar');

            axios.post(`/admin/${this.model}/${this.id}/tags`, {
                title: this.q
            })
                .then(({ data }) => {
                    this.tags.push(data);
                    this.q = '';

                    eventHub.emit('hideProgressBar');
                })
                .catch(error => {
                    eventHub.emit('hideProgressBar');
                });
        },

        assignTag(tag) {
            tag.active = true;

            eventHub.emit('showProgressBar');

            axios.put(`/admin/${this.model}/${this.id}/tags/${tag.id}`)
                .then(() => {
                    eventHub.emit('hideProgressBar');
                    this.q = '';
                })
                .catch(error => {
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                    tag.active = false;
                });
        },

        removeTag(tag, e) {
            if(e) {
                e.preventDefault();
                e.stopPropagation();
            }

            tag.active = false;

            eventHub.emit('showProgressBar');

            axios.delete(`/admin/${this.model}/${this.id}/tags/${tag.id}`)
                .then(() => {
                    eventHub.emit('hideProgressBar');
                })
                .catch(() => {
                    tag.active = true;
                    eventHub.emit('hideProgressBar');
                });
        },
    }
}
</script>

<style>
.tagBody {
    overflow: visible !important;
}
</style>
<template>
	<select class="form-control" tabindex="1"
        :value="modelValue"
        @input="$emit('update:modelValue', $event.target.value)"
    >
		<option
			v-for="(title, key) in statuses"
            :key="key"
            :value="key"
		>{{ title }}</option>
	</select>
</template>

<script>
	export default {
		created: function() {
			this.getStatuses();
		},

        props: ['modelValue', 'excluded'],

        emits: ['update:modelValue'],

        data: function() {
			return {
				statuses: {},
			}
		},

		methods: {
			getStatuses: function() {
	            axios.get('/api/order-statuses', {params: {excluded: this.excluded}})
	                .then(function(response) {
	                    this.statuses = response.data;
	                }.bind(this))
	                .catch(function(error) {
	                    eventHub.emit('error', error);
	                });
			},
		}
	}
</script>

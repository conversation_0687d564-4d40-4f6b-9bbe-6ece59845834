<template>
	<select class="form-control" tabindex="1" @change="$emit('update:modelValue', $event.target.value)">
		<option 
			v-for="user in users" 
			:value="user.id" 
			:selected="user.id === modelValue"
		>{{ user.first_name+' '+user.last_name }}
		</option>
	</select>
</template>

<script>
	export default {
        props: ['modelValue', 'type'],

        emits: ['update:modelValue'],

        created: function() {
			this.getUsers();
		},

		data: function() {
			return {
				users: [],
			}
		},

		methods: {
			getUsers: function() {
	            axios.get('/api/users', {params: {role_id: [1,2]}})
	                .then(function(response) {
	                    this.users = response.data;
	                }.bind(this))
	                .catch(function(error) {
	                    eventHub.emit('error', error);
	                });
			},
		}
	}
</script>
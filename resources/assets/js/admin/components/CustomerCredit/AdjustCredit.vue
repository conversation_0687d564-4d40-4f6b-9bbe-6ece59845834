<template>
<div class="gc-modal gc-modal-mask" id="adjustCreditModal" @mousedown="hideModal('adjustCreditModal')">
	<div class="gc-modal-wrapper">
		<div class="gc-modal-container" @mousedown.stop>
			<div class="gc-modal-header">
				Adjust Credit
			</div>	
			<div class="gc-modal-body">
				<div class="form-group">
					<label>I would like to</label>
					<select class="form-control" v-model="credit_action" tabindex="1">
						<option value="apply">Apply more credit</option>
						<option value="remove">Remove existing credit</option>
					</select>    
				</div>
				<div class="form-group">
					<label>Credit Amount</label>
					<input 
						type="text" 
						class="form-control" 
						v-model="credit.amount" 
						@keypress.enter="store()" 
						tabindex="1" 
						required
					>
				</div>
				<div class="form-group">
					<label>Reason</label>
					<input 
						type="text" 
						class="form-control" 
						v-model="credit.reasoning" 
						@keypress.enter="store()" 
						tabindex="1" 
					>
				</div>
			</div>
			<div class="gc-modal-footer">
				<button type="button" class="btn btn-alt" @click="hideModal('adjustCreditModal')">Close</button>
				<button 
					type="button" 
					class="btn btn-action" 
					@click="store()" 
					tabindex="1" 
					v-if="credit_action === 'apply'"
				>Apply Credit</button>
				<button 
					type="button" 
					class="btn btn-danger" 
					@click="store()" 
					tabindex="1" 
					v-if="credit_action === 'remove'"
				>Remove Credit</button>
			</div>
		</div>	
	</div>	
</div>
</template>

<script>
	export default {

        emits: ['apply', 'remove'],

		data: function() {
			return {
				credit_action: 'apply',
				credit: {
					amount: 0,
					reasoning: null
				}
			}
		},

		methods: {
			store: function() {
				this.$emit(this.credit_action, this.credit);
			},
		}
	}
</script>
<template>
  <div>
    <div class="form-group">
      <button
        type="button"
        class="btn btn-default"
        @click="showModal('adjustCreditModal')"
      >
        <i class="far fa-plus-circle"></i> Adjust Credit
      </button>
    </div>
    <table class="table table-striped table-full" v-if="credits.length">
      <thead>
        <tr>
          <th>Description</th>
          <th>Date</th>
          <th>Adjusted By</th>
          <th class="text-right">Amount</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="credit in credits" :key="credit.id">
          <td>
            {{ credit.description }}
          </td>
          <td>{{ credit.created_at }}</td>
          <td>{{ credit.who }}</td>
          <td
            class="text-right text-danger"
            v-if="credit.event_id === 'credit_removed'"
          >
            -&#36;{{ credit.amount_formatted }}
          </td>
          <td class="text-right" v-else>&#36;{{ credit.amount_formatted }}</td>
        </tr>
        <tr>
          <td
            colspan="100%"
            class="text-right bold"
            v-html="'&#36;' + total"
          ></td>
        </tr>
      </tbody>
    </table>
    <adjust-credit @apply="storeCredit" @remove="removeCredit"></adjust-credit>
  </div>
</template>

<script>
import AdjustCredit from "./AdjustCredit.vue";
export default {
  props: ["customer"],

  components: {
    AdjustCredit,
  },

  created() {
    this.fetchCredits();
  },

  data() {
    return {
      credits: {},
    };
  },

  computed: {
    total: function() {
      return this.credits
        .reduce(function(prev, item) {
          if (item.event_id === "credit_removed") {
            return prev - item.amount / 100;
          }
          return prev + item.amount / 100;
        }, 0)
        .toFixed(2)
        .toLocaleString();
    },
  },

  methods: {
    fetchCredits: function(credit) {
      axios
        .get("/api/users/" + this.customer.id + "/credits")
        .then(
          function(response) {
            eventHub.emit("hideProgressBar");
            this.credits = response.data.data;
          }.bind(this)
        )
        .catch(
          function(error) {
            eventHub.emit("error", error);
          }.bind(this)
        );
    },

    storeCredit: function(credit) {
      axios
        .post("/api/users/" + this.customer.id + "/credits", credit)
        .then(
          function(response) {
            eventHub.emit("hideProgressBar");
            location.reload();
          }.bind(this)
        )
        .catch(
          function(error) {
            eventHub.emit("error", error);
          }.bind(this)
        );
    },

    removeCredit: function(credit) {
      axios
        .post("/api/users/" + this.customer.id + "/debits", credit)
        .then(
          function(response) {
            eventHub.emit("hideProgressBar");
            location.reload();
          }.bind(this)
        )
        .catch(
          function(error) {
            eventHub.emit("error", error);
          }.bind(this)
        );
    },
  },
};
</script>

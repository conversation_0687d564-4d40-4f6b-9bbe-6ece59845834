<template>
    <div v-if="order" class="pickupManager__orderDetails">

        <div class="pickupManager__orderDetails-header">
            <div class="pickupManager__checkbox headercheckbox">
                <i :class="{'fa-check-square-o checked': order.status_id == 4 ,'fa-square-o text-muted': order.status_id != 4}" class="fa" @click="$emit('pickupOrder',order)"></i>
            </div>
            <div class="headerTitle">
                <h1>{{ order.customer_first_name }} {{ order.customer_last_name }} - #{{ order.id }}</h1>
                <span v-if="order.paid" class="label label-action">Paid</span>
                <span v-else="order.paid" class="label label-danger">NOT PAID</span>
                <span v-if="order.first_time_order" class="label label-light">New Customer!</span>
                <span class="label label-light">{{ order.containers }} Frozen</span>
                <span v-if="order.containers_2" class="label label-light">{{ order.containers_2 }} Fresh</span>
                <span v-if="order.flagged" class="label label-light"><i class="fa fa-flag"></i> Flagged</span>
            </div>
            <div class="text-right headerButtons">
                <button class="btn btn-light" @click="$emit('order-toggled', order)">&nbsp;<i class="fa fa-times"></i>&nbsp;</button>
            </div>
        </div>

        <div>
            <div class="row">
                <div class="col-sm-6 col-xs-12">
                    <ul class="pickupManager__orderDetails-list form-group">
                        <li>
                            <i class="fa fa-phone fa-fw"></i>
                            <a :href="'tel:'+order.customer_phone">{{ order.customer_phone }}</a>
                        </li>
                        <li>
                            <i class="fa fa-envelope-o fa-fw"></i>
                            <a :href="'mailto:' + order.customer_email">{{ order.customer_email }}</a>
                        </li>
                        <li>
                            <i class="fa fa-credit-card fa-fw"></i>
                            <strong>Card On File: </strong>NO CARD ON FILE
                        </li>
                        <li>
                            <i class="fa fa-calendar fa-fw"></i>
                            <strong>Order Date:</strong>
                            {{ order.confirmed_date_formatted }}
                        </li>
                        <li>
                            <i class="fa fa-user fa-fw"></i>
                            <strong>Picked Up:</strong>
                            <i v-if="order.status_id == 4" class="fa fa-check-circle checked"></i>
                            <i v-else class="fa fa-times text-danger"></i>
                        </li>
                    </ul>
                    <hr>
                    <!-- Payment Buttons -->
                    <button class="btn btn-default" @click="$emit('showPaymentModal')">
                        <i class="fa fa-credit-card"></i> Add / Update Card
                    </button>
                    <button v-if="!order.paid" class="btn btn-default"
                            @click="$emit('showChargeModal')">
                        <i class="fa fa-credit-card"></i> Charge Default Card On File
                    </button>
                    <button v-if="order.paid"
                            class="btn btn-default" disabled>
                        <i class="fa fa-credit-card"></i> Order Has Been Charged
                    </button>
                    <!-- Customer Notes -->
                    <div v-if="order.customer_notes" class="form-group">
                        <hr>
                        <label><strong>Customer's Notes</strong></label>
                        <p>{{ order.customer_notes }}</p>
                    </div>
                </div>
                <div class="col-sm-6 col-xs-12">

                    <!-- Order Notes -->
                    <div class="form-group">
                        <label>Order Notes</label>
                        <textarea v-model="order.packing_notes" class="form-control" placeholder="Write any notes about this particular order here." rows="6" @change="$emit('updateOrder', order)"></textarea>
                    </div>

                    <!-- Payment Options -->
                    <div class="form-group">
                        <label>Payment Method</label>
                        <select v-model="order.payment_id" class="form-control" name="payment_id" @change="$emit('updateOrder', order)">
                            <option value="1">Credit Card</option>
                            <option value="2">Pay at Pickup</option>
                            <option value="3">Invoice</option>
                        </select>
                    </div>

                    <!-- Paid -->
                    <div class="checkbox2">
                        <label>
                            <input v-model="order.paid" type="checkbox" @change="$emit('updateOrder', order)"> Mark as Paid
                        </label>
                    </div>

                    <!-- Flag -->
                    <div class="checkbox2">
                        <label>
                            <input v-model="order.flagged" type="checkbox" @change="$emit('updateOrder', order)"> Mark as Flagged
                        </label>
                    </div>
                </div>
            </div>

            <hr>
            <table class="table table-striped">
                <thead>
                <tr>
                    <th>Description</th>
                    <th>Qty.</th>
                    <th>Unit Price</th>
                    <th>Weight</th>
                    <th>Subtotal</th>
                </tr>
                </thead>
                <tbody>
                <tr v-for="item in order.items">
                    <td>{{ item.title }}</td>
                    <td>{{ item.qty }}</td>
                    <td>{{ $filters.currency($filters.cents(item.price)) }}</td>
                    <td>{{ $filters.weight(item.weight) }}</td>
                    <td>{{ $filters.currency($filters.cents(item.subtotal)) }}</td>
                </tr>

                <tr>
                    <td>Delivery Fee</td>
                    <td>1</td>
                    <td>{{ $filters.currency($filters.cents(order.delivery_rate)) }} /{{ weightuom }}.</td>
                    <td>{{ $filters.weight(order.weight) }}</td>
                    <td>{{ $filters.currency($filters.cents(order.delivery_fee)) }}</td>
                </tr>

                <tr v-for="fee in order.fees">
                    <td>{{ fee.title }}</td>
                    <td>{{ fee.qty }}</td>
                    <td>{{ $filters.currency($filters.cents(fee.amount)) }}</td>
                    <td></td>
                    <td>{{ $filters.currency($filters.cents(fee.subtotal)) }}</td>
                </tr>

                <tr v-if="order.order_discount">
                    <td>Discount</td>
                    <td></td>
                    <td>-{{ $filters.currency($filters.cents(order.order_discount)) }}</td>
                    <td></td>
                    <td>-{{ $filters.currency($filters.cents(order.order_discount)) }}</td>
                </tr>

                <tr v-if="order.credit_applied">
                    <td>Credit</td>
                    <td></td>
                    <td>-{{ $filters.currency($filters.cents(order.credit_applied)) }}</td>
                    <td></td>
                    <td>-{{ $filters.currency($filters.cents(order.credit_applied)) }}</td>
                </tr>

                <tr v-for="discount in order.discounts">
                    <td>{{ discount.description }} ({{ discount.code }})</td>
                    <td></td>
                    <td>-{{ $filters.currency($filters.cents(discount.pivot.savings)) }}</td>
                    <td></td>
                    <td>-{{ $filters.currency($filters.cents(discount.pivot.savings)) }}</td>
                </tr>
                </tbody>
                <tfoot class="pickupManager__footer">
                <tr>
                    <td class="text-right" colspan="4"><strong>Subtotal:</strong></td>
                    <td>{{ $filters.currency($filters.cents(order.subtotal + order.fees_subtotal)) }}</td>
                </tr>
                <tr>
                    <td class="text-right" colspan="4"><strong>Tax:</strong></td>
                    <td>{{ $filters.currency($filters.cents(order.tax)) }}</td>
                </tr>
                <tr>
                    <td class="text-right" colspan="4"><strong>Discounts &amp; Credit:</strong></td>
                    <td>{{ $filters.currency($filters.cents(order.order_discount + order.credit_applied + order.coupon_subtotal)) }}</td>
                </tr>
                <tr>
                    <td class="text-right" colspan="4"><strong>Total:</strong></td>
                    <td>{{ $filters.currency($filters.cents(order.total)) }}</td>
                </tr>
                <!-- <tr>
                    <td colspan="100%" class="text-right">
                        <ul class="list-unstyled">
                            <li><strong>Subtotal:</strong> {{ $filters.currency($filters.cents(order.subtotal + order.fees_subtotal)) }}</li>
                            <li><strong>Tax:</strong> {{ $filters.currency($filters.cents(order.tax )) }}</li>
                            <li><strong>Discounts &amp; Credit:</strong> {{ $filters.currency($filters.cents(order.order_discount + order.credit_applied + order.coupon_subtotal )) }}</li>
                            <li><strong>Total:</strong> {{ $filters.currency($filters.cents(order.total )) }}</li>
                        </ul>
                    </td>
                </tr> -->
                </tfoot>
            </table>
        </div>
    </div>
</template>

<script>
export default {
    props: ['order'],

    emits: ['pickupOrder', 'showPaymentModal', 'showChargeModal', 'updateOrder', 'orderToggled']

};
</script>








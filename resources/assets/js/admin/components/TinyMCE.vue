<template>
    <div>
        <textarea
            :value="content"
            class="form-control tinymce"
            name="body"
            rows="20"
            @input="emit('update', $event.target.value)"
        ></textarea>
        <!-- <media-browser @insertPhoto="insertPhoto"></media-browser> -->
    </div>
</template>

<script>
import MediaBrowser from './MediaBrowser/MediaBrowser.vue';

export default {

    components: {
        MediaBrowser
    },

    props: {
        content: {
            type: String,
            required: false
        },
        plugins: {
            type: String,
            required: false,
            default: 'link code autoresize paste textcolor colorpicker lists advlist'
        },
        toolbar: {
            type: String,
            required: false,
            default: 'formatselect | link bullist numlist | forecolor bold italic underline | mergeTags'
        },
        group: {
            type: String,
            required: false,
            default: 'defaultMergeTags'
        }

    },

    emits: ['update'],

    mounted: function() {
        let thisComponent = this;
        tinymce.init({
            selector: '.tinymce',
            init_instance_callback: function(editor) {
                editor.on('Keyup', function(e) {
                    thisComponent.$emit('update', editor.getContent());
                }),
                    editor.on('Change', function(e) {
                        thisComponent.$emit('update', editor.getContent());
                    }),
                    editor.on('Undo', function(e) {
                        thisComponent.$emit('update', editor.getContent());
                    })
                    ,
                    editor.on('Redo', function(e) {
                        thisComponent.$emit('update', editor.getContent());
                    });
            },
            advlist_bullet_styles: 'disc circle square none',
            plugins: thisComponent.plugins,
            menubar: 'edit, alignment, tools, mergetags',
            menu: {
                edit: { title: 'Edit', items: 'undo redo | cut copy paste pastetext | selectall' },
                insert: { title: 'Insert', items: 'link media | template hr' },
                format: { title: 'Format', items: 'bold italic underline | removeformat' },
                alignment: { title: 'Align', items: 'alignleft aligncenter alignright' },
                tools: { title: 'Tools', items: 'code removeformat' },
                mergetags: { title: 'Merge Tags', items: 'ordertags' }
            },
            toolbar: thisComponent.toolbar,
            content_css: '/css/email.css',
            skin: 'grazecart',

            invalid_styles: 'font-family,font-weight,font-size,line-height',
            paste_as_text: true,
            relative_urls: false,
            setup: function(editor) {
                editor.addButton('mergeTags', {
                    type: 'listbox',
                    text: 'Merge Tags',
                    tooltip: 'Merge tags will be replaced with dynamic content.',
                    icon: false,
                    values: thisComponent[thisComponent.group],
                    onselect: function(e) {
                        e.preventDefault;
                        if (this.value() === 'cheat_sheet') {
                            var win = window.open('https://help.grazecart.com/hc/en-us/articles/360022362854-Customizing-emails-with-Merge-Tags', '_blank');
                            if (win) {
                                win.focus();
                            } else {
                                //Browser has blocked it
                                alert('Please allow popups to open this window.');
                            }
                        } else {
                            editor.insertContent(this.value());
                        }

                        this.value('');
                    },
                    onPostRender: function() {
                        // Select the second item by default
                        this.value('');
                    }
                });

                editor.addMenuItem('alignleft', {
                    text: 'Align Left',
                    context: 'alignment',
                    icon: 'alignleft',
                    onclick: function() {
                        editor.execCommand('JustifyLeft');
                    }
                });

                editor.addMenuItem('aligncenter', {
                    text: 'Align Center',
                    context: 'alignment',
                    icon: 'aligncenter',
                    onclick: function() {
                        editor.execCommand('JustifyCenter');
                    }
                });

                editor.addMenuItem('alignright', {
                    text: 'Align Right',
                    context: 'alignment',
                    icon: 'alignright',
                    onclick: function() {
                        editor.execCommand('JustifyRight');
                    }
                });
            }
        });
    },

    data: function() {
        return {
            marketingMergeTags: [
                { text: 'Go to documentation...', value: 'cheat_sheet' },
                { text: '-' },
                { text: 'Customer First name', value: '{customer_first_name}' },
                { text: 'Customer Last name', value: '{customer_last_name}' },
                { text: 'Customer Credit', value: '{store_credit}' },
                { text: 'Pickup/Delivery Name', value: '{pickup_title}' }
            ],

            defaultMergeTags: [
                { text: 'Go to documentation...', value: 'cheat_sheet' },
                { text: '-' },
                { text: 'Farm Name', value: '{farm_name}' },
                { text: 'Farm Phone', value: '{farm_phone}' },
                { text: 'Farm Address', value: '{farm_address}' },
                { text: 'Site URL', value: '{site_url}' },
                { text: '-' },
                { text: 'Customer First name', value: '{customer_first_name}' },
                { text: 'Customer Last name', value: '{customer_last_name}' },
                { text: 'Customer Email', value: '{customer_email}' },
                { text: 'Customer Phone', value: '{customer_phone}' },
                { text: 'Customer Street', value: '{customer_street}' },
                { text: 'Customer Street line 2', value: '{customer_street_2}' },
                { text: 'Customer City', value: '{customer_city}' },
                { text: 'Customer State', value: '{customer_state}' },
                { text: 'Customer Zip', value: '{customer_zip}' },
                { text: '-' },
                { text: 'Order Number', value: '{order_number}' },
                { text: 'Order Subtotal', value: '{order_subtotal}' },
                { text: 'Order Total', value: '{order_total}' },
                { text: 'Order Items', value: '{order_items}' },
                { text: 'Order Fees', value: '{order_fees}' },
                { text: 'Order Weight', value: '{order_weight}' },
                { text: 'Order Discount', value: '{order_discount}' },
                { text: 'Order Subscription Savings', value: '{order_subscription_savings}' },
                { text: 'Order Tax', value: '{order_tax}' },
                { text: 'Order Payment Method', value: '{payment_method}' },
                { text: 'Order Payment Instructions', value: '{payment_instructions}' },
                { text: 'Order Shipping Address', value: '{shipping_address}' },
                { text: 'Order Confirmation Date', value: '{order_date}' },
                { text: 'Order Credit applied', value: '{order_credit}' },
                { text: 'Order Delivery Fee Rate', value: '{order_delivery_rate}' },
                { text: 'Order Delivery Fee Total', value: '{order_delivery_fee}' },
                { text: 'Order Invoice Notes', value: '{order_invoice_notes}' },
                { text: 'Order Customer Notes', value: '{order_customer_notes}' },
                { text: 'Order Packing Notes', value: '{order_packing_notes}' },
                { text: 'Order Delivery Method', value: '{delivery_method}' },
                { text: 'Order Pickup Date', value: '{pickup_date}' },
                { text: 'Order Deadline Date', value: '{order_deadline}' },
                { text: 'Order Deadline End Time', value: '{order_deadline_end_time}' },
                { text: '-' },
                { text: 'Pickup/Delivery Name', value: '{pickup_title}' },
                { text: 'Pickup/Delivery Subtitle', value: '{pickup_subtitle}' },
                { text: 'Pickup/Delivery Times', value: '{pickup_times}' },
                { text: 'Pickup Street', value: '{pickup_street}' },
                { text: 'Pickup Street Line 2', value: '{pickup_street_2}' },
                { text: 'Pickup City', value: '{pickup_city}' },
                { text: 'Pickup State', value: '{pickup_state}' },
                { text: 'Pickup Zip', value: '{pickup_zip}' },
                { text: 'Pickup Order Deadline', value: '{pickup_deadline}' },
                { text: 'Next Pickup Date', value: '{pickup_next_date}' },
                { text: '-' },
                { text: 'Email Subject', value: '{email_subject}' },
                { text: '-' },
                { text: 'Re-order Frequency', value: '{reorder_frequency}' },
                { text: 'Next Reorder Deadline Date', value: '{next_reorder_deadline_date}' },
                { text: 'Next Reorder Delivery Date', value: '{next_reorder_pickup_date}' },
                { text: '-' },
                { text: 'Gift Card Code', value: '{gift_card_code}' },
                { text: 'Gift Card Amount', value: '{gift_card_amount}' },
                { text: 'Gift Card Sender', value: '{gift_card_sender}' },
                { text: 'Gift Card Redemption URL', value: '{gift_card_redemption_url}' }
            ]
        };
    },

    methods: {
        setContent: function(content) {
            tinyMCE.activeEditor.setContent(content);
        },
        init: function() {
            tinyMCE.activeEditor.setContent(this.content);
        },
        insertPhoto: function(photo) {
            tinyMCE.activeEditor.insertContent('<img src="' + photo.path + '" alt="' + photo.title + '" style="max-width: 100%;">');
        },
        insertContent: function(content) {
            tinyMCE.activeEditor.insertContent(content);
        }
    }
};
</script>

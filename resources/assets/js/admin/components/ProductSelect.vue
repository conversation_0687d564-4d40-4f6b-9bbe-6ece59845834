<template>
  <div>
    <v-select
      :options="products"
      @search="getProducts"
      v-model="selectedProduct"
      label="title"
    >
      <template v-slot:no-options>
          <span>Search for a product...</span>
      </template>
    </v-select>
    <input type="hidden" :name="name" v-model="selectedId" />
  </div>
</template>

<script>
import vSelect from 'vue-select';
import 'vue-select/dist/vue-select.css';

import { debounce } from 'lodash';

export default {
  components: {
    vSelect
  },

  props: ['selected', 'name', 'title'],

  data: function() {
    return {
      products: [],
      selectedProduct: null,
      selectedId: null
    }
  },

  methods: {
    getProducts: debounce(function(query) {
      this.products = [{title: 'Searching...'}]
      axios.get('/api/products', {params: {q: query}})
        .then(function (response) {
          this.products = response.data.map(product => ({
            value: product.id,
            title: product.title
          }));
        }
        .bind(this))
        .catch(function(error) {
          eventHub.emit('error', error);
        });
    }, 500),
  },

    watch: {
      selectedProduct(product) {
          if(product.value) {
              this.selectedId = product.value;
          }
      }
    }
}
</script>


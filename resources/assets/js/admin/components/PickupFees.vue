<template>
<div>
	<div class="gc-modal gc-modal-mask" id="createFeeModal" @click="hideModal('createFeeModal')">
		<div class="gc-modal-wrapper">
			<div class="gc-modal-container" @click.stop>
				
				<div class="gc-modal-header">
					Add Fee
				</div>
	
				<div class="gc-modal-body">
					<div class="form-group">
	                    <label for="title">Title</label>
	                    <input type="text" class="form-control" v-model="title" placeholder="Handling fee">
	                </div>

	                <div class="form-group">
	                    <label for="qty">Amount</label>
	                    <div class="input-group">
	                    <div class="input-group-addon">$</div>
	                    <input type="text" class="form-control" v-model="amount"/>
	                    </div>
	                </div>

	                <div class="form-group radio">
	                    <label class="mr-sm">
	                        <input type="radio" :value="1" v-model.number="taxable" > Taxable
	                    </label>
	                    <label>
	                        <input type="radio" :value="0" v-model.number="taxable"> Not Taxable
	                    </label>
	                </div>
				</div>
	
				<div class="gc-modal-footer">
					<button type="button" class="btn btn-alt" @click="hideModal('createFeeModal')">Cancel</button>
	                <button type="button" class="btn btn-action" @click="storeFee()" :disabled="! title.length">Add Fee</button>
				</div>
			</div>
		</div>
	</div>

	<div class="gc-modal gc-modal-mask" id="editFeeModal" @click="hideModal('editFeeModal')">
		<div class="gc-modal-wrapper">
			<div class="gc-modal-container" @click.stop>
				
				<div class="gc-modal-header">
					Edit Fee
				</div>
	
				<div class="gc-modal-body">
					<div class="form-group">
	                    <label for="title">Title</label>
	                    <input type="text" class="form-control" v-model="fee.title"/>
	                </div>

	                <div class="form-group">
	                    <label for="qty">Amount</label>
	                    <div class="input-group">
	                    	<div class="input-group-addon">$</div>
	                    	<input type="text" class="form-control" v-model="fee.amountFormatted"/>
	                    </div>
	                </div>

	                <div class="form-group radio">
	                    <label class="mr-sm">
	                        <input type="radio" :value="1" v-model.number="fee.taxable"> Taxable
	                    </label>
	                    <label>
	                        <input type="radio" :value="0" v-model.number="fee.taxable"> Not Taxable
	                    </label>
	                </div>
				</div>
	
				<div class="gc-modal-footer flex">
					<button type="button" class="flex-item btn btn-alt push-left" @click="destroyFee()">Delete</button>
					<button type="button" class="flex-item btn btn-alt" @click="hideModal('editFeeModal')">Cancel</button>
	                <button type="button" class="flex-item btn btn-action" @click="updateFee()">Save</button>
				</div>
			</div>
		</div>
	</div>

	<div class="panel panel-default">
		<div class="panel-heading flex align-items-m">
			<div class="flex-item">Additional Fees</div>
			<button type="button" class="btn btn-alt btn-sm flex-item push-right" @click="showModal('createFeeModal')"><i class="fa fa-plus-circle fa-lg"></i> Add Fee</button>
		</div>
		<div class="panel-body">
		<table class="table  table-striped">
		    <thead>
		    <tr>
		        <th>Name</th>
		        <th>Amount</th>
		    </tr>
		    </thead>
		    <tbody>
		    <tr v-if="!fees.length"><td colspan="100%">No additional fees assigned.</td></tr>
		    <tr v-for="fee in fees">
		        <td><a href="#" @click.prevent="editFee(fee)">{{ fee.title }}</a></td>
		        <td>&#36;{{ fee.amountFormatted }}<span v-if="fee.type === 'weight text-success'">/{{ weightuom }}.</span></td>
		    </tr>
		    </tbody>
		</table>
		</div>
	</div>
</div>	
</template>

<script>
	export default {
		created: function() {
        	this.getPickupFees();
    	},

    	props: ['id'],

	    data: function() {
	    	return {
	    		fees: [],
		        title: '',
		        amount: 0,
		        note: '',
		        type: 'fixed',
		        cap: 0,
		        threshold: 0,
		        apply_limit: false,
		        taxable: 0,
		        fee: {}
	    	}
	    },


	    methods: {
	        getPickupFees: function() {
	            var url = '/admin/pickups/'+this.id+'/fees';
	            axios.get(url)
	                .then(function(response) {
	                    this.fees = response.data;
	                }.bind(this))
	                .catch(function(error) {
	                });
	        },

	        storeFee: function() {
	            var url = '/admin/pickups/'+this.id+'/fees';
	            var payload = {
	                pickup_id: this.id,
	                title: this.title,
	                amount: this.amount,
	                note: this.note,
	                taxable: this.taxable,
	                apply_limit: this.apply_limit
	            }

	            axios.post(url, payload)
	            	.then(function(response) {
	            		this.hideModal('createFeeModal');
	            		this.getPickupFees();
	            	}.bind(this))
	            	.catch(function(error) {
	            	})
	        },

	        updateFee: function() {
	            var url = '/admin/pickups/'+this.id+'/fees/'+this.fee.id;
	            var payload = {
	                title: this.fee.title,
	                amount: this.fee.amountFormatted,
	                note: this.fee.note,
	                type: this.fee.type,
	                taxable: this.fee.taxable,
	                threshold: this.fee.thresholdFormatted,
	                cap: this.fee.capFormatted,
	                apply_limit: this.fee.apply_limit
	            }

	            axios.put(url, payload)
	                .then(function(response) {
	                	this.hideModal('editFeeModal');
	                    this.getPickupFees();
	                }.bind(this))
	                .catch(function(error) {
	                });
	        },

	        destroyFee: function() {
	            var url = '/admin/pickups/'+this.id+'/fees/'+this.fee.id;
	            axios.delete(url)
	                .then(function(response) {
	                	this.hideModal('editFeeModal');
	                    this.getPickupFees();
	                }.bind(this))
	                .catch(function(error) {
	                });
	        },

	        editFee: function(fee) {
	            this.fee = fee
	            this.showModal('editFeeModal');
	        },
    	}
	}
</script>
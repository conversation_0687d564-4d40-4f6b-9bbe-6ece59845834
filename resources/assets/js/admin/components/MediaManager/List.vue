<template>
	<table class="table table-striped table-list table-full">
		<tbody>
	    	<tr v-for="(photo, index) in photos.data">
	    		<td class="br-top-0">
	    			<div class="mediaBrowserList__ImagePreviewContainer">
		        		<img 
		        			:src="photo.thumbnail_path"
		        			class="bg-grid block mt-sm mb-sm" 
		        			@click="select(photo, index)"
		        		>
	        		</div>
	        	</td>
	        	<td class="br-top-0">
	        		<p>{{ photo.title }}</p>
	        		{{ photo.width }} x {{ photo.height }} | {{ photo.file_size_formatted }} | {{ photo.created_at_formatted }}
	        	</td>
	        	<td class="br-top-0">
	        		<button class="btn btn-alt btn-sm pl-0" @click="select(photo, index)">Edit</button>
	        		<button class="btn btn-alt btn-sm" @click="remove(photo, index)">Delete</button>
	        	</td>	
	    	</tr>
	    </tbody>	
	</table>
</template>

<script>
	export default {
		props: ['photos'],

        emits: ['selected', 'remove'],

		methods: {
			select: function(photo, index)
			{
				photo.index = index;
				this.$emit('selected', photo);
			},

			remove: function(photo, index) {
				photo.index = index;
				this.$emit('remove', photo);
			},

			toggleChecked: function(photo)
			{
				if(this.photoIsSelected(photo))
				{
					var index = this.selected.indexOf(photo);
					this.selected.splice(index, 1);
				}
				else
				{
					this.selected.push(photo)
				}
				
			},

			photoIsSelected: function(photo)
			{
				let i;
			    for (i = 0; i < this.selected.length; i++) {
			        if (this.selected[i] === photo) {
			            return true;
			        }
			    }

			    return false;
			}
		}
	} 
</script>

<style>
.mediaBrowserList__ImagePreviewContainer {
	max-width: 150px;
}
</style>
<template>
<div class="gc-modal gc-modal-mask" id="editPhotoModal" @click="hideModal('editPhotoModal')">
    <div class="gc-modal-wrapper">
        <div class="gc-modal-container gc-modal-container--large" @click.stop>
            
            <div class="gc-modal-header">
                {{ photo.title }}
            </div>

            <div class="gc-modal-body">
                <div class="row">
                    <div class="content">
                        <div v-if="photo.type === 'image'" class="text-center">
                            <a :href="photo.path" target="_blank">
                                <img :src="photo.path" :alt="photo.title" class="MediaManager__activePhoto bg-grid">
                            </a>
                        </div>
                        <div v-if="photo.type === 'document'">
                            <object :data="photo.path" type="application/pdf" width="100%" height="600">
                            <p>It appears you don't have a PDF plugin for this browser.
                                No biggie... you can <a :href="photo.path">click here to
                                    download the PDF file.</a></p>
                            </object>
                        </div>

                        <div class="form-group">
                            <small class="text-muted" style="word-break: break-all;">
                            <a class="text-gray-light" :href="photo.path" target="_blank">{{ photo.path }}</a></small>
                        </div>
                    </div>
                    <div class="sidebar-right sidebar sidebar-1">
                        <div class="form-group">
                            <label>Title</label>
                            <input v-model="photo.title" class="form-control">
                        </div>

                        <div class="form-group">
                            <label>Caption</label>
                            <textarea v-model="photo.caption" class="form-control" rows="3"></textarea>
                        </div>
                        <ul class="list-unstyled">
                            <li><label>Uploaded:</label> {{ photo.created_at_formatted }}</li>
                            <li><label>Dimensions:</label> {{ photo.width+' x '+photo.height}}</li>
                            <li><label>File Size:</label> {{ photo.file_size_formatted }}</li>
                        </ul>

                        <tags :id="photo.id" model="photos" v-if="photo" class="mt-md"></tags>
                    </div>
                </div>
            </div>

            <div class="gc-modal-footer">
                <button type="button" class="btn btn-alt" @click="hideModal('editPhotoModal')">Cancel</button>
                <button type="button" class="btn btn-action" @click="update(photo)">Save</button>
            </div>
        </div>
    </div>
</div>
</template>

<script>
import Tags from '../Tags.vue';
export default {
		props: ['photo'],

    emits: ['update', 'destroy'],

        components: {Tags},

		methods: {
			update: function(photo)
			{
                this.$emit('update', photo);
			},

			destroy: function(photo)
			{
                this.$emit('destroy', photo);
			}
	}
}
</script>

<style>
.MediaManager__activePhoto {
	max-height: 500px; max-width: 100%;
}

.MediaManager__activePhoto:hover {
	cursor: zoom-in;
}
</style>
<template>
    <form v-show="show" id="mediaUpload" action="/api/photos" class="dropzone">
        <div class="dz-message">
            <strong>Drop files here or click to upload.</strong><br/>
            <small>(Max file size: 1MB)</small>
        </div>
        <div class="fallback">
            <input multiple name="file" type="file"/>
        </div>
    </form>
</template>

<script>
import Dropzone from 'dropzone';

export default {
    props: {
        show: {
            required: false,
            default: false
        },
        tags: {
            type: Array,
            required: false,
            default: function() {
                return [];
            }
        }
    },

    emits: ['upload-complete'],

    mounted() {
        var that = this;
        Dropzone.autoDiscover = false;
        new Dropzone('#mediaUpload', {
            headers: { 'X-CSRF-TOKEN': document.querySelector('#token').getAttribute('value') },
            paramName: 'file',
            maxFilesize: 1,
            acceptedFiles: 'image/jpg,image/jpeg,image/png,image/webp,application/pdf',
            url: '/api/photos',
            params: {
                tags: this.tags
            },
            init: function() {
                this.on('success', function(e, response) {
                });

                this.on('error', function(e, response) {
                    alert(response);
                });

                this.on('queuecomplete', function(e, response) {
                    that.$emit('upload-complete');
                    this.removeAllFiles();
                });
            }
        });
    }
};
</script>

<style>
.dropzone {
    margin: 30px;
}
</style>

<template>
    <div class="coverPhoto">
        <div class="coverPhoto__preview" style="background-image: url('/images/grid-bg.jpg')">
            <img :src="coverPhoto" :alt="coverPhoto" @click.prevent="add" class="coverPhoto__image">
        </div>
        <small v-if="coverPhoto"><a href="#" @click.prevent="remove" class="text-danger"><i class="fa fa-times"></i> Remove Photo</a></small>
        <div v-else>
            <img
                class="coverPhoto__placeholder"
                src="https://s3.amazonaws.com/grazecart/stockphotos/photo-grid-placeholder.jpg"
                alt="Add Photo"
                @click.prevent="add"
            >
            <button type="button" class="btn btn-default coverPhoto__btn" @click.prevent="add">{{ message }}</button>
        </div>
        <media-browser
            ref="mediaBrowser"
            @insertPhoto="update"
            :tags="tags"
        ></media-browser>
    </div>
</template>

<script>
import mediaBrowser from '../MediaBrowser/MediaBrowser.vue'
export default {
    props: {
        url: {
            type: String,
            required: false
        },
        name: '',
        src: {
            required: false,
        },
        field: {
            required: false
        },
        message: {
            default: 'Add Cover Photo'
        },
        active: {
            required: false,
            default: false
        },
        tags: {
            type: Array,
            required: false,
            default: function() {
                return null
            }
        }
    },

    emits: ['input', 'toggle'],

    components: {
        mediaBrowser
    },

    created: function() {
        if(typeof this.field == 'undefined') {
            this.databaseField = 'photo_path';
        } else {
            this.databaseField = this.field;
        }

        this.coverPhoto = this.src;
    },

    data: function() {
        return {
            coverPhoto: '',
            databaseField: ''
        }
    },

    watch: {
        src: function(val) {
            this.coverPhoto = val;
        }
    },

    methods: {
        remove: function() {
            this.update({path: '', thumbnail_path: ''});
            this.coverPhoto = '';
        },

        add: function() {
            this.$refs.mediaBrowser.toggle();
        },

        update: function(photo) {
            this.coverPhoto = photo.path;
            if(this.url)
            {
                eventHub.emit('showProgressBar');
                var payload = {};
                payload[this.databaseField] = photo.path
                payload['cover_photo_thumbnail'] = photo.thumbnail_path

                axios.put(this.url, payload).then(function(response) {
                    eventHub.emit('hideProgressBar');
                }.bind(this)).catch(function(error) {
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                });
            }

            this.$emit('input', photo);
        }
    }

}
</script>

<style>
.coverPhoto {
    text-align: left;
    max-width: 344px;
    /*margin: 0 auto;*/
}

.coverPhoto__preview img {
    max-width: 100%;
    display: block;
    margin: 0 auto;
    margin-bottom: 10px;
    overflow: hidden;
}

.coverPhoto__preview img:hover, .coverPhoto__placeholder:hover {
    opacity: 0.9;
    cursor: pointer;
}

.coverPhoto__placeholder {
    max-width: 100%;
    display: block;
    margin: 0 auto;
}

.coverPhoto__btn {
    display: block;
    width: 100%;
    max-width: 344px;
    margin: 0 auto;
}
</style>
<template>
	<div class="flex align-items-m justify-around flex-wrap pa-md pr-0">
		<div 
			class="flex-item mr-md mb-md mediaBrowser_container bg-gray-lighter-hover" 
			v-for="(photo, index) in photos.data"
		>
	    	<div class="mediaBrowser_imageContainer relative">
	    		<img :src="photo.thumbnail_path" class="bg-grid block shadow-soft">
		    	<div class="pa-sm mediaBrowser_actions text-center">
		    		<div class="text-center fs-1 mb-md">{{ photo.title }}</div>
		    		<div class="flex align-items-m">
		    			<button class="btn btn-sm btn-alt flex-item-fill" @click="select(photo, index)">Edit</button>
		    			<button class="btn btn-sm btn-alt flex-item-fill mr-sm" @click="remove(photo, index)">Delete</button>
		    		</div>
		    	</div>
	    	</div>
	    </div>
	</div>
</template>

<script>
	export default {
		props: ['photos'],

        emits: ['selected', 'remove'],

		methods: {
			select: function(photo, index)
			{
				photo.index = index;
				this.$emit('selected', photo);
			},

			remove: function(photo, index) {
				photo.index = index;
				this.$emit('remove', photo);
			},
		}
	} 
</script>

<style>
	.mediaBrowser_container {
		width: 250px;
	}
	.mediaBrowser_actions {
		position: absolute;
		bottom: 0;
		left: 0;
		opacity: 0;
		width: 100%;
		background-color: rgba(255,255,255,0.9);
		transition: all ease-in-out 0.1s;
		transform: translateY(0.5rem);
		transform-origin: bottom;
	}
	.mediaBrowser_container:hover > .mediaBrowser_imageContainer > .mediaBrowser_actions {
		opacity: 1;
		transform: translateY(0);
	}
	.mediaBrowser_container:hover > .mediaBrowser_imageContainer > img {
		transition: all ease-in-out 0.1s;
	}
	.mediaBrowser_container:hover > .mediaBrowser_imageContainer > img {
		box-shadow: 0 7px 14px 0 rgba(50,50,93,.1), 0 3px 6px 0 rgba(0,0,0,.07);
	}
	.mediaBrowser_container:focus > .mediaBrowser_actions {
		opacity: 1;
		transform: translateY(0);
	}
</style>
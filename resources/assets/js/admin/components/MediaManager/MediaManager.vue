<template>
    <div>
        <uploader :show="showUploader" @upload-complete="handleUploadCompleted"></uploader>
        <div class="panel">
            <div class="panel-heading">
                <div class="flex align-items-m flex-wrap">
                    <div class="input-group mr-sm flex-item-fill">
                        <button class="btn btn-light br-0 br-right-0" @click="search(query)">
                            <i class="fa fa-search"></i>
                        </button>
                        <input
                            type="text"
                            class="form-control"
                            placeholder="Search photos..."
                            @keyup="search(query)"
                            v-model="query"
                        >
                        <button
                            v-show="query.length"
                            type="button"
                            class="btn btn-light br--r br-left-0"
                            @click="clearSearch()"
                        ><i class="fa fa-times"></i></button>
                    </div>

                    <div class="btn-group mr-sm flex-item">
                        <button class="btn btn-default" @click="prevPage" :disabled="firstPage">
                            <i class="fa fa-chevron-left"></i>
                        </button>
                        <button class="btn btn-default" disabled>
                            Page {{ photos.current_page }} of {{ photos.last_page }}
                        </button>
                        <button class="btn btn-default" @click="nextPage" :disabled="lastPage">
                            <i class="fa fa-chevron-right"></i>
                        </button>
                    </div>

                    <div class="dropdown flex-item">
                        <button class="btn btn-default dropdown-toggle" type="button" data-toggle="dropdown">
                            <i
                                class="fa"
                                :class="{'fa-list': view == 'List', 'fa-th': view == 'Grid'}"
                            >
                            </i> View <i class="fa fa-caret-down"></i>
                        </button>
                        <ul class="dropdown-menu pull-right">
                            <li><a href="#" @click="view = 'Grid'"><i class="fa fa-th"></i> Grid</a></li>
                            <li><a href="#" @click="view = 'List'"><i class="fa fa-list"></i> List</a></li>
                        </ul>
                    </div>
                </div>

            </div>

            <div class="panel-body pa-0">
                <component
                    :is="view"
                    :photos="photos"
                    @selected="select"
                    @remove="remove"
                ></component>
            </div>
            <div class="panel-footer text-center">
                <div class="btn-group">
                    <button class="btn btn-default" @click="prevPage" :disabled="firstPage">
                        <i class="fa fa-chevron-left"></i>
                    </button>
                    <button class="btn btn-default" disabled>
                        Page {{ photos.current_page }} of {{ photos.last_page }}
                    </button>
                    <button class="btn btn-default" @click="nextPage" :disabled="lastPage">
                        <i class="fa fa-chevron-right"></i>
                    </button>
                </div>
            </div>
        </div>
        <edit-photo
            :photo="photo"
            @update="update"
        ></edit-photo>

        <div class="gc-modal gc-modal-mask" id="deletePhotoModal" @click="hideModal('deletePhotoModal')">
            <div class="gc-modal-wrapper">
                <div class="gc-modal-container" @click.stop>

                    <div class="gc-modal-header">
                        Delete Photo
                    </div>

                    <div class="gc-modal-body">
                        Are you sure you want to delete the photo {{ photo.title }}?
                    </div>

                    <div class="gc-modal-footer">
                        <button type="button" class="btn btn-alt" @click="hideModal('deletePhotoModal')">Cancel</button>
                        <button type="submit" class="btn btn-danger" @click="destroy(photo)">Delete Photo</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import _ from "lodash";
import Uploader from "./Uploader.vue";
import List from "./List.vue";
import Grid from "./Grid.vue";
import Stock from "./StockGallery.vue";
import EditPhoto from "./EditPhoto.vue";

export default {
    components: {
        Uploader, List, Grid, Stock, EditPhoto
    },

    created() {
        this.getFiles(1);

        eventHub.on('MediaManager:toggleUpload', () => this.showUploader = !this.showUploader);
        eventHub.on('editPhotoModal:closed', () => this.photo = false);
    },

    data() {
        return {
            show: false,
            photos: [],
            stockPhotos: [],
            photo: false,
            query: '',
            showUploader: false,
            view: 'Grid',
            mode: 'uploads',
            page: 1,
        }
    },

    computed: {
        firstPage() {
            return this.photos.current_page === 1;
        },
        lastPage() {
            return this.photos.current_page === this.photos.last_page || !this.photos.data.length;
        },
        smallOption() {
            return {
                width: Math.round(this.photo.width * 0.25),
                height: Math.round(this.photo.height * 0.25)
            };
        },
        mediumOption() {
            return {
                width: Math.round(this.photo.width * 0.5),
                height: Math.round(this.photo.height * 0.5)
            };
        },
        largeOption() {
            return {
                width: this.photo.width,
                height: this.photo.height
            };
        }
    },

    methods: {
        handleUploadCompleted() {
            this.getFiles(1)
            this.showUploader = false;
        },

        getFiles(page) {
            eventHub.emit('showProgressBar');
            this.mode = 'uploads';
            this.page = page;

            axios.get('/api/photos', { params: {
                page: page,
                q: this.query
            }})
                .then(({ data }) => {
                    eventHub.emit('hideProgressBar');
                    this.photos = data;
                })
                .catch(error => {
                    eventHub.emit('hideProgressBar');
                });
        },

        search: _.debounce(function(query) {
            this.getFiles();
        }, 250),

        clearSearch() {
            this.query = '';
            this.getFiles();
        },

        select(photo) {
            this.photo = photo;
            this.showModal('editPhotoModal');
        },

        toggle() {
            this.show = !this.show;

            let overflow = 'auto';
            if (this.show) {
                overflow = "hidden";
            }

            document.body.style.overflow = overflow
        },

        prevPage() {
            if (this.photos.current_page <= 0) return;

            this.getFiles(this.photos.current_page - 1);
        },

        nextPage() {
            if(this.photos.current_page >= this.photos.last_page) return;
            this.getFiles(this.photos.current_page + 1);
        },

        update(photo) {
            eventHub.emit('showProgressBar');

            axios.put(`/api/photos/${photo.id}`, {
                'title': photo.title,
                'caption': photo.caption
            })
                .then(response => {
                    eventHub.emit('hideProgressBar');
                    this.hideModal('editPhotoModal');
                })
                .catch(error => {
                    eventHub.emit('hideProgressBar');
                });
        },

        remove(photo) {
            this.photo = photo;
            this.showModal('deletePhotoModal');
        },

        destroy(photo) {
            eventHub.emit('showProgressBar');

            axios.delete(`/api/photos/${photo.id}`)
                .then(response => {
                    eventHub.emit('hideProgressBar');
                    this.photos.data.splice(photo.index, 1);
                    this.photo = false;
                    this.hideModal('deletePhotoModal');
                })
                .catch(error => {
                    eventHub.emit('hideProgressBar');
                });
        }
    }
}
</script>
<template>
<div class="gc-modal gc-modal-mask" id="notifyModal" @click="hideModal('notifyModal')">
	<div class="gc-modal-wrapper">
		<div class="gc-modal-container" @click.stop>
			
			<div class="gc-modal-header">
				Send Notification
			</div>

			<div class="gc-modal-body">
				<input type="hidden" name="_method" value="POST">
            	<div class="form-group">
	                <select name="notification" class="form-control" v-model="notification">
	                    <option value="packed">Order Packed</option>
	                    <option value="confirmed">Order Confirmation</option>
	                    <option value="custom">Custom</option>
	                </select>
                </div>
                <div v-show="notification == 'custom'">
                	<div class="form-group">
                		<label>Subject</label>
                		<input type="text" class="form-control" name="subject" v-model="subject" autofocus>
                	</div>
                	<div class="form-group">
                		<label>Message</label>
                		<tiny-editor :content="message" @update="update"></tiny-editor>
                	</div>	
                </div>
			</div>

			<div class="gc-modal-footer">
				<button type="button" class="btn btn-alt" @click="hideModal('notifyModal')">Cancel</button>
                <button type="button" class="btn btn-action" @click="send(notification)">Send</button>
			</div>
		</div>
	</div>
</div>
</template>

<script>
	import TinyEditor from '../TinyMCE.vue'
	export default {

		props: ['order'],

		components: {
            TinyEditor
        },

		data: function()
		{
			return {
				notification: 'packed',
				subject: '',
				message: ''
			}
		},

		methods: {
			update: function(message) {
				this.message = message;
			},

			send: function(notification)
			{
				eventHub.emit('showProgressBar');
	            var url = '/admin/orders/'+this.order+'/notifications/'+notification;
	            axios.post(url, {subject: this.subject, message: this.message})
	                .then(function(response) {
	                    eventHub.emit('hideProgressBar');
	                    eventHub.emit('notify', {level: 'info', message: response.data});
	                    this.hideModal('notifyModal');

	                }.bind(this))
	                .catch(function(error) {
	                	eventHub.emit('hideProgressBar');
	                	eventHub.emit('error', error);
	                });
			}
		}
	}
</script>
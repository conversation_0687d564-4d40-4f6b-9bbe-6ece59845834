<template>
    <input v-if="selected" type="hidden" :name="name" :id="name" :value="selected.id"/>
    <Listbox as="div" v-model="selected">
        <div class="relative z-10 mt-1">
            <ListboxButton @click="focusOnSearchInput" class="relative w-full cursor-default rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-left shadow-sm focus:border-keppel-500 focus:outline-none focus:ring-1 focus:ring-keppel-500 sm:text-sm">
                <span v-if="selected" class="flex items-center">
                    <span class="block truncate">{{ selected.title }}</span>
                </span>
                <span v-else class="flex items-center">
                    <span class="block truncate">None</span>
                </span>
                <span class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                    <ChevronDownIcon class="h-5 w-5 text-gray-400" aria-hidden="true" />
                </span>
            </ListboxButton>

            <transition leave-active-class="transition ease-in duration-100" leave-from-class="opacity-100" leave-to-class="opacity-0">
                <ListboxOptions class="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none sm:text-sm">
                    <li :class="['text-gray-900 relative cursor-default select-none py-2 px-3']">
                        <div class="relative mt-1 rounded-md shadow-sm">
                            <input type="text"
                                   ref="searchInput"
                                   v-model="searchQuery"
                                   @keyup="searching = true"
                                   placeholder="Search products..."
                                   class="block w-full rounded-md border-gray-300 pr-10 focus:border-keppel-500 focus:ring-keppel-500 sm:text-sm"
                            />
                            <div v-show="searching" class="pointer-events-none absolute inset-y-0 right-0 flex items-center">
                                <svg class="animate-spin -ml-1 mr-3 h-5 w-5 text-gray-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </div>
                        </div>
                    </li>
                    <ListboxOption as="template" v-for="option in options" :key="option.id" :value="option" v-slot="{ active, selected }">
                        <li :class="[active ? 'text-white bg-keppel-600' : 'text-gray-900', 'relative cursor-default select-none py-2 pl-3 pr-9']">
                            <div class="flex items-center">
                                <span :class="[selected ? 'font-semibold' : 'font-normal', 'block truncate']" v-text="option.title"></span>
                            </div>

                            <span v-if="selected" :class="[active ? 'text-white' : 'text-keppel-600', 'absolute inset-y-0 right-0 flex items-center pr-4']">
                                <CheckIcon class="h-5 w-5" aria-hidden="true" />
                            </span>
                        </li>
                    </ListboxOption>
                </ListboxOptions>
            </transition>
        </div>
    </Listbox>
</template>

<script>
export default {
    name: "ProductSearchSelect"
};
</script>

<script setup>
import { Listbox, ListboxButton, ListboxLabel, ListboxOptions, ListboxOption } from '@headlessui/vue'
import { ChevronDownIcon, CheckIcon } from '@heroicons/vue/solid'
import { debounce } from "lodash";
import { ref, watch } from "vue";
const props = defineProps({
    name: { type: String },
    selected: { default: null, type: Object }
})
const searchInput = ref(null)
const searchQuery = ref('')
const searching = ref(false)
const options = ref([])

const selected = ref(props.selected)

const focusOnSearchInput = () => {
    const interval = setInterval(() => {
        if ( ! searchInput.value) return;
        searchInput.value.focus()
        clearInterval(interval)
    }, 50)
}

const fetchOptions = debounce(query => {
    axios.get('/api/products', {
        params: { products: query }
    })
        .then(({ data }) => {
            options.value = data
                .filter(product => parseInt(product.id) !== 0)
                .map(product => ({
                    id: product.id,
                    type: product.type_id,
                    title: product.title
                }));

            searching.value = false
        })
        .catch(error => {
            eventHub.emit('error', error)
            searching.value = false
        })
}, 1000)

watch(searchQuery, fetchOptions)

</script>
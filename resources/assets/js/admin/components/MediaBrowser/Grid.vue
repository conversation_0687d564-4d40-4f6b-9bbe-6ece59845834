<template>
	<div class="mediaBrowser-gallery-grid">
		<div class="mediaBrowser-gallery-container" v-for="photo in photos.data">
	    	<div class="mediaBrowser-image-container">
	    		<a :href="photo.path" target="_blank">
	    			<img :src="photo.thumbnail_path">
	    		</a>
	    	</div>
	    	<div class="mediaBrowser-image-title">
	    		<h3>{{ photo.title }}</h3>
	    		<button type="button" class="btn btn-sm btn-default btn-block" @click="select(photo)">Select</button>
	    	</div>
	    </div>
	</div>
</template>

<script>
	export default {
		props: ['photos'],

        emits: ['input'],

		methods: {
			select: function(photo) {
				this.$emit('input', photo);
			}
		}
	} 
</script>
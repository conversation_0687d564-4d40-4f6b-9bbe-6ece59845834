<template>
<form action="" class="dropzone" id="mediaUpload" v-show="show">
    <div class="dz-message">
        <strong>Drop files here or click to upload.</strong><br />
        <small>(Max file size: 1MB)</small>
    </div>
    <div class="fallback">
        <input name="file" type="file" multiple />
    </div>
</form>
</template>

<script>
	import Dropzone from 'Dropzone';
	export default {
		props: ['show'],

        emits: ['upload-complete'],

		mounted: function()
		{
			var that = this

			var myDropzone = new Dropzone("#mediaUpload", {
                headers: {'X-CSRF-TOKEN': document.querySelector('#token').getAttribute('value')},
                paramName: "file",
                maxFilesize: 1,
                acceptedFiles: 'image/jpg,image/jpeg,image/png,image/webp,application/pdf',
                url: "/api/photos",
                init: function() {
                    this.on('success', function(e, response) {
                    })

                    this.on('error', function(e, response) {
                        alert(response)
                    })

                    this.on('queuecomplete', function(e, response) {
                        that.$emit('upload-complete')
                        this.removeAllFiles()
                    })
                }
            });
		}
	}
</script>

<style>
	.dropzone {
		margin: 30px;
	}
</style>
<template>
    <div>
        <transition name="fade">
            <div v-show="show" class="mediaBrowser">
                <div class="mediaBrowser-header">
                    <div class="mediaBrowser-heading">
                        <div class="mediaBrowser-title">
                            <h2>Photo Browser</h2>
                        </div>
                        <div class="mediaBrowser-close">
                            <button type="button" @click="toggle">
                                <i class="fa fa-times"></i> Close
                            </button>
                        </div>
                    </div>
                    <div class="mediaBrowser-toolbar">
                        <div class="flex align-items-m">
                            <div class="flex-item mr-sm">
                                <!-- Search -->
                                <div class="input-group">
                                    <input v-model="query"
                                           class="form-control"
                                           placeholder="Search photos..."
                                           type="text"
                                           @keypress.enter.prevent="getFiles"
                                    />
                                    <button class="br--r br-left-0 btn btn-light input-group-append" type="button" @click="getFiles">
                                        <i class="fa fa-search"></i>
                                    </button>
                                </div>
                            </div>
                            <div class="flex-item-fill text-right">
                                <button
                                    :class="'btn btn-light ' + source == 'uploads' ? 'active' : ''"
                                    class="btn btn-light"
                                    type="button"
                                    @click="getFiles(page)"
                                >
                                    My Uploads
                                </button>

                                <button class="btn btn-light" type="button" @click="view = 'List'">
                                    <i class="fa fa-bars"></i>
                                </button>
                                <button class="btn btn-light" type="button" @click="view = 'Grid'">
                                    <i class="fa fa-th"></i>
                                </button>
                                <button class="btn btn-light" type="button" @click="showUploader = !showUploader">
                                    <i class="fa fa-cloud-upload"></i>
                                    <span class="hidden-sm hidden-xs">Upload</span>
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="mediaBrowser-body">
                    <uploader :show="showUploader"
                              :tags="tags"
                              @upload-complete="handleUploadCompleted"
                    ></uploader>

                    <!-- Gallery -->
                    <div class="mediaBrowser-gallery">
                        <div v-show="!photos?.data?.length">
                            <h2>No photos</h2>
                        </div>
                        <component :is="view"
                                   :photos="photos"
                                   @input="select"
                        ></component>

                    </div>
                    <!-- End gallery -->
                    <div class="mediaBrowser-footer">
                        <div class="btn-group">
                            <button :disabled="isFirstPage" class="btn btn-default" type="button" @click="prevPage()">
                                <i class="fa fa-chevron-left"></i>
                            </button>
                            <button class="btn btn-default" disabled type="button">
                                Page: {{ photos.current_page }} of {{ photos.last_page }}
                            </button>
                            <button :disabled="isLastPage" class="btn btn-default" type="button" @click="nextPage()">
                                <i class="fa fa-chevron-right"></i>
                            </button>
                        </div>
                    </div>

                </div>
            </div>
        </transition>
    </div>
</template>

<script>
import Uploader from '../MediaManager/Uploader.vue';
import List from './List.vue';
import Grid from './Grid.vue';

export default {
    props: {
        tags: {
            type: Array,
            required: false,
            default: null
        }
    },

    emits: ['insertPhoto', 'toggle'],

    components: {
        Uploader, List, Grid
    },

    created() {
        this.getFiles(1);

        eventHub.on('mediaBrowser:toggle', this.toggle);
    },

    data: () => ({
        show: false,
        photos: {},
        photo: '',
        query: '',
        showUploader: false,
        view: 'List',
        mode: '',
        source: 'uploads',
        page: 1,
        redactor: ''
    }),

    computed: {
        isFirstPage() {
            return this.photos.current_page === 1;
        },

        isLastPage() {
            return this.photos.current_page === this.photos.last_page
                || !this.photos.data.length;
        }
    },

    events: {
        'mediaBrowser:toggle': function() {
            this.toggle();
        },

        toggle() {
            this.toggle();
        }
    },

    methods: {
        handleUploadCompleted() {
            this.getFiles(1);
            this.showUploader = false;
        },

        getFiles(page) {
            this.source = 'uploads';
            this.page = page;

            eventHub.emit('showProgressBar');

            var data = {
                page: page,
                type: 'image',
                q: this.query
            };

            axios.get('/api/photos', { params: data })
                .then(({ data }) => {
                    this.photos = data;
                    eventHub.emit('hideProgressBar');
                })
                .catch(() => {
                    eventHub.emit('hideProgressBar');
                });
        },

        select(photo) {
            this.$emit('insertPhoto', photo);

            window.dispatchEvent(
                new CustomEvent('media-selected', { detail: { photo } })
            );

            this.toggle();
        },

        editItem(photo) {
            this.photo = photo;
        },

        toggle() {
            this.show = !this.show;

            document.body.style.overflow = this.show ? 'hidden' : 'auto';
        },

        prevPage() {
            if (this.photos.current_page <= 1) return;

            this.getFiles(this.photos.current_page - 1);
        },

        nextPage() {
            if (this.photos.current_page >= this.photos.last_page) return;

            this.getFiles(this.photos.current_page + 1);
        }
    }
};
</script>

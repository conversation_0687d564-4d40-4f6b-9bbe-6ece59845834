<template>
	<div class="mediaBrowser-gallery-list">
	    <table>
	    	<tr v-for="photo in photos.data">
	    		<td>
	        		<a :href="photo.path" target="_blank">
	        			<img :src="photo.thumbnail_path">
	        		</a>
	        	</td>
	        	<td>
	        		<div>{{ photo.title }}</div>
	        		{{ photo.width }} x {{ photo.height }} | {{ photo.file_size_formatted }} | Added {{ photo.created_at_formatted }}
	        	</td>
	        	<td><button type="button" class="btn btn-default" @click="select(photo)">Select</button></td>	
	    	</tr>
	    </table>
	</div> 
</template>

<script>
	export default {
		props: ['photos'],

        emits: ['input'],

		methods: {
			select: function(photo) {
				this.$emit('input', photo);
			}
		}
	} 
</script>
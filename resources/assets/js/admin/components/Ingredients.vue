<template>
    <div id="ingredients">
        <div class="gc-modal gc-modal-mask" id="createIngredientModal" @click="hideModal('createIngredientModal')">
            <div class="gc-modal-wrapper">
                <div class="gc-modal-container" @click.stop>
                    <div class="gc-modal-header">
                        Add Ingredient
                    </div>

                    <div class="gc-modal-body">
                        <div class="radio form-group">
                            <label class="mr-sm">
                                <input type="radio" :value="false" v-model="addFromProduct"> Add Custom
                            </label>
                            <label>
                                <input type="radio" :value="true" v-model="addFromProduct"> Add From Product
                            </label>
                        </div>

                        <div v-if="addFromProduct" class="select">
                            <div class="form-group">
                                <input type="text"
                                       class="form-control"
                                       autocomplete="off"
                                       @focus="showResults = true"
                                       @blur="showResults = false"
                                       @keyup.enter="addIngredient"
                                       tabindex="2"
                                       v-model="q"
                                       placeholder="Search for a product..."
                                />
                                <ul v-show="showResults" class="select-results">
                                    <li v-for="product in products">
                                        <span @mousedown="selectProduct(product)">{{ product.title }}</span>
                                    </li>
                                </ul>
                            </div>
                        </div>

                        <div v-else>
                            <div class="form-group">
                                <label for="title">Title</label>
                                <input type="text" class="form-control" v-model="title" />
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="amount">Amount</label>
                            <input type="text" class="form-control" v-model="amount" placeholder="Example: 1/2 cup"/>
                        </div>
                    </div>

                    <div class="gc-modal-footer">
                        <button type="button" class="btn btn-alt" @click="hideModal('createIngredientModal')">Cancel</button>
                        <button type="button" class="btn btn-action" :disabled="!title.length" @click=" addIngredient">Add Ingredient</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="panel panel-default">
            <div class="panel-heading flex align-items-m">
                <div class="flex-item">Ingredients</div>
                <div class="flex-item push-right">
                    <button class="btn btn-alt btn-sm" type="button" @click="showModal('createIngredientModal')">
                        <i class="fa fa-plus-circle fa-lg"></i> Add
                    </button>
                </div>
            </div>
            <ul class="pa-sm">
                <li v-for="ingredient in ingredients" :key="ingredient.id" class="flex align-items-m pa-sm border border-b border-light" >
                    <div class="flex-item">
                        {{ ingredient.title }}
                        <div class="text-gray-medium fs-sm">{{ ingredient.amount }}</div>
                    </div>
                    <button
                        type="button"
                        class="btn btn-sm btn-alt push-right flex-item"
                        @click="removeIngredient(ingredient)"
                    >
                        Delete
                    </button>
                </li>
            </ul>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        id: { required: true }
    },

    created() {
        this.getIngredients();
    },


    data() {
        return {
            showResults: false,
            totalMatches: 0,
            q: '',
            ingredients: [],
            products: [],
            filteredCount: 0,
            productId: 0,
            title: '',
            amount: '',
            addFromProduct: false
        }
    },



    methods: {
        searchProducts() {
            axios.get('/api/theme/products', {
                params: { products: this.q }
            })
                .then(({ data }) => {
                    this.products = data;
                })
                .catch(() => {});
        },

        selectProduct(product) {
            this.productId = product.id;
            this.title = product.title;
            this.q = product.title;
        },

        getIngredients() {
            axios.get(`/admin/recipes/${this.id}/ingredients`)
                .then(({ data }) => {
                    this.ingredients = data;
                })
                .catch(() => {});
        },

        addIngredient() {
            axios.post(`/admin/recipes/${this.id}/ingredients`, {
                recipe_id: this.id,
                product_id: this.productId,
                title: this.title,
                amount: this.amount
            })
                .then(({ data }) => {
                    this.ingredients.push(data);
                    this.title = '';
                    this.productId = 0;
                    this.amount = '';
                    this.q = '';

                    this.hideModal('createIngredientModal');
                })
                .catch(() => {});
        },

        removeIngredient(ingredient) {
            axios.delete(`/admin/recipes/${this.id}/ingredients/${ingredient.id}`)
                .then(() => {
                    this.ingredients.splice(this.ingredients.indexOf(ingredient), 1);
                })
                .catch(() => {});
        }
    },

    watch: {
        q() {
            this.searchProducts();
        }
    },
}
</script>
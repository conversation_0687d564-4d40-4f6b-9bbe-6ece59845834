<template>
  <div>
    <create-price :productId="productId" @input="storePrice"></create-price>
    <div
      class="gc-modal gc-modal-mask"
      id="deletePriceModal"
      @click="hideModal('deletePriceModal')"
    >
      <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
          <div class="gc-modal-header">Delete Price</div>

          <div class="gc-modal-body">Would you like to delete this price?</div>

          <div class="gc-modal-footer">
            <button type="button" class="btn btn-alt" @click="hideModal('deletePriceModal')">Cancel</button>
            <button type="button" class="btn btn-danger" @click="destroyPrice(priceToDelete)">Delete</button>
          </div>
        </div>
      </div>
    </div>
    <div class="panel">
      <div class="panel-heading flex align-items-m">
        <div class="flex-item">Other Prices</div>
        <button
          type="button"
          class="btn btn-sm btn-alt push-right flex-item pa-0"
          @click="showModal('createPriceModal')"
        >
          <i class="fa fa-plus-circle fa-lg"></i> Add Price
        </button>
      </div>
      <div class="panel-body pa-0">
        <table class="table table-bordered table-striped table-settings">
          <tbody>
            <tr v-for="price in prices" :key="price.id">
              <td>
                <h2>{{ price.group.title }}</h2>
              </td>
              <td v-if="price.group.type === 1 || price.group.type === 2">
                <div class="flex align-items-m">
                  <div class="flex-item-fill mr-md">
                    <span v-if="price.group.type === 1">
                      Percentage Increase of Retail Price:
                      <strong>{{ price.group.amount }}%</strong>
                    </span>
                    <span v-if="price.group.type === 2">
                      Percentage Decrease of Retail Price:
                      <strong>-{{ price.group.amount }}%</strong>
                    </span>
                  </div>
                  <div class="flex-item">
                    <label>&nbsp;</label>
                    <button
                      type="button"
                      class="btn btn-alt btn-sm"
                      @click="deletePrice(price)"
                    >Delete</button>
                  </div>
                </div>
              </td>
              <td v-if="price.group.type === 0">
                <div class="flex align-items-m mb-sm">
                  <div class="flex-item-fill mr-md">
                    <label for="unitPriceInput">Regular Price</label>
                    <div class="input-group-append">
                      <span class="input-group-addon">$</span>
                      <input
                        type="text"
                        v-model.number="price.unit_price_formatted"
                        class="form-control"
                        @change="update(price)"
                      />
                    </div>
                  </div>

                  <div class="flex-item-fill mr-md">
                    <label for="saleUnitPriceInput">Sale Price</label>
                    <div class="input-group-append">
                      <span class="input-group-addon">$</span>
                      <input
                        type="text"
                        v-model.number="price.sale_unit_price_formatted"
                        class="form-control"
                        @change="update(price)"
                      />
                    </div>
                  </div>

                  <div class="flex-item">
                    <label>&nbsp;</label>
                    <button
                      type="button"
                      class="btn btn-alt btn-sm"
                      @click="deletePrice(price)"
                    >Delete</button>
                  </div>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        <div v-show="!prices.length" class="pa-md">
          Offer alternative prices that can be applied to select locations or customers.
          <a
            href="https://help.grazecart.com/docs/topic/products/article/pricing-groups"
            target="_blank"
          >Learn more.</a>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import CreatePrice from "./CreatePrice.vue";
export default {
  props: ["productId"],

  components: { CreatePrice },

  created: function() {
    this.fetchPrices();
    eventHub.on("ProductPrice:create", this.create);
  },

  data: function() {
    return {
      prices: [],
      priceToDelete: null,
      message: ""
    };
  },

  methods: {
    fetchPrices: function() {
      axios
        .get("/api/products/" + this.productId + "/prices")
        .then(
          function(response) {
            this.prices = response.data;
          }.bind(this)
        )
        .catch(function(error) {});
    },

    storePrice: function(price) {
      this.fetchPrices();
    },

    deletePrice: function(price) {
      this.priceToDelete = price;
      this.showModal("deletePriceModal");
    },

    destroyPrice: function(price) {
      axios
        .delete("/api/products/" + this.productId + "/prices/" + price.id)
        .then(
          function(response) {
            this.hideModal("deletePriceModal");
            this.priceToDelete = null;
            this.fetchPrices();
          }.bind(this)
        )
        .catch(function(error) {});
    },

    update: function(price) {
      eventHub.emit("showProgressBar");
      axios
        .put("/api/products/" + this.productId + "/prices/" + price.id, {
          unit_price: price.unit_price_formatted,
          sale_unit_price: price.sale_unit_price_formatted,
          unit_of_issue: price.unit_of_issue,
          weight: price.weight
        })
        .then(
          function(response) {
            this.message = "Changes saved";
            eventHub.emit("hideProgressBar");
          }.bind(this)
        )
        .catch(function(error) {
          eventHub.emit("hideProgressBar");
          eventHub.emit("error", error);
        });
    }
  }
};
</script>
<template>
  <div>
    <table class="table table-bordered table-striped table-settings" v-if="priceGroups.length">
      <tbody>
        <tr v-for="group in priceGroups" :key="group.id">
          <td>
            <a href="#" @click.prevent="edit(group)">{{ group.title }}</a>
          </td>
          <td>
            <button class="btn btn-alt btn-sm btn-link pa-0 mr-md" @click.prevent="edit(group)">Edit</button>
            <button
              class="btn btn-alt btn-sm btn-link pa-0"
              @click.prevent="deletePriceGroup(group)"
            >Delete</button>
          </td>
        </tr>
      </tbody>
    </table>
    <div class="alert alert-default" v-else>
      You haven't created any price groups yet.
      <a href="#" @click="create()">Add one now</a>.
    </div>

    <div
      class="gc-modal gc-modal-mask"
      id="createPriceGroupModal"
      @click="hideModal('createPriceGroupModal')"
    >
      <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
          <div class="gc-modal-header">Create Price Group</div>

          <div class="gc-modal-body">
            <div class="form-group">
              <label>Title</label>
              <input type="text" v-model="title" class="form-control" />
            </div>

            <div class="form-group">
              <label>Type</label>
              <select v-model="type" class="form-control">
                <option :value="0">Default</option>
                <option :value="1">Percentage Increase</option>
                <option :value="2">Percentage Decrease</option>
              </select>
            </div>

            <div class="form-group" v-if="type > 0">
              <label>Amount</label>
              <input type="number" v-model="amount" class="form-control" />
            </div>

            <div class="form-group">
              <div class="checkbox">
                <label>
                  <input type="checkbox" v-model="auto_assign" />
                  Apply to all products
                </label>
              </div>
            </div>
          </div>

          <div class="gc-modal-footer">
            <button
              type="button"
              class="btn btn-alt"
              @click="hideModal('createPriceGroupModal')"
            >Cancel</button>
            <button
              type="button"
              class="btn btn-action"
              @click="store(title, type, amount, auto_assign)"
              :disabled="!title.length"
            >Save</button>
          </div>
        </div>
      </div>
    </div>

    <div
      class="gc-modal gc-modal-mask"
      id="editPriceGroupModal"
      @click="hideModal('editPriceGroupModal')"
    >
      <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
          <div class="gc-modal-header">Edit Price Group</div>

          <div class="gc-modal-body">
            <div class="form-group">
              <label>Title</label>
              <input type="text" v-model="group.title" class="form-control" />
            </div>

            <div class="form-group">
              <label>Type</label>
              <select v-model="group.type" class="form-control">
                <option :value="0">Default</option>
                <option :value="1">Percentage Increase</option>
                <option :value="2">Percentage Decrease</option>
              </select>
            </div>

            <div class="form-group" v-if="group.type > 0">
              <label>Amount</label>
              <input type="number" v-model="group.amount" class="form-control" />
            </div>
          </div>

          <div class="gc-modal-footer">
            <button
              type="button"
              class="btn btn-alt"
              @click="hideModal('editPriceGroupModal')"
            >Cancel</button>
            <button type="button" class="btn btn-action" @click="update(group)">Save</button>
          </div>
        </div>
      </div>
    </div>

    <div
      class="gc-modal gc-modal-mask"
      id="deletePriceGroupModal"
      @click="hideModal('deletePriceGroupModal')"
    >
      <div class="gc-modal-wrapper">
        <div class="gc-modal-container" @click.stop>
          <div class="gc-modal-header">Delete Price Group</div>

          <div class="gc-modal-body">Are you sure you want to delete this price group?</div>

          <div class="gc-modal-footer">
            <button
              type="button"
              class="btn btn-alt"
              @click="hideModal('deletePriceGroupModal')"
            >Cancel</button>
            <button
              type="button"
              class="btn btn-danger"
              @click="destroy(groupToDelete)"
            >Delete Price Group</button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  created: function() {
    this.fetchPriceGroups();
    eventHub.on("ProductPrice:create", this.create);
  },

  data: function() {
    return {
      priceGroups: [],
      title: "",
      type: 0,
      amount: 0,
      group: {},
      groupToDelete: null,
      auto_assign: false
    };
  },

  methods: {
    fetchPriceGroups: function() {
      axios
        .get("/api/price-groups")
        .then(
          function(response) {
            this.priceGroups = response.data;
          }.bind(this)
        )
        .catch(function(error) {});
    },

    create: function() {
      this.showModal("createPriceGroupModal");
    },

    store: function(title, type, amount, auto_assign) {
      axios
        .post("/api/price-groups", {
          title: title,
          type: type,
          amount: amount,
          auto_assign: auto_assign
        })
        .then(
          function(response) {
            this.hideModal("createPriceGroupModal");
            this.title = "";
            this.type = 0;
            this.amount = 0;
            this.fetchPriceGroups();
          }.bind(this)
        )
        .catch(function(error) {
          eventHub.emit("error", error);
        });
    },

    update: function(group) {
      axios
        .put("/api/price-groups/" + group.id, {
          title: group.title,
          type: group.type,
          amount: group.amount
        })
        .then(
          function(response) {
            this.hideModal("editPriceGroupModal");
            this.fetchPriceGroups();
          }.bind(this)
        )
        .catch(function(error) {
          eventHub.emit("error", error);
        });
    },

    deletePriceGroup: function(group) {
      this.groupToDelete = group;
      this.showModal("deletePriceGroupModal");
    },

    destroy: function(group) {
      axios
        .delete("/api/price-groups/" + group.id)
        .then(
          function(response) {
            this.hideModal("deletePriceGroupModal");
            this.fetchPriceGroups();
          }.bind(this)
        )
        .catch(function(error) {
          eventHub.emit("error", error);
        });
    },

    edit: function(group) {
      this.group = group;
      this.showModal("editPriceGroupModal");
    }
  }
};
</script>
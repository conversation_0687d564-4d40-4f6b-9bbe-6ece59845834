<template>
  <div class="gc-modal gc-modal-mask" id="createPriceModal" @click="hideModal('createPriceModal')">
    <div class="gc-modal-wrapper">
      <div class="gc-modal-container" @click.stop>
        <div class="gc-modal-header">Add Price</div>

        <div class="gc-modal-body">
          <div class="form-group">
            <label>Price Group</label>
            <select v-model="newPrice.group" class="form-control">
              <option :value="null" disabled>Select a price group</option>
              <option v-for="group in priceGroups" :value="group" :key="group.id">{{ group.title }}</option>
            </select>
          </div>

          <div class="flex" v-if="newPrice.group && newPrice.group.type === 0">
            <div class="flex-item-fill mr-sm">
              <label for="unitPriceInput">Regular Price</label>
              <div class="input-group">
                <span class="input-group-addon">$</span>
                <input
                  type="text"
                  v-model.number="newPrice.unit_price"
                  class="form-control"
                  id="unitPriceInput"
                  placeholder="0.00"
                  ref="UnitPriceInput"
                />
              </div>
            </div>

            <div class="flex-item-fill">
              <label for="saleUnitPriceInput">Sale Price</label>
              <div class="input-group">
                <span class="input-group-addon">$</span>
                <input
                  type="text"
                  v-model.number="newPrice.sale_unit_price"
                  class="form-control"
                  id="saleUnitPriceInput"
                  placeholder="0.00"
                />
              </div>
            </div>
          </div>

          <div v-if="newPrice.group && newPrice.group.type === 1">
            <p>Percentage Increase of Retail Price: {{ newPrice.group.amount }}%</p>
          </div>
        </div>

        <div class="gc-modal-footer">
          <button type="button" class="btn btn-alt" @click="hideModal('createPriceModal')">Cancel</button>
          <button type="button" class="btn btn-action" @click="store(newPrice)">Add Price</button>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  props: ["productId"],

    emits: ['input'],


    created: function() {
    this.fetchPriceGroups();
  },

  data: function() {
    return {
      priceGroups: [],
      newPrice: {
        group: null,
        unit_price: "",
        sale_unit_price: "",
        unit_of_issue: "weight",
        weight: "1.5"
      }
    };
  },

  watch: {
    "newPrice.group_id": function(value) {
      if (value) {
        this.$refs.UnitPriceInput.focus();
      }
    }
  },

  methods: {
    fetchPriceGroups: function() {
      axios
        .get("/api/price-groups")
        .then(
          function(response) {
            this.priceGroups = response.data;
          }.bind(this)
        )
        .catch(function(error) {});
    },

    store: function(price) {
      eventHub.emit("showProgressBar");
      axios
        .post("/api/products/" + this.productId + "/prices", {
          group_id: price.group.id,
          unit_price: price.unit_price || 0,
          sale_unit_price: price.sale_unit_price || 0,
          unit_of_issue: "weight",
          weight: "1.5"
        })
        .then(
          function(response) {
            eventHub.emit("hideProgressBar");
            this.$emit("input", price);
            this.hideModal("createPriceModal");
          }.bind(this)
        )
        .catch(function(error) {
          eventHub.emit("hideProgressBar");
          eventHub.emit("error", error);
        });
    }
  }
};
</script>
<template>
    <div class="coupon">
        <div class="panel panel-default">
            <div class="panel-body pa-0">
                <table class="table table-striped table-full table-list">
                    <thead>
                    <tr>
                        <th>Code</th>
                        <th>Description</th>
                        <th>Uses</th>
                        <th>Expires</th>
                    </tr>
                    </thead>
                    <tbody>
                    <tr v-for="coupon in coupons">
                        <td data-label="Code">
                            <a href="#" @click.prevent="edit(coupon)">{{ coupon.code }}</a>
                        </td>
                        <td data-label="Description">
                            {{ coupon.description }}
                        </td>
                        <td data-label="Uses">
                            <span v-if="coupon.limit_usage">
                                {{ coupon.total_uses }}/{{ coupon.max_uses }}
                            </span>
                            <span v-else>
                                {{ coupon.total_uses }}
                            </span>
                        </td>
                        <td data-label="Expires">{{ coupon.expires_at_formatted }}</td>
                    </tr>
                    </tbody>
                </table>
            </div>
        </div>
        <create-coupon :coupon="coupon" :description="description" @store="store"></create-coupon>
        <edit-coupon :coupon="coupon" :description="description" @update="update"></edit-coupon>
    </div>
</template>

<script>
import CreateCoupon from './CreateCoupon.vue';
import EditCoupon from './EditCoupon.vue';

export default {
    components: {
        CreateCoupon,
        EditCoupon
    },

    created() {
        eventHub.on('Coupon:create', this.create);
        this.getAll();
    },

    computed: {
        description() {
            if (this.coupon.discount_type == 'fixed') {
                return '$' + this.coupon.discount_amount_formatted + ' off';
            }

            if (this.coupon.discount_type == 'percentage') {
                return this.coupon.discount_percentage + '% off';
            }

            if (this.coupon.discount_type == 'delivery') {
                return 'Free delivery';
            }
        }
    },

    data: () => ({
        coupons: [],
        coupon: {
            code: '',
            discount_type: 'fixed',
            application_type: 'order',
            discount_amount_formatted: 0,
            discount_percentage: 25,
            min_order: false,
            min_order_amount: 0,
            limit_usage: false,
            max_uses: 1,
            auto_apply: false,
            once_per_customer: false,
            expires: false,
            expires_at: '',
            settings: {}
        }
    }),

    methods: {
        getAll() {
            axios.get('/api/coupons', this.coupon)
                .then(({ data }) => {
                    this.coupons = data;
                })
                .catch(error => {
                });
        },

        create() {
            this.coupon = {
                code: '',
                discount_type: 'fixed',
                application_type: 'order',
                discount_amount_formatted: 10,
                min_order: false,
                min_order_amount: 0,
                limit_usage: false,
                max_uses: 1,
                once_per_customer: false,
                expires: false,
                expires_at: '',
                settings: {}
            };

            this.showModal('createCouponModal');
        },

        edit(coupon) {
            this.coupon = coupon;
            this.showModal('editCouponModal');
        },

        store(coupon) {
            axios.post('/api/coupons', coupon)
                .then(({ data }) => {
                    eventHub.emit('hideProgressBar');
                    this.hideModal('createCouponModal');
                    this.resetCoupon();
                    this.coupons.push(data);
                })
                .catch(error => {
                    eventHub.emit('error', error);
                });
        },

        update(coupon) {
            axios.put('/api/coupons/' + coupon.id, coupon)
                .then(() => {
                    this.hideModal('editCouponModal');
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('notify', { level: 'info', message: 'Coupon Updated' });
                })
                .catch(error => {
                    eventHub.emit('error', error);
                });
        },

        resetCoupon() {
            this.coupon = {
                code: '',
                discount_type: 'fixed',
                application_type: 'order',
                discount_amount: 0,
                min_order: false,
                min_order_amount: 0,
                limit_usage: false,
                max_uses: 1,
                once_per_customer: false,
                expires: false,
                expires_at: '',
                settings: {}
            };
        }
    }
};
</script>

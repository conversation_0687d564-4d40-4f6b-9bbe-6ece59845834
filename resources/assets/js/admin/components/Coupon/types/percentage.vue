<template>
	<div class="form-group">
		<label>Discount Amount (Percent off)</label>
		<div class="input-group">
		  <input type="number" min="1" max="100" step="1" class="form-control" placeholder="50" v-model="coupon.discount_percentage">
		  <span class="input-group-addon">%</span>
		</div>
        <small id="percentageHelp" class="form-text text-muted">Discount amount can only be between 1 - 100</small>
	</div>
</template>

<script>
	export default {
		props: ['coupon']
	}
</script>
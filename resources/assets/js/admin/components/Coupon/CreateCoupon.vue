<template>
    <div id="createCouponModal" class="gc-modal gc-modal-mask" @click="hideModal('createCouponModal')">
        <div class="gc-modal-wrapper">
            <div class="gc-modal-container" @click.stop>
                <div class="gc-modal-header">
                    Create a coupon
                </div>
                <div class="gc-modal-body">
                    <div class="form-group">
                        <label for="code">Code</label>
                        <small class="pull-right"><a href="#" @click="generateCode(coupon)">Generate code</a></small>
                        <input id="code" v-model="coupon.code" class="form-control" placeholder="e.g. SAVEBIG" type="text">
                    </div>

                    <div class="form-group">
                        <label for="type_id">Discount Type</label>
                        <select v-model="coupon.discount_type" class="form-control">
                            <option value="percentage">Percentage discount</option>
                            <option value="fixed">Fixed amount</option>
                            <option value="delivery">Free delivery</option>
                        </select>
                    </div>
                    <hr>
                    <component :is="coupon.discount_type" :coupon="coupon"></component>

                    <div class="form-group">
                        <div class="checkbox">
                            <label>
                                <input v-model="coupon.min_order" type="checkbox">
                                Requires a minimum order amount
                            </label>
                        </div>
                        <div v-show="coupon.min_order" class="mt-sm ml-md">
                            <label>Minimum order amount</label>
                            <div class="input-group">
                                <span class="input-group-addon">&#36;</span>
                                <input v-model="coupon.min_order_amount" class="form-control" type="number">
                            </div>
                        </div>
                    </div>
                    <hr>
                    <div class="form-group">
                        <label>Usage Limits</label>
                        <div class="checkbox">
                            <label>
                                <input v-model="coupon.limit_usage" type="checkbox">
                                Limit how many times this coupon can be used
                            </label>
                        </div>
                        <div v-show="coupon.limit_usage" class="mt-sm mb-sm ml-md">
                            <input v-model="coupon.max_uses" class="form-control" type="number">
                        </div>
                        <div class="checkbox">
                            <label>
                                <input v-model="coupon.once_per_customer" type="checkbox">
                                Limit to one use per customer
                            </label>
                        </div>
                    </div>
                    <hr>
                    <div class="form-group">
                        <label>Expiration</label>
                        <div class="checkbox">
                            <label>
                                <input v-model="coupon.expires" type="checkbox">
                                This coupon expires
                            </label>
                        </div>
                        <div v-show="coupon.expires">
                            <input ref="expires_at" v-model="coupon.expires_at" class="form-control mt-sm" placeholder="Enter expiration date." type="text">
                        </div>
                    </div>
                    <hr>
                    <div class="form-group">
                        <label for="code">Description</label>
                        <input v-model="coupon.description" :placeholder="description" class="form-control" type="text">
                    </div>
                </div>
                <div class="gc-modal-footer">
                    <button class="btn btn-alt" type="button" @click="hideModal('createCouponModal')">Close</button>
                    <button class="btn btn-action" type="button" @click="store(coupon)">Create Coupon</button>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import fixed from './types/fixed.vue';
import percentage from './types/percentage.vue';
import delivery from './types/delivery.vue';
import Pikaday from 'pikaday';
import moment from 'moment';

export default {
    components: {
        fixed,
        percentage,
        delivery
    },

    props: ['coupon', 'description'],

    emits: ['store'],

    mounted() {
        const component = this;

        new Pikaday({
            field: this.$refs.expires_at,
            minDate: new Date(),
            onSelect(date) {
                component.coupon.expires_at = moment(date).format('YYYY-MM-DD');
            }
        });
    },

    methods: {
        store(coupon) {
            this.$emit('store', coupon);
        },

        generateCode(coupon) {
            coupon.code = Math.random().toString(36).substr(2, 12).toUpperCase();
        }
    }
};
</script>

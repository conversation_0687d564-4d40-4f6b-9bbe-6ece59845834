<template>
  <div>
    <div v-for="plan in plans" :key="plan.plan_id" class="form-group">
      <label
        class="btn btn-default btn-lg btn-block flex align-items-m justify-between"
        :for="plan.plan_id"
      >
        <div class="flex-item">
          {{ plan.name }} - ${{ plan.monthly }}/mo
          <small v-if="annualPlanType">(Paid annually)</small>
        </div>
        <i
          v-if="currentPlanId == plan.plan_id"
          class="fas fa-check text-primary"
        ></i>
        <input
          v-else
          type="radio"
          name="plan"
          @change="updateSelectedPlan(plan)"
          :checked="selectedPlan.plan_id == plan.plan_id"
          :id="plan.plan_id"
          :value="plan.plan_id"
          tabindex="-1"
          class="push-right flex-item"
        />
      </label>
    </div>
    <div v-if="showDueToday" class="fs-2 bold text-right">
      Due Today: ${{ selectedPlan.annually_formatted }}
    </div>
  </div>
</template>
<script>
export default {
  /**
   * Loop through the plans and display each with a radio button, unless
   * it's the Client's currenlty chosen plan, in which case we replace
   * the radio button with a checkmark.
   * This component is only meant to handle either annual plans or monthly plans,
   * not both in the same list, since the first use case involves the monthly
   * plans being separated from the annual plans by a tabs.
   *
   * Vue keeps in its data the plan that is selected, which controls the "Due Today" that
   * is displayed and the radio button that is checked.
   *
   * In the input, we use @change and :checked instead of just v-model
   * so that the form can receive just the plan_id (from :value),
   * while Vue deals with the full plan object so that it can update
   * the selectedPlan with the whole object instead of just the plan_id
   * (the whole plan object in selectedPlan is necessary because
   * we grab the "annually_formatted" property out of it
   * for the "Due Today" feature).
   */
  name: "SubscriptionChoices",
  props: {
    /**
     * Map of plans received from AccountPlanController.php
     * (could be monthly or annual plans)
     * Ex.:
     * {
     *   "GC-STARTER-YEARLY": {
     *     "billing_cycle": "annual",
     *     "visible": true,
     *     "name": "Starter",
     *      "plan_id": "GC-STARTER-YEARLY",
     *      "monthly": 59,
     *      "deprecated": false,
     *      "features": {
     *        ...
     *      },
     *      "annually": 708,
     *      "monthly_formatted": "59.00",
     *      "annually_formatted": "708.00"
     *   },
     *   "GC-GROWTH-YEARLY": {
     *     ...
     *   },
     *     "GC-SCALE-YEARLY": {
     *       ...
     *     }
     *   }
     */
    plans: {
      type: Object,
      required: false,
      default: () => {}
    },
    /**
     * The ID of the plan to which the Client is currently
     * subscribed and that he/she is paying for
     * Ex.: GC-STARTER-YEARLY
     */
    currentPlanId: {
      type: String,
      required: false,
      default: ""
    },
    /**
     * If true, the plans given in the plans prop are annual plans
     * If false, the plans given in the plans prop are monthly plans
     */
    annualPlanType: {
      type: Boolean,
      required: false,
      default: false
    }
  },
  data: function() {
    return {
      /**
       * The plan that is "checked" in Vue's data
       * (the one that the user has most recently clicked on),
       * or, if the user has not yet clicked on a plan, this is
       * the plan to which the Client is currently subscribed and
       * that he/she is paying for.
       */
      selectedPlan: {
        plan_id: null
      }
    };
  },
  mounted: function() {
    this.setSelectedPlanToCurrentPlan();
  },
  methods: {
    /**
     * Set the plan that is "checked" in Vue's data to the plan to which
     * the Client is currently subscribed and that he/she is paying for.
     */
    setSelectedPlanToCurrentPlan: function() {
      if (this.currentPlanId in this.plans) {
        this.selectedPlan = this.plans[this.currentPlanId];
      }
      // Otherwise leave it as it's defined in data so that plan_id can be "accessed"
      // and it won't throw an error (this would happen when the Client's current plan
      // isn't in the map of plans passed in, which usually occurs when the Client is subscribed
      // to a monthly plan, but is viewing the annual plans, or vice-a-versa.
    },
    /**
     * Set the selected plan to the plan that is passed in.
     * This is used to set the plan in Vue's data when a radio button is selected.
     * @param plan the plan to which the selected plan should be set
     */
    updateSelectedPlan: function(plan) {
      this.selectedPlan = plan;
    }
  },
  computed: {
    /**
     * Whether or not a plan is selected that is different from
     * the Client's current plan (the value of this property could be emitted in the future
     * to another component that would handle making the "Update" button gray and unclickable
     * so that users don't have to click it before realizing that they'll just get an
     * error if they try to "update" to the same plan.
     * @return Boolean
     */
    newPlanSelected: function() {
      return !(this.currentPlanId == this.selectedPlan.plan_id);
    },
    /**
     * Whether or not the "Due Today" area should be shown.
     * @return Boolean
     */
    showDueToday: function() {
      return this.annualPlanType && this.newPlanSelected;
    }
  }
};
</script>

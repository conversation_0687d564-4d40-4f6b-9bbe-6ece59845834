<template>
<div id="collection">
	<div class="panel">
	    <div class="panel-heading">Collections</div>
	    <div class="panel-body collectionBody">
	        <div class="select">
	            <input 
	            	type="text" 
	            	class="form-control" 
	            	autocomplete="off" 
	            	v-model="q" 
	            	@focus="showResults = true" 
	            	@blur="showResults = false" 
	            	placeholder="add to collection" 
	            	tabindex="1"
	            >

	            <ul class="select-results" v-if="showResults">
	                <li v-show="!filteredResults.length">
	                    <a href="#"><b>No results found</b></a>
	                </li>
	                <li v-for="result in filteredResults">
	                    <span 
	                    	@mousedown="toggleCollection(result, $event)"
	                    >
	                    	<i class="fa fa-lg" :class="result.active ? 'fa-check-circle-o' : 'fa-circle-o'"></i>
	                    	 {{ result.title }}
	                    </span>
	                </li>
	            </ul>
	        </div>
	        <div>
	            <span v-if="!collectionsProductBelongsTo.length">This product does not belong to any collections.</span>
	            <ul class="list-group" v-else>
	                <li class="list-group-item" v-for="collection in collectionsProductBelongsTo" :key="collection.id">
	                    <a :href="'/admin/collections/' + collection.id + '/edit'">
	                    	{{ collection.title }}</a>
	                    <button type="button" class="pull-right btn btn-alt btn-sm pa-0" @click="toggleCollection(collection, $event)"><i class="fa fa-times"></i></button>
	                </li>
	            </ul>
	        </div>
	    </div>
	</div>
</div>
</template>

<script>
	export default {
		created: function() {
        	this.getCollections();
    	},

    	props: ['id', 'model'],

	    data: function() {
	        return {
	            filteredCount: 0,
	            showResults: false,
	            q: '',
	            results: []
	        }
	    },

	    computed: {
	        collectionsProductBelongsTo: function() {
	            return this.results.filter(function(result) {
	                return result.active;
	            });
	        },

	        filteredResults: function() {
	        	var self = this
            	return self.results.filter(function (result) {
              		return result.title.toLowerCase().indexOf(self.q.toLowerCase()) !== -1
            	})
	        }
	    },

	    methods: {
	        getCollections: function() {
	            var url = '/admin/products/'+this.id+'/collections'
	            axios.get(url)
	                .then(function(response) {
	                    this.results = response.data;
	                }.bind(this))
	                .catch(function(error) {

	                });
	        },

	        toggleCollection: function(collection, e) {
	            e.stopPropagation();
	            e.preventDefault();

	            if(!collection.id) return;

	            if(!collection.active)
	            {
	                this.addToCollection(collection);
	            }
	            else
	            {
	                this.removeFromCollection(collection);
	            }
	        },

	        addToCollection: function(collection) {
	            collection.active = true;
	            var url = '/admin/products/'+this.id+'/collections'

	            axios.post(url, {'id': collection.id})
	                .then(function (response) {

	                })
	                .catch(function (error) {

	                });
	        },

	        removeFromCollection: function(collection, e) {
	            collection.active = false;
	            var url = '/admin/products/'+this.id+'/collections/'+collection.id;
	            axios.delete(url)
	                .then(function (response) {
	                })
	                .catch(function (error) {
	                });
	        },

	        sortCollectionItems: function()
	        {
	            var payload = {};
	            $('#productsInCollectionList li').each( function() {
	                var itemId = $(this).attr('data-id');
	                var sort = ( $(this).index() + 1 );
	                payload[itemId] = sort;
	            });
	        }
	    }
	}
</script>

<style>
	.collectionBody {
    overflow: visible !important;
}
</style>
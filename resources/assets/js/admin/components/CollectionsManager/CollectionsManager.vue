<template>
    <div>
        <div class="form-group">
            <label>Sort Order</label>
            <select class="form-control" @change="update" v-model="collection.order">
                <option
                    v-for="option in sortOptions"
                    :value="option.value"
                    :key="option.value"
                >{{ option.text }}
                </option>
            </select>
        </div>

        <div class="collectionManager">
            <label>Products in Collection ({{ collection.products.length }})</label>
            <div class="form-group">
                <v-select
                    :options="products"
                    v-model="selectedProduct"
                    @search="getProducts"
                    ref="addProductSelect"
                    label="title"
                    placeholder="Search for products to add..."
                >
                    <template v-slot:no-options></template>
                </v-select>
            </div>


            <transition-group
                tag="ul"
                class="draggable-list"
                name="fade"
                id="collectionManagerList"
                v-sortable="options"
                v-if="collection.products.length"
            >
                <li v-for="product in collection.products"
                    :data-id="product.id"
                    :key="product.id"
                    class="flex align-items-m"
                >
                    <span
                        class="fa draghandle"
                        title="Drag to reorder"
                        v-show="collection.order == 'custom_sort-asc'"
                    ></span>&nbsp;
                    <a :href="'/admin/products/'+product.id+'/edit'" title="Edit product">{{ product.title }}</a>
                    <span v-show="!product.visible" class="label label-light ml-sm">Hidden</span>
                    <a
                        href="#"
                        class="push-right text-gray-7 pr-sm"
                        title="Remove from collection"
                        @click.prevent="remove(product)"
                    ><i class="fa fa-times"></i></a>
                </li>
            </transition-group>

            <div v-else>There are no products in this collection yet.
            </div>
        </div>
    </div>
</template>

<script>
import vSelect from 'vue-select';
import 'vue-select/dist/vue-select.css';

export default {
    components: {
        vSelect
    },

    props: ['collectionId'],

    created() {
        eventHub.on('collectionManager:save', this.saveSortOrder);
        window.addEventListener("beforeunload", (event) => {
            if (!this.saved) {
                event.returnValue = "Changes have to been published yet.";
            }
        });

        this.get();
    },

    data() {
        return {
            collection: {products: {}},
            sortOptions: [
                {text: 'Title: A-Z', value: 'title-asc'},
                {text: 'Title: Z-A', value: 'title-desc'},
                {text: 'Price: Lowest to highest', value: 'unit_price-asc'},
                {text: 'Price: Highest to lowest', value: 'unit_price-desc'},
                {text: 'Date: Newest to oldest', value: 'created_at-desc'},
                {text: 'Date: Oldest to newest', value: 'created_at-asc'},
                {text: 'SKU: A-Z', value: 'sku-asc'},
                {text: 'SKU: Z-A', value: 'sku-desc'},
                {text: 'Custom', value: 'custom_sort-asc'},
            ],
            products: [],
            selectedProduct: null,
            sortValues: [],
            drake: null,
            saved: true,
            options: {
                onUpdate: this.updateSort, //ToDo: Do we even sort here?
                handle: '.draghandle',
                animation: 150,
                group: {
                    name: 'segments',
                    pull: false,
                    put: true
                }
            }
        }
    },

    watch: {
        selectedProduct(product) {
            if (product) {
                this.selectProduct(product)
            }
        }
    },

    methods: {
        get() {
            axios.get(`/admin/collections/${this.collectionId}/products`)
                .then(({ data }) => {
                    this.collection = data;
                })
                .catch( error => {})
        },

        selectProduct(product) {
            axios.post(`/admin/collections/${this.collectionId}/products`, {
                id: product.id,
                sort: this.products.length
            })
                .then(() => {
                    this.collection.products.push(product);
                })
                .catch(error => {})
        },


        update() {
            axios.put(`/admin/collections/${this.collectionId}`, {
                order: this.collection.order,
                slug: this.collection.slug
            })
                .then(() => {
                    this.get();
                })
                .catch(error => {
                    eventHub.emit('error', error);
                });
        },

        updateSort(event) {
            this.saved = false;

            eventHub.emit('showProgressBar');

            if (event != undefined) {
                this.collection.products.splice(event.newIndex, 0, this.collection.products.splice(event.oldIndex, 1)[0]);
            }

            for (var i = 0; i < this.collection.products.length; i++) {
                this.collection.products[i].sort = i;
                this.sortValues[this.collection.products[i].id] = i;
            }

            eventHub.emit('hideProgressBar');
        },

        saveSortOrder() {
            eventHub.emit('showProgressBar');
            this.saved = true
            axios.put(`/admin/collections/${this.collectionId}/products`, {
                'sort': this.sortValues
            })
                .then(() => {
                    eventHub.emit('hideProgressBar');
                    this.submitForm('updateCollectionForm');
                })
                .catch(error => {
                    eventHub.emit('hideProgressBar');
                })
        },

        getProducts(q) {
            if (q === '') return;

            axios.get('/api/products', {
                params: {q: q}
            })
                .then(({data}) => {
                    this.products = data;
                })
                .catch(error => {})
        },

        remove(product) {
            this.collection.products.filter(item => {
                if (item.id === product.id) {
                    var index = this.collection.products.indexOf(item);
                    this.collection.products.splice(index, 1);
                }
            });

            axios.delete(`/admin/collections/${this.collectionId}/products/${product.id}`)
                .then(() => {})
                .catch(() => {})
        },
    }
}
</script>
<template>
    <div>
        <div id="proposalsMap" class="relative" style="width:100%; height:600px;">
            <div class="btn-toggle-leads">
                <button
                    class="btn btn-light shadow"
                    type="button"
                    @click="toggleProposals()"
                ><i class="fa fa-map-marker"></i> {{ showProposals ? 'Hide' : 'Show' }} Proposals
                </button>
                <button
                    class="btn btn-light shadow"
                    type="button"
                    @click="toggleLeads()"
                ><i class="fa fa-users"></i> {{ showLeads ? 'Hide' : 'Show' }} Leads
                </button>
            </div>
        </div>
    </div>
</template>

<script>
import Leaflet from 'leaflet';

export default {
    mounted() {
        this.fetchProposals();
    },

    data: () => ({
        proposals: [],
        leads: [],
        map: null,
        proposalsMarkers: null,
        leadMarkers: null,
        showProposals: true,
        showLeads: false
    }),

    methods: {
        fetchProposals() {
            axios.get('/api/proposals')
                .then(({ data }) => {
                    this.proposals = data;
                    this.initMap();
                })
                .catch(error => {
                });
        },

        fetchLeads() {
            axios.get('/api/leads')
                .then(({ data }) => {
                    this.leads = data;
                    this.addLeadsToMap();
                })
                .catch(error => {
                });
        },

        initMap() {
            this.map = Leaflet.map('proposalsMap', {
                scrollWheelZoom: false
            }).setView([41.94, -85.3], 7);

            Leaflet.tileLayer('https://a.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                maxZoom: 20,
                attribution: 'I&copy; <a href="http://www.openstreetmap.org/copyright">OpenStreetMap</a>'
            }).addTo(this.map);

            this.addProposalsToMap();
        },

        addProposalsToMap() {
            var markerIcon = L.icon({
                iconUrl: '/images/map-markers/marker-icon-2x.png',
                shadowUrl: '/images/map-markers/marker-shadow.png',
                iconSize: [25, 41], // size of the icon
                iconAnchor: [12.5, 41], // point of the icon which will correspond to marker's location
                shadowSize: [41, 41], // size of the shadow
                shadowAnchor: [12.5, 41],  // the same for the shadow
                popupAnchor: [0, -42] // point from which the popup should open relative to the iconAnchor
            });

            this.proposalsMarkers = Leaflet.markerClusterGroup();

            for (let i = 0; i < this.proposals.length; i++) {
                let marker = Leaflet.marker([this.proposals[i].lat, this.proposals[i].lng], { icon: markerIcon }).bindPopup(this.proposals[i].map_marker_content);
                this.proposalsMarkers.addLayer(marker);
            }

            this.map.addLayer(this.proposalsMarkers);

        },

        addLeadsToMap() {
            var markerIcon = L.icon({
                iconUrl: '/images/map-markers/marker-red-icon-2x.png',
                shadowUrl: '/images/map-markers/marker-shadow.png',
                iconSize: [25, 41], // size of the icon
                iconAnchor: [12.5, 41], // point of the icon which will correspond to marker's location
                shadowSize: [41, 41], // size of the shadow
                shadowAnchor: [12.5, 41],  // the same for the shadow
                popupAnchor: [0, -42] // point from which the popup should open relative to the iconAnchor
            });

            this.leadMarkers = Leaflet.markerClusterGroup({
                iconCreateFunction: function(cluster) {
                    return L.divIcon({ html: '<div>' + cluster.getChildCount() + '</div>', className: 'mycluster', iconSize: 'auto' });
                }
            });

            for (let i = 0; i < this.leads.length; i++) {
                let html = this.leads[i].zip;
                let marker = Leaflet.marker([this.leads[i].lat, this.leads[i].lng], { icon: markerIcon })
                    .bindPopup(html);
                this.leadMarkers.addLayer(marker);
            }

            this.map.addLayer(this.leadMarkers);

        },

        toggleProposals() {
            if (!this.showProposals) {
                this.addProposalsToMap();
            } else {
                this.proposalsMarkers.clearLayers();
            }

            this.showProposals = !this.showProposals;
        },

        toggleLeads() {
            if (!this.showLeads) {
                this.fetchLeads();
            } else {
                this.leadMarkers.clearLayers();
            }

            this.showLeads = !this.showLeads;
        }
    }
};
</script>

<style>
.btn-toggle-leads {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 999;
}

.mycluster {
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: hsla(360, 65%, 64%, 0.8);
    text-align: center;
    vertical-align: middle;
    border: none;
    border-radius: 0.25rem;
    color: #FFF;
    font-size: 1rem;
    padding: 0 0.75rem;
    min-width: 3rem;
    height: 3rem;
    box-shadow: 0 7px 14px 0 rgba(50, 50, 93, .1), 0 3px 6px 0 rgba(0, 0, 0, .07);
}

.mycluster > div {
    flex: 1 1 auto;
}
</style>

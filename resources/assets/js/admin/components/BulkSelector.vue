<template>
    <Popover class="relative">
        <PopoverButton class="inline-flex items-center gap-x-1 text-sm font-semibold leading-6 text-gray-900">
<!--            <span>Solutions</span>-->
<!--            <ChevronDownIcon class="h-5 w-5" aria-hidden="true" />-->
            <button aria-expanded="false" class="rounded active:outline-none active:ring focus:outline-none focus:ring focus:ring-keppel-200" type="button">
                <span class="sr-only">Select All Dropdown</span>
                <div class="toolbar-button flex items-center cursor-pointer select-none h-9 px-2 hover:bg-gray-100 rounded space-x-2">
                    <input type="checkbox" class="tailwind m-0" aria-label="Select this page" :checked="selected !== null" @change="()=>{}">
                    <svg class="flex-shrink-0 ml-2" xmlns="http://www.w3.org/2000/svg" width="10" height="6" viewBox="0 0 10 6">
                        <path class="fill-current" d="M8.292893.292893c.390525-.390524 1.023689-.390524 1.414214 0 .390524.390525.390524 1.023689 0 1.414214l-4 4c-.390525.390524-1.023689.390524-1.414214 0l-4-4c-.390524-.390525-.390524-1.023689 0-1.414214.390525-.390524 1.023689-.390524 1.414214 0L5 3.585786 8.292893.292893z"></path>
                    </svg>
                </div>
            </button>
        </PopoverButton>

        <transition enter-active-class="transition ease-out duration-200" enter-from-class="opacity-0 translate-y-1" enter-to-class="opacity-100 translate-y-0" leave-active-class="transition ease-in duration-150" leave-from-class="opacity-100 translate-y-0" leave-to-class="opacity-0 translate-y-1">
            <PopoverPanel class="absolute left-0 z-10 -ml-4 mt-1 flex w-screen max-w-max px-4">
                <div class="select-none w-[250px] overflow-hidden bg-white shadow-lg rounded-lg border border-gray-200" direction="ltr">
                    <div class="p-4">
                        <ul>
                            <li class="flex items-center mb-4">
                                <label class="flex items-center select-none space-x-2">
                                    <input class="checkbox tailwind m-0" @change="togglePage" :checked="selected !== null" type="checkbox">
                                    <span>Select this page</span>
                                    <span class="h-4 inline-flex items-center justify-center font-bold rounded-full px-2 text-mono text-xs ml-1 bg-keppel-100 text-keppel-800" v-text="pageCount"></span>
                                </label>
                            </li>
                            <li class="flex items-center">
                                <label class="flex items-center select-none space-x-2">
                                    <input class="checkbox tailwind m-0" @change="toggleAll" :checked="selected === 'all'" type="checkbox">
                                    <span>Select all</span>
                                    <span class="h-4 inline-flex items-center justify-center font-bold rounded-full px-2 text-mono text-xs ml-1 bg-keppel-100 text-keppel-800" v-text="allCount"></span>
                                </label>
                            </li>
                        </ul>
                    </div>
                </div>
            </PopoverPanel>
        </transition>
    </Popover>
</template>

<script setup>
import { Popover, PopoverButton, PopoverPanel } from '@headlessui/vue'
import { ref, watch, defineEmits } from "vue";

const props = defineProps({
    pageCount: {
        required: true
    },

    allCount: {
        required: true
    }
})

const emit = defineEmits(['change'])

const selected = ref(null)

const togglePage = () => {
    if (selected.value === 'all') {
        return selected.value = null;
    }

    selected.value = selected.value !== 'page'
        ? 'page'
        : null;
}

const toggleAll = () => {
    selected.value = selected.value === 'all'
        ? 'page'
        : 'all';
}

watch(selected, (currentValue, oldValue) => {
    emit('change', currentValue)
})
</script>

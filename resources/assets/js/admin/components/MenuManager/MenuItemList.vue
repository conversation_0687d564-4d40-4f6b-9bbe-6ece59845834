<template>
	<div>
		<ul class="draggable-list mt-md" v-sortable="options">
			<li 
				v-for="item in items"
                :key="item.id"
                :data-id="item.id"
                class="pr-md"
			>
				<span class="fa draghandle" title="Drag to reorder"></span>
				 <a href="#" @click.prevent="editItem(item)">{{ item.title }}</a>
				 <span v-show="item.type === 'menu'" class="pull-right">
				 <a :href="'/admin/menus/' + item.submenu_id + '/edit'">
				 {{ item.type_formatted }} <i class="fa fa-caret-right"></i></a>
				 </span>
			</li>
		</ul>
		<div class="gc-modal gc-modal-mask" id="editMenuItemModal" @click="hideModal('editMenuItemModal')">
			<div class="gc-modal-wrapper">
				<div class="gc-modal-container" @click.stop>
					
					<div class="gc-modal-header">
						Edit Menu Item
					</div>
		
					<div class="gc-modal-body">
						<div class="form-group">
		                    <label for="name">Display Name</label>
		                    <input name="title" class="form-control" v-model="item.title">
		                </div>

		                <div class="form-group">
		                    <label for="name">URL</label>
		                    <input name="path" class="form-control" v-model="item.path">
		                </div>
					</div>
		
					<div class="gc-modal-footer">
						<button type="button" class="btn btn-alt pull-left" @click="deleteItem(item)">Delete</button>
						<button type="button" class="btn btn-alt" @click="hideModal('editMenuItemModal')">Cancel</button>
		                <button type="submit" class="btn btn-action" @click="updateItem(item)">Save</button>
					</div>
				</div>
			</div>
		</div>
	</div>
</template>

<script>
export default {
    props: ['id'],

    created: function() {
		this.getMenuItems();
	},

	data: function() {
		return {
			items: [],
			item: '',
			options: { 
                onUpdate: this.updateSort, 
                handle: '.draghandle', 
                animation: 100,
                group: {
                    name:'segments',
                    pull: false,
                    put: true
                } 
            }
		}
	},

	methods: {
		getMenuItems: function() {
			let url = '/admin/menus/'+this.id+'/items';
            axios.get(url)
                .then(function(response) {
                	this.items = response.data
                }.bind(this))
                .catch(function(error) {
                });
		},

		editItem: function(item) {
        	this.item = item
            this.showModal('editMenuItemModal');
        },

        updateItem: function(item) {
        	eventHub.emit('showProgressBar');
            let url = '/admin/menus/'+this.id+'/items/'+item.id;
            axios.put(url, item)
                .then(function(response) {
                	eventHub.emit('hideProgressBar');
                    this.hideModal('editMenuItemModal');
                }.bind(this))
                .catch(function(error) {
                	eventHub.emit('hideProgressBar');
                });
        },

        deleteItem: function(item) {
        	eventHub.emit('showProgressBar');
            let url = '/admin/menus/'+this.id+'/items/'+item.id;
            axios.delete(url)
                .then(function(response) {
                	eventHub.emit('hideProgressBar');
                	this.getMenuItems()
                    this.hideModal('editMenuItemModal');
                }.bind(this))
                .catch(function(error) {
                	eventHub.emit('hideProgressBar');
                });
        },

        updateSort: function(event) {
            this.saved = false;
            eventHub.emit('showProgressBar');
            if(event !== undefined) {
                this.items.splice(event.newIndex, 0, this.items.splice(event.oldIndex, 1)[0]);
            }
            
            var payload = {};
            for(var i = 0; i < this.items.length; i++) {
               this.items[i].sort = i;
               payload[this.items[i].id] = i;
            }

            axios.post('/admin/menu-items/sort', payload)
                .then(function(response) {
                	eventHub.emit('hideProgressBar');
                })
                .catch(function(error) {
                    eventHub.emit('hideProgressBar');
                });
		}
	}
}
</script>
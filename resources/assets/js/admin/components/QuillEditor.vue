<template>
    <div style="height: 100%">
        <div v-show="htmlMode">
            <button type="button" @click="toggleHtmlMode">Save</button>
        </div>
        <div v-show="!htmlMode">
            <QuillEditor
                theme="snow"
                :options="options"
                :toolbar="toolbar"
                v-model:content="updatedContent"
                content-type="html"
                ref="myQuillEditor"
            ></QuillEditor>
        </div>


        <textarea
            v-show="htmlMode"
            :name="name"
            :id="name + 'Editor'"
            :value="updatedContent"
            :rows="rows"
            class="html-control"
            ref="htmlControl"
            @blur="htmlUpdated"
        ></textarea>
        <media-browser
            ref="mediaBrowser"
            @insertPhoto="insertPhoto"
        ></media-browser>
    </div>
</template>

<script>
import Quill from "quill";
import { QuillEditor } from '@vueup/vue-quill'
import '@vueup/vue-quill/dist/vue-quill.snow.css';
import MediaBrowser from "./MediaBrowser/MediaBrowser.vue";
const Link = Quill.import("formats/link");
const BlockEmbed = Quill.import("blots/block/embed");

class FigureBlot extends BlockEmbed {
    static blotName = 'figure';
    static tagName = ['figure'];
}
Quill.register(FigureBlot, true);

class LinkWithoutTarget extends Link {
    static create(value) {
        const node = super.create(value);
        node.removeAttribute("target");
        return node;
    }
}
Quill.register(LinkWithoutTarget, true);

export default {

    components: {
        MediaBrowser,
        QuillEditor
    },

    props: {
        rows: {
            type: Number,
            required: false,
            default: 4,
        },
        content: {
            type: String,
            required: false,
        },
        name: {
            type: String,
            required: true,
        },
        placeholder: {
            type: String,
            required: false,
            default: "",
        },
        autoFocus: {
            type: Boolean,
            default: false,
        },
    },

    emits: ['input', 'toggle'],

    created() {
        this.updatedContent = this.content;
    },

    data() {
        return {
            updatedContent: '',
            htmlMode: false,
            toolbar: {
                container: [
                    [{ 'header': [1, 2, 3, 4, false] }],
                    ['bold', 'italic', 'underline', 'strike'],
                    [{ 'list': 'ordered'}, { 'list': 'bullet' }, { 'align': ['', 'center', 'right'] }],
                    ['link', 'image', 'code']
                ],
                handlers: {
                    image: () => this.$refs.mediaBrowser.toggle(),
                    code: () => this.toggleHtmlMode()
                }
            },

            options: {
                bounds: "#mainContentContainer",
                placeholder: this.placeholder,
                formats: {
                    link: LinkWithoutTarget,
                },
            },
        };
    },

    methods: {
        insertPhoto: function(photo) {
            const quill = this.$refs.myQuillEditor.getQuill();
            let selection = quill.getSelection();
            quill.insertEmbed(selection.index, "image", photo.path);
        },

        toggleHtmlMode() {
            if (this.htmlMode) {
                const content = this.$refs.htmlControl.value;
                this.updatedContent = content;
                this.$refs.myQuillEditor.pasteHTML(content);
            }

            this.htmlMode = !this.htmlMode;
        },

        htmlUpdated() {
            if (this.htmlMode) {
                const content = this.$refs.htmlControl.value;
                this.updatedContent = content;
                this.$refs.myQuillEditor.pasteHTML(content);
            }
        },
    },

    watch: {
        updatedContent: function (val) {
            this.$emit('input', val)
        }
    }
};
</script>

<style scoped>
    .html-control {
        width: 100%;
        line-height: 1.5rem;
        padding: 1rem;
    }
</style>

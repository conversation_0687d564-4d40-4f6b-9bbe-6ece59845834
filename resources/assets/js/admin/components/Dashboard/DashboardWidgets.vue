<template>
    <div class="-mt-8">
        <div class="mx-auto max-w-7xl px-4 sm:px-6 lg:px-8">
            <header class="pb-4 pt-6 sm:pb-6">
                <div class="mx-auto flex max-w-7xl flex-wrap items-center gap-6 sm:flex-nowrap">
                    <h1 class="text-base font-semibold leading-7 text-gray-900">Revenue</h1>

                    <div class="ml-auto">
                        <label class="sr-only" for="location">Date Range</label>
                        <select id="location" v-model="dateRange" class="block w-full rounded-md border-0 py-1.5 pl-3 pr-10 text-gray-900 ring-1 ring-inset ring-gray-300 focus:ring-2 focus:ring-keppel-600 sm:text-sm sm:leading-6" name="location">
                            <option v-for="option in dateRangeOptions"
                                    :key="option.key"
                                    :value="option.key"
                                    v-text="option.value"
                            ></option>
                        </select>
                    </div>
                </div>
            </header>

            <revenue-trends
                v-model="dateRange"
                :dateRange="dateRange"
                :dateRangeOptions="dateRangeOptions"
            />

            <div class="mt-12 mx-auto max-w-2xl lg:mx-0 lg:max-w-none">
                <div class="flex items-center justify-between">
                    <h2 class="text-base font-semibold leading-7 text-gray-900">Top performers</h2>
                </div>

                <div class="mt-6 grid grid-cols-1 gap-x-6 gap-y-8 lg:grid-cols-3 xl:gap-x-8">
                    <div class="overflow-hidden rounded-xl bg-white border border-gray-200">
                        <top-locations :dateRange="dateRange" />
                    </div>

                    <div class="overflow-hidden rounded-xl bg-white border border-gray-200">
                        <top-customers :dateRange="dateRange" />
                    </div>

                    <div class="overflow-hidden rounded-xl bg-white border border-gray-200">
                        <top-products :dateRange="dateRange" />
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script>
import RevenueTrends from './RevenueTrends.vue';
import TopProducts from './TopProducts.vue';
import TopLocations from './TopLocations.vue';
import TopCustomers from './TopCustomers.vue';

export default {
    name: 'DashboardWidgets',

    components: {
        RevenueTrends, TopProducts, TopLocations, TopCustomers
    },

    data: () => ({
        dateRangeOptions: [
            { key: 'last_7_days', value: 'Last 7 Days' },
            { key: 'last_30_days', value: 'Last 30 Days' },
            { key: 'week', value: 'Week-to-date' },
            { key: 'month', value: 'Month-to-date' },
            { key: 'year', value: 'Year-to-date' },
            { key: 'lastWeek', value: 'Last Week' },
            { key: 'lastMonth', value: 'Last Month' },
            { key: 'lastYear', value: 'Last Year' }
        ],
        dateRange: 'last_7_days'
    })
};
</script>

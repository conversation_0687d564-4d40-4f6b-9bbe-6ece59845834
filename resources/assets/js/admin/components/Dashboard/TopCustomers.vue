<template>
    <div>
        <div class="flex items-center gap-x-4 border-b border-gray-900/5 bg-gray-50 p-6">
            <div class="p-1 rounded-lg bg-white ring-1 ring-gray-900/10">
                <svg class="h-8 w-8 flex-none text-gray-400 " xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M18 18.72a9.094 9.094 0 0 0 3.741-.479 3 3 0 0 0-4.682-2.72m.94 3.198.001.031c0 .225-.012.447-.037.666A11.944 11.944 0 0 1 12 21c-2.17 0-4.207-.576-5.963-1.584A6.062 6.062 0 0 1 6 18.719m12 0a5.971 5.971 0 0 0-.941-3.197m0 0A5.995 5.995 0 0 0 12 12.75a5.995 5.995 0 0 0-5.058 2.772m0 0a3 3 0 0 0-4.681 2.72 8.986 8.986 0 0 0 3.74.477m.94-3.197a5.971 5.971 0 0 0-.94 3.197M15 6.75a3 3 0 1 1-6 0 3 3 0 0 1 6 0Zm6 3a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Zm-13.5 0a2.25 2.25 0 1 1-4.5 0 2.25 2.25 0 0 1 4.5 0Z" />
                </svg>
            </div>
            <div class="text-sm font-medium leading-6 text-gray-900">Customers</div>
        </div>
        <transition name="fade" mode="out-in">
            <div class="bg-white">
                <dl v-if="customers.length > 0" class="-my-3 divide-y divide-gray-100 px-6 py-4 text-sm leading-6">
                    <div v-for="customer in customers" :key="customer.id" class="flex justify-between gap-x-4 py-3">
                        <dt class="text-gray-500 truncate">
                            <a :href="'/admin/users/' + customer.id + '/edit'">{{ customer.first_name }} {{ customer.last_name }}</a>
                        </dt>
                        <dd class="flex items-start gap-x-2">
                            <div class="font-medium text-gray-900">&#36;{{ customer.revenue }}</div>
                        </dd>
                    </div>
                </dl>
                <div v-if="! loading && customers.length === 0" class="text-center py-6">
                    <h3 class="mt-2 text-sm font-semibold text-gray-900">No customer data found</h3>
                    <p class="mt-1 text-sm text-gray-500">Try selecting another date range.</p>
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
export default {
    name: "TopCustomers",

    props: ['dateRange'],

    data: () => ({
        loading: true,
        customers: []
    }),

    mounted() {
        this.fetchData();
    },

    methods: {
        fetchData() {
            this.loading = true
            this.customers = []

            axios.get('/api/dashboard', {
                params: {
                    chart: "topCustomers",
                    dateRange: this.dateRange,
                },
            })
                .then(({ data }) => {
                    this.customers = data;
                    this.loading = false
                })
                .catch(error => { this.loading = false });
        },
    },

    watch: {
        dateRange() {
            this.fetchData();
        }
    }
}
</script>

<template>
    <div>
        <div class="py-3 px-4 overflow-hidden rounded-xl bg-white border border-gray-200 relative h-80">
            <canvas id="revenueTrendsChart"></canvas>
        </div>
    </div>
</template>

<script>
import {
    Chart,
    Colors,
    LineController,
    CategoryScale,
    LinearScale,
    LineElement,
    PointElement,
    Tooltip
} from 'chart.js'

Chart.register(
    Colors,
    LineController,
    LineElement,
    PointElement,
    CategoryScale,
    LinearScale,
    Tooltip
);

export default {
    name: "RevenueTrends",

    props: ['dateRangeOptions', 'dateRange'],

    emits: ['update:modelValue'],

    data: () => ({
        chartInstance: null,
        color: "hsl(110.59, 22.67%, 55.88%)",
        labels: [],
        chartData: [],
    }),

    mounted() {
        this.fetchData();
    },

    methods: {
        fetchData() {
            axios.get("/api/dashboard", {
                params: {
                    chart: "revenueTrends",
                    dateRange: this.dateRange,
                },
            })
                .then(({ data }) => {
                    this.labels = data.labels;
                    this.chartData = data.chartData;
                    this.initChart();
                })
                .catch(error => {});
        },

        initChart() {
            if (this.chartInstance) {
                this.chartInstance.destroy();
            }

            this.chartInstance = new Chart(document.getElementById("revenueTrendsChart"), {
                type: "line",
                data: {
                    labels: this.labels,
                    datasets: [{
                        data: this.chartData,
                        backgroundColor: "rgba(0, 0, 0, 0)",
                        borderColor: this.color,
                        borderWidth: 2,
                        tension: 0.4
                    }],
                },
                options: {
                    maintainAspectRatio: false,
                    plugins: {
                        legend: {
                            display: false,
                        },
                        tooltip: {
                            titleMarginBottom: 8,
                            bodySpacing: 4,
                            callbacks: {
                                label: function (tooltipItem, data) {
                                    if (!tooltipItem.yLabel) return '  $0.00';
                                    return `  $${new Intl.NumberFormat().format(tooltipItem.yLabel)}`;
                                },
                            },
                        },
                    },
                    scales: {
                        y: {
                            min: 0,
                            ticks: {
                                // Include a dollar sign in the ticks
                                callback: function(value, index, values) {
                                    return '$' + Intl.NumberFormat().format(value);
                                }
                            }
                        }
                    }
                },
            });
        },
    },

    watch: {
        dateRange() {
            this.fetchData();
        }
    }
}
</script>

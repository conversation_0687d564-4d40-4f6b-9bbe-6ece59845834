<template>
    <div>
        <div class="flex items-center gap-x-4 border-b border-gray-900/5 bg-gray-50 p-6">
            <div class="p-1 rounded-lg bg-white ring-1 ring-gray-900/10">
                <svg class="h-8 w-8 flex-none text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12" />
                </svg>
            </div>
            <div class="text-sm font-medium leading-6 text-gray-900">Deliveries</div>
        </div>
        <transition name="fade" mode="out-in">
            <div class="bg-white">
                <dl v-if="locations.length > 0" class="-my-3 divide-y divide-gray-100 px-6 py-4 text-sm leading-6">
                    <div v-for="location in locations" :key="location.id" class="flex justify-between gap-x-4 py-3">
                        <dt class="text-gray-500 truncate">
                            <a :href="`/admin/logistics/${location.fulfillment_type === 1 ? 'pickups' : 'delivery'}/${location.id}/edit`" v-text="location.title"></a>
                        </dt>
                        <dd class="flex items-start gap-x-2">
                            <div class="font-medium text-gray-900">&#36;{{ location.revenue }}</div>
                        </dd>
                    </div>
                </dl>
                <div v-if="! loading && locations.length === 0" class="text-center py-6">
                    <h3 class="mt-2 text-sm font-semibold text-gray-900">No delivery data found</h3>
                    <p class="mt-1 text-sm text-gray-500">Try selecting another date range.</p>
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
export default {
    name: "TopLocations",

    props: ['dateRange'],

    data: () => ({
        loading: true,
        locations: []
    }),

    mounted() {
        this.fetchData();
    },

    methods: {
        fetchData() {
            this.loading = true
            this.locations = []

            axios.get('/api/dashboard', {
                params: {
                    chart: "topLocations",
                    dateRange: this.dateRange,
                },
            })
                .then(({ data }) => {
                    this.locations = data;
                    this.loading = false;
                })
                .catch(error => { this.loading = false });
        },
    },

    watch: {
        dateRange() {
            this.fetchData();
        }
    }
}
</script>

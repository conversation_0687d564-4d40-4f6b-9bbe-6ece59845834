<template>
    <div>
        <div class="flex items-center gap-x-4 border-b border-gray-900/5 bg-gray-50 p-6">
            <div class="p-1 rounded-lg bg-white ring-1 ring-gray-900/10">
                <svg class="h-8 w-8 flex-none text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M9.813 15.904 9 18.75l-.813-2.846a4.5 4.5 0 0 0-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 0 0 3.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 0 0 3.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 0 0-3.09 3.09ZM18.259 8.715 18 9.75l-.259-1.035a3.375 3.375 0 0 0-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 0 0 2.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 0 0 2.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 0 0-2.456 2.456ZM16.894 20.567 16.5 21.75l-.394-1.183a2.25 2.25 0 0 0-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 0 0 1.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 0 0 1.423 1.423l1.183.394-1.183.394a2.25 2.25 0 0 0-1.423 1.423Z" />
                </svg>
            </div>
            <div class="text-sm font-medium leading-6 text-gray-900">Products</div>
        </div>
        <transition name="fade" mode="out-in">
            <div class="bg-white">
                <dl v-if="products.length > 0" class="-my-3 divide-y divide-gray-100 px-6 py-4 text-sm leading-6">
                    <div v-for="product in products" :key="product.id" class="flex justify-between gap-x-4 py-3">
                        <dt class="text-gray-500 truncate">
                            <a :href="`/admin/products/${product.id}/edit`" v-text="product.title"></a>
                        </dt>
                        <dd class="flex items-start gap-x-2">
                            <div class="font-medium text-gray-900">{{ product.count }}</div>
                        </dd>
                    </div>
                </dl>
                <div v-if="! loading && products.length === 0" class="text-center py-6">
                    <h3 class="mt-2 text-sm font-semibold text-gray-900">No product data found</h3>
                    <p class="mt-1 text-sm text-gray-500">Try selecting another date range.</p>
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
export default {
    name: "TopProducts",

    props: ['dateRange'],

    data: () => ({
        loading: true,
        products: []
    }),

    mounted() {
        this.fetchData();
    },

    methods: {
        fetchData() {
            this.loading = true
            this.products = []

            axios.get('/api/dashboard', {
                params: {
                    chart: "topProducts",
                    dateRange: this.dateRange,
                },
            })
                .then(({ data }) => {
                    this.products = data;
                    this.loading = false
                })
                .catch(error => { this.loading = false });
        },
    },

    watch: {
        dateRange() {
            this.fetchData();
        },
    }
}
</script>

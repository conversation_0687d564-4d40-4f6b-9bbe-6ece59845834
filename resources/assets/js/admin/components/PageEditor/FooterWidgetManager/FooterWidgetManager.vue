<template>
    <div class="footerWidget">
        <div class="panel panel-default">
            <div class="panel-heading flex align-items-m">
                <div class="flex-item">
                    Widgets
                </div>
                <div class="flex-item push-right dropup">
                    <button type="button" class="btn btn-light btn-sm" data-toggle="dropdown"><i class="fa fa-plus fa-fw"></i> Add</button>
                    <ul class="dropdown-menu dropdown-menu--right" aria-labelledby="dLabel">
                        <li><a href="#" @click.prevent="addWidget('RichText')"><i class="fa fa-fw fa-paragraph"></i> Text</a></li>
                        <li><a href="#" @click.prevent="addWidget('HTML')"><i class="fa fa-code fa-fw"></i> HTML</a></li>
                        <li><a href="#" @click.prevent="addWidget('LinkList')"><i class="fa fa-link fa-fw"></i> Link List</a></li>
                        <li><a href="#" @click.prevent="addWidget('SocialNetworks')"><i class="fa fa-facebook fa-fw"></i> Social Networks</a></li>
                        <li><a href="#" @click="addWidget('Newsletter')"><i class="fa fa-newspaper-o fa-fw"></i> Newsletter</a></li>
                        <li><a href="#" @click.prevent="addWidget('ContactDetails')"><i class="fa fa-id-card-o fa-fw"></i> Contact Card</a></li>
                        <li><a href="#" @click.prevent="addWidget('Divider')"><i class="fa fa-minus fa-fw"></i> Divider</a></li>
                        <li><a href="#" @click.prevent="addWidget('Spacer')"><i class="fa fa-stop fa-fw"></i> Spacer</a></li>
                    </ul>
                </div>
            </div>
            <div class="panel-body">
                <ul id="widgetList" class="draggable-list" v-sortable="options">
                    <li
                        v-for="widget in widgets"
                        :data-id="widget.id"
                        class="sortItem truncate"
                        :key="widget.id"
                    >
                        <span class="fa fa-bars drag_handle text-gray" title="Drag to reorder"></span>
                        <div>
                            <a href="#" @click="editWidget(widget)"><strong>{{ widget.title }} <small>(edit)</small></strong></a>
                            <div class="widget_list_preview" v-if="widget.content">
                                {{ widgetPreview(widget.content) }}
                            </div>
                            <div class="widget_list_preview" v-else>
                                {{ widget.settings.header }}
                            </div>
                        </div>
                    </li>
                    <li v-if="!widgets.length">No footer widgets added yet.</li>
                </ul>
            </div>
        </div>
        <div class="gc-modal gc-modal-mask" id="widgetModal" @click="hideModal('widgetModal')">
            <div class="gc-modal-wrapper">
                <div class="gc-modal-container" :class="{'gc-modal-container--large': modalLarge}" @click.stop>

                    <div class="gc-modal-header">
                        {{ widget.title }}
                    </div>

                    <div class="gc-modal-body">
                        <component
                            :is="getTemplate(widget.template)"
                            :widget="widget"
                            :block="widget"
                            :page="id"
                            :create="createMode"
                        ></component>
                    </div>

                    <div class="gc-modal-footer" v-if="createMode">
                        <button type="button" class="btn btn-alt" @click="hideModal('widgetModal')">Cancel</button>
                        <button type="button" class="btn btn-action" @click="storeWidget(widget)">Add Widget</button>
                    </div>
                    <div class="gc-modal-footer" v-else>
                        <button type="button" class="btn btn-alt pull-left" @click="removeWidget(widget)">Delete</button>
                        <button type="button" class="btn btn-action" @click="update(widget)">Save &amp; Close</button>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script>
import RichText from '../../WidgetManager/RichText.vue';
import HtmlEditor from '../../WidgetManager/HTML.vue';
import Newsletter from '../../WidgetManager/Newsletter.vue';
import ContactDetails from '../Widgets/ContactDetails.vue';
import LinkList from '../Widgets/LinkList.vue';
import Divider from '../../WidgetManager/Divider.vue';
import Spacer from '../Widgets/Spacer.vue';
import SocialNetworks from '../Widgets/SocialNetworks.vue';

export default {
    components: {
        RichText,
        HtmlEditor,
        Newsletter,
        ContactDetails,
        LinkList,
        Divider,
        Spacer,
        SocialNetworks,
    },

    props: ['id'],

    emits: ['createWidget', 'updateWidget'],

    created() {
        this.getWidgets();
        this.getSettings();

        window.eventHub.on('widgetModal:closed', this.resetWidget);
    },

    data() {
        return {
            widgets: [],
            settings: [],
            count: 1,
            widget: {
                'title': null,
                'template': null,
                'content': '',
                'settings': {}
            },
            showEditor: false,
            createMode: true,
            modalLarge: false,
            options: {
                onUpdate: this.updateSort,
                handle: '.drag_handle',
                animation: 150,
                group: {
                    name:'segments',
                    pull: false,
                    put: true
                }
            }
        }
    },

    events: {
        'removeWidget': function(widget) {
            this.removeWidget(widget)
        },
        'update': function(widget, payload) {
            this.update(widget.id, payload)
        },
        widgetUpdated: function(widget) {
            this.update(widget)
        }
    },

    methods: {
        widgetPreview(widget) {
            return widget.replace(/(<([^>]+)>)/ig,"").replace('&nbsp;', " ")
        },

        getTemplate(template) {
            if (template === 'HTML') {
                return 'HtmlEditor';
            }

            return template;
        },

        toggleEditor() {
            this.showEditor = true;
            this.showModal('widgetModal');
        },

        getWidgets() {
            axios.get('/admin/footer/widgets')
                .then(({ data }) => {
                    this.widgets = data;
                })
                .catch(error => {

                });
        },

        getSettings() {
            axios.get('/api/settings')
                .then(({ data }) => {
                    this.settings = data;
                })
                .catch(error => {});
        },

        addWidget(template) {
            this.createMode = true;
            this.widget = {
                'title': template,
                'template': template,
                'content': '',
                'settings': {}
            }
            this.toggleEditor();
        },

        storeWidget(widget) {
            this.$emit('createWidget');
            eventHub.emit('showProgressBar');

            if (widget) {
                axios.post('/admin/footer/widgets/', widget)
                    .then(() => {
                        document.getElementById('themePreview').contentWindow.location.reload();
                        this.hideModal('widgetModal');
                        this.removeWidget();
                        this.getWidgets();
                        eventHub.emit('hideProgressBar');
                    })
                    .catch(error => {
                        console.log(error);
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('error', error);
                    });
            }
        },

        editWidget(widget) {
            this.createMode = false;
            this.widget = widget;
            this.toggleEditor();
        },

        resetWidget() {
            this.widget = {
                'title': null,
                'template': null,
                'content': '',
                'settings': {}
            }
        },

        update(widget) {
            this.$emit('updateWidget');
            eventHub.emit('showProgressBar');

            axios.put(`/admin/footer/widgets/${widget.id}`, widget)
                .then(() => {
                    document.getElementById('themePreview').contentWindow.location.reload();
                    this.resetWidget();
                    this.hideModal('widgetModal');
                    this.getWidgets();
                    eventHub.emit('hideProgressBar');
                })
                .catch(error => {
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                });
        },

        removeWidget(widget) {
            eventHub.emit('showProgressBar')

            axios.delete(`/admin/footer/widgets/${widget.id}`)
                .then(() => {
                    document.getElementById('themePreview').contentWindow.location.reload();
                    this.resetWidget();
                    this.hideModal('widgetModal');
                    this.getWidgets();
                    eventHub.emit('hideProgressBar');
                })
                .catch(error => {
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                });
        },

        updateSort(event) {
            eventHub.emit('showProgressBar');

            // resort widget list locally
            this.widgets.splice(
                event.newIndex,
                0,
                this.widgets.splice(event.oldIndex, 1)[0]
            );

            // use updated local widget sort order to set sort order via API
            for (let i = 0; i < this.widgets.length; i++) {
                this.widgets[i].sort = i + 1;

                axios.put(`/admin/footer/widgets/${this.widgets[i].id}`, { ... this.widgets[i] })
                    .then(() => {
                        this.getWidgets();
                        eventHub.emit('hideProgressBar');
                    })
                    .catch(error => {
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('error', error);
                    });
            }

            document.getElementById('themePreview').contentWindow.location.reload();
        }
    }
}
</script>

<style>
.dropdown-menu--right {
    left: auto;
    right: 3px;
    top: 40px;
}

.dropdown-menu--right::before {
    left: auto;
    right: 12px;
}

.drag_handle, .draghandle {
    color: #9d9d9d;
    margin-right: 0.5em;
    cursor: move;
}

.draghandle::before {
    content: "\f0c9" !important;
}

.widget_list_preview {
    display: block;
    font-size: 12px;
    overflow: hidden;
    max-width: 175px;
    text-overflow: ellipsis;
    white-space: nowrap;
}
</style>
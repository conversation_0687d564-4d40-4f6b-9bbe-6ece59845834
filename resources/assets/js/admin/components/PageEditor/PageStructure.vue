<template>
<div class="pageStructure">
    <ul 
        id="activeBlockList" 
        class="pageEditor__activeBlocksList" 
        v-sortable="options" 
        v-if="page.widgets"
    >
        <li 
            v-for="widget in page.widgets" 
            :data-id="widget.id"
            class="sortItem" 
            :key="widget.id"
        >
            <div 
                class="activeBlocksList__icon text-left" 
                title="Drag to re-order"
            >
                <i class="fal draghandle"></i>
            </div>

            <a 
                href="#" 
                class="text-center sidebarNavigation__heading" 
                @click.prevent="event('pageEditor:editWidget', widget)"
            >{{ widget.title }}
            </a>

            <div class="dropdown flex-item">
                <a href="#" class="activeBlocksList__icon dropdown-toggle text-right" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="far fa-ellipsis-v"></i>
                </a>
                <ul class="dropdown-menu pull-right">
                    <li><a href="#" class="btn-alt pr-sm pl-sm" @click.prevent="event('pageEditor:editWidget', widget)"><i class="far fa-edit fa-fw"></i> Edit</a></li>
                    <li><a href="#" class="btn-alt pr-sm pl-sm" @click.prevent="event('pageEditor:renameWidget', widget)"><i class="far fa-i-cursor fa-fw"></i> Rename</a></li>
                    <li><a href="#" class="btn-alt pr-sm pl-sm" @click.prevent="event('pageEditor:duplicateWidget', widget)"><i class="far fa-clone fa-fw"></i> Duplicate</a></li>
                    <li><a href="#" class="btn-alt pr-sm pl-sm" @click.prevent="event('pageEditor:deleteWidget', widget)"><i class="far fa-trash fa-fw"></i> Delete</a></li>
                </ul>
            </div>
        </li>
    </ul>    
</div>
</template>

<script>
	export default {

        props: ['page'],
        
        data() {
			return {
				options: { 
                    onUpdate: this.update, 
                    handle: '.draghandle',
                    dragClass: "sortable-drag",
                    animation: 75,
                    group: {
                        name:'page_structure',
                        pull: false,
                        put: true
                    }
                }
			}
		},

		methods: {
			update(event) {
                eventHub.emit('showProgressBar');

                this.page.widgets.splice(
                    event.newIndex,
                    0,
                    this.page.widgets.splice(event.oldIndex, 1)[0]
                );

                let widgets = [];

                for (let i = 0; i < this.page.widgets.length; i++) {
                    widgets.push({ id: this.page.widgets[i].id, sort: i })
                }

                axios.post(`/admin/pages/${this.page.id}/widget-sort`, { widgets })
                    .then(function(response) {
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('pagePreview:refresh')
                    })
                    .catch(function(error) {
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('error', error);
                    });
            }
		}
	}
</script>
<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Buttons
    </div>

    <div class="form-group">
        <label>Primary Call-to-action</label>
        <div class="radio form-group">
            <label class="radio-inline mr-sm">
                <input 
                    type="radio" :value="true" 
                    v-model="cta_show" 
                > Show
            </label>
            <label class="radio-inline">
                <input 
                    type="radio" 
                    :value="false" 
                    v-model="cta_show" 
                > Hide
          </label>
        </div>

        <div v-show="cta_show">
            <div class="form-group">
                <text-input 
                    v-model="cta" 
                    placeholder="Button text"
                ></text-input>
            </div>
            <div class="form-group">
                <text-input 
                    v-model="cta_url" 
                    placeholder="Button URL"
                ></text-input>
            </div>
        </div>    
    </div>
    <hr>
    <div class="form-group">
        <label>Secondary Call-to-action</label>
        <div class="radio form-group">
            <label class="radio-inline mr-sm">
                <input 
                    type="radio" 
                    :value="true" 
                    v-model="cta_2_show" 
                > Show
            </label>
            <label class="radio-inline">
                <input 
                    type="radio" 
                    :value="false" 
                    v-model="cta_2_show" 
                > Hide
          </label>
        </div>
        <div v-show="cta_2_show">
            <div class="form-group">
                <text-input 
                    v-model="cta_2" 
                    placeholder="Button text"
                ></text-input>
            </div>
            <div class="form-group">
                <text-input 
                    v-model="cta_2_url" 
                    placeholder="Button text"
                ></text-input>
            </div>
        </div>    
    </div>
</div>    
</template>

<script>
import TextInput from '../../_inputs/text-input.vue';
export default {
    props: ['widget'],
    
    components: {
        TextInput
    },

    computed: {
        cta_show: {
            get() {
                return this.widget.settings.cta_show;
            },
            set(newVal) {
                this.widget.settings.cta_show = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        cta: {
            get() {
                return this.widget.settings.cta
            },
            set(newVal) {
                this.widget.settings.cta = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        cta_url: {
            get() {
                return this.widget.settings.cta_url
            },
            set(newVal) {
                this.widget.settings.cta_url = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },

        cta_2_show: {
            get() {
                return this.widget.settings.cta_2_show;
            },
            set(newVal) {
                this.widget.settings.cta_2_show = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        cta_2: {
            get() {
                return this.widget.settings.cta_2
            },
            set(newVal) {
                this.widget.settings.cta_2 = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        cta_2_url: {
            get() {
                return this.widget.settings.cta_2_url
            },
            set(newVal) {
                this.widget.settings.cta_2_url = newVal;
                eventHub.emit('pageEditor:update', this.widget);
            }
        }
    }
}
</script>
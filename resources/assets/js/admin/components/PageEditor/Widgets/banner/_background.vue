<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Background
    </div>

    <div class="form-group">
        <label>Photo</label>
        <cover-photo 
            :src="background" 
            @input="updatePhoto"
        ></cover-photo>
    </div>

    <div class="checkbox form-group" v-if="background_height"> 
        <label>
            <input 
            type="checkbox" 
            v-model="match_image_height"
        > Constrain banner height to image
        </label>
    </div>

    <div class="form-group">
        <label>Background Tint</label>
        <input 
            type="text" 
            v-model="background_overlay_color" 
            class="form-control" 
            id="backgroundOverlayColor" 
        >
    </div> 

    <div class="form-group">
        <label>Background Position</label>
        <background-position v-model="background_position"></background-position>
        <!-- <select 
            v-model="background_position" 
            class="form-control" 
        >
            <option value="left top">Left Top</option>
            <option value="left center">Left Center</option>
            <option value="left bottom">Left Bottom</option>
            <option value="right top">Right Top</option>
            <option value="right center">Right Center</option>
            <option value="right bottom">Right Bottom</option>
            <option value="center top">Center Top</option>
            <option value="center center">Center Center</option>
            <option value="center bottom">Center Bottom</option>
        </select> -->
    </div>

    <div class="form-group">
        <label>Background Attachment</label>
        <select 
            v-model="background_attachment" 
            class="form-control" 
        >
            <option value="scroll">Normal</option>
            <option value="fixed">Move While Scrolling</option>
        </select>
    </div>

    <div class="form-group">
        <label>Background Size</label>
        <select 
            v-model="background_size" 
            class="form-control" 
        >
            <option value="auto">Normal</option>
            <option value="cover">Fill banner</option>
            <option value="contain">Contain inside banner</option>
        </select>
    </div>

    <div class="form-group" v-show="background_size !== 'cover'">
        <label>Background Repeat</label>
        <select 
            v-model="background_repeat" 
            class="form-control" 
        >
            <option value="no-repeat">No-Repeat</option>
            <option value="repeat">Repeat</option>
        </select>
    </div>   
</div>    
</template>

<script>
import BackgroundPosition from '../../_inputs/background-position.vue';
import CoverPhoto from '../../../MediaManager/CoverPhoto.vue';
export default {
    props: ['widget'],

    components: {
        CoverPhoto,
        BackgroundPosition
    },

    mounted: function()
    {
        let self = this;
        $("#backgroundOverlayColor").spectrum({
            showInput: true,
            allowEmpty: true,
            showAlpha: true,
            color: self.background_overlay_color,
            change: function(color) {
                self.background_overlay_color = color ? color.toHslString() : 'transparent';
            }
        });
    },

    computed: {
        background: {
            get() {
                return this.widget.settings.background;
            },
            set(newVal) {
                this.widget.settings.background = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        background_overlay_color: {
            get() {
                return this.widget.settings.background_overlay_color
            },
            set(newVal) {
                this.widget.settings.background_overlay_color = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        background_position: {
            get() {
                return this.widget.settings.background_position
            },
            set(newVal) {
                this.widget.settings.background_position = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        background_attachment: {
            get() {
                return this.widget.settings.background_attachment
            },
            set(newVal) {
                this.widget.settings.background_attachment = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        background_size: {
            get() {
                return this.widget.settings.background_size
            },
            set(newVal) {
                this.widget.settings.background_size = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        background_repeat: {
            get() {
                return this.widget.settings.background_repeat
            },
            set(newVal) {
                this.widget.settings.background_repeat = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        background_height: {
            get() {
                return this.widget.settings.background_height
            },
            set(newVal) {
                this.widget.settings.background_height = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        match_image_height: {
            get() {
                return this.widget.settings.match_image_height;
            },
            set(newVal) {
                this.widget.settings.match_image_height = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
    },

    methods: {
            updatePhoto: function(photo) {
                this.background_height = photo.height;
                this.background = photo.path;
            },
        }
}
</script>
<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Heading Text
    </div>

    <div class="form-group">
        <label>Heading Text</label>
        <plain-text v-model="heading_text"></plain-text>
        <!-- <textarea 
            :value="heading_text"
            class="form-control height--200"  
            @change="heading_text = $event.target.value" 
        ></textarea> -->
    </div>

    <div class="form-group">
        <label>Line Height</label>
        <line-height v-model="heading_line_height"></line-height>
    </div>

    <div class="form-group">
        <label>Letter Spacing</label>
        <letter-spacing v-model="heading_letter_spacing"></letter-spacing>
    </div>    

    <div class="form-group">
        <label>Heading Width</label>
        <div class="range-group">
            <input 
                type="range"  
                min="320" 
                max="1366" 
                step="16" 
                class="form-control" 
                :value="heading_width" 
                @change="heading_width = $event.target.value"
            >
            <input 
                type="number" 
                min="320" 
                max="1366" 
                class="form-control" 
                :value="heading_width" 
                @change="heading_width = $event.target.value" 
            >    
        </div>    
    </div>

    <div class="form-group">
        <label>Font Size</label>
        <font-size v-model="heading_font_size"></font-size>
    </div>

    <div class="form-group">
        <label>Text Style</label>
        <text-transform v-model="heading_text_transform"></text-transform>
    </div>

    <div class="form-group">
        <label>Text Color</label>
        <input 
            v-show="header_text_color == 'custom'"
            type="text" 
            v-model="header_text_color" 
            class="form-control colorpicker--modal mt-sm" 
            id="headingTextColor"
        >
    </div>
</div>    
</template>

<script>
import LineHeight from '../../_inputs/line-height.vue';
import LetterSpacing from '../../_inputs/letter-spacing.vue';
import FontSize from '../../_inputs/font-size.vue';
import PlainText from '../../_inputs/plain-text.vue';
import TextTransform from '../../_inputs/text-transform.vue';
export default {
    props: ['widget'],
    
    components: {
        LineHeight,
        LetterSpacing,
        FontSize,
        PlainText,
        TextTransform
    },

    mounted: function()
    {   
        let self = this;
        $("#headingTextColor").spectrum({
            preferredFormat: "hex",
            showInput: true,
            allowEmpty: true,
            change: function(color) {
                self.header_text_color = color ? color.toHexString() : '#FFF';
            }
        });
    },

    computed: {
        props() {
            return this.widget
        },
        heading_text: {
            get() {
                return this.widget.settings.header;
            },
            set(newVal) {
                this.widget.settings.header = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        heading_line_height: {
            get() {
                return this.widget.settings.header_line_height
            },
            set(newVal) {
                this.widget.settings.header_line_height = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        heading_letter_spacing: {
            get() {
                return this.widget.settings.header_letter_spacing
            },
            set(newVal) {
                this.widget.settings.header_letter_spacing = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        heading_width: {
            get() {
                return this.widget.settings.header_width
            },
            set(newVal) {
                this.widget.settings.header_width = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        heading_font_size: {
            get() {
                return this.widget.settings.header_font_size
            },
            set(newVal) {
                this.widget.settings.header_font_size = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        header_text_color: {
            get() {
                return this.widget.settings.header_text_color
            },
            set(newVal) {
                this.widget.settings.header_text_color = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        heading_text_transform: {
            get() {
                return this.widget.settings.header_text_transform ? this.widget.settings.header_text_transform : 'none'
            },
            set(newVal) {
                this.widget.settings.header_text_transform = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        }
    }
}
</script>
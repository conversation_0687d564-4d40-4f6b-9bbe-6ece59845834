<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Appearance
    </div>

    <div class="form-group">
        <label>Padding Top</label>
        <spacing-control v-model="paddingTop"></spacing-control>
    </div>        
    
    <div class="form-group">
        <label>Padding Bottom</label>
        <spacing-control v-model="paddingBottom"></spacing-control>
    </div>
    <hr>    

    <div class="form-group">
        <label>Alignment</label>
        <select 
            v-model="alignment" 
            class="form-control" 
        >
            <option value="text-left">Left</option>
            <option value="text-center">Center</option>
            <option value="text-right">Right</option>
        </select>
    </div>    

    <div class="form-group">
        <label>Banner Width</label>
        <spacing-control 
            v-model="inner_container_max_width" 
            :min="550" 
            :max="1600" 
        ></spacing-control>
    </div> 
</div>    
</template>

<script>
import SpacingControl from '../../_inputs/spacing-control.vue';
export default {
    props: ['widget'],
    
    components: {
        SpacingControl
    },

    created() {
        eventHub.emit('pageEditor:setWidgetElementTitle', 'Appearance')
    },

    computed: {
        paddingTop: {
            get() {
                return this.widget.settings.paddingTop;
            },
            set(newVal) {
                this.widget.settings.paddingTop = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        paddingBottom: {
            get() {
                return this.widget.settings.paddingBottom
            },
            set(newVal) {
                this.widget.settings.paddingBottom = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        alignment: {
            get() {
                return this.widget.settings.alignment
            },
            set(newVal) {
                this.widget.settings.alignment = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        inner_container_max_width: {
            get() {
                return this.widget.settings.inner_container_max_width
            },
            set(newVal) {
                this.widget.settings.inner_container_max_width = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        }
    }
}
</script>
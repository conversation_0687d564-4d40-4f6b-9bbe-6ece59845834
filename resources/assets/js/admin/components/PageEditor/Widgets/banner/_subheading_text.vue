<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Subheading Text
    </div>

    <div class="form-group">
        <label>Subheading Text</label>
        <plain-text v-model="subheading_text"></plain-text>
    </div>

    <div class="form-group">
        <label>Line Height</label>
        <line-height v-model="subheading_line_height"></line-height>
    </div>

    <div class="form-group">
        <label>Letter Spacing</label>
        <letter-spacing v-model="subheading_letter_spacing"></letter-spacing>
    </div>    

    <div class="form-group">
        <label>Subheading Width</label>
        <div class="range-group">
             <input 
                type="range"  
                min="320" 
                max="1366" 
                step="16" 
                class="form-control" 
                :value="subheading_width" 
                @change="subheading_width = $event.target.value" 
            >
            <input 
                type="number" 
                min="320" 
                max="1366" 
                class="form-control" 
                :value="subheading_width" 
                @change="subheading_width = $event.target.value"
            >    
        </div>    
    </div>

    <div class="form-group">
        <label>Font Size</label>
        <font-size v-model="subheading_font_size"></font-size>
    </div>

     <div class="form-group">
        <label>Text Style</label>
        <text-transform v-model="subheading_text_transform"></text-transform>
    </div>

    <div class="form-group">
        <label>Text Color</label>
        <input 
            v-show="subheader_text_color == 'custom'"
            type="text" 
            v-model="subheader_text_color" 
            class="form-control colorpicker--modal mt-sm" 
            id="headingTextColor"
        >
    </div>
</div>    
</template>

<script>
import LineHeight from '../../_inputs/line-height.vue';
import LetterSpacing from '../../_inputs/letter-spacing.vue';
import FontSize from '../../_inputs/font-size.vue';
import PlainText from '../../_inputs/plain-text.vue';
import TextTransform from '../../_inputs/text-transform.vue';
export default {
    props: ['widget'],

     components: {
        LineHeight,
        LetterSpacing,
        FontSize,
        PlainText,
        TextTransform
    },

    mounted: function()
    {   
        let self = this;
        $("#headingTextColor").spectrum({
            preferredFormat: "hex",
            showInput: true,
            allowEmpty: true,
            change: function(color) {
                self.subheader_text_color = color ? color.toHexString() : '#FFF';
            }
        });
    },

    computed: {
        subheading_text: {
            get() {
                return this.widget.settings.message;
            },
            set(newVal) {
                this.widget.settings.message = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        subheading_line_height: {
            get() {
                return this.widget.settings.subheader_line_height
            },
            set(newVal) {
                this.widget.settings.subheader_line_height = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        subheading_letter_spacing: {
            get() {
                return this.widget.settings.subheader_letter_spacing
            },
            set(newVal) {
                this.widget.settings.subheader_letter_spacing = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        subheading_width: {
            get() {
                return this.widget.settings.subheader_width
            },
            set(newVal) {
                this.widget.settings.subheader_width = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        subheading_font_size: {
            get() {
                return this.widget.settings.subheader_font_size
            },
            set(newVal) {
                this.widget.settings.subheader_font_size = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        subheader_text_color: {
            get() {
                return this.widget.settings.subheader_text_color
            },
            set(newVal) {
                this.widget.settings.subheader_text_color = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        subheading_text_transform: {
            get() {
                return this.widget.settings.subheader_text_transform ? this.widget.settings.subheader_text_transform : 'none'
            },
            set(newVal) {
                this.widget.settings.subheader_text_transform = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        }
    }
}
</script>
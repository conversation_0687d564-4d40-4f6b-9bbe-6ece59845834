<template>
<div class="photoEditor">
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'team_members')" 
        ><i class="fa fa-chevron-left"></i></a> Edit Member
    </div>

    <div class="form-group">
        <div class="photoPreview">
            <cover-photo 
                :src="src" 
                @input="updatePhoto"
            ></cover-photo>
        </div>
    </div>

    <div class="form-group">
        <label>Name</label>
        <text-input v-model="caption"></text-input>
    </div>

    <div class="form-group">
        <label>Title</label>
        <text-input v-model="subcaption"></text-input>
    </div>

    <div class="form-group">
        <label>Biography</label>
        <plain-text v-model="bio"></plain-text>
    </div>
</div>                            
</template>

<script>
import TextInput from '../../_inputs/text-input.vue';
import PlainText from '../../_inputs/plain-text.vue';
import CoverPhoto from '../../../MediaManager/CoverPhoto.vue';
export default {
    props: ['widget', 'photo', 'widgetItem'],

    emits: ['update'],

    components: {
        CoverPhoto,
        TextInput,
        PlainText
    },

    methods: {
        updatePhoto(photo) {
            this.$emit('update', photo);
        }
    },

    computed: {
        src: {
            get() {
                return this.photo.src;
            },
            set(newVal) {
                this.photo.src = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        caption: {
            get() {
                return this.photo.caption;
            },
            set(newVal) {
                this.photo.caption = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        subcaption: {
            get() {
                return this.photo.subcaption;
            },
            set(newVal) {
                this.photo.subcaption = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        bio: {
            get() {
                return this.photo.bio;
            },
            set(newVal) {
                this.photo.bio = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
    }
}
</script>    

                                
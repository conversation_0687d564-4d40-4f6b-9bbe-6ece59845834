<template>
<div class="photoGrid__itemListContainer">
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Team Members
    </div>

    <ul class="photoGrid__list draggable-list" v-sortable="options">
        <li 
            v-for="photo in widget.settings.items" 
            class="sortItem flex align-item-m justify-between pa-0" 
            :key="itemIndex(photo)"
        >
            <img 
                :src="photo.src ? photo.src : this.placeholder" 
                :alt="photo.caption" class="photoGrid__imagePreview" 
                :class="{'photoGrid__imagePreview--active': activePhoto == photo}"
                @click.prevent="$emit('edit', photo)"
            >
            <div class="photoGrid__listTitle">
                <a href="#" @click.prevent="$emit('edit', photo)">{{ photo.caption }}</a>
            </div>
            <div 
                class="flex-item pr-md" 
                title="Drag to re-order"
            >
                <i class="fal draghandle"></i>
            </div>
        </li>
        <li style="justify-content: center;">
            <button 
                type="button" 
                class="btn btn-sm btn-alt text-center" 
                @click="addPhoto"
            ><i class="far fa-plus-circle"></i> Add Team Member</button>
        </li>
    </ul>
</div>
</template>

<script>
export default {
    props: ['widget'],

    emits: ['edit'],

    created() {
        this.fixIds() // Fix any items that don't have an ID.
    },

    data: function()
    {
        return {
            linkLabel: 'Beef',
            linkUrl: '/',
            items: [],
            activePhoto: null,
            placeholder: 'https://s3.amazonaws.com/grazecart/stockphotos/photo-grid-placeholder.jpg',
            options: {
                onUpdate: this.updateSortOrder,
                handle: '.draghandle',
                dragClass: "sortable-drag",
                animation: 150,
                group: {
                    name:'page_structure',
                    pull: false,
                    put: false
                }
            }
        }
    },

    methods: {
        addPhoto: function()
        {
        	let photo = {
                id: this.largestId(),
                caption: 'Team Member '+this.widget.settings.items.length, 
                subcaption: '', 
                url: '/',
                src: 'https://s3.amazonaws.com/grazecart/stockphotos/profile-placeholder.jpg'
            }
            this.widget.settings.items.push(photo)
            this.activePhoto = photo

            eventHub.emit('pageEditor:updateWidget', this.widget);
        },

        updateSortOrder: function(event)
        {
            this.widget.settings.items.splice(event.newIndex, 0, this.widget.settings.items.splice(event.oldIndex, 1)[0]);
            eventHub.emit('pageEditor:updateWidget', this.widget);
        },

        largestId: function() {
            let largestId = 0;
            for(let i = 0; i < this.widget.settings.items.length; i++)
            {
                if(this.widget.settings.items[i].id > largestId)
                {
                    largestId = this.widget.settings.items[i].id;
                }
            }
            return largestId + 1;
        },

        fixIds() {
            for(let i = 0; i < this.widget.settings.items.length; i++) {
                if(this.widget.settings.items[i].id === undefined) {
                    this.widget.settings.items[i].id = this.largestId();
                }
            }

            eventHub.emit('pageEditor:updateWidget', this.widget);
        },

        itemIndex: function(photo) {
            return photo.id === undefined ? Math.floor((Math.random() * 10000) + 1) : photo.id
        },
    }
}
</script>

<style>

.photoGrid__itemListContainer {
    width: 100%;
}

.photoGrid__list li {
    display: flex;
    align-items: center;
}

.photoGrid__itemDetailsContainer {
    width: 100%;
}

.photoGrid__imagePreview {
    max-height: 75px;
    max-width: 75px;
    border-radius: 3px 0 0 3px;
    vertical-align: middle;
}

.photoGrid__caption {
    margin: 6px 0;
    /* text-align: center; */
}

.photoGrid__listTitle {
    white-space: nowrap;
    overflow: hidden;
    margin: 0 16px;
    line-height: 1.5;
}
</style>
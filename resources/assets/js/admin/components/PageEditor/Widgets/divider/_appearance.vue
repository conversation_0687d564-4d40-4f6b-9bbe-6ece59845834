<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Appearance
    </div>

    <div class="form-group">
        <label>Height</label>
        <range-input 
            v-model="height" 
            :min="1" 
            :max="64"  
            :steps="1"
        ></range-input>   
    </div>

    <div class="form-group">
        <label>Width</label>
        <select 
            class="form-control" 
            v-model="width" 
        >
            <option value="full">Full Screen</option>
            <option value="half">Half Page</option>
            <option value="page">Full Page</option>
        </select>
    </div>

    <div class="form-group">
        <label>Color</label>
        <input 
            type="text" 
            v-model="bg_color" 
            class="form-control" 
            id="dividerColor"
        >
    </div>
</div>    
</template>

<script>
import RangeInput from '../../_inputs/range-input.vue';
export default {
    props: ['widget'],
    
    components: {
        RangeInput
    },

    created() {
        eventHub.emit('pageEditor:setWidgetElementTitle', 'Appearance')
    },

    mounted: function() {
        let self = this;
        $("#dividerColor").spectrum({
            preferredFormat: "hex",
            showInput: true,
            allowEmpty: true,
            change: function(color) {
                self.bg_color = color? color.toHexString() : 'transparent';
            }
        });
    },

    computed: {
        bg_color: {
            get() {
                return this.widget.settings.bg_color;
            },
            set(newVal) {
                this.widget.settings.bg_color = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        height: {
            get() {
                return this.widget.settings.height
            },
            set(newVal) {
                this.widget.settings.height = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        width: {
            get() {
                return this.widget.settings.width
            },
            set(newVal) {
                this.widget.settings.width = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        }
    }
}
</script>
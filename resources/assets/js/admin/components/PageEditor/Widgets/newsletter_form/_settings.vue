<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Settings
    </div>

    <div class="form-group">
        <label>Available Fields</label>
        <div class="checkbox">
            <label>
                <input 
                    type="checkbox" 
                    v-model="show_first_name" 
                > First Name
            </label>
        </div>
        <div class="checkbox">
            <label>
                <input 
                    type="checkbox" 
                    v-model="show_last_name" 
                > Last Name
            </label>
        </div>
    </div>
</div>    
</template>

<script>
export default {
    props: ['widget'],

    computed: {
        show_first_name: {
            get() {
                return this.widget.settings.show_first_name;
            },
            set(newVal) {
                this.widget.settings.show_first_name = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        show_last_name: {
            get() {
                return this.widget.settings.show_last_name
            },
            set(newVal) {
                this.widget.settings.show_last_name = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
    }
}
</script>
<template>
<div>
	<table class="table table-bordered  table-striped table-settings">
    <tbody>
    <tr>
        <td>
            <h2>Columns</h2>
            <p>How much horizontal space will be taken up.</p>
        </td>
        <td>
            <div class="form-group">
                <select class="form-control" v-model="widget.settings.layout_width">
                    <option value="full-width">Full</option>
                    <option value="three-quarter-width">3/4</option>
                    <option value="half-width">Half</option>
                    <option value="quarter-width">1/4</option>
                </select>   
            </div>  
        </td>
    </tr>
    <tr>
        <td>
            <h2>Limit Visibility</h2>
            <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
        </td>
        <td>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                </label>
            </div>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                </label>
            </div>
        </td>
    </tr>
    </tbody>
    </table>
</div>    
</template>

<script>
	export default {
		props: ['widget', 'create'],

		created: function()
		{
			if(this.create)
            {
                this.widget.title = 'Spacer'
                this.widget.template = 'Spacer'
                this.widget.settings = {
                    'layout_width': 'third-width',
                }
            }
		}
	}
</script>
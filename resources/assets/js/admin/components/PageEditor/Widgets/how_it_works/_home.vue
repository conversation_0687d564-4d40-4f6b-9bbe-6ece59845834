<template>
    <div>
        <ul class="widget__navigation">  
            <li>
                <button 
                    type="button" @click.prevent="event('pageEditor:editWidgetElement', 'step_1')" 
                    class="btn"
                >Step 1</button>
            </li>
            <li>
                <button 
                    type="button" @click.prevent="event('pageEditor:editWidgetElement', 'step_2')" 
                    class="btn"
                >Step 2</button>
            </li>
            <li>
                <button 
                    type="button" @click.prevent="event('pageEditor:editWidgetElement', 'step_3')" 
                    class="btn"
                >Step 3</button>
            </li>
            <li>
                <button 
                    type="button" @click.prevent="event('pageEditor:editWidgetElement', 'appearance')" 
                    class="btn"
                >Appearance</button>
            </li>
            <li>
                <button 
                    type="button" @click.prevent="event('pageEditor:editWidgetElement', 'headingText')" 
                    class="btn"
                >Heading</button>
            </li>
            <li>
                <button 
                    type="button" @click.prevent="event('pageEditor:editWidgetElement', 'buttons')" 
                    class="btn"
                >Buttons</button>
            </li>
            <li>
                <button 
                    type="button" @click.prevent="event('pageEditor:editWidgetElement', 'visibility')" 
                    class="btn"
                >Visibility</button>
            </li>
        </ul>    
    </div>   
</template>

<script>
    export default {
    }
</script>
<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Step 1
    </div>

    <div class="form-group">
        <label>Step 1 Icon</label>
        <icon-select v-model="icon"></icon-select>
    </div>
    
    <div class="form-group">
        <label>Step 1 Title</label>
        <text-input v-model="title"></text-input>
    </div>

    <div class="form-group">
        <label>Step 1 Message</label>
        <plain-text v-model="message"></plain-text>
    </div> 
</div>
</template>

<script>
import IconSelect from '../../_inputs/icon-select.vue';
import TextInput from '../../_inputs/text-input.vue';
import PlainText from '../../_inputs/plain-text.vue';
export default {
    props: ['widget'],

    components: {
        IconSelect,
        TextInput,
        PlainText
    },

    computed: {
        icon: {
            get() {
                return this.widget.settings.step_1_icon;
            },
            set(newVal) {
                this.widget.settings.step_1_icon = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        title: {
            get() {
                return this.widget.settings.step_1_title
            },
            set(newVal) {
                this.widget.settings.step_1_title = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        message: {
            get() {
                return this.widget.settings.step_1_message
            },
            set(newVal) {
                this.widget.settings.step_1_message = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        }
    }
}
</script>
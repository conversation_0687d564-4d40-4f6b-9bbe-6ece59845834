<template>
<div class="widget__innerContainer">
    <slot name="header"></slot>
    <div class="widgetContent">
        <transition 
            name="fade" 
            mode="out-in" 
        >
            <component 
                :key="widget.id" 
                :is="widgetElement"  
                :widget="widget" 
            ></component>
        </transition>    
    </div>    
    <slot name="footer"></slot>
</div>
</template>

<script>
    
    import home from './_home.vue';
    import step_1 from './_step_1.vue';
    import step_2 from './_step_2.vue';
    import step_3 from './_step_3.vue';
    import headingText from './_heading_text.vue';
    import appearance from './_appearance.vue';
    import buttons from './_buttons.vue';
    import visibility from './_visibility.vue';

	export default {

        components: {
            home,
            step_1,
            step_2,
            step_3,
            headingText,
            appearance,
            buttons,
            visibility
        },

        props: ['page','widget','widgetElement']
	}
</script>
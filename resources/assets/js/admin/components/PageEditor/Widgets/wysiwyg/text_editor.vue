<template>
    <div class="richTextEditor__container">
        <div class="richTextEditor__toolbar" id="my-external-toolbar"></div>
        <div
            class="richTextEditor__innerContainer"
            :style="{ backgroundColor: widget.settings.background }"
        >
            <div
                v-html="widget.content"
                id="richTextEditor"
                :style="{
                  backgroundColor: widget.settings.background,
                  color: widget.settings.text_color,
                  maxWidth: widget.settings.width + 'px',
                  lineHeight: widget.settings.line_height,
                  paddingTop: widget.settings.paddingTop + 'px',
                }"
            ></div>
            <media-browser
                ref="mediaBrowser"
                @insertPhoto="insertPhoto"
            ></media-browser>
        </div>
    </div>
</template>

<script>
import MediaBrowser from "../../../MediaBrowser/MediaBrowser.vue";
export default {
    props: ["widget"],

    emits: ['loaded'],

    components: {
        MediaBrowser,
    },

    created() {
        eventHub.on("TextEditor:update", this.update);
    },

    mounted() {
        // Add photo library button
        $R.add("plugin", "photo_manager", {
            init: function(app) {
                this.app = app;

                // define toolbar service
                this.toolbar = app.toolbar;
            },
            start: function() {
                // set up the button
                var buttonData = {
                    title: "Image",
                    api: "plugin.photo_manager.toggle",
                };

                // add the button to the toolbar
                var $button = this.toolbar.addButton("image-button", buttonData);
                $button.setIcon('<i class="far fa-image"></i>');
            },
            toggle: function() {
                window.eventHub.emit("mediaBrowser:toggle");
            },
        });

        $R.add("plugin", "alignment", {
            translations: {
                en: {
                    align: "Align",
                    "align-left": "Align Left",
                    "align-center": "Align Center",
                    "align-right": "Align Right",
                    "align-justify": "Align Justify",
                },
            },
            init: function(app) {
                this.app = app;
                this.lang = app.lang;
                this.block = app.block;
                this.toolbar = app.toolbar;
            },
            // public
            start: function() {
                var dropdown = {};

                dropdown.left = {
                    title: this.lang.get("align-left"),
                    api: "plugin.alignment.set",
                    args: "left",
                };
                dropdown.center = {
                    title: this.lang.get("align-center"),
                    api: "plugin.alignment.set",
                    args: "center",
                };
                dropdown.right = {
                    title: this.lang.get("align-right"),
                    api: "plugin.alignment.set",
                    args: "right",
                };
                dropdown.justify = {
                    title: this.lang.get("align-justify"),
                    api: "plugin.alignment.set",
                    args: "justify",
                };

                var $button = this.toolbar.addButton("alignment", {
                    title: this.lang.get("align"),
                });
                $button.setIcon('<i class="re-icon-alignment"></i>');
                $button.setDropdown(dropdown);
            },
            set: function(type) {
                if (type === "left") {
                    return this._remove();
                }

                var args = {
                    style: { "text-align": type },
                };

                this.block.toggle(args);
            },

            // private
            _remove: function() {
                this.block.remove({ style: "text-align" });
            },
        });

        $R.add("plugin", "video", {
            translations: {
                en: {
                    video: "Video",
                    "video-html-code": "Video Embed Code or Youtube/Vimeo Link",
                },
            },
            modals: {
                video:
                    '<form action=""> \
                                      <div class="form-item"> \
                                          <label for="modal-video-input">## video-html-code ## <span class="req">*</span></label> \
                                          <textarea id="modal-video-input" name="video" style="height: 160px;"></textarea> \
                                      </div> \
                                  </form>',
            },
            init: function(app) {
                this.app = app;
                this.lang = app.lang;
                this.opts = app.opts;
                this.toolbar = app.toolbar;
                this.component = app.component;
                this.insertion = app.insertion;
                this.inspector = app.inspector;
            },
            // messages
            onmodal: {
                video: {
                    opened: function($modal, $form) {
                        $form.getField("video").focus();
                    },
                    insert: function($modal, $form) {
                        var data = $form.getData();
                        this._insert(data);
                    },
                },
            },
            oncontextbar: function(e, contextbar) {
                var data = this.inspector.parse(e.target);
                if (data.isComponentType("video")) {
                    var node = data.getComponent();
                    var buttons = {
                        remove: {
                            title: this.lang.get("delete"),
                            api: "plugin.video.remove",
                            args: node,
                        },
                    };

                    contextbar.set(e, node, buttons, "bottom");
                }
            },

            // public
            start: function() {
                var obj = {
                    title: this.lang.get("video"),
                    api: "plugin.video.open",
                };

                var $button = this.toolbar.addButtonAfter("image", "video", obj);
                $button.setIcon('<i class="re-icon-video"></i>');
            },
            open: function() {
                var options = {
                    title: this.lang.get("video"),
                    width: "600px",
                    name: "video",
                    handle: "insert",
                    commands: {
                        insert: { title: this.lang.get("insert") },
                        cancel: { title: this.lang.get("cancel") },
                    },
                };

                this.app.api("module.modal.build", options);
            },
            remove: function(node) {
                this.component.remove(node);
            },

            // private
            _insert: function(data) {
                this.app.api("module.modal.close");

                if (data.video.trim() === "") {
                    return;
                }

                // parsing
                data.video = this._matchData(data.video);

                // inserting
                if (this._isVideoIframe(data.video)) {
                    var $video = this.component.create("video", data.video);
                    this.insertion.insertHtml($video);
                }
            },

            _isVideoIframe: function(data) {
                return data.match(/<iframe|<video/gi) !== null;
            },
            _matchData: function(data) {
                var iframeStart = '<iframe style="width: 500px; height: 281px;" src="';
                var iframeEnd = '" frameborder="0" allowfullscreen></iframe>';

                if (this._isVideoIframe(data)) {
                    var allowed = ["iframe", "video", "source"];
                    var tags = /<\/?([a-z][a-z0-9]*)\b[^>]*>/gi;

                    data = data.replace(tags, function($0, $1) {
                        return allowed.indexOf($1.toLowerCase()) === -1 ? "" : $0;
                    });
                }

                if (data.match(this.opts.regex.youtube)) {
                    data = data.replace(
                        this.opts.regex.youtube,
                        iframeStart + "//www.youtube.com/embed/$1" + iframeEnd
                    );
                } else if (data.match(this.opts.regex.vimeo)) {
                    data = data.replace(
                        this.opts.regex.vimeo,
                        iframeStart + "//player.vimeo.com/video/$2" + iframeEnd
                    );
                }

                return data;
            },
        });

        $R.add("class", "video.component", {
            mixins: ["dom", "component"],
            init: function(app, el) {
                this.app = app;

                // init
                return el && el.cmnt !== undefined ? el : this._init(el);
            },

            // private
            _init: function(el) {
                if (typeof el !== "undefined") {
                    var $node = $R.dom(el);
                    var $wrapper = $node.closest("figure");
                    if ($wrapper.length !== 0) {
                        this.parse($wrapper);
                    } else {
                        this.parse("<figure>");
                        this.append(el);
                    }
                } else {
                    this.parse("<figure>");
                }

                this._initWrapper();
            },
            _initWrapper: function() {
                this.addClass("redactor-component");
                this.attr({
                    class: "video-container",
                    "data-redactor-type": "video",
                    tabindex: "-1",
                    contenteditable: false,
                });
            },
        });

        $R("#richTextEditor", "destroy");
        this.$nextTick(function() {
            this.redactor = $R("#richTextEditor", this.settings);
            this.$emit("loaded");
        });
    },

    unmounted() {
        $R("#richTextEditor", "destroy");
        eventHub.off("TextEditor:update");
    },

    data() {
        return {
            redactor: null,
            settings: {
                stylesClass: "richTextPreview",
                styles: true,
                buttons: [
                    "format",
                    "bold",
                    "italic",
                    "alignment",
                    "link",
                    "image-button",
                    "lists",
                    "redo",
                    "undo",
                    "html",
                    "video",
                ],
                plugins: [
                    "alignment",
                    "video",
                    "divider",
                    "table",
                    "fullscreen",
                    "photo_manager",
                ],
                formatting: ["p", "h1", "h2", "h3", "h4", "h5", "h6"],
                pastePlainText: true,
                script: false,
                structure: false,
                source: true,
                toolbarExternal: "#my-external-toolbar",
                imageResizable: true,
                imagePosition: true,
                focus: true,
            },
        };
    },

    methods: {
        update() {
            this.widget.content = this.redactor.source.getCode();
            eventHub.emit("pageEditor:closeWidget", this.widget);
        },

        insertPhoto(photo) {
            this.redactor.insertion.insertHtml(
                '<img src="' + photo.path + '" alt="' + photo.title + '">'
            );
        },
    },
};
</script>

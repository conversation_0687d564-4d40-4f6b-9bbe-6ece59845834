<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Appearance
    </div>

    <div class="form-group">
        <label>Padding Top</label>
        <spacing-control v-model="paddingTop"></spacing-control>
    </div>        
    
    <div class="form-group">
        <label>Padding Bottom</label>
        <spacing-control v-model="paddingBottom"></spacing-control>
    </div>

    <div class="form-group">
        <label>Background Color</label>
        <color-picker v-model="background_color" key="background" name="background"></color-picker>
    </div>

    <div class="form-group">
        <label>Text Color</label>
        <color-picker v-model="text_color" key="text_color" name="text_color"></color-picker>
    </div>

    <div class="form-group">
        <label>Link Color</label>
        <color-picker v-model="link_color" key="link_color" name="link_color"></color-picker>
    </div>

    <hr>

    <div class="form-group">
        <label>Width</label>
        <range-input 
            v-model="width" 
            :min="640" 
            :max="1600" 
            :steps="16"
        ></range-input>
        <!-- <div class="flex align-itmes-m">
            <input 
                type="number" 
                min="0" 
                max="1366" 
                class="form-control mr-sm flex-item" 
                v-model="width" 
                style="max-width: 100px;" 
            >
            <input 
                type="range" 
                class="flex-item-fill"  
                min="455" 
                max="1366" 
                v-model="width" 
            >
        </div>     -->
    </div>

    <div class="form-group">
        <label>Columns</label>
        <select 
            class="form-control" 
            v-model="layout_width" 
        >
            <option value="full-width">Full</option>
            <option value="half-width">Half</option>
        </select> 
    </div>  
</div>    
</template>

<script>
import RangeInput from '../../_inputs/range-input.vue';
import ColorPicker from '../../_inputs/color-picker.vue';
import SpacingControl from '../../_inputs/spacing-control.vue';
export default {
    props: ['widget'],
    
    components: {
        RangeInput,
        ColorPicker,
        SpacingControl
    },

    mounted: function() {
        let self = this;
        $("#backgroundColor").spectrum({
            preferredFormat: "hex",
            showInput: true,
            allowEmpty: true,
            change: function(color) {
                self.background_color = color ? color.toHexString() : 'transparent';
            }
        });

        $("#textColor").spectrum({
            preferredFormat: "hex",
            showInput: true,
            allowEmpty: true,
            change: function(color) {
                self.text_color = color ? color.toHexString() : '#3d3d3d';
            }
        });
    },

    computed: {
        paddingTop: {
            get() {
                return this.widget.settings.paddingTop;
            },
            set(newVal) {
                this.widget.settings.paddingTop = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        paddingBottom: {
            get() {
                return this.widget.settings.paddingBottom
            },
            set(newVal) {
                this.widget.settings.paddingBottom = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        background_color: {
            get() {
                return this.widget.settings.background
            },
            set(newVal) {
                this.widget.settings.background = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        text_color: {
            get() {
                return this.widget.settings.text_color
            },
            set(newVal) {
                this.widget.settings.text_color = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        link_color: {
            get() {
                return this.widget.settings.link_color
            },
            set(newVal) {
                this.widget.settings.link_color = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        width: {
            get() {
                return this.widget.settings.width
            },
            set(newVal) {
                this.widget.settings.width = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        layout_width: {
            get() {
                return this.widget.settings.layout_width
            },
            set(newVal) {
                this.widget.settings.layout_width = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        }
    }
}
</script>
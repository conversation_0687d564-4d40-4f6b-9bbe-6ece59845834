<template>
    <div class="widget__container widget__container--full">
        <div class="widget__innerContainer">
            <div class="widgetHeader">
                <div class="widgetHeader__actionsMenu dropdown flex-item">
                    <a
                        aria-expanded="false"
                        aria-haspopup="true"
                        class="btn dropdown-toggle"
                        data-toggle="dropdown"
                        href="#"
                    >
                        <i class="far fa-lg fa-ellipsis-v"></i>
                    </a>
                    <ul class="dropdown-menu">
                        <li>
                            <a
                                class="btn-alt pr-sm pl-sm"
                                href="#"
                                @click.prevent="event('pageEditor:renameWidget', widget)"
                            ><i class="far fa-i-cursor fa-fw"></i> Rename</a
                            >
                        </li>
                        <li>
                            <a
                                class="btn-alt pr-sm pl-sm"
                                href="#"
                                @click.prevent="event('pageEditor:duplicateWidget', widget)"
                            ><i class="far fa-clone fa-fw"></i> Duplicate</a
                            >
                        </li>
                        <li>
                            <a
                                class="btn-alt pr-sm pl-sm"
                                href="#"
                                @click.prevent="event('pageEditor:deleteWidget', widget)"
                            ><i class="far fa-trash fa-fw"></i> Delete</a
                            >
                        </li>
                    </ul>
                </div>
                <div class="widgetHeader__title text-center">
                    <a
                        href="#"
                        @click.prevent="event('pageEditor:editWidgetElement', 'home')"
                    >{{ widget.title }}</a>
                </div>
                <a class="btn" href="#" @click.prevent="event('TextEditor:update')">
                    <i class="fal fa-lg fa-times-circle"></i>
                </a>
            </div>
            <div class="widgetContent">
                <transition mode="out-in" name="fade">
                    <component
                        :is="widgetElement"
                        :key="widget.id"
                        :widget="widget"
                    ></component>
                </transition>
            </div>
            <div class="widgetFooter">
                <button
                    class="btn btn-action btn-block btn-lg"
                    type="button"
                    @click="event('TextEditor:update')"
                >
                    Done
                </button>
            </div>
        </div>
        <div
            class="prose max-w-none prose-headings:text-inherit prose-a:text-inherit prose-p:text-inherit prose-ul:text-inherit prose-li:text-inherit"
            style="height: 100%; background-color: #FFF; z-index: 100; position: relative;"
        >
            <text-editor :widget="widget"></text-editor>
        </div>
    </div>
</template>
<script>
import Home from './_home.vue';
import appearance from './_appearance.vue';
import visibility from './_visibility.vue';
import TextEditor from './text_editor.vue';
import QuillEditor from '../../../QuillEditor.vue';

export default {
    props: ['page', 'widget', 'widgetElement'],

    components: {
        Home,
        TextEditor,
        QuillEditor,
        appearance,
        visibility
    },

    created() {
        this.newContent = this.widget.content;

        eventHub.on(
            'TextEditor:update',
            () => {
                eventHub.emit('pageEditor:closeWidget', this.widget);
            }
        );
    },

    beforeUnmount() {
        eventHub.off('TextEditor:update');
    },

    data() {
        return {
            newContent: ''
        };
    },

    methods: {
        updateContent: function(content) {
            this.widget.content = content;
        }
    }
};
</script>

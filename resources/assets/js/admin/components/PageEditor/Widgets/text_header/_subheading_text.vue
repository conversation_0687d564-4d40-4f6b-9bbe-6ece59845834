<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Text
    </div>

    <div class="form-group">
        <label>Heading Text</label>
        <textarea 
            :value="heading_text"
            class="form-control height--200"  
            @change="heading_text = $event.target.value" 
            ref="heading_input" 
        ></textarea>
    </div>
    <hr>
    <div class="form-group">
        <label>Subheading Text</label>
        <textarea 
            :value="subheading_text"
            class="form-control height--200"  
            @change="subheading_text = $event.target.value" 
            ref="subheading_input" 
            autofocus
        ></textarea>
    </div>
</div>    
</template>

<script>
export default {
    props: ['widget'],
    
    mounted() {
        this.$refs.subheading_input.focus();
    },

    computed: {
        heading_text: {
            get() {
                return this.widget.settings.header;
            },
            set(newVal) {
                this.widget.settings.header = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
         subheading_text: {
            get() {
                return this.widget.settings.subheader;
            },
            set(newVal) {
                this.widget.settings.subheader = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        }
    }
}
</script>
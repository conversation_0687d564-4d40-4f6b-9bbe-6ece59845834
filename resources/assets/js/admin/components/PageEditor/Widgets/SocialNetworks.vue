<template>
<div>
    <ul class="tabsList">
      <li :class="{'active': activeTab == 'appearance'}" @click="activeTab = 'appearance'">
        <a href="#">Appearance</a>
    </li>
    <li :class="{'active': activeTab == 'links'}" @click="activeTab = 'links'">
        <a href="#">Networks</a>
    </li>
    </ul>
    <br>
    <div v-show="activeTab == 'links'" class="linkListWidget__linkTab">
        <div class="pa-sm" v-if="activeLink">
            <div class="mb-md">
                <button class="btn btn-sm btn-action" @click.prevent="editLink(activeLink)">Save</button>
            </div>
            <div class="form-group">
                <label>Type</label>
                <select class="form-control" v-model="activeLink.data">
                    <option :value="network" v-for="network in networks">{{ network.name }}</option>
                </select>
            </div>
            <div class="form-group">
                <label>URL of your {{ activeLink.data.name }} page</label>
                <input type="text" v-model="activeLink.url" class="form-control">
            </div>
        </div>
        <div v-else>
            <div class="form-group">
                <button class="btn btn-light btn-sm" @click="addLink()">Add</button>
            </div>
            <ul class="draggable-list" id="linkWidgetList" v-sortable="options">
                <li 
                    v-for="link in widget.settings.links" 
                    class="truncate sortItem"  
                    :key="itemIndex()"
                >
                    <div class="flex align-items-m">
                        <div>
                            <span class="fa fa-bars draghandle" title="Drag to reorder"></span>
                            <span>{{ link.data.name }}</span>
                        </div>
                        <div class="flex-item push-right">
                            <button class="btn btn-sm btn-alt" @click.prevent="editLink(link)">Edit</button>
                            <button class="btn btn-sm btn-alt" @click.prevent="removeLink(link)">Delete</button>
                        </div>    
                    </div>
                </li>
            </ul>
        </div>    
    </div>
    <div v-show="activeTab == 'appearance'">
    	<table class="table table-bordered  table-striped table-settings">
        <tbody>
        <tr>
            <td>
                <h2>Heading</h2>
                <p>Optional heading that will appear above link list.</p>
            </td>
            <td>
                <div class="form-group">
                    <input type="text" v-model="widget.settings.heading" class="form-control">
                </div>  
            </td>
        </tr>
        <tr>
            <td>
                <h2>Orientation</h2>
            </td>
            <td>
                <div class="form-group">
                    <select class="form-control" v-model="widget.settings.orientation">
                        <option value="vertical">Vertical</option>
                        <option value="horizontal">Horizontal</option>
                    </select>   
                </div>  
            </td>
        </tr>
        <tr>
            <td>
                <h2>Limit Visibility</h2>
                <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
            </td>
            <td>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                    </label>
                </div>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                    </label>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <h2>Columns</h2>
                <p>How much horizontal space this block will take up. For example a 2 column layout would be 2 "Half" blocks.</p>
            </td>
            <td>
                <div class="form-group">
                    <select class="form-control" v-model="widget.settings.layout_width">
                        <option value="full-width">Full</option>
                        <option value="three-quarter-width">3/4</option>
                        <option value="half-width">Half</option>
                        <option value="quarter-width">1/4</option>
                    </select>   
                </div>  
            </td>
        </tr>
        </tbody>
        </table>
    </div>     
</div>    
</template>

<script>
	export default {
		props: ['widget', 'create'],

		created: function()
		{
			if(this.create)
            {
                this.widget.title = 'Social Networks'
                this.widget.template = 'SocialNetworks'
                this.widget.settings = {
                	'orientation': 'vertical',
                    'layout_width': 'third-width',
                    'links': [
                        {
                        	'data': {'name': 'Facebook', 'icon': 'fa-facebook'},
                            'edit': true,
                            'title': this.linkLabel, 
                            'url': this.linkUrl,
                        }
                    ],
                }
            }
		},

        data: function()
        {
            return {
                linkLabel: 'Link',
                linkUrl: '/',
                links: [],
                activeTab: 'appearance',
                activeLink: '',
                networks: [
                	{'name': 'Facebook', 'icon': 'fa-facebook'},
                	{'name': 'Twitter', 'icon': 'fa-twitter'},
                	{'name': 'Instagram', 'icon': 'fa-instagram'},
                	{'name': 'YouTube', 'icon': 'fa-youtube-square'},
                	{'name': 'Google+', 'icon': 'fa-google-plus-square'},
                	{'name': 'Pinterest', 'icon': 'fa-pinterest'},
                	{'name': 'LinkedIn', 'icon': 'fa-linkedin-square'},
                	{'name': 'Yelp', 'icon': 'fa-yelp'},
                	{'name': 'Flickr', 'icon': 'fa-flickr'},
                	{'name': 'Tumblr', 'icon': 'fa-tumblr-square'},
                ],
                options: { 
                    onUpdate: this.updateSort, 
                    handle: '.draghandle',
                    animation: 150,
                    group: {
                        name:'segments',
                        pull: false,
                        put: true
                    } 
                }
            }
        },

        methods: {
            itemIndex: function(item) {
                return Math.floor((Math.random() * 10000) + 1);
            },

            addLink: function()
            {
                this.widget.settings.links.push({
                    'edit': true,
                    'data': {'name': 'Facebook', 'icon': 'fa-facebook'}, 
                    'url': this.linkUrl,
                })
            },

            editLink: function(link) {
                if(this.activeLink === link)
                {
                    this.activeLink = null;
                }
                else
                {
                    this.activeLink = link;
                }
            },

            removeLink: function(link)
            {
                if(this.widget.settings.links.length > 1)
                {
                    var index = this.widget.settings.links.indexOf(link); 
                    this.widget.settings.links.splice(index, 1)
                }
            },

            updateSort: function(event)
            {
                this.widget.settings.links.splice(event.newIndex, 0, this.widget.settings.links.splice(event.oldIndex, 1)[0]);
            }
        }
	}
</script>
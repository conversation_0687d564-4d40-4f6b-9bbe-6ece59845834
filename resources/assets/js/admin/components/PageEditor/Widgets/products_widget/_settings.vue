<template>
    <div>
        <div class="widget__breadcrumb">
            <a
                href="#"
                @click.prevent="event('pageEditor:editWidgetElement', 'home')"
            ><i class="fa fa-chevron-left"></i></a> Settings
        </div>

        <div class="form-group">
            <label>Product Source</label>
            <select
                v-model="source"
                class="form-control"
            >
                <option :value="null" disabled>Choose a product collection</option>
                <option
                    v-for="collection in collections"
                    :value="collection.id"
                    :key="collection.id"
                >{{ collection.title }}</option>
            </select>
        </div>

        <div class="form-group">
            <label>Product Count</label>
            <text-input v-model="count"></text-input>
        </div>

        <hr>

        <div class="form-group">
            <label>Layout Style</label>
            <select
                class="form-control"
                v-model="layout_style"
            >
                <option value="style1">Default</option>
                <option value="style2">Comparison</option>
                <!-- <option value="style3">Choose One</option> -->
                <option value="style4">Choose Variation</option>
            </select>
        </div>

        <div v-show="layout_style == 'style2' || layout_style == 'style4'">
            <div class="form-group">
                <label>Action Button</label>
                <select
                    class="form-control"
                    v-model="action_event"
                >
                    <option value="show_details">Show Details Page</option>
                    <option value="add_to_cart">Add to Cart</option>
                </select>
            </div>

            <div class="form-group">
                <label>Action Text</label>
                <text-input v-model="cta_text"></text-input>
            </div>
        </div>

        <div class="checkbox">
            <label>
                <input
                    type="checkbox"
                    v-model="hide_price"
                > Only show price when logged in
            </label>
        </div>

        <div class="checkbox">
            <label>
                <input
                    type="checkbox"
                    v-model="show_hidden"
                > Show Hidden Products
            </label>
        </div>
    </div>
</template>

<script>
import TextInput from '../../_inputs/text-input.vue';
export default {
    props: ['widget'],

    components: {
        TextInput
    },

    created() {
        this.getCollections()
    },

    data: () => ({
        collections: []
    }),

    methods: {
        getCollections() {
            axios.get('/api/collections')
                .then(({ data }) => {
                    this.collections = data;
                })
                .catch(error => {  });
        }
    },

    computed: {
        source: {
            get() {
                return this.widget.settings.collection;
            },
            set(newVal) {
                this.widget.settings.collection = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        count: {
            get() {
                return this.widget.settings.count
            },
            set(newVal) {
                this.widget.settings.count = newVal && newVal > 0 ? newVal : 1;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        layout_style: {
            get() {
                return this.widget.settings.layout_style
            },
            set(newVal) {
                this.widget.settings.layout_style = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        action_event: {
            get() {
                return this.widget.settings.action_event
            },
            set(newVal) {
                this.widget.settings.action_event = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        cta_text: {
            get() {
                return this.widget.settings.cta_text
            },
            set(newVal) {
                this.widget.settings.cta_text = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        hide_price: {
            get() {
                return this.widget.settings.hide_price
            },
            set(newVal) {
                this.widget.settings.hide_price = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        show_hidden: {
            get() {
                return this.widget.settings.show_hidden
            },
            set(newVal) {
                this.widget.settings.show_hidden = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        }
    }
}
</script>

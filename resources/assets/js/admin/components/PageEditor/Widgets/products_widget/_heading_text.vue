<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Header
    </div>

    <label>Show Header</label>
    <div class="radio form-group">
        <label class="radio-inline mr-sm">
            <input 
                type="radio" :value="true" 
                v-model="header_show" 
            > Show
        </label>
        <label class="radio-inline">
            <input 
                type="radio" 
                :value="false" 
                v-model="header_show" 
            > Hide
      </label>
    </div>

    <div class="form-group" v-show="header_show">
        <label>Heading Text</label>
        <plain-text v-model="header_text"></plain-text>
    </div>

</div>    
</template>

<script>
import PlainText from '../../_inputs/plain-text.vue';
export default {
    props: ['widget'],
    
    components: {
        PlainText,
    },

    mounted: function()
    {   
        let self = this;
        $("#headingTextColor").spectrum({
            preferredFormat: "hex",
            showInput: true,
            allowEmpty: true,
            change: function(color) {
                self.heading_text_color = color ? color.toHexString() : '#FFF';
            }
        });
    },

    computed: {
        header_text: {
            get() {
                return this.widget.settings.header;
            },
            set(newVal) {
                this.widget.settings.header = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        header_show: {
            get() {
                return this.widget.settings.header_show
            },
            set(newVal) {
                this.widget.settings.header_show = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        }
    }
}
</script>
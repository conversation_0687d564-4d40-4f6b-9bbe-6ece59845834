<template>
<div class="widget__container">
    <div class="widget__innerContainer">
        <slot name="header" :backLink="widgetElement === 'PhotoEditor' ? 'photos' : 'home'"></slot>
        <div class="widgetContent">
            <transition 
                name="fade" 
                mode="out-in" 
            >
                <component 
                    @edit="editPhoto" 
                    @update="updatePhoto" 
                    @remove="removePhoto" 
                    :photo="activePhoto" 
                    :key="widgetId" 
                    :is="widgetElement"  
                    :widget="widget" 
                    :widgetItem="widgetItem"
                ></component>
            </transition>    
        </div>

        <!-- Photo Editor Footer -->
        <div v-if="widgetElement === 'PhotoEditor'" class="widgetFooter">
            <button 
                type="button" 
                class="btn btn-link btn-sm" 
                style="margin-right: auto" 
                @click="removePhoto(activePhoto)" 
            >Delete</button>
            <button 
                type="button" 
                class="btn btn-action btn-lg" 
                @click="savePhoto(activePhoto)" 
            >Save Changes</button>
        </div>
        <!-- End Photo Editor Footer -->

        <slot name="footer" v-else></slot>
    </div>
</div>    
</template>

<script>
    import home from './_home.vue';
    import headingText from './_heading_text.vue';
    import appearance from './_appearance.vue';
    import visibility from './_visibility.vue';
    import Photos from './_photos.vue';
    import PhotoEditor from './_photo_editor.vue';
	export default {
        props: ['page','widget','widgetElement','widgetItem'],

		 components: {
            home,
            headingText,
            appearance,
            visibility,
            Photos,
            PhotoEditor
        },

        watch: {
            widgetItem(val) {
                if(this.widgetItem)
                {
                    this.editPhoto(this.widget.settings.items[this.widgetItem])
                }

            }
        },

        created() {
            if(this.widgetItem)
            {
                this.editPhoto(this.widget.settings.items[this.widgetItem])
            }
        },

        data: function()
        {
            return {
                linkLabel: 'Beef',
                linkUrl: '/',
                items: [],
                activePhoto: null,
                placeholder: 'https://s3.amazonaws.com/grazecart/stockphotos/photo-grid-placeholder.jpg',
                options: {
                    onUpdate: this.updateSort,
                    handle: '.draghandle',
                    dragClass: "sortable-drag",
                    animation: 150,
                    group: {
                        name:'page_structure',
                        pull: false,
                        put: false
                    }
                }
            }
        },

        methods: {
            editPhoto: function(photo)
            {
                this.activePhoto = photo;
                eventHub.emit('pageEditor:editWidgetElement', 'PhotoEditor')
            },

            removePhoto: function(photo)
            {
                if(this.widget.settings.items.length > 1)
                {
                    this.activePhoto = null
                    var index = this.widget.settings.items.indexOf(photo); 
                    this.widget.settings.items.splice(index, 1)
                    eventHub.emit('pageEditor:updateWidget', this.widget);
                    eventHub.emit('pageEditor:editWidgetElement', 'photos')
                }
            },

            updatePhoto: function(photo) {
                this.activePhoto.src = photo.path;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            },

            savePhoto: function(photo) {
                eventHub.emit('pageEditor:updateWidget', this.widget)
                eventHub.emit('pageEditor:editWidgetElement', 'photos')
            }
        },

        computed: {
            widgetId() {
                return this.widgetItem ? this.widget.id+'_'+this.widgetItem : this.widget.id
            },
            itemId: function() {
                if(this.widget.settings.items) {
                    return this.widget.settings.items.length;
                }
                else {
                    return 0;
                }
            },
        }
	}
</script>
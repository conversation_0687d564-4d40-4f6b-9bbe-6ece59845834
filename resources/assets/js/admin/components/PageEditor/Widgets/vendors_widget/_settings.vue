<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Settings
    </div>

    <div>
        <label>Sort Order</label>
        <select 
            v-model="sort" 
            class="form-control" 
            @change="$emit('update', block)" 
        >
            <option value="asc">A-Z</option>
            <option value="desc">Z-A</option>
        </select>
    </div>
</div>    
</template>

<script>
export default {
    props: ['widget'],

    emits: ['update'],

    computed: {
        sort: {
            get() {
                return this.widget.settings.sort;
            },
            set(newVal) {
                this.widget.settings.sort = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
    },
}
</script>
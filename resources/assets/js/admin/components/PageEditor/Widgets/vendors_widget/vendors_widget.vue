<template>
<div class="widget__innerContainer">
    <slot name="header"></slot>
    <div class="widgetContent">
        <transition 
            name="fade" 
            mode="out-in" 
        >
            <component 
                :key="widget.id" 
                :is="widgetElement"  
                :widget="widget" 
            ></component>
        </transition>    
    </div>    
    <slot name="footer"></slot>
</div>
</template>

<script>
    
    import home from './_home.vue';
    import headingText from './_heading_text.vue';
    import appearance from './_appearance.vue';
    import settings from './_settings.vue';
    import visibility from './_visibility.vue';

	export default {

        components: {
            home,
            headingText,
            appearance,
            settings,
            visibility
        },

        props: ['page','widget','widgetElement']
	}
</script>
<template>
    <div>
        <ul class="widget__navigation">  
            <li>
                <button 
                    type="button" @click.prevent="event('pageEditor:editWidgetElement', 'image_settings')" 
                    class="btn"
                >Image</button>
            </li>
            <li>
                <button 
                    type="button" @click.prevent="event('pageEditor:editWidgetElement', 'appearance')" 
                    class="btn"
                >Appearance</button>
            </li>
            <li>
                <button 
                    type="button" @click.prevent="event('pageEditor:editWidgetElement', 'visibility')" 
                    class="btn"
                >Visibility</button>
            </li>
        </ul>    
    </div>   
</template>

<script>
    export default {
    }
</script>
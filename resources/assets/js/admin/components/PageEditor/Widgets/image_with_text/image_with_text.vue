<template>
<div class="widget__innerContainer">
    <slot name="header"></slot>
    <div class="widgetContent">
        <transition 
            name="fade" 
            mode="out-in" 
        >
            <component 
                :key="widget.id" 
                :is="widgetElement"  
                :widget="widget" 
            ></component>
        </transition>    
    </div>    
    <slot name="footer"></slot>
</div>
</template>

<script>
    
    // import home from './_home.vue';
    import home from './_appearance.vue';

	export default {

        components: {
            home,
        },

        props: ['page','widget','widgetElement']
	}
</script>
<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Buttons
    </div>

    <div class="form-group">
        <label>Button 1</label>
        <div class="radio form-group">
            <label class="radio-inline mr-sm">
                <input 
                    type="radio" :value="true" 
                    v-model="button_1_show" 
                > Show
            </label>
            <label class="radio-inline">
                <input 
                    type="radio" 
                    :value="false" 
                    v-model="button_1_show" 
                > Hide
          </label>
        </div>

        <div v-show="button_1_show">
            <div class="form-group">
                <label>Button Text</label>
                <text-input 
                    v-model="button_1_text" 
                    placeholder="Button text"
                ></text-input>
            </div>
            <div class="form-group">
                <label>Button URL</label>
                <text-input 
                    v-model="button_1_url" 
                    placeholder="Button URL"
                ></text-input>
            </div>
            <div class="form-group checkbox">
                <label>
                    <input 
                        type="checkbox" 
                        v-model="button_1_target" 
                    > Open in new window
                </label>
            </div>
            <div class="form-group">
                <label for="button_1_style'">Button Style</label>
                <button-style v-model="button_1_style"></button-style>
            </div> 
        </div>  
    </div>
    <hr>
    <div class="form-group">
        <label>Button 2</label>
        <div class="radio form-group">
            <label class="radio-inline mr-sm">
                <input 
                    type="radio" 
                    :value="true" 
                    v-model="button_2_show" 
                > Show
            </label>
            <label class="radio-inline">
                <input 
                    type="radio" 
                    :value="false" 
                    v-model="button_2_show" 
                > Hide
          </label>
        </div>
        <div v-show="button_2_show">
            <div class="form-group">
                <label>Button Text</label>
                <text-input 
                    v-model="button_2_text" 
                    placeholder="Button text"
                ></text-input>
            </div>
            <div class="form-group">
                <label>Button URL</label>
                <text-input 
                    v-model="button_2_url" 
                    placeholder="Button text"
                ></text-input>
            </div>
            <div class="form-group checkbox">
                <label>
                    <input 
                        type="checkbox" 
                        v-model="button_2_target" 
                    > Open in new window
                </label>
            </div>
            <div class="form-group">
                <label for="button_2_style'">Button Style</label>
                <button-style v-model="button_2_style"></button-style>
            </div> 
        </div> 
    </div>
</div>    
</template>

<script>
import TextInput from '../../_inputs/text-input.vue';
import ButtonStyle from '../../_inputs/button-style.vue';
// import ButtonAction from '../../_inputs/button-action.vue';
export default {
    props: ['widget'],
    
    components: {
        TextInput,
        ButtonStyle,
        // ButtonAction
    },

    computed: {
        button_1_show: {
            get() {
                return this.widget.settings.button_1_show;
            },
            set(newVal) {
                this.widget.settings.button_1_show = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        button_1_text: {
            get() {
                return this.widget.settings.title
            },
            set(newVal) {
                this.widget.settings.title = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        button_1_url: {
            get() {
                return this.widget.settings.url
            },
            set(newVal) {
                this.widget.settings.url = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        button_1_style: {
            get() {
                return this.widget.settings.button_1_style
            },
            set(newVal) {
                this.widget.settings.button_1_style = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        button_1_target: {
            get() {
                return this.widget.settings.button_1_target
            },
            set(newVal) {
                this.widget.settings.button_1_target = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },

        button_2_show: {
            get() {
                return this.widget.settings.button_2_show;
            },
            set(newVal) {
                this.widget.settings.button_2_show = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        button_2_text: {
            get() {
                return this.widget.settings.button_2_text
            },
            set(newVal) {
                this.widget.settings.button_2_text = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        button_2_url: {
            get() {
                return this.widget.settings.button_2_url
            },
            set(newVal) {
                this.widget.settings.button_2_url = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        button_2_target: {
            get() {
                return this.widget.settings.button_2_target
            },
            set(newVal) {
                this.widget.settings.button_2_target = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        button_2_style: {
            get() {
                return this.widget.settings.button_2_style
            },
            set(newVal) {
                this.widget.settings.button_2_style = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
    }
}
</script>
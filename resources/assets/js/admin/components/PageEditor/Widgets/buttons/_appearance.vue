<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Appearance
    </div>

    <div class="form-group">
        <label>Padding Top</label>
        <spacing-control v-model="paddingTop"></spacing-control>
    </div>        
    
    <div class="form-group">
        <label>Padding Bottom</label>
        <spacing-control v-model="paddingBottom"></spacing-control>
    </div>

    <div class="form-group">
        <label>Container Background Color</label>
        <input 
            type="text" 
            v-model="background" 
            class="form-control" 
            id="backgroundColor" 
        >
    </div> 
</div>    
</template>

<script>
import SpacingControl from '../../_inputs/spacing-control.vue';
export default {
    props: ['widget'],
    
    components: {
        SpacingControl
    },

    created() {
        eventHub.emit('pageEditor:setWidgetElementTitle', 'Appearance')
    },

    mounted: function() {
        let self = this;
        $("#backgroundColor").spectrum({
            preferredFormat: "hex",
            showInput: true,
            allowEmpty: true,
            change: function(color) {
                self.background = color? color.toHexString() : 'transparent';
            }
        });
    },

    computed: {
        paddingTop: {
            get() {
                return this.widget.settings.paddingTop;
            },
            set(newVal) {
                this.widget.settings.paddingTop = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        paddingBottom: {
            get() {
                return this.widget.settings.paddingBottom
            },
            set(newVal) {
                this.widget.settings.paddingBottom = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        alignment: {
            get() {
                return this.widget.settings.alignment
            },
            set(newVal) {
                this.widget.settings.alignment = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        background: {
            get() {
                return this.widget.settings.background
            },
            set(newVal) {
                this.widget.settings.background = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        }
    }
}
</script>
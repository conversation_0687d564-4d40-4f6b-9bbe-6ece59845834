<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Visibility
    </div>

    <div class="checkbox">
        <label>
            <input 
                type="checkbox" 
                v-model="show_when_auth" 
            > Only show when signed in
        </label>
    </div>
    <div class="checkbox">
        <label>
            <input 
                type="checkbox" 
                v-model="show_when_guest" 
            >Only show when <strong>not</strong> signed in
        </label>
    </div>
</div>
</template>

<script>
export default {
    props: ['widget'],

    computed: {
        show_when_auth: {
            get() {
                return this.widget.settings.show_when_auth;
            },
            set(newVal) {
                this.widget.settings.show_when_auth = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        show_when_guest: {
            get() {
                return this.widget.settings.show_when_guest
            },
            set(newVal) {
                this.widget.settings.show_when_guest = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        }
    }
}
</script>
<template>
<div>
    <!-- <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Appearance
    </div> -->

    <div class="form-group">
        <cover-photo 
            :src="image_src"
            @input="updatePhoto"
        ></cover-photo>
    </div>

    <div class="form-group">
        <label>Alt Text</label>
        <text-input v-model="alt"></text-input>
    </div>

    <div class="form-group">
        <label>Image Size</label>
        <input 
            type="range" 
            :min="50" 
            :value="image_width" 
            :max="original_image_width" 
            step="1" 
            @change="image_width = $event.target.value" 
            ref="range"
        >
    </div>

    <hr>
    <label>Show Caption</label>
    <div class="radio form-group">
        <label class="radio-inline mr-sm">
            <input 
                type="radio" :value="true" 
                v-model="caption_show" 
            > Show
        </label>
        <label class="radio-inline">
            <input 
                type="radio" 
                :value="false" 
                v-model="caption_show" 
            > Hide
      </label>
    </div>

    <div class="form-group" v-if="caption_show">
        <label>Caption Text</label>
        <text-input v-model="caption_text"></text-input>
    </div>

    <hr>
    <div class="form-group">
        <label>Click Action</label>
        <select class="form-control" v-model="click_action">
            <option :value="null">None</option>
            <option value="custom_url">Custom URL</option>
            <!-- <option value="lightbox">Lightbox</option> -->
        </select>
    </div>    

    <div class="form-group" v-if="click_action === 'custom_url'">
        <label>Custom URL</label>
        <text-input v-model="action_url" placeholder="https://someurl.com"></text-input>
    </div>
    <hr>

    <div class="form-group">
        <label>Padding Top</label>
        <spacing-control v-model="paddingTop"></spacing-control>
    </div>        
    
    <div class="form-group">
        <label>Padding Bottom</label>
        <spacing-control v-model="paddingBottom"></spacing-control>
    </div>

    <hr>

    <div class="checkbox">
        <label>
            <input 
                type="checkbox" 
                v-model="show_when_auth" 
            > Only show when signed in
        </label>
    </div>
    <div class="checkbox">
        <label>
            <input 
                type="checkbox" 
                v-model="show_when_guest" 
            >Only show when <strong>not</strong> signed in
        </label>
    </div>
</div>    
</template>

<script>
import CoverPhoto from '../../../MediaManager/CoverPhoto.vue';
import TextInput from '../../_inputs/text-input.vue';
import Alignment from '../../_inputs/alignment.vue';
import ColorPicker from '../../_inputs/color-picker.vue';
import SpacingControl from '../../_inputs/spacing-control.vue';
export default {
    props: ['widget'],
    
    components: {
        CoverPhoto,
        TextInput,
        Alignment,
        ColorPicker,
        SpacingControl
    },

    created() {
        eventHub.emit('pageEditor:setWidgetElementTitle', 'Appearance')
    },

    computed: {
        image_src: {
            get() {
                return this.$s3ToCloudfront(this.widget.settings.image_src);
            },
            set(newVal) {
                this.widget.settings.image_src = newVal;
            }
        },
        alt: {
            get() {
                return this.widget.settings.alt;
            },
            set(newVal) {
                this.widget.settings.alt = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        paddingTop: {
            get() {
                return this.widget.settings.paddingTop;
            },
            set(newVal) {
                this.widget.settings.paddingTop = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        paddingBottom: {
            get() {
                return this.widget.settings.paddingBottom
            },
            set(newVal) {
                this.widget.settings.paddingBottom = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        align_image: {
            get() {
                return this.widget.settings.align_image
            },
            set(newVal) {
                this.widget.settings.align_image = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        image_height: {
            get() {
                return this.widget.settings.image_height
            },
            set(newVal) {
                this.widget.settings.image_height = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        image_width: {
            get() {
                return this.widget.settings.image_width
            },
            set(newVal) {
                this.widget.settings.image_width = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        original_image_height: {
            get() {
                return this.widget.settings.original_image_height
            },
            set(newVal) {
                this.widget.settings.original_image_height = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        original_image_width: {
            get() {
                return this.widget.settings.original_image_width
            },
            set(newVal) {
                this.widget.settings.original_image_width = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
         show_when_auth: {
            get() {
                return this.widget.settings.show_when_auth;
            },
            set(newVal) {
                this.widget.settings.show_when_auth = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        show_when_guest: {
            get() {
                return this.widget.settings.show_when_guest
            },
            set(newVal) {
                this.widget.settings.show_when_guest = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        caption_show: {
            get() {
                return this.widget.settings.caption_show
            },
            set(newVal) {
                this.widget.settings.caption_show = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        caption_text: {
            get() {
                return this.widget.settings.caption_text
            },
            set(newVal) {
                this.widget.settings.caption_text = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        click_action: {
            get() {
                return this.widget.settings.click_action
            },
            set(newVal) {
                this.widget.settings.click_action = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        action_url: {
            get() {
                return this.widget.settings.action_url
            },
            set(newVal) {
                this.widget.settings.action_url = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
    },

    methods: {
            updatePhoto: function(photo) {
                this.widget.settings.image_src = photo.path;
                this.widget.settings.image_height = photo.height;
                this.widget.settings.image_width = photo.width;
                this.widget.settings.original_image_height = photo.height;
                this.widget.settings.original_image_width = photo.width;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            },
        }
}
</script>

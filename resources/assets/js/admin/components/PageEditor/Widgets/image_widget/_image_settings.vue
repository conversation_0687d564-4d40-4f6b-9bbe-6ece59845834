<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Image Settings
    </div>

    <div class="form-group">
        <cover-photo 
            :src="image_src" 
            @input="updatePhoto"
        ></cover-photo>
    </div>

    <div class="form-group">
        <label>Alt Text</label>
        <text-input v-model="alt"></text-input>
    </div>
    
</div>    
</template>

<script>
import CoverPhoto from '../../../MediaManager/CoverPhoto.vue';
import TextInput from '../../_inputs/text-input.vue';
export default {
    props: ['widget'],
    
    components: {
        CoverPhoto,
        TextInput
    },

    computed: {
        image_src: {
            get() {
                return this.widget.settings.image_src;
            },
            set(newVal) {
                this.widget.settings.image_src = newVal;
            }
        },
        alt: {
            get() {
                return this.widget.settings.alt;
            },
            set(newVal) {
                this.widget.settings.alt = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
    },

    methods: {
            updatePhoto: function(photo) {
                this.widget.settings.image_src = photo.path;
                this.widget.settings.image_height = photo.height;
                this.widget.settings.image_width = photo.width;
                this.widget.settings.original_image_height = photo.height;
                this.widget.settings.original_image_width = photo.width;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            },
        }
}
</script>
<template>
<div class="photoEditor">
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'photos')" 
        ><i class="fa fa-chevron-left"></i></a> Edit Photo
    </div>

    <div class="form-group">
        <div class="photoPreview">
            <cover-photo 
                :src="src" 
                @input="updatePhoto"
            ></cover-photo>
        </div>
    </div>

    <div class="form-group">
        <label>Main Caption</label>
        <text-input v-model="caption"></text-input>
    </div>

    <div class="form-group">
        <label>Subcaption</label>
        <text-input v-model="subcaption"></text-input>
    </div>

    <div class="form-group">
        <label>URL</label>
        <text-input v-model="url"></text-input>
    </div>
</div>                            
</template>

<script>
import TextInput from '../../_inputs/text-input.vue';
import CoverPhoto from '../../../MediaManager/CoverPhoto.vue';
export default {
    props: ['widget', 'photo', 'widgetItem'],

    emits: ['updatePhoto'],

    components: {
        CoverPhoto,
        TextInput
    },

    methods: {
        updatePhoto(photo) {
            this.$emit('updatePhoto', photo);
        }
    },

    computed: {
        src: {
            get() {
                return this.photo.src;
            },
            set(newVal) {
                this.photo.src = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        caption: {
            get() {
                return this.photo.caption;
            },
            set(newVal) {
                this.photo.caption = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        subcaption: {
            get() {
                return this.photo.subcaption;
            },
            set(newVal) {
                this.photo.subcaption = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        url: {
            get() {
                return this.photo.url;
            },
            set(newVal) {
                this.photo.url = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
    }
}
</script>    

                                
<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Settings
    </div>

    <div class="form-group">
        <label>Post Source</label>
        <select 
            v-model="source" 
            class="form-control" 
        >
            <option value="most_recent">Show most recently published</option>
            <option v-for="tag in tags" :value="tag.id" :key="tag.id">{{ tag.title }}</option>
        </select>
    </div>
    
    <div class="form-group">
        <label>Post Count</label>
        <text-input v-model="count"></text-input>
    </div> 
</div>    
</template>

<script>
import TextInput from '../../_inputs/text-input.vue';
export default {
    props: ['widget'],

    components: {
        TextInput
    },
    
    created: function()
	{
        this.getTags()
	},

    computed: {
        source: {
            get() {
                return this.widget.settings.source;
            },
            set(newVal) {
                this.widget.settings.source = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        count: {
            get() {
                return this.widget.settings.count
            },
            set(newVal) {
                this.widget.settings.count = newVal && newVal > 0 ? newVal : 1;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        }
    },
    data: function()
    {
        return {
            'tags': []
        }
    },

    methods: {
        'getTags': function()
        {
            axios.get('/api/tags/post').then(function(response) {
                this.tags = response.data;
            }.bind(this)).catch(function(error) {});
        }
    }
}
</script>
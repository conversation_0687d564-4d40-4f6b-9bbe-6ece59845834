<template>
<div>
    <ul class="tabsList">
        <li :class="{'active': activeTab == 'links'}" @click="activeTab = 'links'">
            <a href="#">Links</a>
        </li>
        <li :class="{'active': activeTab == 'appearance'}" @click="activeTab = 'appearance'">
            <a href="#">Appearance</a>
        </li>
    </ul>
    <div v-show="activeTab == 'links'">
        <transition name="fade" mode="in-out">
            <div class="mt-md" v-if="activeLink">
                <div class="mb-md">
                    <button class="btn btn-sm btn-action" @click.prevent="editLink(link)">Save</button>
                </div>
                <div class="flex flex-wrap align-items-m">
                    <div class="form-group flex-item-fill mr-sm">
                        <label>Label</label>
                        <input type="text" v-model="activeLink.title" class="form-control">
                    </div>
                    <div class="form-group flex-item-fill">
                        <label>URL</label>
                        <input type="text" v-model="activeLink.url" class="form-control">
                    </div>
                </div>
            </div> 
            <div v-else>
                <div class="form-group mt-md">
                    <button class="btn btn-sm btn-light" @click="addLink()">Add Link</button>
                </div>
                <ul class="draggable-list" id="linkWidgetList" v-sortable="options">
                    <li 
                        v-for="link in widget.settings.links" 
                        class="sortItem truncate" 
                        :key="itemIndex()"
                    >
                        <div class="flex align-items-m">
                            <div class="flex-item">
                                <i class="fa draghandle" title="Drag to reorder"></i>
                                <span @click.prevent="editLink(link)">{{ link.title }}</span>
                            </div>    
                            <div class="flex-item push-right">
                                <button class="btn btn-sm btn-alt" @click.prevent="editLink(link)">Edit</button>
                                <button class="btn btn-sm btn-alt" @click.prevent="removeLink(link)">Delete</button>
                            </div>
                        </div>   
                    </li>
                </ul>
            </div>    
        </transition>       
    </div>
    <div v-show="activeTab == 'appearance'">
    	<table class="table table-bordered  table-striped table-settings">
        <tbody>
        <tr>
            <td>
                <h2>Heading</h2>
                <p>Optional heading that will appear above link list.</p>
            </td>
            <td>
                <div class="form-group">
                    <input type="text" v-model="widget.settings.heading" class="form-control">
                </div>  
            </td>
        </tr>
        <tr>
            <td>
                <h2>Orientation</h2>
            </td>
            <td>
                <div class="form-group">
                    <select class="form-control" v-model="widget.settings.orientation">
                        <option value="vertical">Vertical</option>
                        <option value="horizontal">Horizontal</option>
                    </select>   
                </div>  
            </td>
        </tr>
        <tr>
            <td>
                <h2>Limit Visibility</h2>
                <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
            </td>
            <td>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                    </label>
                </div>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                    </label>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <h2>Columns</h2>
                <p>How much horizontal space this block will take up. For example a 2 column layout would be 2 "Half" blocks.</p>
            </td>
            <td>
                <div class="form-group">
                    <select class="form-control" v-model="widget.settings.layout_width">
                        <option value="full-width">Full</option>
                        <option value="three-quarter-width">3/4</option>
                        <option value="half-width">Half</option>
                        <option value="quarter-width">1/4</option>
                    </select>   
                </div>  
            </td>
        </tr>
        </tbody>
        </table>
    </div>     
</div>    
</template>

<script>
	export default {
		props: ['widget', 'create'],

		created: function()
		{
			if(this.create)
            {
                this.widget.title = 'Link List'
                this.widget.template = 'LinkList'
                this.widget.settings = {
                    'orientation': 'vertical',
                    'layout_width': 'third-width',
                    'links': [
                        {
                            'edit': true,
                            'title': this.linkLabel, 
                            'url': this.linkUrl,
                        }
                    ],
                }
            }
		},

        data: function()
        {
            return {
                linkLabel: 'Link',
                linkUrl: '/',
                links: [],
                activeTab: 'links',
                activeLink: null,
                options: { 
                    onUpdate: this.updateSort, 
                    handle: '.draghandle',
                    animation: 150,
                    group: {
                        name:'segments',
                        pull: false,
                        put: true
                    } 
                }
            }
        },

        methods: {
            itemIndex: function(item) {
                return Math.floor((Math.random() * 10000) + 1);
            },

            addLink: function()
            {
                let link = {
                    'edit': true,
                    'title': this.linkLabel+' '+this.widget.settings.links.length, 
                    'url': this.linkUrl,
                }

                this.widget.settings.links.push(link);
                this.activeLink = link;
            },

            editLink: function(link)
            {
                if(this.activeLink === link)
                {
                    this.activeLink = null;
                }
                else
                {
                    this.activeLink = link;
                }
            },

            removeLink: function(link)
            {
                if(this.widget.settings.links.length > 1)
                {
                    var index = this.widget.settings.links.indexOf(link); 
                    this.widget.settings.links.splice(index, 1)
                    this.activeLink = null
                }
            },

            updateSort: function(event)
            {
                this.widget.settings.links.splice(event.newIndex, 0, this.widget.settings.links.splice(event.oldIndex, 1)[0])
            },
        }
	}
</script>
<template>
<div>
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'home')" 
        ><i class="fa fa-chevron-left"></i></a> Appearance
    </div>

    <div class="form-group">
        <label>Padding Top</label>
        <spacing-control v-model="paddingTop"></spacing-control>
    </div>        
    
    <div class="form-group">
        <label>Padding Bottom</label>
        <spacing-control v-model="paddingBottom"></spacing-control>
    </div>       
    <hr>
    <div class="form-group">
        <label>Widget Background Color</label>
        <color-picker v-model="background" key="background" name="background"></color-picker>
    </div>

    <div class="form-group">
        <label>Text Color</label>
        <color-picker v-model="text_color" key="text_color" name="text_color"></color-picker>
    </div>

    <div class="form-group">
        <label>Link Color</label>
        <color-picker v-model="link_color" key="link_color" name="link_color"></color-picker>
    </div>
    <hr>
    <div class="form-group">
        <label>Testimonial Background Color</label>
        <color-picker v-model="testimonial_bg_color" key="testimonial_bg_color" name="testimonial_bg_color"></color-picker>
    </div>
    <div class="form-group">
        <label>Testimonial Text Color</label>
        <color-picker v-model="testimonial_text_color" key="testimonial_text_color" name="testimonial_text_color"></color-picker>
    </div>
</div>    
</template>

<script>
import ColorPicker from '../../_inputs/color-picker.vue';
import SpacingControl from '../../_inputs/spacing-control.vue';
export default {
    props: ['widget'],
    
    components: {
        ColorPicker,
        SpacingControl
    },

    created() {
        eventHub.emit('pageEditor:setWidgetElementTitle', 'Appearance')
    },

    computed: {
        paddingTop: {
            get() {
                return this.widget.settings.paddingTop;
            },
            set(newVal) {
                this.widget.settings.paddingTop = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        paddingBottom: {
            get() {
                return this.widget.settings.paddingBottom
            },
            set(newVal) {
                this.widget.settings.paddingBottom = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        alignment: {
            get() {
                return this.widget.settings.alignment
            },
            set(newVal) {
                this.widget.settings.alignment = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        background: {
            get() {
                return this.widget.settings.background
            },
            set(newVal) {
                this.widget.settings.background = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        testimonial_bg_color: {
            get() {
                return this.widget.settings.testimonial_bg_color
            },
            set(newVal) {
                this.widget.settings.testimonial_bg_color = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        text_color: {
            get() {
                return this.widget.settings.text_color
            },
            set(newVal) {
                this.widget.settings.text_color = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        testimonial_text_color: {
            get() {
                return this.widget.settings.testimonial_text_color
            },
            set(newVal) {
                this.widget.settings.testimonial_text_color = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        link_color: {
            get() {
                return this.widget.settings.link_color
            },
            set(newVal) {
                this.widget.settings.link_color = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        }
    }
}
</script>
<template>
<div class="photoEditor">
    <div class="widget__breadcrumb">
        <a 
            href="#" 
            @click.prevent="event('pageEditor:editWidgetElement', 'testimonials')" 
        ><i class="fa fa-chevron-left"></i></a> Edit Testimonial
    </div>


    <div class="form-group">
        <label>Testimonial</label>
        <textarea 
            class="form-control" 
            :value="body" 
            @change="body = $event.target.value" 
            rows="7"
        ></textarea>
    </div>

    <div class="form-group">
        <label>Attribution</label>
        <text-input v-model="attribution"></text-input>
    </div>

    <div class="form-group">
        <label>Profile Photo</label>
        <div class="photoPreview">
            <cover-photo 
                :src="photo_src" 
                @input="updatePhoto"
            ></cover-photo>
        </div>
    </div>
</div>                            
</template>

<script>
import TextInput from '../../_inputs/text-input.vue';
import CoverPhoto from '../../../MediaManager/CoverPhoto.vue';
export default {
    props: ['widget', 'photo', 'widgetItem'],

    emits: ['update'],

    components: {
        CoverPhoto,
        TextInput
    },

    methods: {
        updatePhoto(photo) {
            this.photo_src = photo.path;
            // this.$emit('update', photo);
        }
    },

    computed: {
        body: {
            get() {
                return this.photo.body;
            },
            set(newVal) {
                this.photo.body = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        attribution: {
            get() {
                return this.photo.attribution;
            },
            set(newVal) {
                this.photo.attribution = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
        photo_src: {
            get() {
                return this.photo.photo_src;
            },
            set(newVal) {
                this.photo.photo_src = newVal;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            }
        },
    }
}
</script>    

                                
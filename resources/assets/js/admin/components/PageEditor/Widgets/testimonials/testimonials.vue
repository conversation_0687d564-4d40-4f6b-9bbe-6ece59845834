<template>
<div class="widget__container">
    <div class="widget__innerContainer">
        <slot name="header" :backLink="widgetElement === 'PhotoEditor' ? 'photos' : 'home'"></slot>
        <div class="widgetContent">
            <transition 
                name="fade" 
                mode="out-in" 
            >
                <component 
                    @edit="editItme" 
                    @update="updateItem" 
                    @remove="removeItem" 
                    :photo="activePhoto" 
                    :key="widget.id" 
                    :is="widgetElement"  
                    :widget="widget" 
                    :widgetItem="widgetItem"
                ></component>
            </transition>    
        </div>

        <!-- Photo Editor Footer -->
        <div v-if="widgetElement === 'TestimonialEditor'" class="widgetFooter">
            <button 
                type="button" 
                class="btn btn-link btn-sm" 
                style="margin-right: auto" 
                @click="removeItem(activePhoto)" 
            >Delete</button>
            <button 
                type="button" 
                class="btn btn-action btn-lg" 
                @click="saveItem(activePhoto)" 
            >Save Changes</button>
        </div>
        <!-- End Photo Editor Footer -->

        <slot name="footer" v-else></slot>
    </div>
</div>    
</template>

<script>
    import home from './_home.vue';
    import headingText from './_heading_text.vue';
    import appearance from './_appearance.vue';
    import visibility from './_visibility.vue';
    import Testimonials from './_testimonials.vue';
    import TestimonialEditor from './_testimonial_editor.vue';
	export default {
        props: ['page','widget','widgetElement','widgetItem'],

		 components: {
            home,
            headingText,
            appearance,
            visibility,
            Testimonials,
            TestimonialEditor
        },

        watch: {
            widgetItem(val) {
                if(this.widgetItem)
                {
                    this.editItme(this.widget.settings.items[this.widgetItem])
                }
            }
        },

        created() {
            if(this.widgetItem)
            {
                this.editItme(this.widget.settings.items[this.widgetItem])
            }
        },

        data: function()
        {
            return {
                activePhoto: null,
                options: {
                    onUpdate: this.updateSort,
                    handle: '.draghandle',
                    dragClass: "sortable-drag",
                    animation: 150,
                    group: {
                        name:'page_structure',
                        pull: false,
                        put: false
                    }
                }
            }
        },

        methods: {
            editItme: function(photo)
            {
                this.activePhoto = photo;
                eventHub.emit('pageEditor:editWidgetElement', 'TestimonialEditor')
            },

            removeItem: function(photo)
            {
                if(this.widget.settings.items.length > 1)
                {
                    this.activePhoto = null
                    var index = this.widget.settings.items.indexOf(photo); 
                    this.widget.settings.items.splice(index, 1)
                    eventHub.emit('pageEditor:updateWidget', this.widget);
                    eventHub.emit('pageEditor:editWidgetElement', 'Testimonials')
                }
            },

            updateItem: function(photo) {
                this.activePhoto.src = photo.path;
                eventHub.emit('pageEditor:updateWidget', this.widget);
            },

            saveItem: function(photo) {
                eventHub.emit('pageEditor:updateWidget', this.widget)
                eventHub.emit('pageEditor:editWidgetElement', 'Testimonials')
            }
        },

        computed: {
            itemId: function() {
                if(this.widget.settings.items) {
                    return this.widget.settings.items.length;
                }
                else {
                    return 0;
                }
            },
        }
	}
</script>
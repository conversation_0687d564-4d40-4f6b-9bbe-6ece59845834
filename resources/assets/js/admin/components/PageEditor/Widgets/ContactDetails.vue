<template>
<div class="pageEditor__activeBlockContainer">
	<table class="table table-bordered  table-striped table-settings">
    <tbody>
    <tr>
        <td>
            <h2>Text Alignment</h2>
        </td>
        <td>
            <select v-model="widget.settings.alignment" class="form-control">
                <option value="text-left">Left</option>
                <option value="text-center">Center</option>
                <option value="text-right">Right</option>
            </select>
        </td>
    </tr>
    <tr>
        <td>
            <h2>Limit Visibility</h2>
            <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
        </td>
        <td>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                </label>
            </div>
            <div class="checkbox">
                <label>
                    <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                </label>
            </div>
        </td>
    </tr>
    <tr>
        <td>
            <h2>Columns</h2>
            <p>How much horizontal space this block will take up. For example a 2 column layout would be 2 "Half" blocks.</p>
        </td>
        <td>
            <div class="form-group">
                <select class="form-control" v-model="widget.settings.layout_width">
                    <option value="full-width">Full</option>
                    <option value="three-quarter-width">3/4</option>
                    <option value="half-width">Half</option>
                    <option value="quarter-width">1/4</option>
                </select>   
            </div>  
        </td>
    </tr>
    </tbody>
    </table>
</div>    
</template>

<script>
	export default {
		props: ['widget', 'create'],

		created: function()
		{
			if(this.create)
            {
                this.widget.title = 'Contact Card'
                this.widget.template = 'ContactDetails'
                this.widget.settings = {
                    'layout_width': 'full-width',
                    'alignment': 'text-center'
                }
            }
		}
	}
</script>
<template>
<div class="widget__container widget__container--full">
	<div class="widget__innerContainer">
		<div class="widgetHeader">
            <div class="widgetHeader__actionsMenu dropdown flex-item">
                <a href="#" class="btn dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                    <i class="far fa-lg fa-ellipsis-v"></i>
                </a>
                <ul class="dropdown-menu">
                    <li><a href="#" class="btn-alt pr-sm pl-sm" @click.prevent="event('pageEditor:renameWidget', widget)"><i class="far fa-i-cursor fa-fw"></i> Rename</a></li>
                    <li><a href="#" class="btn-alt pr-sm pl-sm" @click.prevent="event('pageEditor:duplicateWidget', widget)"><i class="far fa-clone fa-fw"></i> Duplicate</a></li>
                    <li><a href="#" class="btn-alt pr-sm pl-sm" @click.prevent="event('pageEditor:deleteWidget', widget)"><i class="far fa-trash fa-fw"></i> Delete</a></li>
                </ul>
            </div>
            <div class="widgetHeader__title text-center">
                <a href="#" @click.prevent="event('pageEditor:editWidgetElement', 'home')">{{ widget.title }}</a>
            </div>
            <a
                class="btn"  
                href="#" 
                @click.prevent="event('CodeEditor:update', widget)" 
            ><i class="fal fa-lg fa-times-circle"></i></a> 
        </div>
		<div class="widgetContent">
			<transition 
				name="fade" 
				mode="out-in" 
			>
				<component 
					:key="widget.id" 
					:is="widgetElement"  
					:widget="widget" 
				></component>
			</transition>    
		</div>    
		<div class="widgetFooter">
            <button 
                type="button" 
                class="btn btn-action btn-block btn-lg" 
                @click="event('CodeEditor:update')" 
            >Done</button>
        </div> 
	</div>
	<code-editor 
		:widget="widget" 
	></code-editor>
</div>
</template>
<script>
	import Home from './_visibility.vue';
	import CodeEditor from './_code_editor.vue';
	export default {
		props: ['page','widget','widgetElement'],

		components: {
			Home,
			CodeEditor
		}
	}
</script>
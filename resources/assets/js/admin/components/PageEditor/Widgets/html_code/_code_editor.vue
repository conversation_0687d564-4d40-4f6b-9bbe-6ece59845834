<template>
<div>
    <div 
        id="html_textarea" 
        style="min-height: 500px; max-heght: 100vh; border-bottom: solid #e5e6e6 1px;" 
    ></div>
</div>
</template>

<script>
export default {
    props: ['widget'],

    mounted: function() {
        
        this.editor = ace.edit("html_textarea");
        this.editor.getSession().setMode("ace/mode/html");
        this.editor.setValue(this.widget.content);
        this.editor.focus();
        this.editor.commands.addCommand({
            name: "save",
            exec: this.update,
            bindKey: { win: "ctrl-s", mac: "cmd-s" }
        });
    },

    created() {
        eventHub.on('CodeEditor:update', function()
        {
            this.update();
        }.bind(this));
    },

    unmounted() {
        eventHub.off('CodeEditor:update')
    },

    data: function()
    {
        return {
            editor: {},
        }
    },

    methods: {
        update: function()
        {
            this.widget.content = this.editor.getSession().getValue();
            eventHub.emit('pageEditor:closeWidget', this.widget);
        }
    }
}
</script>
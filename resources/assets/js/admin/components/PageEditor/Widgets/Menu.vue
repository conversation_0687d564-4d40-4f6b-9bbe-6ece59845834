<template>
<div>
    <ul class="tabsList">
      <li :class="{'active': activeTab == 'appearance'}" @click="activeTab = 'appearance'">
        <a href="#">Appearance</a>
    </li>
    <li :class="{'active': activeTab == 'links'}" @click="activeTab = 'links'">
        <a href="#">Links</a>
    </li>
    </ul>
    <br>
    <div v-show="activeTab == 'links'" class="linkListWidget__linkTab">
        <ul class="linkListWidget__list" id="linkWidgetList" v-sortable="options">
            <li 
                v-for="link in widget.settings.links" 
                class="linkListWidget__listItem sortItem" 
                :data-title="link.title"
                :data-url="link.url"
                :class="{'linkListWidget__listItem--active': link.edit}"
                :key="itemIndex()"
            >
                <div class="linkListWidget__linkHeader" @click="link.edit = !link.edit">
                    <div class="linkListWidget__linkTitle">
                        <span class="fa fa-grip draghandle" title="Drag to reorder"></span> <span>{{ link.title }}</span>
                    </div><!--
                    --><div class="linkListWidget__removeButton text-right"><a href="#" @click.prevent="removeLink(link)"><i class="fa fa-trash-o fa-lg"></i></a></div>
                </div>
                <div class="linkListWidget__linkInputs" v-if="link.edit">
                    <div class="form-group">
                        <label>Label</label>
                        <input type="text" v-model="link.title" class="form-control">
                    </div>
                    <div class="form-group">
                        <label>URL</label>
                        <input type="text" v-model="link.url" class="form-control">
                    </div>
                </div>
            </li>
        </ul>
        <button class="btn btn-primary btn-lg btn-block" @click="addLink()">Add</button>
    </div>
    <div v-show="activeTab == 'appearance'">
    	<table class="table table-bordered  table-striped table-settings">
        <tbody>
        <tr>
            <td>
                <h2>Heading</h2>
                <p>Optional heading that will appear above link list.</p>
            </td>
            <td>
                <div class="form-group">
                    <input type="text" v-model="widget.settings.heading" class="form-control">
                </div>  
            </td>
        </tr>
        <tr>
            <td>
                <h2>Orientation</h2>
            </td>
            <td>
                <div class="form-group">
                    <select class="form-control" v-model="widget.settings.orientation">
                        <option value="vertical">Vertical</option>
                        <option value="horizontal">Horizontal</option>
                    </select>   
                </div>  
            </td>
        </tr>
        <tr>
            <td>
                <h2>Limit Visibility</h2>
                <p>Limit when a visitor will see this widget. If no options are selected this widget will always be visible.</p>
            </td>
            <td>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" v-model="widget.settings.show_when_auth"> Only show when signed in
                    </label>
                </div>
                <div class="checkbox">
                    <label>
                        <input type="checkbox" v-model="widget.settings.show_when_guest">Only show when <strong>not</strong> signed in
                    </label>
                </div>
            </td>
        </tr>
        <tr>
            <td>
                <h2>Columns</h2>
                <p>How much horizontal space this block will take up. For example a 2 column layout would be 2 "Half" blocks.</p>
            </td>
            <td>
                <div class="form-group">
                    <select class="form-control" v-model="widget.settings.layout_width">
                        <option value="full-width">Full</option>
                        <option value="three-quarter-width">3/4</option>
                        <option value="half-width">Half</option>
                        <option value="quarter-width">1/4</option>
                    </select>   
                </div>  
            </td>
        </tr>
        </tbody>
        </table>
    </div>     
</div>    
</template>

<script>
	export default {
		props: ['widget', 'create'],

		created: function()
		{
			if(this.create)
            {
                this.widget.title = 'Link List'
                this.widget.template = 'LinkList'
                this.widget.settings = {
                    'orientation': 'vertical',
                    'layout_width': 'third-width',
                    'links': [
                        {
                            'edit': true,
                            'title': this.linkLabel, 
                            'url': this.linkUrl,
                        }
                    ],
                }
            }
		},

        data: function()
        {
            return {
                linkLabel: 'Link',
                linkUrl: '/',
                links: [],
                activeTab: 'appearance',
                activeLink: '',
                options: { 
                    onUpdate: this.updateSort, 
                    handle: '.draghandle',
                    animation: 150,
                    group: {
                        name:'segments',
                        pull: false,
                        put: true
                    } 
                }
            }
        },

        methods: {
            itemIndex: function(item) {
                return Math.floor((Math.random() * 10000) + 1);
            },

            addLink: function()
            {
                this.widget.settings.links.push({
                    'edit': true,
                    'title': this.linkLabel, 
                    'url': this.linkUrl,
                })
            },

            removeLink: function(link)
            {
                if(this.widget.settings.links.length > 1)
                {
                    var index = this.widget.settings.links.indexOf(link); 
                    this.widget.settings.links.splice(index, 1)
                }
            },

            updateSort: function(event)
            {
                this.widget.settings.links.splice(event.newIndex, 0, this.widget.settings.links.splice(event.oldIndex, 1)[0]);
            },

            calculateSortOrder: function(start, end)
            {

                let temp = this.widget.settings.links[start]

                // Remove item
                this.widget.settings.links.splice(start, 1)

                // Add item
                this.widget.settings.links.splice(end, 0, temp)
            },
        }
	}
</script>
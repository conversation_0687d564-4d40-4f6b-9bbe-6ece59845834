<template>
<div class="widgetLibrary__container">

    <div class="widgetLibrary__searchContainer">
        <input 
            type="text" 
            class="form-control widgetLibrary__searchInput" 
            v-model="search" 
            placeholder="Search widgets..." 
            ref="searchInput" 
        >
    </div>

    <div class="widgetLibrary__libraryContainer">
        <section class="widgetLibrary__section" v-show="filteredElements.length">
            <header><h2>Elements</h2></header>
            <ul class="widgetLibrary__list" v-sortable="options">
                <li 
                    v-for="widget in filteredElements" 
                    :data-id="widget.id"
                    class="widgetLibrary__listItem" 
                    :key="widget.id" 
                    @click="store(widget)"
                >
                    <div class="widgetLibrary__listItemTitle">
                        <i class="widgetLibrary__listItemIcon" :class="widget.icon"></i>
                        {{ widget.title }}
                    </div>
                </li>
            </ul>
        </section>

        <section class="widgetLibrary__section" v-show="filteredBlocks.length">
            <header><h2>Blocks</h2></header>
            <ul class="widgetLibrary__list" v-sortable="options">
                <li 
                    v-for="widget in filteredBlocks" 
                    :data-id="widget.id"
                    class="widgetLibrary__listItem" 
                    :key="widget.id" 
                    @click="store(widget)"
                >
                    <div class="widgetLibrary__listItemTitle">
                        <i class="widgetLibrary__listItemIcon" :class="widget.icon"></i>
                        {{ widget.title }}
                    </div>
                </li>
            </ul>
        </section>      

        <section class="widgetLibrary__section" v-show="filteredLiveWidgets.length">
            <header><h2>Live Widgets</h2></header>
            <ul class="widgetLibrary__list" v-sortable="options">
                <li 
                    v-for="widget in filteredLiveWidgets" 
                    :data-id="widget.id"
                    class="widgetLibrary__listItem" 
                    :key="widget.id" 
                    @click="store(widget)"
                >
                   <div class="widgetLibrary__listItemTitle">
                        <i class="widgetLibrary__listItemIcon" :class="widget.icon"></i>
                        {{ widget.title }}
                    </div>
                </li>
            </ul>
        </section>   

        <section class="widgetLibrary__section" v-show="filteredForms.length">
            <header><h2>Forms</h2></header>
            <ul class="widgetLibrary__list" v-sortable="options">
                <li 
                    v-for="widget in filteredForms" 
                    :data-id="widget.id"
                    class="widgetLibrary__listItem" 
                    :key="widget.id" 
                    @click="store(widget)"
                >
                   <div class="widgetLibrary__listItemTitle">
                        <i class="widgetLibrary__listItemIcon" :class="widget.icon"></i>
                        {{ widget.title }}
                    </div>
                </li>
            </ul>
        </section> 

        <section class="widgetLibrary__section" v-show="filteredOther.length">
            <header><h2>Other</h2></header>
            <ul class="widgetLibrary__list" v-sortable="options">
                <li 
                    v-for="widget in filteredOther" 
                    :data-id="widget.id"
                    class="widgetLibrary__listItem" 
                    :key="widget.id" 
                    @click="store(widget)"
                >
                   <div class="widgetLibrary__listItemTitle">
                        <i class="widgetLibrary__listItemIcon" :class="widget.icon"></i>
                        {{ widget.title }}
                    </div>
                </li>
            </ul>
        </section> 
    </div>    
</div>
</template>

<script>
	export default {
        
        props: ['page','widgetLibrary','widget'],

        mounted() {
            this.$refs.searchInput.focus();
        },

        computed: {
            handleClass() {
                return '.widgetLibrary__listItem'
            },

            filteredElements() {
            return this.widgetLibrary.elements.filter(widget => {
                    return widget.title.toLowerCase().includes(this.search.toLowerCase())
                })
            },
            filteredBlocks() {
            return this.widgetLibrary.blocks.filter(widget => {
                    return widget.title.toLowerCase().includes(this.search.toLowerCase())
                })
            },
            filteredLiveWidgets() {
            return this.widgetLibrary.dynamic.filter(widget => {
                    return widget.title.toLowerCase().includes(this.search.toLowerCase())
                })
            },
            filteredForms() {
            return this.widgetLibrary.forms.filter(widget => {
                    return widget.title.toLowerCase().includes(this.search.toLowerCase())
                })
            },
            filteredOther() {
            return this.widgetLibrary.other.filter(widget => {
                    return widget.title.toLowerCase().includes(this.search.toLowerCase())
                })
            }
        },

        data: function()
        {
            return {
                search: '',
                options: { 
                    sort: false,
                    handle: '.widgetLibrary__listItem',
                    animation: 150,
                    group: {
                        name:'page_structure',
                        pull: 'clone',
                        put: false
                    },
                    onStart: function (event) {
                        eventHub.emit('widgetLibrary:dragStarted', event)
                    }.bind(this),
                    onEnd: function (event) {
                        eventHub.emit('widgetLibrary:dragEnded', event)
                    }.bind(this),
                    setData: function (dataTransfer, dragEl) {
                        eventHub.emit('widgetLibrary:dataSet', dragEl)
                        dataTransfer.setData('Text', dragEl.getAttribute('data-id'))
                    },
                }
            }
        },

        methods: {
            store: function(widget) {
                eventHub.emit('showProgressBar');

                let sort = 0;
                if(this.widget && this.widget.id)
                {
                    sort = this.page.widgets.indexOf(this.widget);
                }
                else
                {
                    sort = this.page.widgets.length;
                }

                let data = {
                    template: widget.id,
                    sort: sort
                };

                axios.post('/api/pages/'+this.page.id+'/widgets', {'id': data.template, 'sort': data.sort})
                    .then(function(response) {
                        eventHub.emit('hideProgressBar');
                        this.page.widgets.splice(data.sort, 0, response.data.block);
                        eventHub.emit('pageEditor:editWidget', response.data.block)
                        eventHub.emit('pagePreview:refresh');
                        eventHub.emit('pageLayout:refresh');
                        this.hideModal('widgetLibraryModal');
                    }.bind(this))
                    .catch(function(error) {
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('error', error);
                });
            }
        }
	}
</script>
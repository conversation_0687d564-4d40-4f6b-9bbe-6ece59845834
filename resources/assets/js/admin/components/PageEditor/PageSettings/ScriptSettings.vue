<template>
    <div class="pageSettings__contentContainer">
        <div class="pageSettings__footer">
            <button
                class="btn btn-action"
                type="button"
                @click="$emit('store', {settings: {
                meta_tags: page.settings.meta_tags,
                body_scripts: page.settings.body_scripts
            }})"
            >Save Settings
            </button>
        </div>
        <div class="pageSettings__contentBody">
            <div class="form-group">
                <label>Meta tags</label>
                <p class="mt-1 text-gray-500 text-sm max-w-2xl">
                    This is generally used to add meta information inside the {{ `
                    <head />
                    ` }} element of this page only. If the meta information should be included on all pages, use the <a href="/admin/settings/advanced">global
                    meta tags</a> config instead.
                </p>
                <textarea
                    v-model="page.settings.meta_tags"
                    class="form-control height--300"
                    rows="9"
                ></textarea>
            </div>

            <div class="mt-8 form-group">
                <label>Footer scripts</label>
                <p class="mt-1 text-gray-500 text-sm max-w-2xl">
                    This is generally used to add additional script tags to the bottom of this page only. For example, an embeddable form on this page requires
                    some <strong>{{ `\<\script\>...\<\/script\>` }}</strong> tag to render. The script tag should be included here. However, if the embeddable
                    is included on all pages, use the<a href="/admin/settings/advanced"> global footer scripts</a> config instead.
                </p>
                <textarea
                    v-model="page.settings.body_scripts"
                    class="form-control height--300"
                    rows="9"
                ></textarea>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: ['page'],

    emits: ['store']
};
</script>

<template>
<div class="pageSettings__contentContainer">
    <div class="pageSettings__footer">
        <button 
            type="button" 
            class="btn btn-action" 
            @click="$emit('store', {
                page_title: page.page_title,
                description: page.description,
                slug: page.slug,
                seo_visibility: page.seo_visibility,
            })"
        >Save Settings</button>
    </div>
    <div class="pageSettings__contentBody">
        <div class="form-group">
            <label>Page Title</label>
            <input 
                type="text" 
                class="form-control" 
                v-model="page.page_title" 
            >
        </div>

        <div class="form-group">
            <label>Page Description</label>
            <textarea
                class="form-control height--200" 
                v-model="page.description" 
                rows="3"
            ></textarea>
        </div>

        <div class="form-group">
            <label>Page Slug</label>
            <input 
                type="text" 
                class="form-control" 
                v-model="page.slug" 
            >
        </div>

        <div class="form-group">
            <label for="seo_visibility">Visible to Search Engines</label>
            <select class="form-control" name="seo_visibility" v-model="page.seo_visibility">
                <option value="1">Yes</option>
                <option value="0">No</option>
            </select>
        </div>
    </div>
</div>      
</template>

<script>
    export default {
        props: ['page'],

        emits: ['store'],
    }
</script>
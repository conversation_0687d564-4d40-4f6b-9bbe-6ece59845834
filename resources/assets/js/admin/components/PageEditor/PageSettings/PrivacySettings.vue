<template>
<div class="pageSettings__contentContainer">
    <div class="pageSettings__footer">
        <button 
            type="button" 
            class="btn btn-action" 
            @click="$emit('store', {settings: {require_login: require_login}})"
        >Save Settings</button>
    </div>
    <div class="pageSettings__contentBody">
        <div class="form-group">
            <label>Visitor must be logged in (coming soon)</label>
            <div class="radio">
                <label for="require_login_yes" class="block">
                    <input 
                        type="radio" 
                        name="require_login" 
                        id="require_login_yes" 
                        :value="true" 
                        v-model="require_login"
                    > Yes
                </label>
                <label for="require_login_no" class="block">
                    <input 
                        type="radio" 
                        name="require_login" 
                        id="require_login_no" 
                        :value="false" 
                        v-model="require_login"
                    > No
                </label>
            </div>
        </div>
    </div>
</div>  
</template>

<script>
    export default {
        props: ['page'],

        emits: ['store'],

        computed: {
            require_login: {
                get() {
                    return this.page.settings.require_login !== undefined ? this.page.settings.require_login : false;
                },
                set(newVal) {
                    this.page.settings.require_login = newVal;
                }
            }
        }
    }
</script>
<template>
<div class="pageSettings__contentContainer">
    <div class="pageSettings__footer">
        <button 
            type="button" 
            class="btn btn-action" 
            @click="$emit('store', {title: page.title, visible: page.visible})"
        >Save Settings</button>
    </div>
    <div class="pageSettings__contentBody">
        <div class="form-group">
            <label>Page Name</label>
            <input 
                type="text" 
                class="form-control" 
                v-model="page.title" 
            >
        </div>

        <div class="form-group">
            <label>Visibility</label>
            <div class="radio">
                <label for="page_visible" class="block">
                    <input 
                        type="radio" 
                        id="page_visible" 
                        :value="1" 
                        v-model="page.visible"
                    > Public
                </label>
                <label for="page_hidden" class="block">
                    <input 
                        type="radio" 
                        id="page_hidden" 
                        :value="0" 
                        v-model="page.visible"
                    > Admin Only
                </label>
            </div>
        </div> 
    </div>
</div>    
</template>

<script>
    export default {
        props: ['page'],

        emits: ['store'],
    }
</script>
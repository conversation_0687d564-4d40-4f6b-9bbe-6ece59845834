<template>
<div class="pageSettings">
    <div class="pageSettings__sidebar">
        <select class="form-control mobileNav" @change="changeTab($event.target.value)">
            <option value="GeneralSettings">General Settings</option>
            <option value="SeoSettings">Search Engine Optimization</option>
            <option value="ScriptSettings">Tags &amp; Scripts</option>
            <option value="PrivacySettings">Security &amp; Privacy</option>
            <!-- <option value="AppearanceSettings">Appearance</option> -->
        </select>  
        <ul class="widget__navigation">
             <li :class="{'active': component === 'GeneralSettings'}">
                <button 
                    type="button" @click.prevent="changeTab('GeneralSettings')" 
                    class="btn"
                ><i class="fal fa-sliders-h-square fa-fw"></i> General Settings</button>
            </li>
            <li :class="{'active': component === 'SeoSettings'}">
                <button 
                    type="button" @click.prevent="changeTab('SeoSettings')" 
                    class="btn" 
                ><i class="fal fa-search fa-fw"></i> Search Engine Optimization</button>
            </li>
            <li :class="{'active': component === 'ScriptSettings'}">
                <button 
                    type="button" @click.prevent="changeTab('ScriptSettings')" 
                    class="btn" 
                ><i class="fal fa-code fa-fw"></i> Tags &amp; Scripts</button>
            </li>
            <li :class="{'active': component === 'PrivacySettings'}">
                <button 
                    type="button" @click.prevent="changeTab('PrivacySettings')" 
                    class="btn" 
                ><i class="fal fa-lock fa-fw"></i> Security &amp; Privacy</button>
            </li>
            <!-- <li :class="{'active': component === 'AppearanceSettings'}">
                <button 
                    type="button" @click.prevent="changeTab('AppearanceSettings')"  
                    class="btn"
                ><i class="fal fa-paint-brush fa-fw"></i> Appearance</button>
            </li> -->
        </ul>
    </div>    
    <component :is="component" :page="page" @store="store"></component>   
</div>    
</template>

<script>
    import SeoSettings from './SeoSettings.vue';
    import ScriptSettings from './ScriptSettings.vue';
    import PrivacySettings from './PrivacySettings.vue';
    import GeneralSettings from './GeneralSettings.vue';
    import AppearanceSettings from './AppearanceSettings.vue';
    export default {
        props: ['page'],

        components: {
            SeoSettings,
            ScriptSettings,
            PrivacySettings,
            GeneralSettings,
            AppearanceSettings
        },

        data: function()
        {
            return {
                component: 'GeneralSettings',
            }
        },

        computed: {
            pageURL: function()
            {
                return 'https://'+window.location.hostname+'/';
            }
        },

        methods: {
            store: function(payload)
            {
                eventHub.emit('showProgressBar');
                axios.put('/api/pages/'+this.page.id, payload).then(function(response) {
                    eventHub.emit('pageEditor:loadPage');
                    eventHub.emit('hideProgressBar');
                }.bind(this)).catch(function(error) {
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                });
            },

            changeTab: function(component)
            {
                this.component = component;
            },
        }
    }
</script>
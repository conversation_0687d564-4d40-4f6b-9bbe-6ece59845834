<template>
  <ul class="pageEditorToolbar">
    <li class="pageEditorToolbar__breadcrumb">
      <ul class="breadcrumb">
        <li><a href="/admin/pages">Pages</a></li>
        <li class="pageEditorToolbar__Pagetitle">{{ page.title }}</li>
      </ul>
    </li>
    <li class="pageEditorToolbar__tabs">
      <ul class="toolbarTabs">
        <li class="toolbarTabs__first">
          <button
            type="button"
            class="btn btn-alt toolbarButton"
            :class="{ active: activeToolbarItem == 'content' }"
            @click="$emit('showAddWidget')"
          >
            <i class="fal fa-layer-plus fa-fw toolbarButtonIcon"></i>
            <span class="toolbarButtonLabel">Widgets</span>
          </button>
        </li>
        <li>
          <button
            type="button"
            class="btn btn-alt toolbarButton"
            :class="{ active: activeToolbarItem == 'layout' }"
            @click="$emit('showPageLayout')"
          >
            <i class="fal fa-layer-group fa-fw toolbarButtonIcon"></i>
            <span class="toolbarButtonLabel">Layout</span>
          </button>
        </li>
        <li>
          <button
            type="button"
            class="btn btn-alt toolbarButton"
            :class="{ active: activeToolbarItem == 'preview' }"
            @click="$emit('showPagePreview')"
          >
            <i class="fal fa-eye fa-fw toolbarButtonIcon"></i>
            <span class="toolbarButtonLabel">Preview</span>
          </button>
        </li>
        <li>
          <button
            type="button"
            class="btn btn-alt toolbarButton"
            :class="{ active: activeToolbarItem == 'settings' }"
            @click="$emit('showPageSettings')"
          >
            <i class="fal fa-cog fa-fw toolbarButtonIcon"></i>
            <span class="toolbarButtonLabel">Settings</span>
          </button>
        </li>
        <li class="toolbarTabs__last">
          <div class="dropdown">
            <button
              type="button"
              class="btn btn-alt toolbarButton"
              data-toggle="dropdown"
            >
              <i class="far fa-ellipsis-v fa-fw toolbarButtonIcon"></i>
            </button>
            <ul class="dropdown-menu pull-right">
              <li>
                <a
                  :href="'/' + page.slug"
                  class="btn-alt pr-sm pl-sm"
                  target="_blank"
                  ><i class="far fa-external-link-square fa-fw"></i> Visit
                  Page</a
                >
              </li>
              <li>
                <a
                  href="#"
                  class="btn-alt pr-sm pl-sm"
                  @click="showModal('duplicatePageModal')"
                  ><i class="far fa-clone fa-fw"></i> Duplicate Page</a
                >
              </li>
            </ul>
          </div>
        </li>
      </ul>
    </li>
  </ul>
</template>

<script>
export default {
  props: ["page", "activeToolbarItem", "previewSize"],

    emits: [
        'showAddWidget',
        'showAddPageLayout',
        'showPagePreview',
        'showPageSettings',
        'showPageLayout'
    ],
};
</script>

<style scoped>
.dropdown-menu li a {
  text-align: left;
  font-size: 1rem !important;
}
</style>

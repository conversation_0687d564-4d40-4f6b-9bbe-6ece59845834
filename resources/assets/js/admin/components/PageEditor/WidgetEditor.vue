<template>
<div class="widget">
    <transition 
        name="fade" 
        mode="out-in" 
    >
        <component 
            :key="widget.id" 
            :is="widget.template" 
            :page="page" 
            :widget="widget" 
            :widgetElement="widgetElement" 
            :widgetItem="widgetItem" 
        >
            <template v-slot:header>
                <div class="widgetHeader">
                    <div class="widgetHeader__actionsMenu dropdown flex-item">
                        <a href="#" class="btn dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
                            <i class="far fa-lg fa-ellipsis-v"></i>
                        </a>
                        <ul class="dropdown-menu">
                            <li><a href="#" class="btn-alt pr-sm pl-sm" @click.prevent="event('pageEditor:renameWidget', widget)"><i class="far fa-i-cursor fa-fw"></i> Rename</a></li>
                            <li><a href="#" class="btn-alt pr-sm pl-sm" @click.prevent="event('pageEditor:duplicateWidget', widget)"><i class="far fa-clone fa-fw"></i> Duplicate</a></li>
                            <li><a href="#" class="btn-alt pr-sm pl-sm" @click.prevent="event('pageEditor:deleteWidget', widget)"><i class="far fa-trash fa-fw"></i> Delete</a></li>
                        </ul>
                    </div>
                    <div class="widgetHeader__title text-center">
                        <a href="#" @click.prevent="event('pageEditor:editWidgetElement', 'home')">{{ widget.title }}</a>
                    </div>
                    <a
                        class="btn"  
                        href="#" 
                        @click.prevent="event('pageEditor:closeWidget', widget)" 
                    ><i class="fal fa-lg fa-times-circle"></i></a> 
                </div>
            </template>

            <template v-slot:footer>
                <div class="widgetFooter">
                    <button 
                        type="button" 
                        class="btn btn-action btn-block btn-lg" 
                        @click="event('pageEditor:closeWidget', widget)" 
                    >Done</button>
                </div>    
            </template>
        </component>
    </transition>
</div>    
</template>

<script>
    // import PageStructure from './PageStructure.vue';
    import HTML from './Widgets/html_code/html_code.vue';
    import Team from './Widgets/team_widget/team_widget.vue';
    import CtaButton from './Widgets/buttons/buttons.vue';
    import Banner from './Widgets/banner/banner.vue';
    import Vendors from './Widgets/vendors_widget/vendors_widget.vue';
    import Divider from './Widgets/divider/divider.vue';
    import image_widget from './Widgets/image_widget/image_widget.vue';
    import image_with_text from './Widgets/image_with_text/image_with_text.vue';
    import RichText from './Widgets/wysiwyg/wysiwyg.vue';
    import LinkList from './Widgets/LinkList.vue';
    import PhotoGrid from './Widgets/photo_grid/photo_grid.vue';
    import photo_gallery from './Widgets/photo_gallery/photo_gallery.vue';
    import Testimonials from './Widgets/testimonials/testimonials.vue';
    import TextHeader from './Widgets/text_header/text_header.vue';
    import Newsletter from './Widgets/newsletter_form/newsletter_form.vue';
    import HowItWorks from './Widgets/how_it_works/how_it_works.vue';
    import ContactForm from './Widgets/contact_form/contact_form.vue';
    import FeaturedPosts from './Widgets/blog_widget/blog_widget.vue';
    import FeaturedRecipes from './Widgets/recipes_widget/recipes_widget.vue';
    import ProductProtocols from './Widgets/protocols_widget/protocols_widget.vue';
    import FeaturedProducts from './Widgets/products_widget/products_widget.vue';

	export default {
		props: ['page','widget','widgetElement','widgetItem'],

        components: {
            HTML,
            Team,
            CtaButton,
            Banner,
            Vendors,
            Divider,
            image_widget,
            image_with_text,
            RichText,
            LinkList,
            PhotoGrid,
            photo_gallery,
            HowItWorks,
            TextHeader,
            Newsletter,
            ContactForm,
            FeaturedPosts,
            FeaturedRecipes,
            FeaturedProducts,
            ProductProtocols,
            Testimonials
            
        },

        unmounted() {
            eventHub.emit('widgetEditor:unmounted')
        },

        methods: {
            rename() {
                this.$refs.widgetTitle.select();
            },

            getElementLabel()
            {
                if(this.elementLabels[this.widgetElement])
                {
                    return this.elementLabels[this.widgetElement]
                }
                else
                {
                    return this.widgetElement
                }
            }
        }
	}
</script>
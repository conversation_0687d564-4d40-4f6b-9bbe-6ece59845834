<template>
<div class="addWidgets">
    <widget-library :widgetLibrary="widgetLibrary" :page="page"></widget-library>
    <div class="addWidgets__sidebar">
    	<ul 
            id="activeBlockList" 
            class="pageEditor__activeBlocksList" 
            v-sortable="options" 
            v-if="page.widgets"
        >
            <li 
                v-for="widget in page.widgets" 
                :data-id="widget.id"
                class="sortItem" 
                :key="widget.id"
            >
                <a 
                    href="#" 
                    class="flex-item-fill text-center sidebarNavigation__heading" 
                    @click.prevent="event('pageEditor:editWidget', widget)"
                >{{ widget.title }}
                </a>
            </li>
            <li  
                v-if="showDropGuide" 
                class="pageEditor__dropGuide" 
                :class="{'highlight': highlightDropZone}" 
                @drop="store" 
                @dragover.prevent 
                @dragenter.prevent="highlightDropZone = true"  
                >
                    Drop content blocks here to build your page.
            </li>   
        </ul>    
    </div>
</div>
</template>

<script>
    import WidgetLibrary from './WidgetLibrary.vue';
	export default {

        props: ['page','widgetLibrary'],
        
        components: {
            WidgetLibrary
        },

		data: function() {
			return {
                highlightDropZone: false,
				options: { 
                    onUpdate: this.update, 
                    onAdd: this.store,
                    handle: '.draghandle',
                    dragClass: "sortable-drag",
                    animation: 75,
                    group: {
                        name:'page_structure',
                        pull: false,
                        put: true
                    }
                }
			}
		},

        computed: {
            showDropGuide: function()
            {
                return !this.page.widgets.length;
            }
        },

		methods: {
			update(event) {
                eventHub.emit('showProgressBar');

                this.page.widgets.splice(event.newIndex, 0, this.page.widgets.splice(event.oldIndex, 1)[0]);
                let widgets = [];

                for (let i = 0; i < this.page.widgets.length; i++) {
                    widgets.push({ id: this.page.widgets[i].id, sort: i })
                }

                axios.post(`/admin/pages/${this.page.id}/widget-sort`, { widgets })
                    .then(function(response) {
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('pagePreview:refresh')
                    }.bind(this))
                    .catch(function(error) {
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('error', error);
                    });
            },


            // Insert new block into page structure.
            store: function(event) {
            	// Hide the cloned item from the Block Library
            	let activeBlockList = document.getElementById("activeBlockList");
                if(event.item.parentNode == activeBlockList) {
                    activeBlockList.removeChild(event.item);
                }

                let data = {
                    template: event.item.getAttribute('data-id'),
                    sort: event.newIndex
                };

                axios.post('/api/pages/'+this.page.id+'/widgets', {'id': data.template, 'sort': data.sort})
                    .then(function(response) {
                        eventHub.emit('hideProgressBar');
                        this.page.widgets.splice(data.sort, 0, response.data.block);
                        eventHub.emit('pagePreview:refresh',response.data.block)
                        eventHub.emit('pageEditor:editWidget', response.data.block)
                    }.bind(this))
                    .catch(function(error) {
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('error', error);
                });
            }
		}
	}
</script>
<template>
    <textarea 
        class="form-control height--200" 
        :value="formattedValue"
        @input="update"
    ></textarea>
</template>

<script>
	export default {
		props: ['modelValue'],

        emits: ['update:modelValue'],

        created() {
            this.input = this.modelValue;
        },

        computed: {
            formattedValue() {
                if (this.input) {
                    return this.input.replace(new RegExp('<br />','g'), '\n');
                }
            },
        },

        data() {
            return {
                input: null
            }
        },

        methods: {
            update(event) {
                this.$emit('update:modelValue', event.target.value.replace(new RegExp('\n','g'), '<br />'));
            }
        },

        watch: {
            modelValue(newValue) {
                this.input = newValue;
            }
        },
	}
</script>	
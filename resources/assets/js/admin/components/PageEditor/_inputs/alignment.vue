<template>
<div class="radio-btn-group">
    <div class="radio-btn flex-item-fill">
        <input  
            type="radio" 
            v-model="input" 
            :value="values[0]" 
            :class="{'active': input === 'left'}"
            @change="$emit('update:modelValue', $event.target.value)"
            id="container_text_alignment_left"
        >
        <label class="btn btn-dark" for="container_text_alignment_left">
            <i class="fas fa-align-left"></i>
        </label>
    </div>
    
    <div class="radio-btn flex-item-fill">
        <input  
            type="radio" 
            v-model="input" 
            :value="values[1]" 
            :class="{'active': input === 'center'}"
            @change="$emit('update:modelValue', $event.target.value)"
            id="container_text_alignment_center"
        >
        <label class="btn btn-dark" for="container_text_alignment_center">
            <i class="fas fa-align-center"></i>
        </label>
    </div>
    <div class="radio-btn flex-item-fill">
        <input  
            type="radio" 
            v-model="input" 
            :value="values[2]" 
            :class="{'active': input === 'right'}"
            @change="$emit('update:modelValue', $event.target.value)"
            id="container_text_alignment_right"
        >
        <label class="btn btn-dark" for="container_text_alignment_right">
            <i class="fas fa-align-right"></i>
        </label>
    </div>
</div>	
</template>

<script>
	export default {
		props: {
            modelValue: {},
            values: {
                required: false,
                type: Array,
                default: [
                    'left','center','right'
                ]
            }
        },

        emits: ['update:modelValue'],

        data() {
            return {
                input: null
            }
        },

        created() {
            this.input = this.modelValue;
        },

        watch: {
            modelValue(newValue) {
                this.input = newValue;
            }
        },
	}
</script>	
<template>
    <select
        :value="modelValue"
        class="form-control"
        @change="$emit('update:modelValue', $event.target.value)"
    >
        <option :value="1">1</option>
        <option :value="1.25">1.25</option>
        <option :value="1.5">1.5</option>
        <option :value="1.75">1.75</option>
        <option :value="2">2</option>
    </select>
</template>

<script>
export default {
    props: ['modelValue'],

    emits: ['update:modelValue']
};
</script>	

<template>
<div class="range-group">
    <select 
        class="form-control" 
        v-model="modelValue"
        @change="$emit('update:modelValue', $event.target.value)"
    >
        <option value="0">None</option>
        <option value="1">1</option>
        <option value="2">2</option>
        <option value="3">3</option>
        <option value="4">4</option>
        <option value="5">5</option>
    </select>
    <input 
        type="range" 
        min="0"  
		max="5" 
        step="1" 
        v-model="modelValue"
        class="slider" 
        @change="$emit('update:modelValue', $event.target.value)"
    >
</div>
</template>

<script>
	export default {
        props: ['modelValue'],

        emits: ['update:modelValue'],
	}
</script>	
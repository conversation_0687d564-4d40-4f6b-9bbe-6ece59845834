<template>
<div class="range-group">
    <input 
        type="text" 
        v-model="input" 
        @change="updateSelect($event.target.value)" 
        class="form-control"
    >    
    <input 
        type="range" 
        :min="min" 
		:max="max" 
        :step="steps"  
        class="slider" 
        @input="updateRange($event.target.value)" 
        @change="updateSelect($event.target.value)" 
        ref="range"
    >
</div>
</template>

<script>
	export default {
		props: {
            modelValue: {
                required: true
            },
            min: {
                required: false,
                type: Number,
                default: 0
            },
            max: {
                required: false,
                type: Number,
                default: 9999
            },
            steps: {
                required: false,
                type: Number,
                default: 1,
            }
        },

        emits: ['update:modelValue'],

        created() {
            this.input = this.modelValue;
        },

        methods: {
            updateRange: function(value) {
                this.input = value;
            },

            updateSelect: function(value) {
                this.$emit('update:modelValue', value)
            }
        },

        data() {
            return {
                input: 0
            }
        },
	}
</script>	
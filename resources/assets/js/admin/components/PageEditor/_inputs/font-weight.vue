<template>
<div class="radio-btn-group">
    <div class="radio-btn flex-item-fill">
        <input  
            type="radio" 
            v-model="modelValue"
            :value="400" 
            :class="{'active': modelValue === 400}"
            @change="$emit('update:modelValue', $event.target.value)"
            id="font_weight_400"
        >
        <label class="btn btn-dark" for="font_weight_400">
            Default
        </label>
    </div>
    <div class="radio-btn flex-item-fill">
        <input  
            type="radio" 
            v-model="modelValue"
            :value="700" 
            :class="{'active': modelValue === 700}"
            @change="$emit('update:modelValue', $event.target.value)"
            id="font_weight_700"
        >
        <label class="btn btn-dark" for="font_weight_700">
            Bolder
        </label>
    </div>
</div>	
</template>

<script>
	export default {
        props: ['modelValue'],

        emits: ['update:modelValue'],
	}
</script>	
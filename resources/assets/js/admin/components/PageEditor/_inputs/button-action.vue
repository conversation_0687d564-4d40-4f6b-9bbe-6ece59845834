<template>
    <div>
        <div class="form-group">
            <label>Button Action</label>    
            <select 
                v-model="action" 
                class="form-control" 
            >
                <option value="page">Link to page</option>
                <option value="url">Custom URL</option>
            </select>
        </div>
        <div class="form-group" v-if="action === 'page'">
            <label>Select page</label>    
            <select 
                v-model="input" 
                class="form-control" 
            >
                <option value="/store">Store</option>
                <option value="/register">Register</option>
                <option value="/login">Login</option>
            </select>
        </div>
        <div class="form-group" v-else>
            <label>Custom URL</label>    
            <input type="text" class="form-control" placeholder="/">
        </div> 
    </div>
</template>

<script>
	export default {
        props: ['modelValue'],

        emits: ['update:modelValue'],
        
        computed: {
            input: {
                get() {
                    return this.modelValue
                },
                set(newVal){
                    this.$emit('update:modelValue', newVal)
                }
            }
        },

        data() {
            return {
                action: 'page'
            }
        }
	}
</script>	
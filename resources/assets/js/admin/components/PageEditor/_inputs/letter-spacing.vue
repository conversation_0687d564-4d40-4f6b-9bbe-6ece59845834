<template>
    <div class="select-input">
        <select
            :value="modelValue"
            class="form-control"
            @change="$emit('update:modelValue', $event.target.value)"
        >
            <option value="normal">Normal</option>
            <option value="0.025em">Small</option>
            <option value="0.05em">Medium</option>
            <option value="0.075em">Large</option>
        </select>
    </div>
</template>

<script>
export default {
    props: ['modelValue'],

    emits: ['update:modelValue']
};
</script>	

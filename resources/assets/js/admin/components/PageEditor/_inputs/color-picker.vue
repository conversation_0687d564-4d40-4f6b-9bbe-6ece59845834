<template>           
    <input 
        type="text" 
        v-model="color" 
        class="form-control colorPicker" 
        :id="name"
    >
</template>

<script>
	export default {
        props: ['modelValue', 'name'],

        emits: ['update:modelValue'],

        mounted() {
            let colorPicker = this;
            $("#"+colorPicker.name).spectrum({
                preferredFormat: "hex",
                showInput: true,
                allowEmpty: true,
                change: function(color) {
                    colorPicker.color = color;
                }
            });
        },

        computed: {
            color: {
                get() {
                    let color = getComputedStyle(document.getElementById('page_preview')
                        .contentWindow.document.body)
                        .getPropertyValue(this.modelValue);

                    return !color || color.length === 0 ? this.modelValue : color;
                },
                set(newVal) {
                    this.$emit('update:modelValue', newVal ? newVal.toHexString() : null)
                }
            }
        }
	}
</script>
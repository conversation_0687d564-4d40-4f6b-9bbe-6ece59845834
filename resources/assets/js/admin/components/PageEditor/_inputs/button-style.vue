<template>           
    <select 
        v-model="input" 
        class="form-control" 
    >
        <option value="btn-primary">Primary</option>
        <option value="btn-action">Secondary</option>
        <option value="btn-light">Light</option>
    </select>
</template>

<script>
	export default {
        props: ['modelValue'],

        emits: ['update:modelValue'],
        
        computed: {
            input: {
                get() {
                    return this.modelValue
                },
                set(newVal){
                    this.$emit('update:modelValue', newVal)
                }
            }
        }
	}
</script>	
<template>
<div class="input-group">
    <input 
        type="number" 
        class="form-control" 
        :value="input" 
        @change="input = $event.target.value"
    >
    <span class="input-group-addon">px</span>
</div>
</template>

<script>
	export default {
        props: ['modelValue'],

        emits: ['update:modelValue'],

        computed: {
            input: {
                get() {
                    return this.modelValue
                },
                set(newVal) {
                    var parsed = Number.parseInt(newVal);
                    if ( Number.isNaN(parsed)) {
                        alert('Font size must be an integer.')
                        return false;
                    }
                    this.$emit('update:modelValue', parsed);
                }
            }
        }
	}
</script>	
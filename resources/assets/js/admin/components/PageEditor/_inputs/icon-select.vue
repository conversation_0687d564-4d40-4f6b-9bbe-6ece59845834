<template>
<div>
	<div class="dropdown">
        <button 
			class="icon-preview btn btn-light btn-block dropdown-toggle" 
			type="button" 
            data-toggle="dropdown" 
            v-html="modelValue"
		>
		</button>
        <div class="dropdown-menu">
            <ul class="icon-menu">
                <li><a href="#" @click="setText(1)">1</a></li>
                <li><a href="#" @click="setText(2)">2</a></li>
                <li><a href="#" @click="setText(3)">3</a></li>
                <li v-for="icon in icons" :key="icon.name">
                    <a href="#" @click="setIcon(icon.name)"><i :class="icon.class"></i></a>
                </li>
            </ul>
        </div>     
    </div>
</div>    
</template>

<script>
	export default {
        props: ['modelValue'],

        emits: ['update:modelValue'],
        
        data: function() {
            return {
                    icons: [
                    {class: 'fa fa-map-marker-alt', name: 'map-marker-alt'},
                    {class: 'fa fa-map-pin', name: 'map-pin'},
                    {class: 'fa fa-globe-americas', name: 'globe-americas'},
                    {class: 'fa fa-map-marked-alt', name: 'map-marked-alt'},
                    {class: 'fa fa-globe', name: 'globe'},
                    {class: 'fa fa-truck', name: 'truck'},
                    {class: 'fa fa-truck-moving', name: 'truck-moving'},
                    {class: 'fa fa-truck-loading', name: 'truck-loading'},
                    {class: 'fa fa-shipping-fast', name: 'shipping-fast'},
                    {class: 'fa fa-box', name: 'box'},
                    {class: 'fa fa-boxes', name: 'boxes'},
                    {class: 'fa fa-archive', name: 'archive'},
                    {class: 'fa fa-dolly', name: 'dolly'},
                    {class: 'fa fa-clipboard-check', name: 'clipboard-check'},
                    {class: 'fa fa-clipboard-list', name: 'clipboard-list'},
                    {class: 'fa fa-home', name: 'home'},
                    {class: 'fa fa-building', name: 'building'},
                    {class: 'fa fa-calendar', name: 'calendart'},
                    {class: 'fa fa-calendar-alt', name: 'calendar-alt'},
                    {class: 'fa fa-tag', name: 'tag'},
                    {class: 'fa fa-tags', name: 'tags'},
                    {class: 'fa fa-dollar-sign', name: 'dollar-sign'},
                    {class: 'fa fa-money-check-alt', name: 'money-check-alt'},
                    {class: 'fa fa-credit-card', name: 'credit-card'},
                    {class: 'fa fa-handshake', name: 'handshake'},
                    {class: 'fa fa-thumbs-up', name: 'thumbs-up'},
                    {class: 'fa fa-heart', name: 'heart'},
                    {class: 'fa fa-star', name: 'star'},
                    {class: 'fa fa-shopping-cart', name: 'shopping-cart'},
                    {class: 'fa fa-shopping-basket', name: 'shopping-basket'},
                    {class: 'fa fa-shopping-bag', name: 'shopping-bag'},
                    {class: 'fa fa-cart-plus', name: 'cart-plus'},
                    {class: 'fa fa-gift', name: 'gift'},
                    {class: 'fa fa-smile', name: 'smile'},
                    {class: 'fa fa-user', name: 'user'},
                    {class: 'fa fa-user-alt', name: 'user-alt'},
                    {class: 'fa fa-users', name: 'users'},
                    {class: 'fa fa-user-circle', name: 'user-circle'},
                    {class: 'fa fa-user-tag', name: 'user-tag'},
                ]
            }
        },

        methods: {
            setIcon: function(icon)
            {
                // Use fontawesome icons.
                this.$emit('update:modelValue', '<i class="fa fa-'+icon+'"></i>');
            },

            setText: function(text)
            {
                this.$emit('update:modelValue', text);
            }
        }
	}
</script>

<style scoped>
.dropdown-menu {
    margin-top: -1px;
    max-height: 400px;
    overflow: auto;
    width: 100%;
}

.dropdown-menu li a {
    text-align: center;
    font-size: 2em;
}

.icon-preview {
    font-size: 2em;
}
</style>
<template>
    <div class="range-group form-group">
        <input 
            type="range" 
            :min="min"  
            :max="max" 
            step="1" 
            class="form-control" 
            :value="input" 
            @change="input = $event.target.value" 
        >
        <input 
            type="number" 
            :min="min" 
            class="form-control"  
            :value="input" 
            @change="input = $event.target.value"
        >
    </div>
</template>

<script>
	export default {
		props: {
            modelValue: {
                required: false,
                default: 0
            },
            min: {
                required: false,
                default: 0
            },
            max: {
                required: false,
                default: 256
            }
        },

        emits: ['update:modelValue'],

        computed: {
            input: {
                get() {
                    return this.modelValue
                },
                set(newVal) {
                    var parsed = Number.parseInt(newVal);
                    if ( Number.isNaN(parsed)) {
                        alert('Padding must be an integer.')
                        return false;
                    }
                    this.$emit('update:modelValue', parsed);
                }
            }
        },

        data() {
            return {
                spacingSizes: [0, 4, 8, 12, 16, 24, 32, 48, 64, 96, 128, 192, 256, 384, 512]
            }
        }
	}
</script>	
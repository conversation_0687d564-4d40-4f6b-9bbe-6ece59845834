<template>
<div>
    <label>Vertical Position</label>
    <div class="range-group form-group">
        <input 
            type="range" 
            min="0" 
            max="100" 
            step="1" 
            class="form-control" 
            :value="y_position" 
            @change="y_position = keepInRange($event.target.value)" 
        >
        <input 
            type="number" 
            min="0" 
            max="100" 
            class="form-control"  
            :value="y_position" 
            @change="y_position = keepInRange($event.target.value)"
        >
    </div>

    <label>Horizontal Position</label>
    <div class="range-group form-group">
         <input 
            type="range" 
            min="0" 
            max="100" 
            step="1" 
             class="form-control" 
            :value="x_position" 
            @change="x_position = keepInRange($event.target.value)" 
        >
        <input 
            type="number" 
            min="0" 
            max="100" 
            class="form-control"  
            :value="x_position" 
            @change="x_position = keepInRange($event.target.value)"
        >
    </div>
</div>    
</template>

<script>
	export default {
        props: ['modelValue'],

        emits: ['update:modelValue'],

        created() {
            let coordinates = this.getCoordinatesFromString(this.modelValue);
            this.x_position = coordinates[0];
            this.y_position = coordinates[1];
        },

        watch: {
            x_position() {
                this.$emit('update:modelValue', this.x_position+'% '+this.y_position+'%')
            },
             y_position() {
                this.$emit('update:modelValue', this.x_position+'% '+this.y_position+'%')
            },
        },

        data() {
            return {
                input: 0,
                x_position: 0,
                y_position: 0,
                legacy: {
                    'left top': [0,0],
                    'left center': [0,50],
                    'left bottom': [0,100],
                    'right top': [100,0],
                    'right center': [50,0],
                    'right bottom': [100,0],
                    'center top': [50,0],
                    'center center': [50,50],
                    'center bottom': [50,100],
                }
            }
        },

        methods: {
            getCoordinatesFromString(string)
            {
                let parsedString = string.replace(/%/g, '');
                var legacy = this.legacy[parsedString];
                if(legacy) {
                   return legacy; 
                }

                return parsedString.split(" ");
            },

            keepInRange(value)
            {
                if(value > 100) {
                    return 100;
                }

                if(value < 0) {
                    return 0;
                }

                return value;
            }
        }
	}
</script>   
<template>
<div class="pageEditor" v-if="page">
    <toolbar 
        :page="page" 
        :previewSize="previewSize" 
        :activeToolbarItem="activeToolbarItem" 
        @changePreviewSize="changePreviewSize" 
        @showAddWidget="showPageContent" 
        @showPageLayout="showPageLayout" 
        @showPagePreview="showPagePreview" 
        @showPageSettings="showPageSettings" 
        @showPageActions="showPageActions" 
    ></toolbar>

    <div class="pageEditor__content">
        <page-preview 
            v-if="page" 
            :editing="component" 
            :page="page" 
            :widget="widget" 
            :previewSize="previewSize" 
            @reload="fetchPage" 
        >    
        </page-preview>

        <div class="pageEditor__widgetContent" v-show="component" v-if="widget">
            <component 
                :is="component" 
                :page="page" 
                :widget="widget" 
                :widgetView="widgetView" 
                :widgetElement="widgetElement" 
                :widgetItem="widgetItem" 
                :widgetLibrary="widgetLibrary" 
            ></component>
        </div>
    </div>


    <div class="gc-modal gc-modal-mask" id="renameWidgetModal" @click="hideModal('renameWidgetModal')">
        <div class="gc-modal-wrapper">
            <div class="gc-modal-container" @click.stop>
                
                <div class="gc-modal-header">
                    Rename Widget
                </div>
    
                <div class="gc-modal-body">
                    <label for="rename_widget_field">What would you like to name this widget?</label>
                    <input type="text" v-model="widget.title" class="form-control" id="rename_widget_field">
                </div>
    
                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('renameWidgetModal')">Never Mind</button>
                    <button type="button" class="btn btn-danger" @click="updateWidgetTitle(widget)">Save</button>
                </div>
            </div>
        </div>
    </div>


    <!-- Delete Block Start -->
    <div class="gc-modal gc-modal-mask" id="deleteWidgetModal" @click="hideModal('deleteWidgetModal')">
        <div class="gc-modal-wrapper">
            <div class="gc-modal-container" @click.stop>
                
                <div class="gc-modal-header">
                    Delete Content Widget
                </div>
    
                <div class="gc-modal-body">
                    Are you sure you want to delete this {{ widget.title }} content widget?
                </div>
    
                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('deleteWidgetModal')">Cancel</button>
                    <button type="button" class="btn btn-danger" @click="destroyWidget(widget)">Delete Widget</button>
                </div>
            </div>
        </div>
    </div>
    <!-- Delete Block End -->    

    <!-- Duplicate Page -->
    <div class="gc-modal gc-modal-mask" id="duplicatePageModal" @click="hideModal('duplicatePageModal')">
        <div class="gc-modal-wrapper">
            <div class="gc-modal-container" @click.stop>
                
                <div class="gc-modal-header">
                    Duplicate Page
                </div>
    
                <div class="gc-modal-body">
                    Make a duplicate of this page?
                </div>
    
                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('duplicatePageModal')">Never Mind</button>
                    <button type="button" class="btn btn-action" @click="duplicate(page)">Duplicate Page</button>
                </div>
            </div>
        </div>
    </div>
    <!-- Duplicate Page End -->
</div>
</template>

<script>
    import Toolbar from './Toolbar.vue';
    import PageStructure from './PageStructure.vue';
    import AddWidgets from './AddWidgets.vue';
    import WidgetEditor from './WidgetEditor.vue';
    import PageSettings from './PageSettings/PageSettings.vue';
    import PagePreview from './PagePreview.vue';

    import WidgetLibrary from './WidgetLibrary.vue';

	export default {
		components: {
            Toolbar,
            AddWidgets,
            PageStructure,
            WidgetEditor,
            PageSettings,
            PagePreview,
            WidgetLibrary
    	},

		props: ['id'],

		created() {
            this.fetchPage();
            this.fetchWidgetLibrary();

            // Page Events
            eventHub.on('pageEditor:toggleView', (activeToolbarItem, component) => {
                this.activeToolbarItem = activeToolbarItem;
                this.component = component;
            });

            eventHub.on('pageEditor:loadPage', this.fetchPage);

            eventHub.on('pageEditor:modified', this.update);

            eventHub.on('pageEditor:changePreviewSize', size => {
                this.previewSize = size;
            });
            
            // Widget Events
            window.addEventListener('message', event => {
                 if (event.data['widgetAction']) {
                    if (event.data['widgetAction'] === 'removeWidget') {
                        this.widget = this.findWidgetById(event.data['editWidget']);
                        return this.showModal('deleteWidgetModal');
                    }



                    if (event.data['widgetAction'] === 'addWidget') {
                        this.widget = this.findWidgetById(event.data['editWidget']);
                        return this.showModal('widgetLibraryModal');
                    }
                }

                if (event.data['addWidgetToBottom']) {
                    this.showPageContent();
                    this.widget = {};
                }

                if (event.data['editWidget']) {
                    return this.editWidget(
                        event.data['editWidget'], 
                        event.data['editElement'], 
                        event.data['editItem']
                    );
                }
            });

            eventHub.on('pageEditor:editWidget', widget => { this.editWidget(widget.id) });

            eventHub.on('pageEditor:editWidgetElement', (element, elementTitle) => {
                if ( ! this.widget) {
                    return console.error('No widget set')
                }

                this.widgetElement = element
            });

            eventHub.on('pageEditor:setWidgetElementTitle', elementTitle => { this.widgetElementTitle = elementTitle });

            eventHub.on('pageEditor:storeWidget', widget => { this.editWidget(widget.id) });

            eventHub.on('pageEditor:updateWidget', this.updateWidget);

            eventHub.on('pageEditor:closeWidget', widget => {
                this.updateWidget(widget)
                this.showPagePreview()
                this.widget = {};
            });

            eventHub.on('pageEditor:duplicateWidget', widget => { this.duplicateWidget(widget) });

            eventHub.on('pageEditor:renameWidget', widget => {
                this.widget = widget;
                this.showModal('renameWidgetModal');
            });

            eventHub.on('pageEditor:deleteWidget', widget => {
                this.widget = widget;
                this.showModal('deleteWidgetModal');
            });

            eventHub.on('pageEditor:destroyWidget', widget => {
                this.destroy(widget);
            });

            eventHub.on('widgetEditor:unmounted', widget => {
                this.widget = {};
            });
		},

    	data: function() {
    		return {
                page: null,
                settings: [],
                widgetLibrary: [],
                component: null,
                activeToolbarItem: 'preview',
                widget: {},
                widgetView: null,
                widgetElement: null,
                widgetItem: null,
                widgetElementTitle: null,
                previewSize: 'desktop',
    		}
    	},

    	methods: {
            fetchPage() {
                axios.get(`/api/pages/${this.id}`)
                    .then(({ data }) => {
                        this.page = data.data;
                    })
                    .catch(error => {});
            },

            getSettings() {
                axios.get('/api/settings')
                    .then(({ data }) => {
                        this.settings = data;
                    })
                    .catch(error => {});
            },

            fetchWidgetLibrary() {
                axios.get('/api/content-widget-library')
                    .then(({ data }) => {
                        this.widgetLibrary = data;
                    })
                    .catch(error => {
                        eventHub.emit('error', error);
                    });
            },


            update() {
                axios.put(`/api/pages/${this.page.id}`, {
                    needs_published: true,
                })
                    .then(({ data }) => {
                        eventHub.emit('hideProgressBar');
                        this.page = data;
                        eventHub.emit('pagePreview:refresh')
                    })
                    .catch(error => {
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('error', error);
                    });
            },

            duplicate(page) {
                axios.post(`/api/duplicate-pages/${this.id}`)
                    .then(({ data }) => {
                        window.location.replace(`/admin/pages/${data.page.id}/edit`);
                    })
                    .catch(error => {});
            },

            destroy(page) {
                axios.delete(`/api/pages/${this.id}`)
                    .then(() => {
                        window.location.replace("/admin/pages");
                    }).catch(error => {});
            },

            // Widget methods
            editWidget(widgetId, elementId, itemId) {
                this.widgetElement = elementId !== undefined ? elementId : 'home';
                this.widgetItem = itemId !== undefined ? itemId : null;
                
                if (this.widgetIsLoaded(widgetId)) return;

                eventHub.emit('showProgressBar');

                axios.get(`/api/pages/${this.page.id}/widgets/${widgetId}`)
                    .then(({ data }) => {
                        this.component = 'WidgetEditor';
                        this.widget = data;
                        this.view = this.widget.template;
                        eventHub.emit('pagePreview:scrollToWidget', widgetId);
                        eventHub.emit('hideProgressBar');
                    })
                    .catch(error => {
                        eventHub.emit('hideProgressBar');
                    });
            },

            updateWidget(widget) {
                eventHub.emit('showProgressBar')

                axios.put(`/api/pages/${this.page.id}/widgets/${widget.id}`, widget)
                    .then(response => {
                        eventHub.emit('hideProgressBar')
                        eventHub.emit('pagePreview:refresh')
                    })
                    .catch(error => {
                        eventHub.emit('hideProgressBar')
                        eventHub.emit('error', error.response)
                    });
            },

            updateWidgetTitle(widget) {
                this.hideModal('renameWidgetModal')
                this.updateWidget({'id': widget.id, 'title': widget.title})
            },

            duplicateWidget(widget) {
                eventHub.emit('showProgressBar');

                axios.post(`/api/pages/${this.page.id}/widgets/${widget.id}/duplicate`)
                    .then(({ data }) => {
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('pagePreview:refresh', data)
                    })
                    .catch(function(error) {
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('error', error);
                    });
            },

            destroyWidget: function(widget) {
                this.page.widgets.splice(this.page.widgets.indexOf(widget), 1)

                axios.delete(`/api/pages/${this.page.id}/widgets/${widget.id}`)
                    .then(() => {
                        eventHub.emit('hideProgressBar')
                        this.widget = {}
                        if (this.component !== 'PageStructure' || ! this.page.widgets.length) {
                            this.showPagePreview()
                        }

                        this.hideModal('deleteWidgetModal')
                        eventHub.emit('pagePreview:refresh')
                    })
                    .catch(error => {
                        eventHub.emit('hideProgressBar');
                        eventHub.emit('error', error);
                    });
            },

            // Helper methods
            showPageContent() {
                this.activeToolbarItem = 'content';
                this.component = 'AddWidgets';
            },

            showPageLayout() {
                this.activeToolbarItem = 'layout';
                this.component = 'PageStructure';
            },

            showPagePreview(size) {
                this.activeToolbarItem = 'preview';
                this.component = null;
            },

            showPageSettings() {
                this.activeToolbarItem = 'settings';
                this.component = 'PageSettings';
            },

            showPageActions() {
                this.activeToolbarItem = 'actions';
                this.component = 'PageActions';
            },

            changePreviewSize(size) {
                this.previewSize = size;
            },

            findWidgetById(id) {
                for (let i = 0; i < this.page.widgets.length; i++) {
                    if (this.page.widgets[i].id === id) {
                        return this.page.widgets[i];
                    }
                }
            },

            widgetIsLoaded(widgetId) {
                return this.widget.id === widgetId;
            }
    	}
	}
</script>
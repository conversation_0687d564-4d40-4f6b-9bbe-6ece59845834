<template>
    <div 
        class="pagePreview__container" 
        :class="[editingClass, previewSize]"
    >
        <iframe 
            name="page_preview" 
            id="page_preview" 
            width="100%"  
            sandbox="allow-same-origin allow-scripts" 
            :src="'/page-preview/'+this.page.slug" 
            @load="$emit('previewLoaded')" 
            seamless 
        >
        </iframe>
    </div>
</template>

<script>
	export default {
        props: ['page', 'editing', 'previewSize', 'widget'],

        emits: ['previewLoaded'],

        mounted() {
            eventHub.on('pagePreview:refresh', this.refresh);
            eventHub.on('pagePreview:scrollToWidget', this.scrollToWidget);
        },

        data() {
            return {
                scrollPositionY: 0
            }
        },

        computed: {
            editingClass() {
                return this.editing ? 'editing' : null;
            }
        },

        methods: {
            refresh() {
                eventHub.emit('showProgressBar');

                const previewArea = document.getElementById('page_preview')
                    .contentWindow
                    .document.getElementById('pageWidgets');

                this.scrollPositionY = document.getElementById('page_preview').contentWindow.scrollY;

                const page = { ...this.page }

                axios.get(`/api/pages/${page.id}/content`)
                    .then(({ data }) => {
                        if (navigator.userAgent.toLowerCase().indexOf('firefox') > -1){
                            document.getElementById('page_preview').contentWindow.location.reload();
                        } else {
                            previewArea.innerHTML = data.content;
                            document.getElementById('page_preview').contentWindow.document.getElementById('pageStyles').href = '/theme/pages/'+this.page.id+'/page.css?id='+data.timestamp;
                        }

                        document.getElementById('page_preview').contentWindow.scrollTo(0, this.scrollPositionY);
                        eventHub.emit('hideProgressBar');
                    })
                    .catch(error => { eventHub.emit('hideProgressBar'); });
            },

            scrollToWidget(widgetId) {
                let behavior = this.page.widgets.length <= 10 ? 'smooth' : 'auto';

                let element = document.getElementById('page_preview')
                    .contentWindow.document
                    .getElementById('pageWidget--'+widgetId);

                if (element) {
                    element.scrollIntoView({ block: 'start', behavior: behavior });
                } else {
                    setTimeout(() => {
                        let element = document.getElementById('page_preview')
                            .contentWindow.document
                            .getElementById('pageWidget--'+widgetId);

                        if (element) {
                            element.scrollIntoView({ block: 'start', behavior: behavior });
                        }
                     }, 300);
                }
            },
        }
    }
</script>        

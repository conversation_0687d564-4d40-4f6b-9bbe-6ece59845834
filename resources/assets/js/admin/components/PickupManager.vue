<template>
    <div>
        <div class="pickupManager__toolbar">
            <ul class="breadcrumb-toolbar">
                <li><a href="/admin/pickup-manager">Schedules</a></li>
                <li><a :href="'/admin/pickup-manager/'+schedule.slug">{{ schedule.title }}</a></li>
                <li class="active">{{ pickup.title }} <span class="badge">{{ orders.length }}</span></li>
            </ul>
        </div>
        <div v-if="message" class="pickupManager__statusBar">
            <i class="fa fa-spinner fa-lg fa-spin"></i> {{ message }}
        </div>

        <transition name="fade">
            <div v-if="showPaymentForm" class="pickupManager__payment-form">

                <div class="pickupManager__orderDetails-close">
                    <button class="btn btn-alt" @click="showPaymentForm = false">
                        <i class="fa fa-times"></i> Close
                    </button>
                </div>

                <!-- Payment Screen 1 -->
                <div v-if="paymentScreen == 1">
                    <h4 class="text-center">Enter Your Credit Card</h4>

                    <form id="paymentForm" novalidate @submit.prevent="getStripeToken">
                        <div class="form-group">
                            <label>Name On Card</label>
                            <input v-model="nameOnCard" :value="fullName" autocorrect="off" class="form-control" data-stripe="name" type="text"/>
                        </div>

                        <div class="form-group">
                            <label>Card Number</label>
                            <input autocorrect="off" class="form-control" data-stripe="number" novalidate pattern="\d*" size="20" type="text"/>
                        </div>

                        <div class="form-group">
                            <label>Security Code <small>(Found on back of card)</small></label>
                            <input autocorrect="off" class="form-control" data-stripe="cvc" novalidate pattern="\d*" size="4" type="text"/>
                        </div>

                        <div class="form-group">
                            <label>Exipration Month</label>
                            <select class="form-control" data-stripe="exp-month">
                                <option value="01">January</option>
                                <option value="02">February</option>
                                <option value="03">March</option>
                                <option value="04">April</option>
                                <option value="05">May</option>
                                <option value="06">June</option>
                                <option value="07">July</option>
                                <option value="08">August</option>
                                <option value="09">September</option>
                                <option value="10">October</option>
                                <option value="11">November</option>
                                <option value="12">December</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <label>Exipration Year</label>
                            <select class="form-control" data-stripe="exp-year">
                                <option v-for="year in years" :value="year">{{ year }}</option>
                            </select>
                        </div>

                        <div class="form-group">
                            <input :disabled="disabled" class="btn btn-success btn-block btn-lg" type="submit" value="Save Card"/>
                        </div>
                    </form>
                </div>

                <!-- Payment Screen 2 -->
                <div v-if="paymentScreen == 2" class="text-center">
                    <h4>Charge {{ fullName }}'s order to their default card?</h4>
                    <button :disabled="disabled" class="btn btn-danger btn-lg" @click="chargeCardOnFile()">
                        Charge {{ $filters.currency($filters.cents(activeOrder.total)) }}
                    </button>
                    <button class="btn btn-default btn-lg" @click="showPaymentForm = false">Close</button>
                </div>
            </div>
        </transition>

        <transition mode="out-in" name="fade">
            <order-details
                v-if="activeOrder"
                :order="activeOrder"
                :weightuom="weightuom"
                @orderToggled="toggleOrder"
                @pickupOrder="pickupOrder"
                @showChargeModal="showChargeModal"
                @showPaymentModal="showPaymentModal"
                @updateOrder="updateOrder"
            ></order-details>

            <div v-else>
                <div class="pickupManager__toolbar">
                    <div>
                        <label>Picked Up</label>
                        <select v-model="filterPickedUp" class="form-control">
                            <option value="4">Picked Up</option>
                            <option value="3">Not Picked Up</option>
                            <option value="all">All</option>
                        </select>
                    </div>

                    <div>
                        <label>Payment</label>
                        <select v-model="filterPaid" class="form-control">
                            <option value="1">Paid</option>
                            <option value="0">Not Paid</option>
                            <option value="all">All</option>
                        </select>
                    </div>

                    <div>
                        <label>Flagged</label>
                        <select v-model="filterFlagged" class="form-control">
                            <option value="1">Flagged</option>
                            <option value="0">Not Flagged</option>
                            <option value="all">All</option>
                        </select>
                    </div>

                    <div>
                        <label>Search</label>
                        <input v-model="filterSearch" class="form-control" type="text">
                    </div>
                </div>
                <ul class="pickupManager__list">
                    <li v-for="order in filteredOrders">
                        <div class="pickupManager__list-item--order">

                            <div class="pickupManager__checkbox">
                                <i :class="{'fa-check-square-o checked': order.status_id == 4 ,'fa-square-o text-muted': order.status_id != 4}" class="fa" @click="pickupOrder(order)"></i>
                            </div>

                            <div class="pickupManager__customer" @click="toggleOrder(order)">
                                <h2>{{ order.customer_first_name }} {{ order.customer_last_name }} - #{{ order.id }}</h2>
                                <span v-if="order.paid" class="label label-action">Paid</span>
                                <span v-else="order.paid" class="label label-danger">NOT PAID</span>
                                <span v-if="order.first_time_order" class="label label-light">New Customer!</span>
                                <span class="label label-light">{{ order.containers }} Frozen</span>
                                <span v-if="order.containers_2" class="label label-light">{{ order.containers_2 }} Fresh</span>
                                <span v-if="order.flagged" class="label label-light"><i class="fa fa-flag"></i> Flagged</span>
                            </div>
                        </div>
                    </li>
                </ul>
            </div>
        </transition>
    </div>
</template>

<script>
import OrderDetails from './PickupManager/OrderDetails.vue';

export default {
    props: ['pickup', 'schedule', 'weightuom'],

    components: {
        OrderDetails
    },

    created: function() {
        this.getOrdersForPickup();
    },

    data: function() {
        return {
            orders: [],
            showOrder: 0,
            showPaymentForm: false,
            paymentScreen: 0,
            activeOrder: false,
            nameOnCard: '',
            disabled: false,
            message: '',
            filterPaid: 'all',
            filterPickedUp: 'all',
            filterFlagged: 'all',
            filterSearch: ''
        };
    },

    computed: {
        filteredOrders: function() {
            var self = this;
            var orders = this.orders.filter(function(order) {
                if (order.customer_first_name.concat(' ' + order.customer_last_name).toLowerCase().indexOf(self.filterSearch.toLowerCase()) !== -1 || String(order.id).indexOf(self.filterSearch) !== -1) {
                    return true;
                }

            });

            if (this.filterPaid != 'all') {
                orders = orders.filter(function(order) {
                    return order.paid == self.filterPaid;
                });
            }

            if (this.filterPickedUp != 'all') {
                orders = orders.filter(function(order) {
                    return order.status_id == self.filterPickedUp;
                });
            }


            if (this.filterFlagged != 'all') {
                orders = orders.filter(function(order) {
                    return order.flagged == self.filterFlagged;
                });
            }

            return orders;
        },

        years: function() {
            var currentYear = new Date().getFullYear();
            var years = [];
            for (var i = 0; i < 10; i++) {
                years.push(currentYear + i);
            }

            return years;
        },

        fullName: function() {
            return this.activeOrder.customer_first_name + ' ' + this.activeOrder.customer_last_name;
        }
    },

    methods: {
        updateOrderCount: function(count) {
            var orderCounter = document.getElementById('orderCount');
            orderCounter.innerHTML = count;

        },

        getOrdersForPickup: function() {
            axios.get('/api/pickup-manager/' + this.pickup.id + '/orders').then(function(response) {
                this.orders = response.data;
                this.updateOrderCount(this.orders.length);
            }.bind(this)).catch(function(error) {

            });
        },

        updateOrder: function(order) {
            this.disabled = true;
            this.message = 'Saving order...';
            var data = {
                packing_notes: order.packing_notes,
                payment_id: order.payment_id,
                paid: order.paid,
                flagged: order.flagged
            };

            this.sendPayload(order.id, data);
        },

        pickupOrder: function(order) {
            order.picked_up = !order.picked_up;

            if (order.picked_up) {
                order.status_id = 4;
            } else {
                order.status_id = 3;
            }

            var data = {
                picked_up: order.picked_up,
                status_id: order.status_id
            };

            this.sendPayload(order.id, data);
        },

        flagOrder: function(order) {
            order.flagged = !order.flagged;
            var data = {
                flagged: order.flagged
            };

            this.sendPayload(order.id, data);
        },

        toggleOrder: function(order) {
            if (order.id == this.activeOrder.id) {
                this.activeOrder = false;
            } else {
                this.activeOrder = order;
            }
        },

        showPaymentModal: function() {
            this.paymentScreen = 1;
            this.showPaymentForm = true;
        },

        showChargeModal: function() {
            this.paymentScreen = 2;
            this.showPaymentForm = true;
        },

        toggelPayment: function(order, paymentScreen) {
            if (this.showPaymentForm) {
                this.showPaymentForm = false;
                this.paymentScreen = 0;
                this.activeOrder = {};
                this.nameOnCard = '';
            } else {
                this.paymentScreen = paymentScreen;
                this.showPaymentForm = true;
                this.activeOrder = order;
            }
        },

        sendPayload: function(id, data) {
            axios.put('/api/order/' + id + '/update', data).then(function(response) {
                this.message = '';
                this.disabled = false;
            }.bind(this)).catch(function(error) {

            });
        },

        getStripeToken: function() {
            this.disabled = true;
            var form = document.getElementById('paymentForm');
            Stripe.card.createToken(form, this.saveCreditCard);
            this.message = 'Saving card...';
        },

        saveCreditCard: function(status, response) {
            if (response.error) {
                alert(response.error.message);
                this.disabled = false;
                this.message = '';
            } else {

                // response contains id and card, which contains additional card details
                var token = response.id;

                var payload = {
                    name: this.nameOnCard,
                    stripeToken: token
                };

                axios.post('/api/user/' + this.activeOrder.customer_id + '/update-card', payload).then(function(response) {
                    this.disabled = false;
                    this.showPaymentForm = false;
                    this.message = '';
                }.bind(this)).catch(function(error) {
                    this.disabled = false;
                    alert('There was an error adding your card.');
                }.bind(this));
            }
        },

        chargeCardOnFile: function() {
            if (this.activeOrder.paid) {
                return alert('This order has already been marked as paid');
            } else {
                var payload = {};
                this.disabled = true;
                this.message = 'Charging card on file...';

                axios.post('/api/order/' + this.activeOrder.id + '/charge-customer', payload).then(function(response) {
                    this.disabled = false;
                    this.activeOrder.paid = true;
                    this.showPaymentForm = false;
                    this.message = '';
                }.bind(this)).catch(function(error) {
                    this.disabled = false;
                    this.message = '';
                });
            }
        }
    }

};

</script>


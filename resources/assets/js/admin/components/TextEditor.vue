<template>
    <div>
        <textarea
            :id="name+'Editor'"
            :name="name"
            :rows="rows"
            :value="content"
            @input="emit('input', $event.target.value)"
        ></textarea>
        <media-browser
            ref="mediaBrowser"
            @insertPhoto="insertPhoto"
        ></media-browser>
    </div>
</template>

<script>
import MediaBrowser from './MediaBrowser/MediaBrowser.vue';

export default {
    props: {
        block: { type: Object },
        offset: {
            type: Number,
            default: 63
        },
        fixed: {
            type: Boolean,
            default: true
        },
        content: {
            type: String,
            required: false
        },
        name: {
            type: String,
            required: true
        },
        rows: {
            type: Number,
            default: 10
        },
        minHeight: {
            type: String,
            default: '400px'
        },
        disableMedia: {
            type: Boolean,
            default: false
        },
        characterLimit: {
            type: Number,
            default: null
        }
    },

    emits: ['loaded', 'input', 'toggle'],

    components: {
        MediaBrowser
    },

    data() {
        const component = this;
        const pluginsArray = ['alignment', 'divider', 'table', 'fullscreen', 'counter'];
        const buttonsArray = ['format', 'bold', 'italic', 'alignment', 'link', 'lists', 'redo', 'undo', 'html', 'fullscreen'];

        if (!this.disableMedia) {
            pluginsArray.value = pluginsArray.push('video', 'photo_manager');
            buttonsArray.value = pluginsArray.push('image-button', 'video');
        }

        if (this.characterLimit) {
            pluginsArray.value = pluginsArray.push('limiter');
        }

        return {
            settings: {
                buttons: buttonsArray,
                plugins: pluginsArray,
                limiter: this.characterLimit,
                formatting: ['p', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6'],
                pastePlainText: false,
                imageResizable: true,
                imagePosition: {
                    'left': 'image-left',
                    'right': 'image-right',
                    'center': 'image-center'
                },
                imageFloatMargin: '32px',
                script: false,
                structure: true,
                toolbarFixed: this.fixed,
                toolbarFixedTopOffset: this.offset,
                minHeight: this.minHeight,
                toolbarOverflow: true,
                callbacks: {
                    blur(e) {
                        component.$emit('input', this.api('module.source.getCode'));
                    }
                }
            },
            redactor: null
        };
    },

    mounted() {
        const component = this;

        $R.add('plugin', 'fullscreen', {
            translations: {
                en: { 'fullscreen': 'Fullscreen' }
            },

            init(app) {
                this.app = app;
                this.opts = app.opts;
                this.lang = app.lang;
                this.$win = app.$win;
                this.$doc = app.$doc;
                this.$body = app.$body;
                this.editor = app.editor;
                this.toolbar = app.toolbar;
                this.container = app.container;
                this.selection = app.selection;

                // local
                this.isOpen = false;
            },

            start() {
                const button = this.toolbar.addButton('fullscreen', {
                    title: this.lang.get('fullscreen'),
                    api: 'plugin.fullscreen.toggle'
                });

                button.setIcon('<i class="re-icon-expand"></i>');

                this.isTarget = (this.opts.toolbarFixedTarget !== document);
                this.$target = (this.isTarget) ? $R.dom(this.opts.toolbarFixedTarget) : this.$body;

                if (this.opts.fullscreen) this.toggle();

            },

            toggle() {
                return this.isOpen ? this.close() : this.open();
            },

            open() {
                this._createPlacemarker();
                this.selection.save();

                const $container = this.container.getElement();
                const $editor = this.editor.getElement();
                const $html = (this.isTarget) ? $R.dom('body, html') : this.$target;

                if (this.opts.toolbarExternal) this._buildInternalToolbar();

                this.$target.prepend($container);
                this.$target.addClass('redactor-body-fullscreen');

                $container.addClass('redactor-box-fullscreen');
                if (this.isTarget) $container.addClass('redactor-box-fullscreen-target');

                $html.css('overflow', 'hidden');

                if (this.opts.maxHeight) $editor.css('max-height', '');
                if (this.opts.minHeight) $editor.css('min-height', '');

                this._resize();
                this.$win.on('resize.redactor-plugin-fullscreen', this._resize.bind(this));
                this.$doc.scrollTop(0);

                const button = this.toolbar.getButton('fullscreen');
                button.setIcon('<i class="re-icon-retract"></i>');

                this.selection.restore();
                this.isOpen = true;
                this.opts.zindex = 1051;
            },

            close() {
                this.isOpen = false;
                this.opts.zindex = false;
                this.selection.save();

                const $container = this.container.getElement();
                const $editor = this.editor.getElement();
                const $html = $R.dom('body, html');

                if (this.opts.toolbarExternal) this._buildExternalToolbar();

                this.$target.removeClass('redactor-body-fullscreen');
                this.$win.off('resize.redactor-plugin-fullscreen');
                $html.css('overflow', '');

                $container.removeClass('redactor-box-fullscreen redactor-box-fullscreen-target');
                $editor.css('height', 'auto');

                if (this.opts.minHeight) $editor.css('minHeight', this.opts.minHeight);
                if (this.opts.maxHeight) $editor.css('maxHeight', this.opts.maxHeight);

                const button = this.toolbar.getButton('fullscreen');
                button.setIcon('<i class="re-icon-expand"></i>');

                this._removePlacemarker($container);
                this.selection.restore();
            },

            _resize() {
                const $editor = this.editor.getElement();
                const height = this.$win.height();

                $editor.height(height);
            },

            _buildInternalToolbar() {
                const $container = this.container.getElement();
                const $wrapper = this.toolbar.getWrapper();
                const $toolbar = this.toolbar.getElement();

                $wrapper.addClass('redactor-toolbar-wrapper');
                $wrapper.append($toolbar);

                $toolbar.removeClass('redactor-toolbar-external');
                $container.prepend($wrapper);
            },

            _buildExternalToolbar() {
                const $wrapper = this.toolbar.getWrapper();
                const $toolbar = this.toolbar.getElement();

                this.$external = $R.dom(this.opts.toolbarExternal);

                $toolbar.addClass('redactor-toolbar-external');
                this.$external.append($toolbar);

                $wrapper.remove();
            },

            _createPlacemarker() {
                const $container = this.container.getElement();

                this.$placemarker = $R.dom('<span />');
                $container.after(this.$placemarker);
            },

            _removePlacemarker($container) {
                this.$placemarker.before($container);
                this.$placemarker.remove();
            }
        });

        $R.add('plugin', 'photo_manager', {
            init(app) {
                this.app = app;

                // define toolbar service
                this.toolbar = app.toolbar;
            },

            start() {
                // add the button to the toolbar
                const $button = this.toolbar.addButton('image-button', {
                    title: 'Image',
                    api: 'plugin.photo_manager.toggle'
                });

                $button.setIcon('<i class="far fa-image"></i>');
            },

            toggle() {
                component.$refs.mediaBrowser.toggle();
            }
        });

        $R.add('plugin', 'alignment', {
            translations: {
                en: {
                    'align': 'Align',
                    'align-left': 'Align Left',
                    'align-center': 'Align Center',
                    'align-right': 'Align Right',
                    'align-justify': 'Align Justify'
                }
            },

            init(app) {
                this.app = app;
                this.lang = app.lang;
                this.block = app.block;
                this.toolbar = app.toolbar;
            },

            start() {
                const dropdown = {};

                dropdown.left = { title: this.lang.get('align-left'), api: 'plugin.alignment.set', args: 'left' };
                dropdown.center = { title: this.lang.get('align-center'), api: 'plugin.alignment.set', args: 'center' };
                dropdown.right = { title: this.lang.get('align-right'), api: 'plugin.alignment.set', args: 'right' };
                dropdown.justify = { title: this.lang.get('align-justify'), api: 'plugin.alignment.set', args: 'justify' };

                const $button = this.toolbar.addButton('alignment', { title: this.lang.get('align') });
                $button.setIcon('<i class="re-icon-alignment"></i>');
                $button.setDropdown(dropdown);
            },

            set(type) {
                if (type === 'left') {
                    return this._remove();
                }

                this.block.toggle({
                    style: { 'text-align': type }
                });
            },

            _remove() {
                this.block.remove({ style: 'text-align' });
            }
        });

        $R.add('plugin', 'video', {
            translations: {
                en: {
                    'video': 'Video',
                    'video-html-code': 'Video Embed Code or Youtube/Vimeo Link'
                }
            },

            modals: {
                'video':
                    '<form action=""> \
                        <div class="form-item"> \
                            <label for="modal-video-input">## video-html-code ## <span class="req">*</span></label> \
                            <textarea id="modal-video-input" name="video" style="height: 160px;"></textarea> \
                        </div> \
                    </form>'
            },

            init(app) {
                this.app = app;
                this.lang = app.lang;
                this.opts = app.opts;
                this.toolbar = app.toolbar;
                this.component = app.component;
                this.insertion = app.insertion;
                this.inspector = app.inspector;
            },

            // messages
            onmodal: {
                video: {
                    opened($modal, $form) {
                        $form.getField('video').focus();
                    },
                    insert($modal, $form) {
                        this._insert($form.getData());
                    }
                }
            },

            oncontextbar(e, contextbar) {
                const data = this.inspector.parse(e.target);

                if (data.isComponentType('video')) {
                    const node = data.getComponent();

                    contextbar.set(e, node, {
                        'remove': {
                            title: this.lang.get('delete'),
                            api: 'plugin.video.remove',
                            args: node
                        }
                    }, 'bottom');
                }

            },

            start() {
                const $button = this.toolbar.addButtonAfter('image', 'video', {
                    title: this.lang.get('video'),
                    api: 'plugin.video.open'
                });

                $button.setIcon('<i class="re-icon-video"></i>');
            },

            open() {
                this.app.api('module.modal.build', {
                    title: this.lang.get('video'),
                    width: '600px',
                    name: 'video',
                    handle: 'insert',
                    commands: {
                        insert: { title: this.lang.get('insert') },
                        cancel: { title: this.lang.get('cancel') }
                    }
                });
            },

            remove(node) {
                this.component.remove(node);
            },

            _insert(data) {
                this.app.api('module.modal.close');

                if (data.video.trim() === '') {
                    return;
                }

                // parsing
                data.video = this._matchData(data.video);

                // inserting
                if (this._isVideoIframe(data.video)) {
                    const $video = this.component.create('video', data.video);
                    this.insertion.insertHtml($video);
                }
            },

            _isVideoIframe(data) {
                return (data.match(/<iframe|<video/gi) !== null);
            },

            _matchData(data) {
                const iframeStart = '<iframe style="width: 500px; height: 281px;" src="';
                const iframeEnd = '" frameborder="0" allowfullscreen></iframe>';

                if (this._isVideoIframe(data)) {
                    const allowed = ['iframe', 'video', 'source'];
                    const tags = /<\/?([a-z][a-z0-9]*)\b[^>]*>/gi;

                    data = data.replace(tags, function($0, $1) {
                        return (allowed.indexOf($1.toLowerCase()) === -1) ? '' : $0;
                    });
                }

                if (data.match(this.opts.regex.youtube)) {
                    data = data.replace(this.opts.regex.youtube, iframeStart + '//www.youtube.com/embed/$1' + iframeEnd);
                } else if (data.match(this.opts.regex.vimeo)) {
                    data = data.replace(this.opts.regex.vimeo, iframeStart + '//player.vimeo.com/video/$2' + iframeEnd);
                }

                return data;
            }
        });

        $R.add('class', 'video.component', {
            mixins: ['dom', 'component'],

            init(app, el) {
                this.app = app;
                return (el && el.cmnt !== undefined) ? el : this._init(el);
            },

            _init(el) {
                if (typeof el !== 'undefined') {
                    const $wrapper = $R.dom(el).closest('figure');

                    if ($wrapper.length !== 0) {
                        this.parse($wrapper);
                    } else {
                        this.parse('<figure>');
                        this.append(el);
                    }
                } else {
                    this.parse('<figure>');
                }

                this._initWrapper();
            },

            _initWrapper() {
                this.addClass('redactor-component');
                this.attr({
                    'class': 'video-container',
                    'data-redactor-type': 'video',
                    'tabindex': '-1',
                    'contenteditable': false
                });
            }
        });

        this.$nextTick(() => {
            this.redactor = $R('#' + this.name + 'Editor', this.settings);
            this.$emit('loaded');
        });
    },

    methods: {
        insertPhoto(photo) {
            this.redactor.insertion.insertHtml('<img src="' + photo.path + '" alt="' + photo.title + '">');
        }
    }
};
</script>

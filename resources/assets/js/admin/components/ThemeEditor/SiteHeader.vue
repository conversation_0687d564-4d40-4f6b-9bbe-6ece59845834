<template>
    <div>
        <ul class="themeEditorWidget__navigation">
            <li><a href="#" @click="$emit('loadSection', 'Navigation')"><i class="fa fa-chevron-left"></i> Back</a></li>
        </ul>
        <div class="themeEditor__settingControls">
            <h2>Header</h2>
            <div class="form-group">
                <label>Header Background Color</label>
                <input id="headerBgColor" v-model="theme.settings.header_bg_color" class="form-control colorpicker" type="text">
            </div>

            <hr>

            <div class="form-group">
                <label>Show Announcement Bar</label>
                <div class="radio">
                    <label>
                        <input
                            v-model="show_announcement_bar"
                            :value="true"
                            name="show_announcement_bar"
                            type="radio"
                            @change="$emit('set', {key: 'show_announcement_bar', value: true})"
                        > Show
                    </label>
                    <label>
                        <input
                            v-model="show_announcement_bar"
                            :value="false"
                            name="show_announcement_bar"
                            type="radio"
                            @change="$emit('set', {key: 'show_announcement_bar', value: false})"
                        > Hide
                    </label>
                </div>
            </div>
            <div v-show="show_announcement_bar">
                <div class="form-group">
                    <label>Announcement Bar Text</label>
                    <textarea
                        v-model="theme.settings.announcement_bar_content"
                        class="form-control"
                        @change="$emit('set', {key: 'announcement_bar_content', value: $event.target.value})"
                    ></textarea>
                </div>

                <div class="form-group">
                    <label>Background color</label>
                    <input
                        id="announcementBarBgColor"
                        :value="theme.settings.announcement_bar_bg_color"
                        class="form-control colorpicker"
                        type="text"
                    >
                </div>

                <div class="form-group">
                    <label>Text color</label>
                    <input
                        id="announcementBarTextColor"
                        :value="theme.settings.announcement_bar_text_color"
                        class="form-control colorpicker"
                        type="text"
                    >
                </div>

                <div class="form-group">
                    <label>Link color</label>
                    <input
                        id="announcementBarLinkColor"
                        :value="theme.settings.announcement_bar_link_color"
                        class="form-control colorpicker"
                        type="text"
                    >
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        theme: { default: () => ({}) }
    },

    emits: ['loadSection', 'set'],

    mounted() {
        this.show_announcement_bar = this.theme.settings.show_announcement_bar;

        document.getElementById('themePreview').contentWindow.location = '/#top-of-page';

        $('#announcementBarBgColor').spectrum({
            preferredFormat: 'hex',
            showInput: true,
            allowEmpty: true,
            showPalette: true,
            palette: self.palette,
            change: color => {
                this.$emit('set', {
                    key: 'announcement_bar_bg_color',
                    value: color ? color.toHexString() : 'transparent'
                });
            }
        });

        $('#announcementBarTextColor').spectrum({
            preferredFormat: 'hex',
            showInput: true,
            allowEmpty: true,
            showPalette: true,
            palette: self.palette,
            change: color => {
                this.$emit('set', {
                    key: 'announcement_bar_text_color',
                    value: color ? color.toHexString() : 'transparent'
                });
            }
        });

        $('#announcementBarLinkColor').spectrum({
            preferredFormat: 'hex',
            showInput: true,
            allowEmpty: true,
            showPalette: true,
            palette: self.palette,
            change: color => {
                this.$emit('set', {
                    key: 'announcement_bar_link_color',
                    value: color ? color.toHexString() : 'transparent'
                });
            }
        });
    },

    data: () => ({
        show_announcement_bar: false
    })
};
</script>

<template>
    <div>
        <ul class="themeEditorWidget__navigation">
            <li><a href="#" @click="$emit('loadSection', 'Navigation')"><i class="fa fa-chevron-left"></i> Back</a></li>
        </ul>
        <div class="themeEditor__settingControls">
            <h2>Main Menu</h2>
            <div class="form-group">
                <label>Background Color</label>
                <input id="mainNavigationBg" v-model="theme.settings.main_navigation_bg" class="form-control colorpicker" type="text">
            </div>
            <div class="form-group">
                <label>Link Color</label>
                <input id="mainNavigationLinkColor" v-model="theme.settings.main_navigation_link_color" class="form-control colorpicker" type="text">
            </div>
            <div class="form-group">
                <label>Font Size</label>
                <select v-model="theme.settings.main_navigation_link_font_size" class="form-control"
                        @change="$emit('set', {key: 'main_navigation_link_font_size', value: $event.target.value})">
                    <option value="14px">14px</option>
                    <option value="15px">15px</option>
                    <option value="16px">16px</option>
                    <option value="17px">17px</option>
                    <option value="18px">18px</option>
                    <option value="19px">19px</option>
                    <option value="20px">20px</option>
                </select>
            </div>
            <div class="form-group">
                <label>Alignment</label>
                <select v-model="theme.settings.main_navigation_link_alignment" class="form-control"
                        @change="$emit('set', {key: 'main_navigation_link_alignment', value: $event.target.value})">
                    <option value="center">Centered</option>
                    <option value="right">Right</option>
                    <option value="left">Left</option>
                </select>
            </div>
            <hr>
            <div class="form-group">
                <label>Link Hover Effect</label>
                <select v-model="linkHoverEffect" class="form-control"
                        @change="$emit('set', {key: 'main_navigation_link_hover_effect', value: $event.target.value})">
                    <option value="none">Change Color</option>
                    <option value="underline">Underline</option>
                    <option value="inverted">Invert</option>
                    <option value="custom">Custom</option>
                </select>
            </div>
            <div class="form-group">
                <label>Link Hover Color</label>
                <input id="mainNavigationLinkColorHover" v-model="theme.settings.main_navigation_link_color_hover" class="form-control colorpicker" type="text">
            </div>
            <div v-show="linkHoverEffect == 'custom'">
                <div class="form-group">
                    <label>Link Background Color</label>
                    <input id="mainNavigationLinkBgColor" v-model="theme.settings.main_navigation_link_bg_color" class="form-control colorpicker" type="text">
                </div>
                <div class="form-group">
                    <label>Link Hover Background Color</label>
                    <input id="mainNavigationLinkBgColorHover" v-model="theme.settings.main_navigation_link_bg_color_hover" class="form-control colorpicker" type="text">
                </div>
            </div>
            <div>
                <div class="form-group checkbox">
                    <label>
                        <input v-model="theme.settings.main_navigation_link_transition" class="checkbox" type="checkbox" value="true"
                               @change="$emit('set', {key: 'main_navigation_link_transition', value: $event.target.value})"> Smooth Transitions</label>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: ['theme'],

    emits: ['loadSection', 'set'],

    mounted() {
        this.linkHoverEffect = this.theme.settings.main_navigation_link_hover_effect;

        $('#mainNavigationBg').spectrum({
            preferredFormat: 'hex',
            showInput: true,
            allowEmpty: true,
            showPalette: true,
            palette: this.palette,
            change: color => {
                this.$emit('set', {
                    key: 'main_navigation_bg',
                    value: color ? color.toHexString() : 'transparent'
                });
            }
        });
        $('#mainNavigationLinkColor').spectrum({
            preferredFormat: 'hex',
            showInput: true,
            allowEmpty: true,
            showPalette: true,
            palette: this.palette,
            change: color => {
                this.$emit('set', {
                    key: 'main_navigation_link_color',
                    value: color ? color.toHexString() : 'transparent'
                });
            }
        });
        $('#mainNavigationLinkColorHover').spectrum({
            preferredFormat: 'hex',
            showInput: true,
            allowEmpty: true,
            showPalette: true,
            palette: this.palette,
            change: color => {
                this.$emit('set', {
                    key: 'main_navigation_link_color_hover',
                    value: color ? color.toHexString() : 'transparent'
                });
            }
        });
        $('#mainNavigationLinkBgColor').spectrum({
            preferredFormat: 'hex',
            showInput: true,
            allowEmpty: true,
            showPalette: true,
            palette: this.palette,
            change: color => {
                this.$emit('set', {
                    key: 'main_navigation_link_bg_color',
                    value: color ? color.toHexString() : 'transparent'
                });
            }
        });
        $('#mainNavigationLinkBgColorHover').spectrum({
            preferredFormat: 'hex',
            showInput: true,
            allowEmpty: true,
            showPalette: true,
            palette: this.palette,
            change: color => {
                this.$emit('set', {
                    key: 'main_navigation_link_bg_color_hover',
                    value: color ? color.toHexString() : 'transparent'
                });
            }
        });
    },

    data: () => ({
        linkHoverEffect: 'none'
    })
};
</script>

<template>
    <div>
        <ul class="themeEditorWidget__navigation">
            <li><a href="#" @click="$emit('loadSection', 'Navigation')"><i class="fa fa-chevron-left"></i> Back</a></li>
        </ul>
        <div class="themeEditor__settingControls">
            <h2>Fonts</h2>
            <div class="form-group">
                <label>Heading Font</label>
                <div class="dropdown">
                    <button
                        :style="{'fontFamily': headingFont}"
                        aria-expanded="false"
                        aria-haspopup="true"
                        class="btn btn-light btn-block fontMenu__button"
                        data-toggle="dropdown"
                        type="button"
                    >
                        {{ headingFont }} <i class="fa fa-caret-down"></i>
                    </button>
                    <ul v-show="fonts.length" aria-labelledby="dLabel" class="dropdown-menu fontMenu__list">
                        <li v-for="font in fonts" :key="font.replace(' ', '')">
                            <a
                                :style="{'fontFamily': font}"
                                href="#"
                                @click="setFont(font)"
                            >{{ font }}</a>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="form-group">
                <label>Paragraph Font</label>
                <div class="dropdown">
                    <button
                        :style="{'fontFamily': paragraphFont}"
                        aria-expanded="false"
                        aria-haspopup="true"
                        class="btn btn-light btn-block fontMenu__button"
                        data-toggle="dropdown"
                        type="button"
                    >
                        {{ paragraphFont }} <i class="fa fa-caret-down"></i>
                    </button>
                    <ul v-show="fonts.length" aria-labelledby="dLabel" class="dropdown-menu fontMenu__list">
                        <li v-for="font in fonts" :key="font.replace(' ', '')">
                            <a
                                :style="{'fontFamily': font}"
                                href="#"
                                @click="setParagraphFont(font)"
                            >{{ font }}</a>
                        </li>
                    </ul>
                </div>
            </div>

            <div class="form-group">
                <label>Paragraph Font Size</label>
                <select
                    v-model="fontSize"
                    class="form-control"
                    @change="setFontSize($event.target.value)"
                >
                    <option value="15px">15px</option>
                    <option value="16px">16px</option>
                    <option value="17px">17px</option>
                    <option value="18px">18px</option>
                    <option value="19px">19px</option>
                    <option value="20px">20px</option>
                    <option value="21px">21px</option>
                </select>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: ['theme'],

    emits: ['loadSection', 'save', 'set'],

    watch: {
        'theme.settings': {
            handler: function() {
                this.$emit('save');
            },
            deep: true
        }
    },

    created() {
        this.headingFont = this.theme.settings.heading_font;
        this.paragraphFont = this.theme.settings.paragraph_font;
        this.fontSize = this.theme.settings.paragraph_font_size;
    },

    mounted() {
        this.fetchFonts();
    },

    data: () => ({
        headingFont: 'Acme',
        paragraphFont: 'Cardo',
        fontSize: '16px',
        fonts: []
    }),

    methods: {
        setFont(font) {
            this.headingFont = font;
            this.$emit('set', { key: 'heading_font', value: font });
        },

        setParagraphFont(font) {
            this.paragraphFont = font;
            this.$emit('set', { key: 'paragraph_font', value: font });
        },

        setFontSize(size) {
            this.$emit('set', { key: 'paragraph_font_size', value: size });
            `${this.theme.id}`;
        },

        fetchFonts() {
            axios.get(`/admin/themes/${this.theme.id}/fonts`)
                .then(({ data }) => {
                    this.fonts = data.data;
                })
                .catch(error => {
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error);
                });
        }
    }
};
</script>

<style>
.fontMenu__button {
    font-size: 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.fontMenu__list {
    width: 100%;
    max-height: 270px;
    overflow: scroll;
    font-family: 'Arial';
}
</style>

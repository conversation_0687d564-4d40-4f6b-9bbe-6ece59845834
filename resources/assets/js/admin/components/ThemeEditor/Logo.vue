<template>
    <div>
        <ul class="themeEditorWidget__navigation">
            <li><a href="#" @click="$emit('loadSection', 'Navigation')"><i class="fa fa-chevron-left"></i> Back</a></li>
        </ul>
        <div class="themeEditor__settingControls">
            <div class="form-group">
                <label>Logo</label>
                <cover-photo
                    :src="theme.settings.logo_src"
                    message="Add Your Logo"
                    @input="updateLogo"
                ></cover-photo>
            </div>

            <div class="form-group">
                <label>Mobile Logo</label>
                <cover-photo
                    :src="theme.settings.mobile_logo_src"
                    message="Add Your Logo"
                    @input="updateMobileLogo"
                ></cover-photo>
            </div>
        </div>
    </div>
</template>

<script>
import CoverPhoto from '../MediaManager/CoverPhoto.vue';

export default {
    props: ['theme'],

    emits: ['loadSection', 'set'],

    components: {
        CoverPhoto
    },

    mounted() {
        document.getElementById('themePreview').contentWindow.location = '/#top-of-page';
    },


    methods: {
        update(photo) {
            this.$emit('set', { key: 'logo_src', value: photo.path });
        },

        updateLogo(photo) {
            this.$emit('set', { key: 'mobile_logo_src', value: photo.path });
        }
    }
};
</script>

<template>
<div>
	<ul class="themeEditorWidget__navigation">
		<li><a href="#" @click="$emit('loadSection', 'Navigation')"><i class="fa fa-chevron-left"></i> Back</a></li>
	</ul>
	<div class="themeEditor__settingControls">
		<h2>Footer</h2>
		<div class="form-group">
			<label>Background Color</label>
			<input type="text" class="form-control colorpicker" v-model="theme.settings.footer_bg" id="footerBg">
		</div>
		<div class="form-group">
			<label>Text Color</label>
			<input type="text" class="form-control colorpicker" v-model="theme.settings.footer_text_color" id="footerTextColor">
		</div>
		<div class="form-group">
			<label>Link Color</label>
			<input type="text" class="form-control colorpicker" v-model="theme.settings.footer_link_color" id="footerLinkColor">
		</div>
		<div class="form-group">
			<label>Footer Style</label>
			<select class="form-control" v-model="footerStyle" @change="$emit('set', {key: 'footer_style', value: $event.target.value})">
				<option value="classic">Classic</option>
				<option value="style1">Customize With Widgets</option>
			</select>
		</div>
		<hr>
		<footer-widgets v-if="footerStyle == 'style1'"></footer-widgets>
	</div>

</div>	
</template>

<script>
	import FooterWidgets from '../PageEditor/FooterWidgetManager/FooterWidgetManager.vue';
	export default {
		props: ['theme'],

        emits: ['loadSection', 'set'],


        components: {
            FooterWidgets
    	},

		mounted: function()
		{
			this.footerStyle = this.theme.settings.footer_style;
			document.getElementById('themePreview').contentWindow.location = '/#bottom-of-page';

			let self = this;
			$('#footerBg').spectrum({
			    preferredFormat: "hex",
			    showInput: true,
			    allowEmpty: true,
			    showPalette: true,
			    palette: self.palette,
			    change: function(color) {
			    	self.$emit('set', {key: 'footer_bg', value: color.toHexString()});
			    }
			});
			$('#footerTextColor').spectrum({
			    preferredFormat: "hex",
			    showInput: true,
			    allowEmpty: true,
			    showPalette: true,
			    palette: self.palette,
			    change: function(color) {
			    	self.$emit('set', {key: 'footer_text_color', value: color.toHexString()});
			    }
			});
			$('#footerLinkColor').spectrum({
			    preferredFormat: "hex",
			    showInput: true,
			    allowEmpty: true,
			    showPalette: true,
			    palette: self.palette,
			    change: function(color) {
			    	self.$emit('set', {key: 'footer_link_color', value: color.toHexString()});
			    }
			});

		},

		data: function() {
			return {
				footerStyle: 'style1'
			}
		}
	}
</script>
<template>
<div>
	<ul class="themeEditorWidget__navigation">
		<li><a href="#" @click="$emit('loadSection', 'Navigation')"><i class="fa fa-chevron-left"></i> Back</a></li>
	</ul>
	<div class="themeEditor__settingControls">	
			<div class="form-group">
				<label>Custom CSS</label>
                <p style="margin: 0.25rem 0 0.5rem; color: rgb(107 114 128); font-size: 0.75rem; line-height: 1rem;">Warning: GrazeCart is always evolving. There is a chance any customizations you make will break if we change the site. We recommend only making small changes.</p>
				<div id="custom_CSS" style="height: calc(100vh - 240px);">{{ theme.custom_css }}</div>
			</div>	
			<div class="form-group text-center">
				<button class="btn btn-light" @click="saveCss()"><i class="fa fa-check-circle"></i> Save Custom CSS</button>
			</div>	
	</div>	
</div>	
</template>

<script>
	export default {
		props: ['theme'],

        emits: ['loadSection', 'save'],

        mounted: function()
		{
			this.editor = ace.edit("custom_CSS");
		    this.editor.getSession().setMode("ace/mode/css");
		},

		data: function()
		{
			return {
				editor: {}
			}
		},

		methods: {
			saveCss: function()
			{
				this.theme.custom_css = this.editor.getSession().getValue();
				this.$emit('save');
			}
		}
	}
</script>
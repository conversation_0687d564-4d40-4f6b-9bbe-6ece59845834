<template>
    <div>
        <ul class="themeEditorWidget__navigation">
            <li>
                <a href="#" @click="$emit('loadSection', 'Navigation')">
                    <i class="fa fa-chevron-left"></i> Back
                </a>
            </li>
        </ul>
        <div class="themeEditor__settingControls">
            <h2>Store</h2>

            <div class="form-group">
                <label>Store Button Style</label>
                <select v-model="theme.settings.store_button_style" class="form-control" @change="$emit('set', {key: 'store_button_style', value: $event.target.value})">
                    <option value="btn-brand">Brand Solid</option>
                    <option value="btn-brand-inverted">Brand Outlined</option>
                    <option value="btn-action">Action</option>
                    <option value="btn-action-inverted">Action Outlined</option>
                </select>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: ['theme'],

    emits: ['loadSection', 'set'],

    created() {
        this.settings = this.theme.settings;
    },

    mounted() {
        document.getElementById('themePreview').contentWindow.location = '/store';
    },

    data: () => ({
        settings: []
    })
};
</script>

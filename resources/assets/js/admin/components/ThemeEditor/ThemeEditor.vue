<template>
    <div class="themeEditor">
        <div class="themeEditor__navigation" :class="{'themeEditor__navigation--wide': section === 'CustomCss'}">
            <div class="themeEditorWidget">
                <div class="themeEditor__previewToolbar">
                    <div class="btn-group" role="group" aria-label="...">
                        <a href="/admin" class="btn btn-light"><i class="fa fa-arrow-circle-o-left"></i> Exit</a>
                        <button type="button" class="btn btn-light" @click="publish()"><i class="fa fa-check-circle"></i> Save</button>
                    </div>
                    <div class="btn-group" role="group" aria-label="...">
                        <button class="btn btn-light" :class="{'active': previewWidth === '375px'}" @click="setPreview('mobile')">
                            <i class="fa fa-fw fa-lg fa-mobile" aria-hidden="true"></i>
                        </button>
                        <button class="btn btn-light" :class="{'active': previewWidth === '768px'}" @click="setPreview('tablet')">
                            <i class="fa fa-lg fa-tablet" aria-hidden="true"></i>
                        </button>
                        <button class="btn btn-light" :class="{'active': previewWidth === '100%'}" @click="setPreview('desktop')">
                            <i class="fa fa-fw fa-desktop" aria-hidden="true"></i>
                        </button>
                    </div>
                </div>
                <component
                    :is="section"
                    :theme="theme"
                    @set="setSetting"
                    @save="update"
                    @loadSection="loadSection"
                ></component>
            </div>
        </div>
        <div class="themeEditor__previewContainer">
            <iframe
                src="/"
                class="themeEditor__preview"
                width="100%"
                frameborder="0"
                id="themePreview"
                :style="{maxWidth: previewWidth, maxHeight: previewHeight}"
            ></iframe>
        </div>
    </div>
</template>

<script>
import Navigation from './Navigation.vue';
import Fonts from './Fonts.vue';
import Colors from './Colors.vue';
import CustomCss from './CustomCss.vue';
import Logo from './Logo.vue';
import SiteHeader from './SiteHeader.vue';
import SiteNavigation from './SiteNavigation.vue';
import SiteFooter from './SiteFooter.vue';
import Store from './Store.vue';
import General from './General.vue';
export default {

    components: {
        Navigation,
        Fonts,
        Colors,
        CustomCss,
        Logo,
        SiteHeader,
        SiteNavigation,
        SiteFooter,
        Store,
        General
    },

    props: {
        theme: {
            type: Object,
            required: true
        }
    },

    created() {
        window.addEventListener("beforeunload", function (event) {
            if ( ! this.saved) {
                event.returnValue = "Changes have not been published yet.";
            }
        });
    },

    data() {
        return {
            settings: null,
            section: 'Navigation',
            saved: true,
            previewWidth: '100%',
            previewHeight: '100%'
        }
    },

    methods: {
        loadSection(section) {
            this.section = section

            $(".colorpicker").spectrum({
                preferredFormat: "hex",
                showInput: true,
            });
        },

        setSetting(payload) {
            this.theme.settings[payload.key] = payload.value;
            this.update();
        },

        update() {
            eventHub.emit('showProgressBar');

            this.saved = false

            axios.put(`/admin/themes/${this.theme.id}`, {
                settings: this.theme.settings,
                custom_css: this.theme.custom_css
            })
                .then(() => {
                    eventHub.emit('hideProgressBar')
                    document.getElementById('themePreview').contentWindow.location.reload();
                })
                .catch((error) => {
                    eventHub.emit('hideProgressBar');
                    eventHub.emit('error', error)
                });
        },

        publish() {
            eventHub.emit('showProgressBar')

            this.saved = true

            axios.put(`/admin/themes/${this.theme.id}`, {
                publish: true
            })
                .then(() => {
                    eventHub.emit('hideProgressBar')
                    eventHub.emit('notify', {level: 'info', message: 'Your theme changes are now live!'});
                })
                .catch((error) => {
                    eventHub.emit('error', error);
                    eventHub.emit('hideProgressBar');
                });
        },

        setPreview(type) {
            if (type === 'mobile') {
                this.previewWidth = '375px'
                this.previewHeight = '667px'
            } else if (type === 'tablet') {
                this.previewWidth = '768px'
                this.previewHeight = '1024px'
            } else {
                this.previewWidth = '100%'
                this.previewHeight = '100%'
            }
        }
    }
}
</script>
<template>
<div>
	<ul class="themeEditorWidget__navigation">
		<li><a href="#" @click="$emit('loadSection', 'Navigation')"><i class="fa fa-chevron-left"></i> Back</a></li>
	</ul>
	<div class="themeEditor__settingControls">
		<h2>General</h2>
		<div class="form-group">
			<label>Favicon</label>
			<div><em>Use a .png file that is 32x32.</em></div>
			<cover-photo 
				:src="theme.settings.favicon" 
                message="Add Your Favicon" 
                @input="update"
            ></cover-photo>
		</div>
	</div>
</div>	
</template>

<script>
	import CoverPhoto from '../MediaManager/CoverPhoto.vue';
	export default {
		props: ['theme'],

        emits: ['loadSection', 'set'],

		components: {
			CoverPhoto
		},

		methods: {
			update: function(photo) {
				this.$emit('set', {key: 'favicon', value: photo.path});
			}
		},
	}
</script>
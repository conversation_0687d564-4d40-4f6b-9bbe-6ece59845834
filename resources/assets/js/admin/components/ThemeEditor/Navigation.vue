<template>
<div>
<ul class="themeEditorWidget__navigation">
	<li>
		<a href="#" @click="$emit('loadSection', 'Logo')">Logo <i class="fa fa-chevron-right pull-right"></i></a>
	</li>
	<li>
		<a href="#" @click="$emit('loadSection', 'Fonts')">Fonts <i class="fa fa-chevron-right pull-right"></i></a>
	</li>
	<li>
		<a href="#" @click="$emit('loadSection', 'Colors')">Colors <i class="fa fa-chevron-right pull-right"></i></a>
	</li>
	<li>
		<a href="#" @click="$emit('loadSection', 'SiteHeader')">Header<i class="fa fa-chevron-right pull-right"></i></a>
	</li>
	<li>
		<a href="#" @click="$emit('loadSection', 'SiteNavigation')">Main Menu<i class="fa fa-chevron-right pull-right"></i></a>
	</li>
	<li>
		<a href="#" @click="$emit('loadSection', 'SiteFooter')">Footer <i class="fa fa-chevron-right pull-right"></i></a>
	</li>
	<li>
		<a href="#" @click="$emit('loadSection', 'Store')">Store <i class="fa fa-chevron-right pull-right"></i></a>
	</li>
	<li>
		<a href="#" @click="$emit('loadSection', 'General')">General <i class="fa fa-chevron-right pull-right"></i></a>
	</li>
	<li>
		<a href="#" @click="$emit('loadSection', 'CustomCss')">Custom CSS <i class="fa fa-chevron-right pull-right"></i></a>
	</li>
</ul>
</div>	
</template>

<script>
	export default {
		props: ['theme'],

        emits: ['loadSection'],

    }
</script>
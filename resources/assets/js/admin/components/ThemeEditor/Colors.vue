<template>
<div>
	<ul class="themeEditorWidget__navigation">
		<li><a href="#" @click="$emit('loadSection', 'Navigation')"><i class="fa fa-chevron-left"></i> Back</a></li>
	</ul>
	<div class="themeEditor__settingControls">
		<h2>Colors</h2>
		<div class="form-group">
			<label>Brand Color</label>
			<input type="text" class="form-control colorpicker" :value="theme.settings.brand_color" id="brandColor">
		</div>

		<div class="form-group">
			<label>Brand Color Inverted</label>
			<input type="text" class="form-control colorpicker" :value="theme.settings.brand_color_inverted" id="brandColorInverted">
		</div>
		<hr>
		<div class="form-group">
			<label>Action Color</label>
			<input type="text" class="form-control colorpicker" :value="theme.settings.action_color" id="actionColor">
		</div>

		<div class="form-group">
			<label>Action Color Inverted</label>
			<input type="text" class="form-control colorpicker" :value="theme.settings.action_color_inverted" id="actionColorInverted">
		</div>

		<hr>
		<div class="form-group">
			<label>Link Color</label>
			<input type="text" class="form-control colorpicker" :value="theme.settings.link_color" id="linkColor">
		</div>
	</div>
</div>	
</template>

<script>
	export default {
		props: ['theme'],

        emits: ['loadSection', 'set'],


        mounted: function()
		{
			self = this;
			$('#brandColor').spectrum({
			    preferredFormat: "hex",
			    showInput: true,
			    allowEmpty: true,
			    showPalette: true,
			    palette: self.palette,
			    change: function(color) {
			    	self.$emit('set', {key: 'brand_color', value: color.toHexString()});
			    }
			});

			$('#brandColorInverted').spectrum({
			    preferredFormat: "hex",
			    showInput: true,
			    allowEmpty: true,
			    showPalette: true,
			    palette: self.palette,
			    change: function(color) {
			    	self.$emit('set', {key: 'brand_color_inverted', value: color.toHexString()});
			    }
			});

			$('#actionColor').spectrum({
			    preferredFormat: "hex",
			    showInput: true,
			    allowEmpty: true,
			    showPalette: true,
			    palette: self.palette,
			    change: function(color) {
			    	self.$emit('set', {key: 'action_color', value: color.toHexString()});
			    }
			});

			$('#actionColorInverted').spectrum({
			    preferredFormat: "hex",
			    showInput: true,
			    allowEmpty: true,
			    showPalette: true,
			    palette: self.palette,
			    change: function(color) {
			    	self.$emit('set', {key: 'action_color_inverted', value: color.toHexString()});
			    }
			});

			$('#linkColor').spectrum({
			    preferredFormat: "hex",
			    showInput: true,
			    allowEmpty: true,
			    showPalette: true,
			    palette: self.palette,
			    change: function(color) {
			    	self.$emit('set', {key: 'link_color', value: color.toHexString()});
			    }
			});
		},

		data: function() {
			return {
				palette: [
					[
			        	this.theme.settings.brand_color, 
			        	this.theme.settings.action_color, 
			        	'#FFF','#EEE','#000'
			        ]
				]
			}
		}
	}
</script>
<template>
<div>
<div class="gc-modal gc-modal-mask" id="createBundleModal" @click="hideModal('createBundleModal')">
	<div class="gc-modal-wrapper">
		<div class="gc-modal-container" @click.stop>
			
			<div class="gc-modal-header">
				Add Product
        <p class="m-0 mt-1 font-normal text-gray-500 text-xs">Select a product below. Bundle products cannot be added as product items.</p>
			</div>

			<div class="gc-modal-body">
				<div class="select">
                    <div class="form-group">
                        <input 
                        	type="text" 
                        	class="form-control" 
                        	autocomplete="off" 
                        	@focus="isFocused = true" 
                        	@blur="isFocused = false"
                        	tabindex="2"
                        	v-model="q" 
                        	placeholder="search for a product to add..." 
                        	id="productSearchInput"
                        >

                      <ul class="select-results" v-show="isFocused">
                        <li>
                          <span class="bold">{{ searchMessage }}</span>
                        </li>
                        <li v-for="product in products" :key="product.id">
                                <span href="#" @mousedown.prevent="selectProduct(product)" class="pa-sm block">
                                  <span class="inline-block" v-text="product.title"></span><br>
                                  <span class="inline-block italic">{{ product.unit_description }}</span>
                                </span>
                        </li>
                      </ul>
                    </div>
                </div>


                <div class="form-group">
                    <label for="amount">Quantity</label>
                    <input type="text" class="form-control" v-model="qty"/>
                </div>
			</div>

			<div class="gc-modal-footer">
				<button type="button" class="btn btn-alt" @click="hideModal('createBundleModal')">Cancel</button>
        <button type="button" class="btn btn-action" :disabled="productId === 0 || qty < 1" @click="addToBundle">Add Product</button>
			</div>
		</div>
	</div>
</div>

	<div class="gc-modal gc-modal-mask" id="editBundledModal" @click="hideModal('editBundledModal')">
		<div class="gc-modal-wrapper">
			<div class="gc-modal-container" @click.stop>
				
				<div class="gc-modal-header">
					Edit Bundled Item
				</div>

				<div class="gc-modal-body">
					<div class="form-group">
                <label for="amount">Quantity</label>
                <input type="text" class="form-control" v-model="item.qty" id="qtyInput" @keyup.enter.prevent.stop="()=>{}">
          </div>
				</div>

				<div class="gc-modal-footer">
					<button type="button" class="btn btn-alt" @click="hideModal('editBundledModal')">Cancel</button>
	                <button type="button" class="btn btn-action" :disabled="this.item.qty < 1" @click="updateBundleItem()">Save</button>
				</div>
			</div>
		</div>
	</div>

	<div class="gc-modal gc-modal-mask" id="deleteBundleModal" @click="hideModal('deleteBundleModal')">
		<div class="gc-modal-wrapper">
			<div class="gc-modal-container" @click.stop>
				
				<div class="gc-modal-header">
					Remove From Bundle
				</div>
	
				<div class="gc-modal-body">
					Remove this product from this bundle?
				</div>
	
				<div class="gc-modal-footer">
					<button type="button" class="btn btn-alt" @click="hideModal('deleteBundleModal')">Cancel</button>
	                <button type="button" class="btn btn-danger" @click="destroy(product)">Remove From Bundle</button>
				</div>
			</div>
		</div>
	</div>

	<div class="panel panel-tabs">
		<div class="panel-heading flex align-items-m">
		<button type="button" class="flex-item push-right btn btn-alt btn-sm pr-0" @click="showModal('createBundleModal')"><i class="fa fa-plus-circle fa-lg"></i> Add Product</button>
		</div>
		<div class="panel-body">
		    <table class="table" v-if="bundle.length">
		        <thead>
		        <tr>
		            <th>Product</th>
		            <th>Qty.</th>
		            <th></th>
		        </tr>
		        </thead>
		        <tbody>
		        <tr v-for="item in bundle" :key="item.id">
		            <td><a href="#" @click.prevent="editBundleItem(item)"> {{ item.title }}</a> </td>
		            <td>{{ item.qty }}</td>
		            <td>
		            	<button class="btn btn-sm btn-alt" @click.prevent="remove(item)">Delete</button>
		            	<button class="btn btn-sm btn-alt" @click.prevent="editBundleItem(item)">Edit</button>
		            </td>
		        </tr>
		        </tbody>
		    </table>
		    <div v-else>
		    	There are no products in this bundle yet.
		    </div>
		</div>
	</div>	    
</div>    
</template>

<script>
	export default {
		created: function() {
			this.getBundle();
		},

	    props: ['id', 'model'],

	    data: function() {
	        return {
	            bundle: [],
	            productId: 0,
	            qty: 1,
	            products: [],
	            q: '',
	            item: {

	            },
	            isFocused: false,
	            searchMessage: 'Start typing to search for products...',
	            product: null
	        }
	    },

	    watch: {
	        q: function(value)
	        {
	            if(value.length > 2)
	            {
	                this.searchProducts();
	            }
	        },
	        products: function(value)
	        {
	            if(value.length)
	            {
	                this.searchMessage = 'Select a product to add:';
	            }
	            else
	            {
	                this.searchMessage = 'No results found';
	            }
	        }
	    },

	    methods: {
	        getBundle: function() {
	            var url = '/admin/products/'+this.id+'/bundles';
	            axios.get(url)
	                .then(function(response) {
	                    this.bundle = response.data;
	                }.bind(this))
	                .catch(function(error) {

	                });
	        },

	        searchProducts: function() {
	            var url = '/api/products';
	            this.searchMessage = 'Searching...';
	            axios.get(url, {params: {products: this.q, is_bundle: false}})
	                .then(function(response) {
	                    this.products = response.data.filter(product => parseInt(product.id) !== 0);
	                }.bind(this))
	                .catch(function(error) {

	                });
	        },

	        selectProduct: function(product)
	        {
	            this.productId = product.id;
	            this.q = product.title;
	            this.isFocused = false;
	        },

	        addToBundle: function() {
	            var url = '/admin/products/'+this.id+'/bundles';
	            var payload = {
	                product_id: this.productId,
	                qty: this.qty
	            }
	            axios.post(url, payload)
	                .then(function(response) {
	                    this.hideModal('createBundleModal');
	                    this.getBundle();
	                }.bind(this))
	                .catch(function(error) {
						eventHub.emit('error', error);
	                });
	        },

	        editBundleItem: function(item) {
	            this.item = item;
	            this.showModal('editBundledModal');
	        },

	        updateBundleItem: function()
	        {
	            var url = '/admin/products/'+this.id+'/bundles/'+this.item.id;
	            var payload = {qty: this.item.qty}
	            axios.put(url, payload)
	                .then(function(response) {
	                    this.getBundle();
	                    this.hideModal('editBundledModal');
	                }.bind(this))
	                .catch(function(error) {
						eventHub.emit('error', error);
	                });
	        },

	        remove: function(product) {
	        	this.item = product;
	        	this.showModal('deleteBundleModal');
	        },

	        destroy: function()
	        {
	            var url = '/admin/products/'+this.id+'/bundles/'+this.item.id;
	            axios.delete(url)
	                .then(function(response) {
	                    this.getBundle();
	                    this.hideModal('deleteBundleModal');
	                }.bind(this))
	                .catch(function(error) {
	                	
	                });
	        }
	    }
	}
</script>
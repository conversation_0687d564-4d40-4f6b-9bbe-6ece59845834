<template>
    <div>
        <transition name="slideup">
            <div
                class="systemNotification"
                :class="alertLevel"
                v-if="visible"
                @click="hide()"
            >
                <div class="systemNotification__inner-container">
                    <a href="#" class="systemNotification__close" @click.prevent="visible = false">&times;</a>
                    <div class="systemNotification__message" v-html="message"></div>
                </div>
            </div>
        </transition>
    </div>
</template>

<script>
	export default {
		created: function() {
			eventHub.on('notify', function(data) {
	            this.show(data);
	        }.bind(this));

	        eventHub.on('error', function(data) {
	            this.error(data);
	        }.bind(this));

	        eventHub.on('hideSystemNotification', function(data) {
	        	this.hide();
			}.bind(this));
		},

		data: function()
		{
			return {
				visible: false,
				message: '',
				level: 'info'
			}
		},

		computed: {
			alertLevel: function() {
				return 'systemNotification--'+this.level
			}
		},

		methods: {
			show: function(data) {
				this.level = data.level
				this.message = data.message
				this.visible = true

				setTimeout(function() {
					this.visible = false
				}.bind(this), 16000)
			},

			hide: function() {
				this.level = 'info';
				this.message = '';
				this.visible = false;
				$('#systemNotification').hide();
			},

			error: function(error) {
				var message = '';

	            if(error.response.status == 422 && typeof error.response.data.errors == 'object')
	            {
	                message = '<ul class="list-spacing-sm">';
	                $.each(error.response.data.errors, function(index, value) {
	                    $.each(value, function(index, value) {
	                        message += '<li>'+value+'</li>';
	                    });
	                });
	                message += '</ul>';
	            }
	            else
	            {
	                message = error.response.data;
	            }

	            this.show({
	            	level: 'danger',
	            	message: message
	            })
			}
		}
	}
</script>
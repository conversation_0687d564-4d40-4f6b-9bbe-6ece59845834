<template>
	<div class="addressForm">
		<div class="form-group">
			<label for="country">Country</label>
			<select v-model="country" class="form-control" id="country">
				<option :value="country.value" v-for="country in countries">{{ country.title }}</option>
			</select>
		</div>
		<slot name="usa" v-if="country == 'USA'"></slot>
		<slot name="canada" v-if="country == 'Canada'"></slot>
	</div>
</template>

<script>
	export default {
		data: function()
		{
			return {
				country: 'USA',
				countries: [
					{title: 'United States', value: 'USA'},
					{title: 'Canada', value: 'Canada'}
				]
			}
		}
	}
</script>
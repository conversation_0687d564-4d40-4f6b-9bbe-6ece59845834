<template>
    <div class="createShippingZone">
        <div id="createDeliveryZoneModal" class="gc-modal gc-modal-mask" @click="hideModal('createDeliveryZoneModal')">
            <div class="gc-modal-wrapper">
                <div class="gc-modal-container" @click.stop>

                    <div class="gc-modal-header">
                        Create a Delivery Zone
                    </div>

                    <div class="gc-modal-body">
                        <div class="form-group">
                            <label for="zoneTitle">Delivery Zone Name</label>
                            <input id="zoneTitle" v-model="zone.title" autofocus class="form-control" name="title" tabindex="1" type="text">
                        </div>
                    </div>

                    <div class="gc-modal-footer">
                        <button class="btn btn-alt" type="button" @click="hideModal('createDeliveryZoneModal')">Cancel</button>
                        <button :disabled="!saveable" class="btn btn-action" type="button" @click="store(zone)">Create Zone</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
export default {

    created: function() {
        this.getAvailableShippingZones();
    },

    props: ['permitted'],

    emits: ['createDeliveryZone:toggle', 'stored'],

    data: function() {
        return {
            search: '',
            zone: {
                title: '',
                fulfillment_type: 2
            },
            states: [],
            zips: '',
            zoneType: 'states'
        };
    },

    computed: {
        saveable: function() {
            if (!this.zone.title) {
                return false;
            }

            return true;
        },

        selectedCount: function() {
            return this.states.filter(function(state) {
                return state.selected;
            });
        },

        filteredStates: function() {
            var self = this;
            return self.states.filter(function(state) {
                return state.title.indexOf(self.search) !== -1;
            });
        }
    },

    methods: {
        store: function(zone) {
            axios.post('/api/logistics/delivery', {
                title: zone.title
            })
                .then(function(response) {
                    window.location = '/admin/logistics/delivery/' + response.data.zone.id + '/edit';
                    this.$emit('createDeliveryZone:toggle');
                    this.$emit('stored', response.data.zone);
                    this.resetZone();
                }.bind(this))
                .catch(function(error) {
                    eventHub.emit('error', error);
                }.bind(this));
        },

        getAvailableShippingZones: function() {
            axios.get('/api/theme/shipping-availability')
                .then(function(response) {
                    this.states = response.data;
                }.bind(this))
                .catch(function(error) {
                    eventHub.emit('error', error);
                }.bind(this));
        },

        resetZone: function() {
            this.zone = {
                title: '',
                fulfillment_type: 2
            };
            this.selectedStates = [];
            this.zips = '';
            this.getAvailableShippingZones();
        }
    }
};
</script>

<style>
.createShippingZone__stateList {
    max-height: 300px;
    overflow: auto;
}
</style>

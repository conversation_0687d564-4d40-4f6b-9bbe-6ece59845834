<template>
<div>
	<div class="panel panel-tabs" v-show="zoneType == 'states'">
		<div class="panel-heading text-center">
			<button type="button" class="btn btn-sm btn-alt btn-active" @click.prevent="zoneType = 'states'">Refine by states / provinces</button>
			<button type="button" class="btn btn-sm btn-alt btn-not-active" @click.prevent="zoneType = 'zips'">Refine by postal code</button>
		</div>
		<div class="panel-body">
      <div id="updateResourceForm">
        <div class="form-group">
          <label>
            Select the States/Provinces You Deliver To
          </label>
          <input type="text" class="form-control" v-model="search" tabindex="1" placeholder="Search states/provinces">
          <ul class="max-h-200 overflow-auto mt-sm pa-md pt-sm pb-sm shadow-in-soft br-sm">
            <li v-for="state in filteredStates" :key="state.abbreviation" class="mt-xs mb-xs">
              <label :class="{'text-muted': state.active}">
                <input
                  :value="state.abbreviation"
                  type="checkbox"
                  v-model="state.selected"
                  :disabled="state.active"
                  tabindex="1"
                >
                <span>
                  {{ state.title }} <small v-show="state.active" class="inputNote">(already in another delivery zone)</small>
                </span>
              </label>
            </li>
          </ul>
        </div>
      </div>
		</div>
		<div class="panel-footer text-right">
			<button type="button" class="btn btn-action" @click="update">Save</button>
		</div>
	</div>
	<div class="panel panel-tabs" v-if="zoneType == 'zips'">
		<div class="panel-heading text-center">
			<button type="button" class="btn btn-sm btn-alt btn-not-active" @click.prevent="zoneType = 'states'">Refine by states / provinces</button>
			<button type="button" class="btn btn-sm btn-alt btn-active" @click.prevent="zoneType = 'zips'">Refine by postal code</button>
		</div>
		<div class="panel-body">
			<div class="mb-lg flex align-items-m">
				<div class="input-group flex-item postalCodeSearch">
					<input
						type="text"
						class="form-control"
						v-model="query"
						:placeholder="'Search '+postalCodes.total+' postal codes'"
						@keypress.enter="searchPostalCodes(query)"
					>
					<button
                        @click="getPostalCodes()"
                        type="button"
                        title="Search postal codes"
                        class="br--r br-left-0 btn btn-light input-group-append"
                    ><i class="fa fa-search fa-fw"></i></button>
				</div>
                <div class="flex-item push-right">
                    <button
                        type="button"
                        class="btn btn-default block"
                        @click="showModal('storePostalCodeModal')"
                    ><i class="far fa-plus-circle fa-fw"></i> Add Postal Codes</button>
                    <button
                        v-if="postalCodes.data.length"
                        type="button"
                        class="btn btn-default block"
                        @click="showModal('deletePostalCodeModal')"
                    ><i class="far fa-minus-circle fa-fw"></i> Delete Postal Codes</button>
                </div>
			</div>
			<ul class="postalCodes">
				<li v-for="postalCode in postalCodes.data" :key="postalCode.id">
					<a href="#" @click="editPostalCode(postalCode)">{{ postalCode.zip }}</a>
				</li>
			</ul>
			<div v-show="!postalCodes.data.length" class="pa-sm">
				No postal codes found
			</div>
		</div>
		<div class="panel-footer text-center" v-show="showPager">
			<div class="button-group">
				<button type="button" class="btn btn-light" @click="prevPage()" :disabled="!showPrevPager">Prev</button>
				<button type="button" class="btn btn-light" @click="nextPage()" :disabled="!showNextPager">Next</button>
			</div>
		</div>
	</div>

	<div class="gc-modal gc-modal-mask" id="storePostalCodeModal" @click="hideModal('storePostalCodeModal')">
		<div class="gc-modal-wrapper">
			<div class="gc-modal-container" @click.stop>

				<div class="gc-modal-header">
					Add Postal Codes
				</div>

				<div class="gc-modal-body">
					<label>Add up to 500 postal codes at a time. Separate each by a comma.</label>
					<div class="form-group">
	                    <textarea class="form-control" v-model="newPostalCodes" rows="10"></textarea>
	                </div>
				</div>

				<div class="gc-modal-footer">
					<button type="button" class="flex-item btn btn-alt" @click="hideModal('storePostalCodeModal')">Cancel</button>
	                <button type="button" class="flex-item btn btn-action" @click="storePostalCode()">Add Postal Codes</button>
				</div>
			</div>
		</div>
	</div>

	<div class="gc-modal gc-modal-mask" id="editPostalCodeModal" @click="hideModal('editPostalCodeModal')">
		<div class="gc-modal-wrapper">
			<div class="gc-modal-container" @click.stop>

				<div class="gc-modal-header">
					Edit Postal Code
				</div>

				<div class="gc-modal-body">
					<div class="form-group">
	                    <input type="text" class="form-control" v-model="postalCode.zip">
	                </div>
				</div>

				<div class="gc-modal-footer flex">
					<button
						type="button"
						class="flex-item btn push-left"
						@click="destroyPostalCode(postalCode)"
					>Delete</button>
					<button type="button" class="flex-item btn btn-alt" @click="hideModal('editPostalCodeModal')">Cancel</button>
	                <button type="button" class="flex-item btn btn-action" @click="updatePostalCode(postalCode)">Save</button>
				</div>
			</div>
		</div>
	</div>

    <div class="gc-modal gc-modal-mask" id="deletePostalCodeModal" @click="hideModal('deletePostalCodeModal')">
		<div class="gc-modal-wrapper">
			<div class="gc-modal-container" @click.stop>

				<div class="gc-modal-header">
					Delete Postal Codes
				</div>

				<div class="gc-modal-body">
					<label>Add up to 500 postal codes you want to delete. Separate each by a comma.</label>
					<div class="form-group">
	                    <textarea class="form-control" v-model="postalCodesToDelete" rows="10"></textarea>
	                </div>
				</div>

				<div class="gc-modal-footer">
					<button type="button" class="flex-item btn btn-alt" @click="hideModal('deletePostalCodeModal')">Cancel</button>
	                <button type="button" class="flex-item btn btn-danger" @click="destroyPostalCodes(postalCodesToDelete)">Delete Postal Codes</button>
				</div>
			</div>
		</div>
	</div>
</div>
</template>

<script>
	export default {

		created: function()
		{
			this.getAvailableShippingZones();
			this.getPostalCodes();
			eventHub.on('changesSaved', this.update);
			this.newZips = this.zips;
		},

		props: ['zone','zips'],

		data: function()
		{
			return {
				search: '',
				states: [],
				postalCodes: [],
				postalCode: {},
				query: '',
				newPostalCodes: '',
                postalCodesToDelete: '',
				zoneType: 'states',
				stateCount: 0,
				newZips: '',
				page: null
			}
		},

		watch: {
			query: function(newVal, oldVal)
			{
				if(newVal === '')
				{
					this.searchPostalCodes();
				}
			}
		},

		computed: {
			saveable: function()
			{
				if(!this.zone.title)
				{
					return false
				}

				if(this.zoneType == 'zips' && !this.zips)
				{
					return false
				}

				return true
			},

			selectedCount: function()
			{
				return this.states.filter(function(state)
				{
					return state.selected
				})
			},

			filteredStates: function() {
				var self = this
	            return self.states.filter(function (state) {
	              return state.title.indexOf(self.search) !== -1
	            })
			},

			showPager: function()
			{
				return this.postalCodes.last_page > 1;
			},

			showNextPager: function()
			{
				return this.postalCodes.current_page < this.postalCodes.last_page;
			},

			showPrevPager: function()
			{
				return this.postalCodes.current_page > 1;
			}
		},

		events: {
			changesSaved: function()
			{
				this.update(this.zone)
			}
		},

		methods: {
			getPostalCodes: function()
			{
				axios.get('/api/delivery-zones/'+this.zone.id+'/postal-codes', {params:
					{page: this.page, query: this.query}
				})
					.then(function(response) {
						this.postalCodes = response.data;
					}.bind(this))
					.catch(function(error) {
						eventHub.emit('error', error);
					}.bind(this))
			},

			prevPage: function() {
				this.page = this.postalCodes.current_page > 1 ? this.postalCodes.current_page - 1 : 0;
				this.getPostalCodes();
			},

			nextPage: function() {
				this.page = this.postalCodes.current_page < this.postalCodes.last_page ? this.postalCodes.current_page + 1 : this.postalCodes.last_page;
				this.getPostalCodes();
			},

			searchPostalCodes: function(query)
			{
				this.page = 0;
				this.getPostalCodes();
			},

			editPostalCode: function(postalCode)
			{
				this.postalCode = postalCode;
				this.showModal('editPostalCodeModal');
			},

			storePostalCode: function(postalCodes)
			{
				axios.post('/api/delivery-zones/'+this.zone.id+'/postal-codes', {
					postal_codes: this.newPostalCodes
				})
				.then(function(response) {
					this.getPostalCodes();
					this.hideModal('storePostalCodeModal');
					if(response.data.ignored)
					{
						eventHub.emit('notify', {level: 'info', message: response.data.ignored});
					}
					else
					{
						eventHub.emit('notify', {level: 'info', message: 'Postal codes added <i class="fa fa-check-circle text-success"></i>'});
					}
				}.bind(this))
				.catch(function(error) {
					eventHub.emit('error', error);
				}.bind(this))
			},

			updatePostalCode: function(postalCode)
			{
				axios.put('/api/delivery-zones/'+this.zone.id+'/postal-codes/'+postalCode.id, {
					postal_code: postalCode.zip
				})
				.then(function(response) {
					eventHub.emit('notify', {level: 'info', message: 'Changes Saved <i class="fa fa-check-circle text-success"></i>'});
				}.bind(this))
				.catch(function(error) {
					eventHub.emit('error', error);
				}.bind(this))
			},

			destroyPostalCode: function(postalCode)
			{
				axios.delete('/api/delivery-zones/'+this.zone.id+'/postal-codes/'+postalCode.id)
				.then(function(response) {
					this.getPostalCodes();
					this.hideModal('editPostalCodeModal');
					eventHub.emit('notify', {level: 'info', message: 'Postal code deleted <i class="fa fa-check-circle text-success"></i>'});
				}.bind(this))
				.catch(function(error) {
					eventHub.emit('error', error);
				}.bind(this))
			},

            destroyPostalCodes: function()
			{
				axios.put('/api/delivery-zones/'+this.zone.id+'/bulk-delete-postal-codes', {
					postal_codes_to_delete: this.postalCodesToDelete
				})
				.then(function(response) {
					this.getPostalCodes();
					this.hideModal('deletePostalCodeModal');
					eventHub.emit('notify', {level: 'info', message: 'Postal codes deleted <i class="fa fa-check-circle text-success"></i>'});
				}.bind(this))
				.catch(function(error) {
					eventHub.emit('error', error);
				}.bind(this))
			},

			update: function()
			{
				axios.put('/api/logistics/delivery/'+this.zone.id, {
					states: this.zoneType == 'states' ? this.states : null,
					zips: this.zoneType == 'zips' ? this.newZips : null,
					zoneType: this.zoneType
				})
				.then(function(response) {
					eventHub.emit('notify', {level: 'info', message: 'Changes Saved <i class="fa fa-check-circle text-success"></i>'});
				}.bind(this))
				.catch(function(error) {
					eventHub.emit('error', error);
				}.bind(this))
			},

			getAvailableShippingZones: function()
			{
				axios.get('/api/theme/shipping-availability', {
					params: {zone_id: this.zone.id}
				})
				.then(function(response) {
					this.states = response.data;
				}.bind(this))
				.catch(function(error) {
					eventHub.emit('error', error);
				}.bind(this))
			}
		}
	}
</script>

<style>
.postalCodes {
	display: grid;
	grid-template-columns: repeat(auto-fill, minmax(100px, 1fr));
	border-top: solid 1px #eee;
	border-left: solid 1px #eee;
}

.postalCodes > li {
	background-color: #FFF;
	border-bottom: solid 1px #eee;
	border-right: solid 1px #eee;
	text-align: center;
	padding: 0.5rem 0.25rem;
}

.postalCodeSearch {
	width: auto;
}
</style>

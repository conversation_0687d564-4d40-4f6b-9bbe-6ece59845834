<template>
<div>
	<div class="panel panel-default">
	    <div class="panel-body pa-0">
	        <div class="table-responsive">
	            <table class="table table-striped table-full table-list">
	                <thead>
	                <tr>
	                    <th>Name</th>
	                </tr>
	                </thead>
	                <tbody>
	                	<tr v-for="zone in zones" :key="zone.id">
	                		<td><a :href="'/admin/logistics/delivery/' + zone.id + '/edit'">{{ zone.title }}</a></td>
	                	</tr>
	                	<tr v-if="!zones.length">
	                		<td>
	                			<a href="#" class="btn btn-light" @click="$broadcast('createDeliveryZone:toggle')">Add Delivery Zone</a>
	                		</td>
	                	</tr>
	                </tbody>
	            </table>
	        </div>
	    </div>
	</div>
	<create-delivery-zone :permitted="permitted" @stored="stored"></create-delivery-zone>
</div>	
</template>

<script>
	import CreateDeliveryZone from './CreateDeliveryZone.vue';
	export default {

		created: function()
		{
			this.getDeliveryZones()
		},

		props: ['permitted'],

		components: {
			CreateDeliveryZone,
		},

		data: function()
		{
			return {
				zones: [],
			}
		},

		methods: {
			getDeliveryZones: function()
			{
				axios.get('/api/logistics/delivery')
					.then(function(response)
					{
						this.zones = response.data
					}.bind(this))
					.catch(function(error)
					{
						eventHub.emit('error', error)
					}.bind(this))
			},

			stored: function(zone) {
				this.zones.push(zone);
			}
		}
	}
</script>
<template>
    <div class="table-responsive">
        <table class="table table-striped table-full table-list">
            <thead>
                <tr>
                    <th>Member</th>
                    <th>Email</th>
                    <th>Role</th>
                    <th>Last Login</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                <tr v-for="member in members">
                    <td>{{ member.first_name }} {{ member.last_name }}
                        <small><a :href="'/admin/users/' + member.id + '/edit'">(Edit customer profile)</a></small>
                    </td>
                    <td>{{ member.email }}</td>
                    <td>{{ member.role }}</td>
                    <td>{{ member.last_login }}</td>
                    <td>
                        <button type="button" class="btn btn-sm btn-danger" @click="initiateRevoke(member)" v-show="member.role_id != 1">
                            <i class="fa fa-ban fa-lg"></i> Revoke Access
                        </button>
                    </td>
                </tr>
            </tbody>
        </table>
        <div class="gc-modal gc-modal-mask" id="grantAccessModal" @click="hideModal('grantAccessModal')">
            <div class="gc-modal-wrapper">
                <div class="gc-modal-container" @click.stop>
                    <div class="gc-modal-header">
                        Add Team Member
                    </div>
                    <div class="gc-modal-body">
                        <div class="form-group">
                            <v-select
                                :options="searchResults"
                                @search="search"
                                v-model="member"
                                label="full_name_with_email"
                                placeholder="Search for an existing customer account to add to your team."
                            ></v-select>
                        </div>

                        <div class="form-group" v-if="member">
                            <label>Choose access privilege:</label>
                            <div class="radio">
                                <label>
                                    <input type="radio" name="role_admin" v-model="member.type" id="role_admin" value="2" checked>
                                    <strong>Admin</strong><br>
                                    Has full access except for billing &amp; team.
                                </label>
                            </div>
                            <div class="radio">
                                <label>
                                    <input type="radio" name="role_admin" v-model="member.type" id="role_editor" value="3">
                                    <strong>Editor</strong><br>
                                    Can only author blog posts &amp; recipes.
                                </label>
                            </div>
                        </div>
                    </div>

                    <div class="gc-modal-footer">
                        <button type="button" class="btn btn-alt" @click="hideModal('grantAccessModal')">Cancel</button>
                        <button
                            type="button"
                            class="btn btn-action"
                            @click="grantAccess(member)"
                            :disabled="!member"
                        >Grant Access</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="gc-modal gc-modal-mask" id="revokeAccessModal" @click="hideModal('revokeAccessModal')">
            <div class="gc-modal-wrapper">
                <div class="gc-modal-container" @click.stop>

                    <div class="gc-modal-header">
                        Revoke Access
                    </div>

                    <div class="gc-modal-body">
                        <span v-if="member">
                            Are you sure you want to revoke access for <strong>{{ member.first_name }} {{ member.last_name }}</strong>? They will no longer be able to access the GrazeCart admin.
                        </span>
                    </div>

                    <div class="gc-modal-footer">
                        <button type="button" class="btn btn-alt" @click="hideModal('revokeAccessModal')">Cancel</button>
                        <button type="button" class="btn btn-danger" @click="revokeAccess(member)">Revoke Access</button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import vSelect from 'vue-select'
import 'vue-select/dist/vue-select.css';

export default {

    components: {
        vSelect
    },

    created: function() {
        this.getMembers();
        this.search();
        eventHub.on('TeamManager:create', this.initiateGrant);
    },

    data: function() {
        return {
            members: [],
            member: null,
            searchResults: []
        }
    },

    methods: {
        search: function(search) {
            axios.get('/api/users', {params: {
                    users: search,
                    types: [4]
                }})
                .then(function(response) {
                    this.searchResults = response.data
                }.bind(this))
                .catch(function(error) {
                })
        },

        getMembers: function() {
            axios.get('/api/team')
                .then(function(response) {
                    this.members = response.data;
                }.bind(this))
                .catch(function(error) {
                })
        },

        initiateGrant: function() {
            this.member = '';
            this.showModal('grantAccessModal');
        },

        grantAccess: function(member) {
            axios.post('/api/team', {email: member.email, type: member.type})
                .then(function(response) {
                    this.hideModal('grantAccessModal');
                    this.email = ''
                    this.getMembers()
                }.bind(this))
                .catch(function(error) {
                    this.hideModal('grantAccessModal');
                    eventHub.emit('error', error);
                }.bind(this))
        },

        initiateRevoke: function(member) {
            this.member = member
            this.showModal('revokeAccessModal');
        },

        revokeAccess: function(member) {
            eventHub.emit('showProgressBar');
            axios.delete('/api/team/'+member.id)
                .then(function(response) {
                    this.hideModal('revokeAccessModal');
                    this.member = null
                    eventHub.emit('hideProgressBar');
                    this.getMembers();
                }.bind(this))
                .catch(function(error) {
                    eventHub.emit('error', error);
                    eventHub.emit('hideProgressBar');
                })
        }
    }
}
</script>
function bulkAction(action, param, openInNewWindow) {
    var form = document.getElementById('bulkActionForm');

    form.action = '/admin/orders/bulk-update/' + action + '/' + param;

    // actionInput.attr('name', action).val(param);
    if (typeof (openInNewWindow) !== 'undefined') {
        form.setAttribute('target', '_blank');
    } else {
        form.setAttribute('target', '_self');
    }


    form.submit();
}

$.fn.extend({
    insertAtCaret: function(myValue) {
        this.each(function() {
            if (document.selection) {
                this.focus();
                var sel = document.selection.createRange();
                sel.text = myValue;
                this.focus();
            } else if (this.selectionStart || this.selectionStart == '0') {
                var startPos = this.selectionStart;
                var endPos = this.selectionEnd;
                var scrollTop = this.scrollTop;
                this.value = this.value.substring(0, startPos) +
                    myValue + this.value.substring(endPos, this.value.length);
                this.focus();
                this.selectionStart = startPos + myValue.length;
                this.selectionEnd = startPos + myValue.length;
                this.scrollTop = scrollTop;
            } else {
                this.value += myValue;
                this.focus();
            }
        });
        return this;
    }
});

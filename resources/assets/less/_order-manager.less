.orderPager__count {
    font-weight: lighter;
  }
  
@media (max-width: @mobile-navigation-break) {
    .orderPager__count {
        padding-right: 0;
        padding-left: 0;
    }

    .processOrderButton--unprocessed {
        width: 90px;
        padding-right: 0px;
        padding-left: 0px;
    }

    .processOrderButton--unprocessed i {
        color: @warning;
    }

    .processOrderButton-processed {
        color: @gray-6 !important;
        width: 90px;
        padding-right: 0px;
        padding-left: 0px;
    }

    .processOrderButton-processed i {
        color: @primary;
    }
}

@media (max-width: @phone-sm) {
    .orderPager__count {
      display: none;
    }
  }
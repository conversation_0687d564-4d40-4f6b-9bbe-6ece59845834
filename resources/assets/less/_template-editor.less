.emailTemplateEditor {
	height: 100%;
}

.emailTemplateEditor__header {
	position: absolute;
	width: 100%;
	top: 0;
	left: 0;
	max-height: 63px;
	z-index: 200;
}


.emailTemplateEditor__body {
	width: 100%;
	height: 100%;
	display: flex;
	flex-wrap: no-wrap;
	z-index: 100;
	position: absolute;
	top: 0;
	bottom: 0;
}

.emailTemplateEditor__previewContainer {
	flex: 1 1 auto;
	height: 100%;
	overflow: auto;
	padding: @spacing-small;
	iframe, img {
		max-width: 100% !important;;
	}
	table {
		width: 100% !important;
	}
	.segment--dragged {
		width: 100%;
		height: 200px;
		border: solid 2px rgba(0, 0, 0, 0.6);
	}
}

.emailTemplateEditor__toolbarContainer {
	flex: 1 0 400px;
	max-width: 400px;
	height: 100%;
	border-right: solid 1px @border-color;
	background-color: #FFF;
	position: relative;
}

.TemplateSettings .emailTemplateEditor__toolbarContainer {
	flex: 1 0 100%;
	max-width: 100%;
}

.TemplateSettings .emailTemplateEditor__previewContainer {
	display: none;
}

.emailTemplateEditor__segmentList {
	list-style-type: none;
	padding: 0;
	max-width: 600px;
	margin: 30px auto;
	background-color: #FFF;
	overflow: hidden;
}

.emailTemplateEditor__segmentListItem {
	margin: 0;
	padding: 0;
	display: block;
	line-height: 1;
}

.emailTemplateEditor__segmentContainer {
	position: relative;
	border: solid 0px transparent;
	min-height: 2px;
}

.emailTemplateEditor__segmentContainer:hover {
	min-height: 42px;
}

.draghandle:hover {
	cursor: move;
}

.emailTemplateEditor__segmentHeaderContainer {
	position: absolute;
	top: 0;
	left: 0;
	height: 100%;
	width: 100%;
	border: dotted 2px transparent;
	z-index: 100;
	.emailTemplateEditor__segmentHeader {
		display: none;
	}
}


.emailTemplateEditor__segmentHeaderContainer:hover {
	border: dashed 2px @gray-3;
	cursor: pointer;
	.emailTemplateEditor__segmentHeader {
		display: flex;
		z-index: 200;
	}
}

.emailTemplateEditor__segmentHeaderContainer--active {
	border: dashed 2px @gray-3;
}

.emailTemplateEditor__segmentHeader {
	width: 100%;
	height: 40px;
	background-color: @gray-3-t;
	color: #FFF;
	padding: 0px 12px;
	font-size: @font-size-1;
	display: flex;
	align-items: center; 
	span {
		display: inline-block;
		flex: 0 0 auto;
	}
	span.draghandle {
		margin-right: auto;
	}
	a {
		color: #FFF;
		padding: 0 @spacing-small;
	}
	a:hover {
		color: #EEE;
	}
}

.segmentToolbar {
	padding: 1em 1.5em;
}

.emailTemplateEditor__navigation {
	width: 100%;
	padding: 0px;
}

.emailTemplateEditor__blocksContainer {
	display: grid;
	grid-template-columns: 1fr 1fr 1fr;
	grid-gap: @spacing-small;
	margin-top: @spacing-small;
	i {
		display: block;
		margin-bottom: 0.25em;
		font-size: 2rem !important;
	}
}

.emailTemplateEditor__block {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 100px;
	width: 100px;
	width: 100%;
	text-align: center;
	border: solid 1px @gray-9;
	background-color: @gray-10;
}

.emailTemplateEditor__segmentEditorContainer {
	height: 100%;
	position: relative;
}

// Template Segments
.emailTemplateEditor__emptySegmentList {
	text-align: center;
	padding: 30px 30px;
	border: dotted 2px rgba(0, 0, 0, 0.6);

}

.templateSegment__container {
	height: 100%;
	position: relative;
}

// Default email styles
.emailTemplateEditor__segmentList {
	font-size: 17px;
	background-color: #FFFFFF;
	color: #3D3D3D;
}

.emailTemplate_richText {
	line-height: 1.75;
	font-size: 17px;
	font-weight: normal;
	color: #3d3d3d;
	font-family: 'Arial';

	h1, h2, h3, h4, h5, h6 {
		margin: 0 0 0.5em 0;
        padding: 0;
        font-weight: bold;
        line-height: 1.25;
	}

	h1 {font-size: 36px;}
    h2 {font-size: 30px;}
    h3 {font-size: 24px;}
    h4 {font-size: 18px;}
    h5 {font-size: 14px;}
    h6 {font-size: 12px;}

	p {
		line-height: 1.75;
		margin: 0 0 1em 0;
	}
}
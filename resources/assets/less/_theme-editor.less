.themeEditor__toolbar {
	width: 100%;
	max-width: 300px;
	background-color: #FFF;
	text-align: center;
	padding: 10px;
	> div > .btn {
		width: 100%;
		transition: all 0.2s ease-in-out;
	}
}

.themeEditor {
	display: flex;
	height: 100%;
}

.themeEditor__navigation {
	flex-basis: 300px;
	flex-grow: auto;
	flex-shrink: 0;
	max-width: 300px;
	background-color: #FFF;
	overflow: auto;
	vertical-align: top;
	transition: all 0.1s ease-in-out;
}

.themeEditor__navigation--wide {
	flex-basis: 450px;
	max-width: 450px;
}

.themeEditorWidget {
	height: 100vh;
}

.themeEditor__settingControls {
	padding: 20px;
	color: @gray;
	> h2 {
		margin: 0 0 20px 0;
		font-weight: bolder;
		font-size: @font-size-2;
		text-align: center;
	}
	> h3 {
		margin: 0 0 20px 0;
		font-weight: bolder;
		font-size: @font-size-3;
		text-align: center;
	}
}

.themeEditor__previewContainer {
	flex-basis: 1;
	flex-grow: 1;
	flex-shrink: 1;
	height: 100%;
	z-index: 0;
	position: relative;
}

.themeEditor__preview {
	width: 100%;
	height: 100%;
	top: 0;
	left: 0;
	vertical-align: top;
	margin: 0 auto;
	display: block;
	transition: all 0.3s ease-in-out;
	border-top: none;
}

.themeEditor__previewToolbar {
	width: 100%;
	text-align: center;
	border-bottom: solid 1px @gray-lighter;
	padding: 10px 0;

}

.themeEditorWidget__navigation {
	list-style-type: none;
	margin: 0;
	padding: 0;
	> li {
		display: block;
		width: 100%;
		position: relative;
		> a {
			transition: all 0.1s ease-in-out;
			display: block;
			width: 100%;
			padding: 16px 16px;
			border-bottom: solid 1px @gray-lighter;
			color: @gray-medium;
			font-weight: bold;
		}
		> a:hover {
			text-decoration: none;
			background-color: @gray-lighter;
		}
	}
}

.themeEditorWidget__navigation--inverted {
	background-color: @gray;
	color: #FFF !important;
	> li > a {color: #FFF;}
}
.panel {
	margin-bottom: @spacing-large;
	background-color: #FFF;
	border-color: #FFF;
	.br-sm();
	.shadow();
  max-width: 100%;
}

.panel-tabs {
  .br--b();
}

.panel-body {
  padding: @spacing-medium-small @spacing-medium;
  font-size: @font-size-normal;
}

.panel-tabs {
  .panel-body {
    padding-top: @spacing-medium;
  }
}

.panel-footer {
  padding: @spacing-medium-small @spacing-medium;
  border-top: solid 1px @gray-light;
}

.panel-heading {
  font-weight: bold;
  font-size: @font-size-1;
  padding: @spacing-medium-small @spacing-medium;
  border-bottom: solid 1px @gray-light;
  color: @gray;
  background-color: #FFF;
  .br-sm();
  .br--t();
  a {
  	font-size: @font-size-normal;
  	color: @gray;
  }
}

.panel {
  > .list-group,
  > .panel-collapse > .list-group {
    margin-bottom: 0;

    .list-group-item {
      border-width: 1px 0;
      border-radius: 0;
    }

    // Add border top radius for first one
    &:first-child {
      .list-group-item:first-child {
        border-top: 0;

      }
    }
    // Add border bottom radius for last one
    &:last-child {
      .list-group-item:last-child {
        border-bottom: 0;

      }
    }
  }
}
// Collapse space between when there's no additional content.
.panel-heading + .list-group {
  .list-group-item:first-child {
    border-top-width: 0;
  }
}
.list-group + .panel-footer {
  border-top-width: 0;
}

// Styles
.panel-success, .panel-info {
	.panel-heading {
		background-color: @primary;
		border-color: @primary;
		color: #FFF;
	}
}

.panel-warning, .panel-danger {
	.panel-heading {
		background-color: @warning-light;
		border-color: @warning-light;
		color: #FFF;
	}
}

// Modal panels
.gc-modal-body .panel, .panel-body .panel {
  .shadow-soft();
  background-color: @gray-lighter;
  border: solid 1px @gray-light;
}

.gc-modal-body .panel-heading, .panel-body .panel {
  font-size: @font-size-normal;
  padding: @spacing-small @spacing-medium;
}

// Dashboard
.dashboard-panel {
  min-height: 500px;
  max-height: 500px;
  overflow: scroll;
}

// Full height panels

.settingsPanel {
  height: 100%;
  width: 100%;
  position: relative;
}

.settingsPanel__heading {
  text-align: center;
  font-size: @font-size-1;
  font-weight: bold;
  padding: @spacing-medium;
}

.settingsPanel__header {
  height: @spacing-extra-large;
  width: 100%;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  border-bottom: solid 1px @gray-light;
  overflow: hidden;
  background-color: #FFF;
  padding: 0 @spacing-medium;
}

.settingsPanel__body {
  width: 100%;
  position: absolute;
  top: 0;
  bottom: @spacing-extra-large;
  left: 0;
  right: 0;
  overflow: auto;
  padding: @spacing-medium;
}

.settingsPanel__header + .settingsPanel__body {
  top: @spacing-extra-large;
}

.settingsPanel__footer {
  height: @spacing-extra-large;
  width: 100%;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  border-top: solid 1px @gray-light;
  overflow: hidden;
  background-color: #FFF;
  padding: 0 @spacing-medium;
}

.panel-open {
  position: fixed;
  overflow: hidden;
  width: 100%;
  height: 100%;
}

.panel-open::after {
  content: '';
  position: fixed;
  z-index: @zindex-modal - 11;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: hsla(165, 1%, 1%, 0.7);
  opacity: 1;
  animation: fadein 0.2s ease-in-out 1;
}
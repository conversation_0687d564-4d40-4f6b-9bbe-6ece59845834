// .alert {
//   padding: @spacing-medium;
//   border: 1px solid @gray-light;
//   background-color: transparent;
//   border-radius: @border-radius-small;
//   color: @gray;
//   line-height: 1.25rem;
//   a {
//     color: @gray;
//     text-decoration: underline;
//   }
//   a.btn {
//     text-decoration: none;
//     color: inherit;
//   }
// }

// .alert-info {
//   background-color: @gray;
//   border-color: @gray;
//   .shadow();
//   color: #FFF;
//   a {color: #FFF;}
// }

// .alert-danger, .alert-warning {
//   background-color: @warning;
//   border-color: @warning;
//   .shadow();
//   color: #FFF;
//   a {color: #FFF;}
// }

.alert {
  padding: @spacing-medium;
  margin-bottom: @spacing-large;
  background-color: #FFF;
  border-radius: @border-radius-small;
  color: @gray;
  line-height: 1.25rem;
  .shadow();
  a {
    color: #FFF;
    text-decoration: underline;
  }
  .btn {
    text-decoration: none;
    i {
      color: inherit;
    }
  }
}

.alert-info {
  border-left: solid 6px @primary;
  i {
    color: @primary;
  }
}

.alert-danger, .alert-warning {
  border-left: solid 6px @warning;
  i {
    color: @warning;
  }
}

.panel-body, .gc-modal-body {
  .alert-info {
    border: solid 1px @primary;
    .shadow-soft();
  }
  .alert-danger, .alert-warning {
    border: solid 1px @warning;
    .shadow-soft();
  }
}

.btn {
  display: inline-block;
  margin-bottom: 0;
  font-weight: bold;
  font-size: @font-size-normal;
  text-align: center;
  vertical-align: middle;
  touch-action: manipulation;
  cursor: pointer;
  background-image: none;
  border: 1px solid transparent;
  border-radius: @border-radius-extra-small;
  white-space: nowrap;
  min-height: @input-height;
  line-height: 1rem;
  padding: @spacing-small @spacing-medium-small;
  user-select: none;
  &.disabled,
  &[disabled] {
    cursor: not-allowed;
    pointer-events: none;
    opacity: 0.75;
  }
  &:focus {
    outline: none;
  }
  &:hover {
    // transform: translate(0, -1px);
  }
}

.btn-sm {
  height: auto;
  min-height: auto;
  line-height: auto;
  padding: @spacing-extra-small @spacing-small;
  font-size: @font-size-small !important;
}

.btn-lg {
  height: auto;
  min-height: auto;
  line-height: auto;
  padding: @spacing-medium-small @spacing-medium;
  font-size: @font-size-1 !important;
}

.btn-block {
  display: block;
  width: 100%;
}

.btn-fill {
  height: 100%;
  width: 100%;
  vertical-align: middle;
}

input[type="submit"],
input[type="reset"],
input[type="button"] {
  &.btn-block {
    width: 100%;
  }
}

/* Button Styles */

.btn-link {
  min-height: auto;
}

.btn-alt, .btn-secondary {
  border-color: transparent;
  background-color: transparent;
  color: @gray;
    &:hover, &:focus {
    color: @gray-dark;
  }
}

.btn-clear {
  border-color: transparent;
  background-color: transparent;
  color: @gray-medium;
  &:hover, &:focus {
    border-color: transparent;
    background-color: transparent;
    color: @gray-dark;
  }
}

.btn-white {
  border-color: @gray-light;
  background-color: #FFF;
  color: @gray;
  &:hover, &:focus {
    border-color: @gray;
    background-color: @gray;
    color: #FFF;
    .shadow();
  }
}

.btn-light, .btn-primary, .btn-default, .btn-success {
  border-color: @gray-light;
  background-color: @gray-lighter;
  color: @gray;
  &:hover, &:focus {
    border-color: @gray;
    background-color: @gray;
    color: #FFF;
    .shadow();
  }
}

.btn-action, .btn-cta {
  border-color: @primary;
  background-color: @primary;
  color: #FFF;
  &:hover, &:focus {
    border-color: @primary-light;
    background-color: @primary-light;
    color: #FFF;
    .shadow();
  }
}

.btn-danger {
  border-color: @warning;
  background-color: @warning;
  color: #FFF;
  &:hover, &:focus {
    border-color: @warning-dark;
    background-color: @warning-dark;
    color: #FFF;
    .shadow();
  }
}

.btn-gray {
  border-color: @gray;
  background-color: @gray;
  color: #FFF;
  &:hover, &:focus {
    border-color: @warning-dark;
    background-color: @warning-dark;
    color: #FFF;
    .shadow();
  }
}

.input-overlay-left {
  padding-left: @spacing-large;
}

.btn-input-overlay {
  width: 34px !important;
  margin-right: -34px;
  padding: 0 !important;
  z-index: 100;
  float: left;
}

.btn-group {
  display: inline-flex;
  position: relative;
}

.btn-group {
  .btn + .btn,
  .btn + .btn-group,
  .btn-group + .btn,
  .btn-group + .btn-group {
    margin-left: -1px;
  }
}

.btn-group > .btn:first-child {
  .br--l();
}

.btn-group > .btn:last-child {
  .br--r();
}

.btn-group > .btn:not(:first-child):not(:last-child):not(.dropdown-toggle) {
  border-radius: 0;
}

.btn-active {
  color: @gray-dark;
}

.btn-not-active {
  color: @gray-medium;
}

.btn:focus:not(:active) {
  animation: pop 0.2s ease-out 1;
}

// Close button
.close {
  float: right;
  font-size: @font-size-2;
  font-weight: bold;
  line-height: 1rem;
  color: @gray;

  &:hover,
  &:focus {
    color: @primary;
    text-decoration: none;
    cursor: pointer;
  }
  button& {
    padding: 0;
    cursor: pointer;
    background: transparent;
    border: 0;
    -webkit-appearance: none;
  }
}
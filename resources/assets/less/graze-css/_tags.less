.tags {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.tag {
  background-color: @gray-light;
  flex: 0 1 auto;
  display: flex;
  align-items: center;
  font-size: @font-size-small;
  margin-top: 0;
  margin-bottom: 6px;
  margin-right: 6px;
  color: @gray;
  overflow: hidden;
  .br-sm();
}

.tag.confirmed {
  background:hsl(165, 55%, 45%);
  color:white;
}

.tag.paid {
  background-color: #def7ec;
  color: #03543f;
}

.tag.payment-failed {
  background-color: #fde8e8;
  color: rgb(155, 28, 28)
}

.tag.inventory-error, .tag.address-error, .tag.product-error, .tag.minimum-error {
  background-color: rgb(254, 243, 199);
  color: rgb(146, 64, 14);
}

.tag.stock-error,
.tag.address-error
.product-error {
  background-color:rgb(221, 121, 39);
  color:white;
}

.tagTitle {
  flex: 1 1 auto;
  padding: @spacing-extra-small @spacing-small @spacing-extra-small @spacing-small ;
  white-space: nowrap;
  overflow: auto;
}

a.tagClose {
  flex: 0 0 auto;
  color: @gray-medium;
  font-size: @font-size-small;
  padding: @spacing-extra-small @spacing-extra-small @spacing-extra-small 0;
  font-size: @font-size-small !important;
  &:hover {
    color: @gray-dark;
  }
}

@media (max-width: @mobile-navigation-break) {
  .tag.push-right {
    margin-left: 0;
  }
}

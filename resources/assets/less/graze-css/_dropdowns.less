.dropup,
.dropdown {
  position: relative;
}

@keyframes fadeIn {
    0% { 
      opacity: 0.4; 
      transform-origin: 50% -11px; 
      transform: scale(0.8, 0.8);
      transform: translateY(-12px);
    }
    100% {
      opacity: 1;
      transform-origin: 50% -11px;
      transform: scale(1, 1);
      transform: translateY(0);
    }
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  left: -3px;
  z-index: 99999;
  transition: all 0.5s ease-in-out;
  min-width: 160px;
  padding: @spacing-small 0;
  font-size: @font-size-normal;
  text-align: left;
  background-color: rgba(255,255,255,0.98);
  background-clip: padding-box;
  display: none;
  .shadow-hard();
  .br-sm();
  .divider {
  	height: 1px;
  	margin: @spacing-extra-small;
  	overflow: hidden;
  	background-color: @gray-lighter;
  }
  > li > a {
    display: block;
    padding: @spacing-medium-small @spacing-medium @spacing-medium-small @spacing-medium;
    clear: both;
    font-weight: normal;
    line-height: 1rem;
    color: @gray;
    transition: all ease-in-out 0.1s;
    white-space: nowrap;
    &:hover {
    	background-color: @primary;
    	color: #FFF;
    }
    &:first-of-type {
      margin-top: 3px;
    }
  }
  > li.dropdown-header {
    padding: @spacing-medium @spacing-medium;
    border: solid 1px @border-color;
    border-top: none;
    border-left: none;
    border-right: none;
    .border-light();
    .bold();
    .text-gray-medium();
  }
  &::before {
    content: "";
    display: block;
    height: 16px;
    width: 16px;
    transform: rotate(45deg);
    background-color: #FFF;
    position: absolute;
    top: -8px;
    left: 16px;
    filter: drop-shadow(-1px -1px 1px rgba(50,50,93,0.06));
  }
  &::after {
    content: "";
    display: block;
    height: 10px;
    width: 30px;
    background-color: #FFF;
    position: absolute;
    top: 0;
    left: 10px;
  }
}

.dropdown .pull-right {
  right: 0;
  left: auto;
  transform-origin: 100% 0%;

  &::before {
    content: "";
    display: block;
    height: 12px;
    width: 12px;
    transform: rotate(45deg);
    background-color: #FFF;
    position: absolute;
    top: -6px;
    left: calc(100% - 18px);
    filter: drop-shadow(-1px -1px 1px rgba(0,0,0,.08));
  }
}

.open {
  > .dropdown-menu {
    display: block !important;
    // transform: scale(1, 1);
    // opacity: 1;
    animation: fadeIn 0.15s;
  }
  > a {
    outline: 0;
  }
}

.dropdown-menu.daterangepicker {
  display: none;
  transform: scale(1, 1);
  opacity: 100;
}

.dropdown-backdrop {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  top: 0;
  z-index: (99999 - 10);
}
iframe {
  width: 100%;
}

.form-clear {
  position: relative;
  > a {
    position: absolute;
    top: 0px;
    right: 0px;
    height: 34px;
    width: 34px;
    background-color: transparent;
    border: none;
    outline: none;
    font-size: 1rem;
    line-height: 34px;
    display: none;
    vertical-align: middle;
    text-align: center;
    > i {
      margin: auto;
      flex: 0 0 auto;
    }
    &:hover {
      color: @primary;
    }
  }
  input[type="text"]:focus + a {
    display: block;
  }
}

.filterPanel {
  .shadow();
  position: fixed;
  height: 100%;
  top: 0;
  left: -350px;
  transform: translate(-350px, 0);
  z-index: @zindex-panel;
  background-color: rgba(255, 255, 255, 0.98);
  width: 350px;
  max-width: 100%;
  -webkit-overflow-scrolling: touch;
  transition: transform ease-in-out 0.15s;
}

.filterPanel-enter {
  left: 0;
  position: absolute;
  transform: translate(0, 0);
}

.mediaBrowser {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  height: 100vh;
  background-color: #fff;
  overflow: scroll;
  z-index: 10000;
  padding-top: 120px;
  padding-bottom: 55px;
}

.mediaBrowser-header {
  position: fixed;
  top: 0;
  left: 0;
  z-index: 1030;
  width: 100%;
  height: auto;
  padding: 0px;
}

.mediaBrowser-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  z-index: 1030;
  width: 100%;
  background-color: #fafafa;
  border-bottom: solid 1px #eee;
  padding: 10px;
  text-align: center;
}

.mediaBrowser-heading {
  display: flex;
  height: 50px;
  border-bottom: solid 1px #eee;
  background-color: #fafafa;
  justify-content: space-between;
  align-items: center;
  padding: 0 30px;
  > div {
    flex: 1 1 auto;
    max-width: 50%;
  }
}

.mediaBrowser-toolbar {
  height: auto;
  background-color: #fff;
  border-bottom: solid 1px #ddd;
  padding: 20px 30px;
}

.mediaBrowser-title {
  h2 {
    margin: 0;
    padding: 0;
    line-height: 1;
    font-size: 20px;
  }
}

.mediaBrowser-body {
  position: relative;
  width: 100%;
  padding: 10px 0;
  max-height: 100%;
  overflow: scroll;
}

.mediaBrowser-navigation {
  margin: 20px auto;
  width: 100%;
  text-align: center;
}

.mediaBrowser-close {
  text-align: right;
}

.mediaBrowser-close > a {
  color: #4d4d4d;
  font-size: 20px;
  padding: 8px 8px;
  margin: 0;
  line-height: 1;
  display: block;
}

.mediaBrowser-close > a:hover {
  color: @primary;
  text-decoration: none;
}

.mediaBrowser-gallery-list {
  padding: 0 30px;
  text-align: left;
  table {
    width: 100%;
    border-collapse: collapse;
    border: none;
  }
  table tr {
    padding: 20px 0px;
    border-bottom: solid 1px #eee;
  }
  table tr td {
    padding: 20px 5px;
  }
  img {
    max-width: 100px;
  }
  img:hover {
    cursor: zoom-in;
  }
}

.mediaBrowser-gallery {
  text-align: center;
  overflow: scroll;
}

.mediaBrowser-gallery-container {
  display: inline-block;
  vertical-align: middle;
  margin: 14px;
  background-color: #f4f4f4;
}

.mediaBrowser-image-container {
  margin: 0 auto 10px auto;
  text-align: center;
  width: 250px;
  height: 166px;
  img {
    max-width: 100%;
    max-height: 100%;
  }
  img:hover {
    cursor: pointer;
  }
}

.mediaBrowser-image-title {
  padding: 10px;
  > h3 {
    font-size: 13px;
    line-height: 1;
    margin: 0 0 10px 0;
  }
}

.gallerySquare {
  display: flex;
  flex-wrap: wrap;
  justify-content: flex-start;
}

.thumbnailContainer {
  margin: 0 19px 30px 19px;
  .caption a {
    display: block;
    width: 150px;
    text-align: center;
    overflow: hidden;
    white-space: pre-wrap;
    word-wrap: break-word;
    margin-top: 0.25em;
  }
}

.thumbnailSquare {
  position: relative;
  width: 150px;
  height: 150px;
  overflow: hidden;
}

.thumbnailContainer-sm {
  margin: 0 1em 1em 0;
}

.thumbnailSquare-sm {
  width: 100px;
  height: 100px;
  border: solid 4px #ffffff;
}

.selected {
  border: solid 4px @primary;
}

.thumbnailSquare img {
  padding: 0px;
  position: absolute;
  left: 50%;
  top: 50%;
  height: 100%;
  width: auto;
  -webkit-transform: translate(-50%, -50%);
  -ms-transform: translate(-50%, -50%);
  transform: translate(-50%, -50%);
}

.thumbnailSquare:hover {
  cursor: pointer;
}
.thumbnailSquare img.portrait {
  width: 100%;
  height: auto;
}

img.thumbnailList {
  height: 75px;
}

img.thumbnailList:hover {
  cursor: pointer;
}

#mediaManagerModal {
  .modal-dialog {
    max-height: 100%;
    width: 90%;
  }
  .nav-tabs {
    margin-bottom: 1em;
  }
}

.media-manager-gallery {
  max-height: 500px;
  overflow: scroll;
}

.previewImage {
  max-height: 275px;
  max-width: 100%;
}

// Dropzone
.dropzone,
.dropzone * {
  box-sizing: border-box;
}

.dropzone {
  min-height: 150px;
  border: 4px dashed @gray;
  margin-bottom: 1em;
  background: transparent;
  padding: 20px 20px;
  &:hover {
    border-color: @primary;
  }
}
.dropzone.dz-clickable {
  cursor: pointer;
}
.dropzone.dz-clickable * {
  cursor: default;
}
.dropzone.dz-clickable .dz-message,
.dropzone.dz-clickable .dz-message * {
  cursor: pointer;
}
.dropzone.dz-started .dz-message {
  display: none;
}
.dropzone.dz-drag-hover {
  border-style: solid;
}
.dropzone.dz-drag-hover .dz-message {
  opacity: 0.5;
}
.dropzone .dz-message {
  text-align: center;
  font-size: 20px;
  color: @gray;
  margin: 2em 0;
}
.dropzone .dz-preview {
  position: relative;
  display: inline-block;
  vertical-align: top;
  margin: 16px;
  min-height: 100px;
}
.dropzone .dz-preview:hover {
  z-index: 1000;
}
.dropzone .dz-preview:hover .dz-details {
  opacity: 1;
}
.dropzone .dz-preview.dz-file-preview .dz-image {
  border-radius: 20px;
  background: #999;
  background: linear-gradient(to bottom, #eee, #ddd);
}
.dropzone .dz-preview.dz-file-preview .dz-details {
  opacity: 1;
}
.dropzone .dz-preview.dz-image-preview {
  background: white;
}
.dropzone .dz-preview.dz-image-preview .dz-details {
  -webkit-transition: opacity 0.2s linear;
  -moz-transition: opacity 0.2s linear;
  -ms-transition: opacity 0.2s linear;
  -o-transition: opacity 0.2s linear;
  transition: opacity 0.2s linear;
}
.dropzone .dz-preview .dz-remove {
  font-size: 14px;
  text-align: center;
  display: block;
  cursor: pointer;
  border: none;
}
.dropzone .dz-preview .dz-remove:hover {
  text-decoration: underline;
}
.dropzone .dz-preview:hover .dz-details {
  opacity: 1;
}
.dropzone .dz-preview .dz-details {
  z-index: 20;
  position: absolute;
  top: 0;
  left: 0;
  opacity: 0;
  font-size: 13px;
  min-width: 100%;
  max-width: 100%;
  padding: 2em 1em;
  text-align: center;
  color: rgba(0, 0, 0, 0.9);
  line-height: 150%;
}
.dropzone .dz-preview .dz-details .dz-size {
  margin-bottom: 1em;
  font-size: 16px;
}
.dropzone .dz-preview .dz-details .dz-filename {
  white-space: nowrap;
}
.dropzone .dz-preview .dz-details .dz-filename:hover span {
  border: 1px solid rgba(200, 200, 200, 0.8);
  background-color: rgba(255, 255, 255, 0.8);
}
.dropzone .dz-preview .dz-details .dz-filename:not(:hover) {
  overflow: hidden;
  text-overflow: ellipsis;
}
.dropzone .dz-preview .dz-details .dz-filename:not(:hover) span {
  border: 1px solid transparent;
}
.dropzone .dz-preview .dz-details .dz-filename span,
.dropzone .dz-preview .dz-details .dz-size span {
  background-color: rgba(255, 255, 255, 0.4);
  padding: 0 0.4em;
  border-radius: 3px;
}
.dropzone .dz-preview:hover .dz-image img {
  -webkit-transform: scale(1.05, 1.05);
  -moz-transform: scale(1.05, 1.05);
  -ms-transform: scale(1.05, 1.05);
  -o-transform: scale(1.05, 1.05);
  transform: scale(1.05, 1.05);
  -webkit-filter: blur(8px);
  filter: blur(8px);
}
.dropzone .dz-preview .dz-image {
  border-radius: 20px;
  overflow: hidden;
  width: 120px;
  height: 120px;
  position: relative;
  display: block;
  z-index: 10;
}
.dropzone .dz-preview .dz-image img {
  display: block;
}
.dropzone .dz-preview.dz-success .dz-success-mark {
  -webkit-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
  -moz-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
  -ms-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
  -o-animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
  animation: passing-through 3s cubic-bezier(0.77, 0, 0.175, 1);
}
.dropzone .dz-preview.dz-error .dz-error-mark {
  opacity: 1;
  -webkit-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
  -moz-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
  -ms-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
  -o-animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
  animation: slide-in 3s cubic-bezier(0.77, 0, 0.175, 1);
}
.dropzone .dz-preview .dz-success-mark,
.dropzone .dz-preview .dz-error-mark {
  pointer-events: none;
  opacity: 0;
  z-index: 500;
  position: absolute;
  display: block;
  top: 50%;
  left: 50%;
  margin-left: -27px;
  margin-top: -27px;
}
.dropzone .dz-preview .dz-success-mark svg,
.dropzone .dz-preview .dz-error-mark svg {
  display: block;
  width: 54px;
  height: 54px;
}
.dropzone .dz-preview.dz-processing .dz-progress {
  opacity: 1;
  -webkit-transition: all 0.2s linear;
  -moz-transition: all 0.2s linear;
  -ms-transition: all 0.2s linear;
  -o-transition: all 0.2s linear;
  transition: all 0.2s linear;
}
.dropzone .dz-preview.dz-complete .dz-progress {
  opacity: 0;
  -webkit-transition: opacity 0.4s ease-in;
  -moz-transition: opacity 0.4s ease-in;
  -ms-transition: opacity 0.4s ease-in;
  -o-transition: opacity 0.4s ease-in;
  transition: opacity 0.4s ease-in;
}
.dropzone .dz-preview:not(.dz-processing) .dz-progress {
  -webkit-animation: pulse 6s ease infinite;
  -moz-animation: pulse 6s ease infinite;
  -ms-animation: pulse 6s ease infinite;
  -o-animation: pulse 6s ease infinite;
  animation: pulse 6s ease infinite;
}
.dropzone .dz-preview .dz-progress {
  opacity: 1;
  z-index: 1000;
  pointer-events: none;
  position: absolute;
  height: 16px;
  left: 50%;
  top: 50%;
  margin-top: -8px;
  width: 80px;
  margin-left: -40px;
  background: rgba(255, 255, 255, 0.9);
  -webkit-transform: scale(1);
  border-radius: 8px;
  overflow: hidden;
}
.dropzone .dz-preview .dz-progress .dz-upload {
  background: #333;
  background: linear-gradient(to bottom, #666, #444);
  position: absolute;
  top: 0;
  left: 0;
  bottom: 0;
  width: 0;
  -webkit-transition: width 300ms ease-in-out;
  -moz-transition: width 300ms ease-in-out;
  -ms-transition: width 300ms ease-in-out;
  -o-transition: width 300ms ease-in-out;
  transition: width 300ms ease-in-out;
}
.dropzone .dz-preview.dz-error .dz-error-message {
  display: block;
}
.dropzone .dz-preview.dz-error:hover .dz-error-message {
  opacity: 1;
  pointer-events: auto;
}
.dropzone .dz-preview .dz-error-message {
  pointer-events: none;
  z-index: 1000;
  position: absolute;
  display: block;
  display: none;
  opacity: 0;
  -webkit-transition: opacity 0.3s ease;
  -moz-transition: opacity 0.3s ease;
  -ms-transition: opacity 0.3s ease;
  -o-transition: opacity 0.3s ease;
  transition: opacity 0.3s ease;
  border-radius: 8px;
  font-size: 13px;
  top: 130px;
  left: -10px;
  width: 140px;
  background: #be2626;
  background: linear-gradient(to bottom, #be2626, #a92222);
  padding: 0.5em 1.2em;
  color: white;
}
.dropzone .dz-preview .dz-error-message:after {
  content: "";
  position: absolute;
  top: -6px;
  left: 64px;
  width: 0;
  height: 0;
  border-left: 6px solid transparent;
  border-right: 6px solid transparent;
  border-bottom: 6px solid #be2626;
}

.subscribe-and-save-table {
  .subscribe-and-save-form {
    display: flex;
    justify-content: flex-end;
  }

  input[name="percentage"],
  input[name="deadline"] {
    width: 100%;
    max-width: 75px;
  }

  input[name="percentage"] {
    margin-left: 5px;
  }

  input[name="deadline"] {
    margin-right: 5px;
  }

  .free-item-option-group {
    margin: 0 0 10px 5px;
  }

  .free-item-options {
    margin: 0 0 0 5px;
    width: 40%;
  }

  .time {
    .free-item-options {
      width: 15%;
    }
  }
  textarea {
    width: 60%;
  }
}

.select2-container--graze {
  display: block;
  width: auto;
  height: 2.375rem;
  font-size: 0.875rem;
  line-height: 2.125rem;
  color: hsl(165, 3%, 40%);
  background-color: #fff;
  background-image: none;
  border: 1px solid hsl(165, 3%, 90%);
  border-radius: 0.25rem;
  outline-color: hsl(165, 55%, 45%);
  position: relative;

  .selected,
  .select2-selection {
    padding: 0 0.5rem;
    display: inline-block;
    width: 100%;
    outline: none;
    &:hover {
      cursor: pointer;
    }
  }

  .select2-results__option--highlighted {
    background-color: hsl(165, 55%, 45%);
    color: #fff;
  }
}

.daysOfWeekButtonGroup {
    display: grid;
    grid-template-columns: repeat(7, 1fr);
    gap: 0.25rem;

    input[type=checkbox] {
        display: none;
    }
    input[type=checkbox]:checked + label {
        border-color: @primary-light;
        background-color: @primary-light;
        color: #FFF;
    }
}

.daysOfWeekButtonGroup label {
    display: flex;
    width: 100%;
    align-items: center;
    .btn();
    .btn-light();
}

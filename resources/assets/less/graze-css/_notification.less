.systemNotification {
	width: 100%;
	min-height: 130px;
	position: fixed;
	top: 0;
	left: 0;
	background-color: rgba(255,255,255,0.95);
	z-index: @zindex-notification;
	border-top: solid 6px @primary;
	.shadow();
	transition: all 0.3s ease-in-out;
	display: table;
}

.systemNotification--success {
	border-top: solid 6px @primary;
}

.systemNotification--error, .systemNotification--danger {
	border-top: solid 6px @warning;
}

.systemNotification__inner-container {
	position: relative;
	text-align: center;
	padding: @spacing-medium @spacing-small;
	font-size: @font-size-1;
	color: @gray;
	font-weight: bold;
	display: table-cell;
	vertical-align: middle;
  	height: 100%;
}

.systemNotification__message {
	flex: 1 0 auto;
}

.systemNotification__close {
	position: absolute;
	top: 10px;
	right: 20px;
	height: 50px;
	width: 50px;
	border-radius: 50px;
	background-color: transparent;
	line-height: 50px;
	font-size: @font-size-4 !important;
	text-align: center;
	color: @gray;
	transition: all ease-in-out 0.2s;
}

.systemNotification__close:hover {
	background-color: @gray-light;
	color: @gray-dark;
	text-decoration: none;
}

.systemNotification__hide {
	top: -300px;
}
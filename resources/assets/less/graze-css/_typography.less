h1, h2, h3, h4, h5, h6 {
  font-family: @font-stack;
  font-weight: bolder;
  line-height: 1;
  margin: 0;
  padding: 0;

  small,
  .small {
    font-weight: normal;
    line-height: 1;
  }
}

h1 { font-size: @font-size-5; }
h2 { font-size: @font-size-4; }
h3 { font-size: @font-size-3; }
h4 { font-size: @font-size-2; }
h5 { font-size: @font-size-normal; }
h6 { font-size: @font-size-normal; }

p {
  margin: 0 0 @spacing-medium 0;
}

small,
.small {
  font-size: @font-size-small;
}

.text-justify        { text-align: justify; }
.text-nowrap         { white-space: nowrap; }

// Line Height
.lh-1 {
  line-height: 1;
}

.lh-2 {
  line-height: 1.25;
}

.lh-3 {
  line-height: 1.5;
}

.lh-4 {
  line-height: 1.75;
}

.lh-5 {
  line-height: 2;
}

.light {font-weight: lighter !important;}
.bold {font-weight: bolder !important;}
.normal {font-weight: normal !important;}

.numberBullet {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 2rem;
  height: 2rem;
  border-radius: 50%;
  flex-shrink: 0;
}

.form-description {
    font-size: @font-size-small;
    line-height: 1.1rem;
    color: @gray-medium;
  }

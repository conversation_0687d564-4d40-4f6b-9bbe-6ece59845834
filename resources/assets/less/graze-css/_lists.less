ul.list-spacing-xs > li {margin-bottom: @spacing-extra-small;}
ul.list-spacing-xs > li:last-child {margin-bottom: 0;}

ul.list-spacing-sm > li {margin-bottom: @spacing-small;}
ul.list-spacing-sm > li:last-child {margin-bottom: 0;}

ul.list-spacing-md > li {margin-bottom: @spacing-medium;}
ul.list-spacing-md > li:last-child {margin-bottom: 0;}

ul.list-spacing-lg > li {margin-bottom: @spacing-large;}
ul.list-spacing-lg > li:last-child {margin-bottom: 0;}

ul.list-spacing-xl > li {margin-bottom: @spacing-extra-large;}
ul.list-spacing-xl > li:last-child {margin-bottom: 0;}

ul.list-spacing-xxl > li {margin-bottom: @spacing-extra-extra-large;}
ul.list-spacing-xxl > li:last-child {margin-bottom: 0;}

// Settings List
.settings-list {
  display: flex;
  flex-wrap: wrap;
  > li {
    flex: 1 0 auto;
    flex-basis: 20%;
    max-width: 20%;
    text-align: center;
    display: block;
    margin: @spacing-medium 0 @spacing-medium 0;
    a {
      display: block;
      width: 100%;
      font-size: @font-size-normal;
      color: @gray-dark;
      transition: all ease-in-out 0.2s;
    }
    a:hover {
      text-decoration: none;
      color: @primary;
      .text-shadow();
    }
    i {
      	display: block;
      	font-size: @font-size-4;
      	margin: 0 auto @spacing-medium-small auto;
    }
  }
}

.draggable-list {
  > li {
    display: block;
    width: 100%;
    margin-bottom: @spacing-medium;
    .br-sm();
    background-color: @gray-lighter;
    border: solid 1px @gray-light;
    .shadow-soft();
    padding: @spacing-medium-small @spacing-small;
  }
  .sortable-chosen {
    .shadow();
  }
  .sortable-ghost {
    .shadow();
  }
}

.draghandle {
  cursor: move;
  display: inline-block;
  padding-left: @spacing-extra-small;
  padding-right: @spacing-extra-small;
  color: @gray-medium;
  &:hover {
    color: @gray;
  }
  &:before {
    content: "\f7a4";
    font-weight: normal;
  }
}

.list-styled {
  list-style-type: disc;
  margin-left: @spacing-medium;
}
@media (max-width: 800px) {
  .settings-list {
    > li {
      flex-basis: 33.333%;
      max-width: 33.333%;
    }
  }
}

@media (max-width: 450px) {
  .settings-list {
    > li {
      flex-basis: 50%;
      max-width: 50%;
    }
  }
}
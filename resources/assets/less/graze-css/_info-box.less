.info-boxes {
  padding: 0;
  display: flex;
  flex-wrap: wrap;
  margin-bottom: -@spacing-medium + @spacing-large;
  margin-right: -@spacing-medium;
  > li {
    flex: 1 1 auto;
    color: @gray;
    font-size: @font-size-normal;
    margin-right: @spacing-medium;
    margin-bottom: @spacing-medium;
    background-color: #FFF;
    border: 1px solid transparent;
    .br-sm();
    .shadow();
    padding: @spacing-medium-small @spacing-medium;
    align-items: stretch;
    .info-box-heading {
      color: @gray;
      font-size: @font-size-1;
      display: block;
      margin-bottom: @spacing-small;
      font-weight: bolder;
    }
    &:last-of-type {
      // margin-right: 0;
    }
  }

  .info-hide {
    display: none;
  }

  .show-hidden-info {
    .info-hide {
      display: block;
    }
  }
}

@media (max-width: @mobile-navigation-break) {
  // .info-boxes {
  //   > li {
  //     margin-right: 0;
  //     margin-bottom: @spacing-medium;
  //   }    
  // }
}
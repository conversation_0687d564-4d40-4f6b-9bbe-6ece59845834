label {
  display: inline-block;
  font-size: @font-size-normal;
  font-weight: bold;
  .label-context {
    display: block;
    color: @gray-5;
    font-weight: normal;
    font-size: @font-size-small;
  }
}

.checkbox, .radio {
  label {
    font-weight: normal;
    margin-top: @spacing-extra-small;
  }
}

input[type="radio"],
input[type="checkbox"]:not(.tailwind) {
  margin: @spacing-extra-small @spacing-extra-small 0 0;
  line-height: normal;
}

.btn {
  input[type="radio"]:not(.tailwind),
  input[type="checkbox"]:not(.tailwind) {
    margin: 0;
    line-height: 1;
    padding: 0;
    display: inline-block;
  }
}

input[type="range"] {
  display: block;
}

select[multiple],
select[size] {
  height: auto;
}

select::-ms-expand {
    background-color: transparent;
    border: none;
    color: @gray;
}

input[type=number] {
  -moz-appearance:textfield;
}

.form-control {
  -webkit-appearance: none;
  display: block;
  width: 100%;
  height: @input-height;
  padding: 0 @spacing-small;
  font-size: @font-size-normal;
  vertical-align: middle;
  line-height: @input-height;
  color: @gray;
  background-color: #FFF;
  background-image: none;
  border: 1px solid @gray-light;
  border-radius: @border-radius-small;
  outline-color: @primary;
  &::-moz-placeholder {
    color: @gray-light;
    opacity: 1;
  }
  &:-ms-input-placeholder { color: @gray-light; }
  &::-webkit-input-placeholder  { color: @gray-light; }

  &[disabled] {cursor: not-allowed;}

  textarea& {
    max-height: auto;
    height: auto;
    line-height: 1.5;
    padding: @spacing-small @spacing-small;
  }
  &:focus, &:active {
    outline: none;
    border-color: @primary;
  }
  &[disabled], &.readonly {
    background-color: @gray-lighter;
    color: @gray-medium-light;
    opacity: 1;
    &:focus, &:active {
      outline: none;
      border-color: @gray-light;
    }
  }
}

.range-group {
  display: flex;
  align-items: center;
  position: relative;
  max-width: 100%;
}


.range-group input[type=range] {
  flex: 1 1 auto;
  min-width: 0;
  max-width: 100%;
  border-top-right-radius: 0;
  border-bottom-right-radius: 0;
  &::-webkit-slider-runnable-track {
    border-top-right-radius: 0;
    border-bottom-right-radius: 0;
  }
}

.range-group input[type=number],
.range-group input[type=text],
.range-group select {
  flex: 0 1 auto;
  width: 76px;
  min-width: 76px;
  text-align: center;
  margin-left: -1px;
  border-top-left-radius: 0;
  border-bottom-left-radius: 0;
}

.range-group select {
  text-align-last: center;
}

.input-fat {
  height: @input-height-medium;
}

// Input sizes
.input-sm {max-width: 100px; min-width: 45px;}

// Hide number spinners
.hide-spinners {
  &::-webkit-inner-spin-button,
  &::-webkit-outer-spin-button {
  -webkit-appearance: none;
  -moz-appearance:textfield;
  margin: 0;
  }
}

.input-overlay {
  position: relative;
  .form-control {
    // padding-left: 34px;
    box-sizing: border-box;
  }
  > button {
    position: absolute;
    top: 0;
    left: 0px;
    height: 34px;
    width: 34px;
    padding: 0;
  }
}

// Validation

.validation-error {
  border-color: @warning;
}

.validation-text {
  display: block;
  font-size: @font-size-small;
  font-weight: lighter;
  color: @warning;
}

// Helpers
.form-group {margin-bottom: @spacing-medium;}

// Hidden input
.hiddenInput {
  background-color: transparent;
  border: 0;
  color: @gray;
  line-height: auto;
  display: inline-block;
  width: 36px;
  text-align: right;
}

// Custom Select (Used on collection component)
.select {
  position: relative;
  margin-bottom: @spacing-medium;
  .select-results {
    position: absolute;
    top: 100%;
    left: 0;
    z-index: @zindex-dropdown;
    min-width: 160px;
    max-width: 100%;
    width: 100%;
    padding: 5px;
    list-style: none;
    font-size: @font-size-small;
    background-color: rgba(255,255,255,0.95);
    .shadow();
    background-clip: padding-box;
    max-height: 300px;
    overflow: scroll;

    > li > span {
      padding: @spacing-small @spacing-medium;
      display: block;
      width: 100%;

      .fa-check-circle-o {
        color: @primary;
      }

      .fa-circle-o {
        color: #fff;
        visibility: hidden;
      }
    }

    > li > span:hover {
      background-color: @primary;
      color: #fff;
      text-decoration: none;
      cursor: pointer;

      .fa-check-circle-o {
        color: #fff;
      }
    }
  }
}

.slider {
  -webkit-appearance: none;
  width: 100%;
  background: transparent;
  &::-webkit-slider-thumb {
    -webkit-appearance: none;
  }
  &::-ms-track {
    width: 100%;
    cursor: pointer;
    background: transparent;
    border-color: transparent;
    color: transparent;
  }
  &:focus {
    outline: none;
  }
}

.slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  border: none;
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background: @primary-6;
  cursor: pointer;
}

.slider::-webkit-slider-runnable-track {
  width: 100%;
  max-width: 100%;
  height: @input-height;
  cursor: pointer;
  background: @gray-3;
  border-radius: @border-radius-small;
  border: solid 1px transparent;
  display: flex;
  align-items: center;
  padding: 0 @spacing-small 0 @spacing-small;
}

.slider:focus::-webkit-slider-runnable-track {
  background: @gray-4;
}

.slider::-moz-range-track {
  width: 100%;
  max-width: 100%;
  height: @input-height;
  cursor: pointer;
  background: @gray-3;
  border-radius: @border-radius-small;
  border: solid 1px transparent;
  display: flex;
  align-items: center;
  padding: 0 @spacing-small 0 @spacing-small;
}

.slider:focus::-moz-range-track {
  background: @gray-4;
}

.slider::-ms-track {
  width: 100%;
  max-width: 100%;
  height: @input-height;
  cursor: pointer;
  background: @gray-3;
  border-radius: @border-radius-small;
  border: solid 1px transparent;
  display: flex;
  align-items: center;
  padding: 0 @spacing-small 0 @spacing-small;
}

.slider:focus::-ms-track {
  background: @gray-4;
}

.slider::-moz-range-thumb {
  -webkit-appearance: none;
  border: none;
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background: @gray-5;
  cursor: pointer;
}

.slider::-ms-thumb {
  -webkit-appearance: none;
  border: none;
  height: 24px;
  width: 24px;
  border-radius: 50%;
  background: @gray-5;
  cursor: pointer;
}
.month-select{
  min-width: 115px;
  select{
    padding-left: 30px;
  }
}

fieldset:disabled {
  opacity:0.7;
}
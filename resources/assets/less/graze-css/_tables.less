table {
  border-collapse: collapse;
  border-spacing: 0;
  background-color: #FFF;
}

th {
  text-align: left;
}


// Baseline styles

.table {
  width: 100%;
  max-width: 100%;
  font-size: @font-size-normal;
  > thead,
  > tbody,
  > tfoot {
    > tr {
      > th, > td {
        padding: @spacing-small;
        vertical-align: middle;
        border-bottom: 1px solid @table-border-color;
      }
    }
  }
  // Bottom align for column headings
  > thead > tr > th {
    vertical-align: bottom;
    border-bottom: 1px solid @table-border-color;
    background-color: #FFF;
  }
  // Remove top border from thead by default
  > tbody > tr:first-of-type > td {
    border-top: 0px solid @table-border-color;
  }
  > caption + thead,
  > colgroup + thead,
  > thead:first-child {
    > tr:first-child {
      > th,
      > td {
        border-top: 0;
      }
    }
  }
  // Account for multiple tbody instances
  > tbody + tbody {
    border-top: 2px solid @table-border-color;
  }
}

.panel-body .table tr:last-of-type td {
  border-bottom: none;
}

.table-full {
  > thead,
  > tbody,
  > tfoot {
    > tr {
      > th, > td {
        padding: @spacing-medium @spacing-medium;
      }
    }
  }
  > thead > tr > th {padding: @spacing-medium @spacing-medium;}
}

// Zebra-striping
//
// Default zebra-stripe styles (alternating gray and transparent backgrounds)

.table-striped {
  > tbody > tr:nth-of-type(even) {
    background-color: @table-accent-color;
  }
}

.table-hover {
  tbody > tr:hover {
    background-color: @table-hightlight-color;
  }
}

.table-summary {
  td {
    font-size: @font-size-1;
    padding: @spacing-small 0;
  }

  tr:first-of-type {
    td {
      padding: 0 0 @spacing-small 0;
    }
  }

  tr {
    border-bottom: solid 1px @gray-lighter;
  }
  
  tr:last-of-type {
    border-bottom: none;
    td {
      font-size: @font-size-2;
      font-weight: bolder;
      padding: @spacing-small 0 0 0;
    }
  }
}

.table-bordered {
  > thead,
  > tbody,
  > tfoot {
    > tr {
      > th, > td {
        border: 1px solid @table-border-color !important;
      }
    }
  }
}

.table-comparison {
  table-layout: fixed;
  > thead > tr > th {
    background-color: @gray-lighter;
  }
  > thead,
  > tbody,
  > tfoot {
    > tr {
      > th {
        vertical-align: top;
      }
      > th, > td {
        width: 24%;
        text-align: center !important;
        padding: @spacing-medium;
        &:first-of-type {
          width: 28%;
          text-align: left !important;
          color: @gray-medium;
        }
      }
    }
    > tr.header {
      > td {
        background-color: @gray-lighter;
        border-right: none;
        font-weight: bolder;
        color: @gray;
        font-size: @font-size-1;
        &:last-of-type {
          border-right: 1px solid @table-border-color;
        }
      }
    }
  }
}

.table-responsive {
  display: block;
  width: 100%;
  max-width: 100%;
  overflow-y: visible;
  -ms-overflow-style: -ms-autohiding-scrollbar !important;
  -webkit-overflow-scrolling: touch;
  z-index: 99;
}

// Settings table
.table-settings {
  background-color: #ffffff;
  > tbody > tr > td {
    padding: @spacing-medium-small @spacing-medium;
  }
  > tbody > tr:first-child td {
    border-top: 0; // Hide the border on the first row.
  }
}

.table-settings > tbody > tr > td {
  vertical-align: top !important;
  width: 50%;
  h2 {
    font-size: @font-size-normal;
    font-weight: bolder;
    margin: @spacing-extra-small 0;
  }
  p {
    font-size: @font-size-small;
    line-height: 1.1rem;
    color: @gray-medium;
  }
}

.visible-table-row {display: none;}
.hidden-table-row {display: table-row;}

.visible-table-cell {display: none;}
.hidden-table-cell {display: table-cell;}

.visible-table-block {display: none;}

@media screen and (max-width: @mobile-table-break) {
  .visible-table-row {display: table-row;}
  .hidden-table-row {display: none;}

  .visible-table-cell {display: table-cell;}
  .hidden-table-cell {display: none !important;}

  .visible-table-block {display: block;}
  .table-settings > tbody > tr > td {
    width: 100%;
    display: block;
    border: none;
    &:first-of-type {
      padding-bottom: 0;
    }
    &:last-of-type {
      padding-top: @spacing-small;
      padding-bottom: @spacing-medium;
      border-bottom: solid 1px @gray-light;
    }
    h2 {
      font-size: @font-size-1;
      font-weight: bolder;
      margin: @spacing-extra-small 0;
    }
    p {
      font-size: @font-size-normal;
      line-height: 1.1rem;
      color: @gray-medium;
    }
  }
  .table-settings > tbody > tr:last-of-type > td {
    border-bottom: none;
  }
  .table-compact {
    thead {display: none;}

    tbody > tr {
      display: flex;
      flex-wrap: wrap;
      align-items: center;
      border-top: solid @gray-light 1px;
      &:first-of-type {border: 0;}
    }
    tbody > tr > td:first-of-type {
      display: block;
      flex: 1 0 100%;
    }
    tbody > tr > td {
      display: inline-block;
      flex: 1 1 auto;
    }
    tbody > tr > td {
      border: 0 !important;
    }
  }

  // Table list presents table data in a format that works well on mobile
  .table-list {
    thead {display: none;}
    tbody, tr, td {display: block; border: none !important; width: 100%;}
    tr > td:first-of-type {
      padding-top: @spacing-medium !important;
    }
    tr > td:last-of-type {
      padding-bottom: @spacing-medium !important;
      border-bottom: solid 2px @gray-light !important;
    }
    tr:last-of-type td {
      border-bottom: none !important;
    }
    td[data-label]:before {
      font-weight: bolder;
      display: inline-block;
      margin-right: @spacing-extra-small;
      margin-bottom: @spacing-extra-small;
      content: attr(data-label)":";
    }
  }

  .table-comparison {
    table-layout: auto;
  }

  .table-comparison.first {
    thead > tr > th:nth-child(1), th:nth-child(3), th:nth-child(4), th:nth-child(5) {
      display: none;
    }
    tbody > tr > td:nth-child(3), td:nth-child(4), td:nth-child(5) {
      display: none;
    }
  }

  .table-comparison.second {
    thead > tr > th:nth-child(1), th:nth-child(2), th:nth-child(4), th:nth-child(5) {
      display: none;
    }
    tbody > tr > td:nth-child(2), td:nth-child(4), td:nth-child(5) {
      display: none;
    }
  }

  .table-comparison.third {
    thead > tr > th:nth-child(1), th:nth-child(2), th:nth-child(3), th:nth-child(5) {
      display: none;
    }
    tbody > tr > td:nth-child(2), td:nth-child(3), td:nth-child(5) {
      display: none;
    }
  }

  .table-comparison.fourth {
    thead > tr > th:nth-child(1), th:nth-child(2), th:nth-child(3), th:nth-child(4) {
      display: none;
    }
    tbody > tr > td:nth-child(2), td:nth-child(3), td:nth-child(4) {
      display: none;
    }
  }
}

@media print {
  .table {
    a {
      text-decoration: none !important;
    }
    a[href]:after {
        content: none !important;
    }

    abbr[title]:after {
        content: none !important;
    }
  }
}  

.highlight-first-row tr:first-of-type {
  background-color: @gray-light;
  font-weight: bold;
}

.table-header {
  background-color: @gray-lighter;
  color: @gray-medium;
  font-weight: bolder;
  text-align: center;
  border: dashed 2px @gray-light;
  border-top: dashed 2px @gray-light !important;
}

.table-column-highlight-left {
  border-left: dashed 2px #ddd;
}

.table-column-highlight-right {
  border-right: dashed 2px #ddd;
}

.tableSorter {
  white-space: nowrap;
}

// Grid Table
.table-grid {
  width: 100%;
  display: grid;
  grid-auto-columns: minmax(100px, 1fr);
  grid-auto-flow: column;
  align-items: center;
  > div {
    font-size: @font-size-normal;
    padding: @spacing-medium;
    vertical-align: middle;
  }
}
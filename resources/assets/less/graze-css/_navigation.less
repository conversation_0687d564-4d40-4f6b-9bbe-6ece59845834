.navigation-vertical {
	li {
		display: block;
		a {
			color: @gray-4;
			padding: 0.5rem 0;
			display: block;
			i {
				color: @gray-7;
				margin-right: 0.5rem;
			}
			&:hover {
				color: @primary;
				i {
					color: @primary;
				}
			}
		}
	}
}

.navigationBorder-top {border-top: solid 6px @primary;}

.navigation {
	padding: 0 @spacing-medium;
	background-color: #FFF;
	.navigationBorder-top();
}

.navigation-transparent {
	background-color: transparent;
}

.navigation-container--fixed {
	position: fixed;
  	z-index: @zindex-navbar-fixed;
  	top: 0;
  	left: 0;
  	width: 100%;
  	transition: all 0.3s;
}

.navigation-container--absolute {
	position: absolute;
  	z-index: @zindex-navbar-fixed;
  	top: 0;
  	left: 0;
  	width: 100%;
  	transition: all 0.3s;
}

.navigation-container--hide {
  top: -62px;
  .toolbar {
    background-color: rgba(255,255,255,0.95);
  }
}

.navigation-fixed-offset {
	padding-top: 125px + (16 * 2);
}

.navigation-items {
	display: flex;
	align-items: center;
	padding: 0 @spacing-medium;
	> li {
		flex: 0 1 auto;
		margin-right: @spacing-large;
		padding: 0;
		padding-top: 2px;
		line-height: 1rem;
		border-bottom: solid 2px transparent;
		&:hover, &:focus {
			border-bottom-color: @primary;
		}
	}
	> li:last-of-type {
		margin-right: 0;
	}
	> li > a {
		display: inline-block;
		color: @gray;
		padding: @spacing-medium 0 @spacing-medium 0;
		font-size: 15px;
		line-height: 1rem;
		&:hover, &:focus {
			color: @primary;
		}
	}
	> li.cta > a {
		background-color: @primary;
		color: #FFF;
		.br-xs();
		.shadow();
		padding: @spacing-small @spacing-medium;
		&:hover, &:focus {
			.shadow-soft();
			background-color: @primary-light;
		}
	}
	> li.cta {
		&:hover, &:focus {
			border-bottom-color: transparent;
		}
	}
}

.navigation-items--light {
	> li {
		&:hover, &:focus {
			border-bottom-color: #FFF;
		}
	}
	> li > a {
		color: #FFF;
		&:hover, &:focus {
			color: #FFF;
		}
	}
}

.navigation-logo {
	&:hover, &:focus {
		border-bottom-color: transparent !important;
		opacity: 0.8;
	}
	> a {
		padding-top: 2px !important; 
		padding-bottom: 0 !important;
	}
}


// Navigation List
.navigationList_item {
	display: block;
	padding: @spacing-medium;
	color: @gray;
	i {
		// color: @gray-5;
	}
}

.navigationList_item.active {
	color: @primary;
	margin-left: 0.25rem;
}

// Toolbar
.toolbar {
  border-top: @gray-lighter 1px solid;
  background-color: #fff;
  padding: @spacing-medium @spacing-large;
  .shadow-soft();
  display: flex;
  flex-wrap: wrap;
  align-items: center;
}

.toolbar__breadcrumbs {
  flex: 1 0 auto;
  margin-right: @spacing-small;
}

.toolbar__buttons {
  flex: auto auto auto;
}

.navigation--mobile {display: none !important;}

@media (max-width: @mobile-navigation-break) {
	.navigation-container--fixed {
		position: relative;
	}

	.navigation-fixed-offset {
		padding-top: 0px !important;
	}

	.navigation {border-top: 0; padding: 0;}

	.navigation--mobile {
		display: flex !important;
		width: 100%;
		background-color: #FFF;
		padding: 0 @spacing-medium;
		height: 60px;
		border-top: solid 6px @primary;
	}

	.navigation-transparent .navigation--mobile {
		background-color: rgba(255, 255, 255, 0.1);
	}

	.hidden-on-mobile {
		display: none !important;
	}
	.navigation-items {
		display: none;
		background-color: rgba(255, 255, 255, 0.98);
		.shadow();
		> li {
			margin-right: 0;
			padding: 0;
			border-bottom: solid 2px transparent;
			transition: none;
			display: block;
			width: 100%;
			a {
				display: block;
				color: @gray;
			}
			a:hover {
				color: @primary;
			}
			a:focus, a:active {
				color: @gray-dark;
			}
			.dropdown-menu {
				position: relative;
				display: none;
				top: 0;
				width: 100%;
				max-width: 100%;
				.shadow-none();
				float: none;
			}
		}
		> li.cta > a {
			display: inline-block;
			padding: @spacing-small @spacing-medium;
			margin-bottom: @spacing-medium;
			&:hover, &:focus {
				.shadow-soft();
				background-color: @primary-light;
			}
		}
		> li.cta {
			&:hover, &:focus {
				border-bottom-color: transparent;
			}
		}
	}

	.navigation-items--show {
		.navigation-items {
			display: block;
		}
	}

	.toolbar {
	  .shadow-soft();
	  flex-wrap: wrap;
	  margin-bottom: @spacing-large;
	  padding-top: 0;
	}
	.breadcrumb-toolbar {
		margin-top: @spacing-medium;
	}

	.toolbar__buttons {
		flex-wrap: wrap;
		margin-top: @spacing-medium;
		margin-bottom: -@spacing-small;
		.btn {font-size: @font-size-small; margin-bottom: @spacing-small;}
	}
}
.input-group-append {
	display: flex;
	
	.form-control {
		flex: 1 1 auto;
		border-radius: 0 3px 3px 0;
	}
	.input-group-addon {
		flex: 0 1 auto;
	  	display: inline-flex;
	  	align-items: center;
	  	justify-content: center;
	  	border: solid 1px #ddd;
	  	border-right: none;
		border-radius: 3px 0 0 3px;
	  	background: #EEE;
	  	font-weight: normal;
	  	font-style: normal;
	  	font-size: @font-size-small;
	  	color: @gray;
	  	padding: 0 1em;
	}
}

.input-group {
	display: flex;
	align-items: center;
	position: relative;
	.form-control {
		flex: 1 auto auto !important;
		.br--l();
		.br--r();
	}
	.input-group-addon {
		flex: 0 auto auto;
		border: 1px solid transparent;
		border-radius: @border-radius-extra-small;
		white-space: nowrap;
		max-height: @input-height;
		line-height: 1rem;
		padding: @spacing-small @spacing-medium-small;
		border-color: @gray-light;
  		background-color: @gray-lighter;
  		color: @gray;
  		font-size: @font-size-normal;
	}
}

.input-group-inline {
	display: inline-flex;
}

.input-group > .input-group-addon:first-child {
	.br-right-0();
	.br--l();
}

.input-group > .input-group-addon:last-child {
	.br-left-0();
	.br--r();
}
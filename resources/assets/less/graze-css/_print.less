@media print {
  html, body {
    height: auto;
  }
}

.print:last-child {
  page-break-after: auto;
}

@media print {
    *,
    *:before,
    *:after {
        background: transparent !important;
        color: #000 !important; /* Black prints faster:
                                   http://www.sanbeiji.com/archives/953 */
        box-shadow: none !important;
        text-shadow: none !important;
    }

    a,
    a:visited {
        text-decoration: underline;
    }

    // a[href]:after {
    //     content: " (" attr(href) ")";
    // }

    // abbr[title]:after {
    //     content: " (" attr(title) ")";
    // }

    /*
     * Don't show links that are fragment identifiers,
     * or use the `javascript:` pseudo protocol
     */

    a[href^="#"]:after,
    a[href^="javascript:"]:after {
        content: "";
    }

    pre {
        white-space: pre-wrap !important;
    }
    pre,
    blockquote {
        border: 1px solid #999;
        page-break-inside: avoid;
    }

    .container, .container-fluid, body, table, .panel, .panel-body, .row, .main {
      margin: 0 !important;
      padding: 0 !important;
      border: none;
      overflow: visible !important;
    }

    .fa {
      display: none;
    }

    .table tr td {
      padding: 1em 0.5em !important;
      font-size: 12px;
    }
    .table-print {
      height: 100%;
      position: absolute;
    }

    .footer {
      display: none;
    }

    .panel-heading {
      padding-left: 0;
    }

    .tag {
      .fs-xs();
      padding: 0 !important;
      margin-left: 1rem;
    }

    /*
     * Printing Tables:
     * http://css-discuss.incutio.com/wiki/Printing_Tables
     */

    thead {
        display: table-header-group;
    }

    tr,
    img {
        page-break-inside: avoid;
    }

    p,
    h2,
    h3 {
        orphans: 3;
        widows: 3;
    }

    h2,
    h3 {
        page-break-after: avoid;
    }
    .navbar {
        display: none;
    }

    .sidebarLayoutContainer .navigation {
      display: none;
    }

    .sidebarLayoutContainer .toolbar {
      display: none;
    }

    .sidebarLayoutContainer {
      display: block;
      .wrapper {
        max-height: auto;
        overflow: initial;
      }
    }
}

.visible-print {
  display: none !important;

  @media print {
    display: block !important;
  }
}
.visible-print-block {
  display: none !important;

  @media print {
    display: block !important;
  }
}
.visible-print-inline {
  display: none !important;

  @media print {
    display: inline !important;
  }
}
.visible-print-inline-block {
  display: none !important;

  @media print {
    display: inline-block !important;
  }
}

.hidden-print {
  @media print {
    display: none !important;
  }
}
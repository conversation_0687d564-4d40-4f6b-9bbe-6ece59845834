@keyframes pop {
  0%  {transform: scale(1);}
  50%  {transform: scale(1.1);}
  100%  {transform: scale(1);}
}

@keyframes fadein {
  0%  {opacity: 0;}
  100%  {opacity: 1;}
}

@keyframes slide {
  0%  {right: 0px;}
  50%  {right: 10px;}
  100%  {right: 0px;}
}

@keyframes showModal {
  0%    { transform: scale(0.8);  -webkit-transform: scale(0.8);  }
  100%  { transform: scale(1);    -webkit-transform: scale(1);    }
}

@keyframes hideModal {
  0%    { transform: scale(1);    -webkit-transform: scale(1);    }
  100%  { transform: scale(0.8);  -webkit-transform: scale(0.8);  }
}


.fade-color {
  transition: color ease-in-out 0.1s;
}

.background-color-text-color-transition {
  transition: background-color 0.4s ease, color 0.4s ease;
}

// Vue.js transitions
.fade-enter-active {
  transition: all .2s ease-in-out;
}
.fade-leave-active {
  transition: all .2s ease-in-out;
}
.fade-enter, .fade-leave-to {
  opacity: 0;
}

.fadeOut-transition {
  opacity: 1;
  transition: all .2s ease;
}
.fadeOut-enter {
  opacity: 0.5;
}

.fadeOut-leave {
  opacity: 0;
}

.slideup-transition {
  top: 0;
  opacity: 0.5;
}

.slideup-enter-active, .slideup-leave-active {
  transition: top 0.3s ease-in-out;
}

.slideup-enter {
  transform: translate(0, -100%);
}

.slideup-leave-to {
  transform: translate(0, -100%);
}

.slide-enter {
  left: -300px !important;
}

.slide-leave {
  left: -300px !important;
}

.scale-transition {
  transition: all .15s ease;
  transform: scale(1,1);
  transform-origin: top center;
  opacity: 1;
}
.scale-enter, .scale-leave {
  transform: scale(1,0);
  opacity: 0;
}

.collection-transition {
  transition: all .3s ease;
  transform: scale(1);
}

.collection-leave {
  transform: scale(0);
  transform-origin: right center;
}
.progress-bar {
	height: 6px;
	width: 100%;
	background-color: #FFF;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 9999;
	display: none;
}

.show-progress-bar {
	display: block;
}

.progress-bar span {
	display:block;
	height:100%;
	width:100%;
	border:1px solid @primary;
	background-color: @primary-light;
	background-image:
		-webkit-linear-gradient(
		-45deg,
		rgba(52, 179, 147, 1) 25%,
		transparent 25%,
		transparent 50%,
		rgba(52, 179, 147, 1) 50%,
		rgba(52, 179, 147, 1) 75%,
		transparent 75%,
		transparent
	);
	background-image:
		-moz-linear-gradient(
		-45deg,
		rgba(52, 179, 147, 1) 25%,
		transparent 25%,
		transparent 50%,
		rgba(52, 179, 147, 1) 50%,
		rgba(52, 179, 147, 1) 75%,
		transparent 75%,
		transparent
	);
	background-image:
		-ms-linear-gradient(
		-45deg,
		rgba(52, 179, 147, 1) 25%,
		transparent 25%,
		transparent 50%,
		rgba(52, 179, 147, 1) 50%,
		rgba(52, 179, 147, 1) 75%,
		transparent 75%,
		transparent
	);
	background-image:
		linear-gradient(
		-45deg,
		rgba(52, 179, 147, 1) 25%,
		transparent 25%,
		transparent 50%,
		rgba(52, 179, 147, 1) 50%,
		rgba(52, 179, 147, 1) 75%,
		transparent 75%,
		transparent
	);
	background-size: 16px 16px;
	animation:move 1.5s linear infinite;
	overflow: hidden;
}

@-webkit-keyframes move{
  0% {
  	background-position: 0 0;
  }
  100% {
  	background-position: 50px 50px;
  }
}	
@-moz-keyframes move{
  0% {
  	background-position: 0 0;
  }
  100% {
  	background-position: 50px 50px;
  }
}	
@-ms-keyframes move{
  0% {
  	background-position: 0 0;
  }
  100% {
  	background-position: 50px 50px;
  }
}	
@keyframes move{
  0% {
  	background-position: 0 0;
  }
  100% {
  	background-position: 50px 50px;
  }
}

// Full page loader
.progress-loader {
    position: fixed;
    z-index: @zindex-modal-background;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, .5);
    display: table;
    transition: opacity .3s ease;
    > div {
        display: table-cell;
        vertical-align: middle;
        text-align: center;
        font-size: 50px;
        color: #EEE;
        font-weight: bolder;
    }
}
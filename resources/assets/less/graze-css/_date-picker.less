@charset "UTF-8";

/*!
 * Pikaday
 * Copyright © 2014 <PERSON> | BSD & MIT license | http://dbushell.com/
 */

.pika-single {
  z-index: @zindex-modal;
  display: block;
  position: relative;
  color: @gray;
  background: #fff;
  .br-sm();
  .br--b();
}

/*
clear child float (pika-lendar), using the famous micro clearfix hack
http://nicolasgallagher.com/micro-clearfix-hack/
*/
.pika-single:before,
.pika-single:after {
  content: " ";
  display: table;
}

.pika-single:after {
  clear: both
}

.pika-single {
  zoom: 1
}

.pika-single.is-hidden {
  display: none;
}

.pika-single.is-bound {
  position: absolute;
  .shadow();
}

.pika-lendar {
  float: left;
  width: 240px;
  margin: @spacing-small;
}

.pika-title {
  position: relative;
  text-align: center;
}

.pika-label {
  display: inline-block;
  position: relative;
  z-index: @zindex-modal;
  overflow: hidden;
  margin: 0;
  padding: 5px 3px;
  font-size: @font-size-normal;
  line-height: 20px;
  font-weight: bold;
}

.pika-title select {
  cursor: pointer;
  position: absolute;
  z-index: @zindex-modal - 1;
  margin: 0;
  left: 0;
  top: 5px;
  opacity: 0;
}

.pika-prev,
.pika-next {
  display: block;
  cursor: pointer;
  position: relative;
  outline: none;
  border: 0;
  padding: 0;
  width: 20px;
  height: 30px;
  /* hide text using text-indent trick, using width value (it's enough) */
  text-indent: 20px;
  white-space: nowrap;
  overflow: hidden;
  background-color: transparent;
  background-position: center center;
  background-repeat: no-repeat;
  background-size: 75% 75%;
  opacity: .5;
}

.pika-prev:hover,
.pika-next:hover {
  opacity: 1;
}

.pika-prev,
.is-rtl .pika-next {
  float: left;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAeCAYAAAAsEj5rAAAAUklEQVR42u3VMQoAIBADQf8Pgj+OD9hG2CtONJB2ymQkKe0HbwAP0xucDiQWARITIDEBEnMgMQ8S8+AqBIl6kKgHiXqQqAeJepBo/z38J/U0uAHlaBkBl9I4GwAAAABJRU5ErkJggg==');
}

.pika-next,
.is-rtl .pika-prev {
  float: right;
  background-image: url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAeCAYAAAAsEj5rAAAAU0lEQVR42u3VOwoAMAgE0dwfAnNjU26bYkBCFGwfiL9VVWoO+BJ4Gf3gtsEKKoFBNTCoCAYVwaAiGNQGMUHMkjGbgjk2mIONuXo0nC8XnCf1JXgArVIZAQh5TKYAAAAASUVORK5CYII=');
}

.pika-prev.is-disabled,
.pika-next.is-disabled {
  cursor: default;
  opacity: .2;
}

.pika-select {
  display: inline-block;
}

.pika-table {
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
  border: 0;
}

.pika-table th,
.pika-table td {
  width: 14.285714285714286%;
  padding: 0;
}

.pika-table th {
  color: @gray-medium;
  font-size: 12px;
  line-height: 25px;
  font-weight: bold;
  text-align: center;
}

.pika-button {
  cursor: pointer;
  display: block;
  box-sizing: border-box;
  -moz-box-sizing: border-box;
  outline: none;
  border: 0;
  margin: 0;
  width: 100%;
  padding: 5px;
  color: @gray;
  font-size: @font-size-small;
  line-height: 1rem;
  text-align: right;
  background: @gray-lighter;
}

.pika-week {
  font-size: 11px;
  color: #999;
}

.is-today .pika-button {
  color: @gray-dark;
  font-weight: bold;
}

.is-selected .pika-button,
.has-event .pika-button {
  color: #fff;
  font-weight: bold;
  background: @primary;
}

.has-event .pika-button {
  background: @primary;
}

.is-disabled .pika-button,
.is-inrange .pika-button {
  background: @gray-light;
}

.is-startrange .pika-button {
  color: #fff;
  background: #6CB31D;
  box-shadow: none;
  border-radius: 3px;
}

.is-endrange .pika-button {
  color: #fff;
  background: #33aaff;
  box-shadow: none;
  border-radius: 3px;
}

.is-disabled .pika-button {
  pointer-events: none;
  cursor: default;
  color: #999;
  opacity: .3;
}

.is-outside-current-month .pika-button {
  color: #999;
  opacity: .3;
}

.is-selection-disabled {
  pointer-events: none;
  cursor: default;
}

.pika-button:hover,
.pika-row.pick-whole-week:hover .pika-button {
  color: #FFF;
  background-color: @gray;
}

/* styling for abbr */
.pika-table abbr {
  border-bottom: none;
  cursor: help;
}

.nav {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	> li {
		flex: 0 1 auto;
		margin-bottom: -1px;
	}
	> li > a {
		display: inline-block;
		padding: @spacing-medium-small @spacing-medium;
		font-size: @font-size-normal;
		color: @gray;
		border-bottom: solid 2px transparent;
		background-color: transparent;
		border-radius: @border-radius-small;
		.br--t();
		margin-right: @spacing-extra-small;
		transition: all ease-in-out 0.15s;
		&:hover, &:focus {
			border-bottom: solid 2px transparent;
			color: @primary;
		}
	}
	> li.active > a {
		color: @primary;
		font-weight: bolder;
		border-color: @gray-light;
		background-color: transparent;
		border-bottom: solid 2px @primary;
	}
	> li.divider {
		width: 1px;
		height: 1rem;
		background-color: @gray-light;
		margin-left: @spacing-medium-small;
		margin-right: @spacing-medium-small;
	}
}

@media (max-width: @mobile-navigation-break) {
	.nav {
		flex-wrap: nowrap;
		overflow: auto;
		-webkit-overflow-scrolling: touch;
		> li {
			flex: 0 0 auto;
			margin-bottom: 0px;

		}
	}
}	

// Tab list
.tabsList {
	list-style: none;
	padding: 0;
	display: flex;

	> li {
		flex: 1 0 auto;
		display: inline-block;
		min-width: 150px;
		> a {
			display: block;
			padding: @spacing-medium @spacing-small;
			margin: 0;
			font-weight: bolder;
			font-size: @font-size-normal;
			border-bottom: solid 2px @gray-light;
			color: @gray;
			transition: all 0.3s ease-in-out;
		}
		> a:hover {
			border-bottom: solid 2px @gray-light;
			color: @primary;
			text-decoration: none;
		}
	}
	> li.active {
		> a {
			color: @primary;
			border-bottom: solid 2px @primary;
		}
		> a:hover {
			color: @primary;
			border-bottom: solid 2px @primary;
		}
	}
}
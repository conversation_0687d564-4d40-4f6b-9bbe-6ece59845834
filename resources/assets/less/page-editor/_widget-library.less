.addWidgets {
    display: grid;
    grid-template-columns: 1fr auto;
    height: 100%;
    width: 100vw;
    .pageStructure {
        width: auto;
    }
}

.addWidgets__sidebar {
    min-width: 400px;
    background-color: @gray-3;
    border-left: solid 1px @gray-2;
    padding: @spacing-medium;
    overflow: auto;
    a {color: @gray-7 !important;}
}

.addWidgets__sidebar::-webkit-scrollbar { width: 0 !important }

.widgetLibrary__container {
    -webkit-overflow-scrolling: touch;
    overflow: auto;
    background-color: @gray-3;
    position: relative;
    max-height: 100vh;
    display: grid;
    grid-template-rows: auto 1fr;
}

.widgetLibrary__libraryContainer {
    overflow: auto;
    max-height: 100vh;
}

.widgetLibrary__libraryContainer::-webkit-scrollbar { width: 0 !important ;}

.widgetLibrary__container::-webkit-scrollbar { width: 0 !important ;}


.widgetLibrary__searchInput {
    background-color: @gray-2;
    border-color: transparent;
    color: @primary-7;
    &::-moz-placeholder {
      color: @gray-5;
      opacity: 1;
    }
    &:-ms-input-placeholder { color: @gray-5; }
    &::-webkit-input-placeholder  { color: @gray-5; }
}

.widgetLibrary__searchContainer {
    padding: @spacing-medium;
}

.widgetLibrary__section {
    border-bottom: solid 1px @gray-3;
    padding: @spacing-medium;
    h2 {
        font-size: 1em;
        color: @gray-6;
        margin-bottom: 0.5em;
    }
}

.widgetLibrary__list {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(150px, 1fr) );
    grid-gap: 1em;
}

.widgetLibrary__listItem {
    display: flex;
    align-items: center;
    width: 100%;
    min-height: 32px;
    border: solid 1px @gray-1;
    color: @primary-8;
    background-color: @gray-2;
    border-radius: 3px;
    padding: 0;
    height: 64px;

    .widgetLibrary__listItemIcon {
        color: @gray-5;
    }

    .widgetLibrary__listItemTitle {
        color: @gray-7;
        font-weight: normal;
        font-size: 14px;
        text-align: center;
        margin: 0 auto;
        .widgetLibrary__listItemIcon {
            display: block;
            margin-bottom: 0.25em;
            font-size: 18px;
        }

    }

    &:hover {
        cursor: grab;
    }
}

@media (max-width: 644px)
{
    .addWidgets {
        display: block;
        height: 100%;
        width: 100vw;
    }

    .addWidgets__sidebar {
        display: none;
    }

    .widgetLibrary__list {
        display: block;
        // padding: @spacing-medium;
        max-height: 100vh;
        max-width: 100vw;
        overflow: auto;
    }

    .widgetLibrary__listItem {
        max-width: 100%;
        margin-bottom: @spacing-medium;
        .draghandle::before {
            content: "\f055";
        }
    }
}
.pageEditor__widgetContent {
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    box-shadow:  0 7px 14px 0 rgba(50,50,93,.1), 0 3px 6px 0 rgba(0,0,0,.07);
    max-height: 100vh;
}

.widget {
    height: 100%;
    background-color: var(--background-color);
    min-width: var(--sidebar-width);
}

.widget__container {
    display: grid;
    grid-template-columns: var(--sidebar-width) auto;
    height: 100%;
}

.widget__container--full {
    width: 100vw;
}

.widgetHeader {
    position: absolute;
    top: 0;
    right: 0;
    left: 0;
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 64px;
    padding: 0 @spacing-medium;
    color: @gray-3;
    a, .btn {
        color: @gray-6;
        font-weight: lighter;
    }
    border-top: solid 1px  @gray-9;
    border-bottom: solid 1px  @gray-9;
}

.widgetHeader__title {
    a {
        color: @gray-3;
        font-size: 18px;
        font-weight: 700;
        display: block;
    }
}

.widgetHeader__view {
    font-size: 22px;
    font-weight: 700;
    text-transform: capitalize;
}

.widgetContent {
    position: absolute;
    top: 64px;
    left: 0;
    right: 0;
    width: 100%;
    bottom: 64px;
    overflow: auto;
    padding: @spacing-medium;
    background-color: #fafafa;
}

.widgetContent__title {
    font-size: @font-size-2;
    margin-bottom: @spacing-medium;
    font-weight: bolder;
    text-align: center;
}

.widgetFooter {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    width: 100%;
    display: flex;
    justify-content: flex-end;
    align-items: center;
    height: 64px;
    padding: 0 @spacing-medium;
    border-top: solid 1px  @gray-9;
}

.widget__content {
    padding: @spacing-medium;
}

.widget__navigation {
    list-style: none;
    background-color: transparent;
    li a, li .btn {
        background-color: transparent;
        color: @gray-4;
        display: block;
        padding: @spacing-medium;
        height: auto;
        width: 100%;
        text-align: left;
        border-left: solid 2px transparent;
    }
    li {
        border-bottom: solid 1px  @gray-9;
    }
    li.active a, li.active .btn {
        color: @primary-7;
        i {
            font-weight: bolder;
        }
    }
}

.widget__breadcrumb {
    color: @gray-3;
    font-size: 16px;
    margin: @spacing-small 0 @spacing-large 0;
    font-weight: bold;
    a {
        color: @gray-4;
        font-size: 14px;
        padding-right: @spacing-small;
    }
}

.widget__innerContainer {
    min-height: 100%;
    position: relative;
}
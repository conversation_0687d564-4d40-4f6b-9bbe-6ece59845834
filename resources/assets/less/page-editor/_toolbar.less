.pageEditorToolbar {
    width: 100vw;
    list-style-type: none;
    display: flex;
    align-items: center;
    background-color: var(--toolbar-bg-color);
    box-shadow:  0 7px 14px 0 rgba(50,50,93,.1), 0 3px 6px 0 rgba(0,0,0,.07);
    z-index: 1000;
    > li {
        display: flex;
        align-items: center;
    }
}

.pageEditorToolbar__breadcrumb {
    width: auto;
    padding-left: @spacing-medium;
    color: var(--toolbar-text-color);
    white-space: nowrap;
    .breadcrumb > li {
        font-size: 16px;
        color: var(--toolbar-text-color);
    }
    .breadcrumb > li > a {
        color: @primary-9;
    }
    a {
        color: var(--toolbar-text-color);
        &:hover {
            color: var(--toolbar-link-color);
        }
    }
}

.pageEditorToolbar__tabs {
    flex: 1 1 auto;
    width: 100%;
    padding-right: @spacing-medium;
}

.toolbarTabs {
    display: flex;
    align-items: center;
    width: 100%;
    margin: 0 auto;
}

.toolbarButton {
    width: 120px;
    font-weight: normal;
    font-size: 1em;
    border-radius: 0px !important;
    border: none;
    border-bottom: solid 2px transparent;
    color: var(--toolbar-text-color);
    background-color: var(--toolbar-bg-color);
    border-color: transparent;
    padding-top: 12px;
    padding-bottom: 12px;
    &:hover {
        color: var(--toolbar-link-color);
        background-color: var(--toolbar-bg-color);
    }
    .active {
        border-color: var(--toolbar-active-bg-color);
    }
}

.toolbarButton.active {
    color: @primary-7;
    border-color: @primary-7;
}

.toolbarTabs__first {
    margin-left: auto;
}

.toolbarTabs__last {
    margin-left: auto;
    width: auto;
    // width: 333px;
    text-align: right;
    .toolbarButton {width: auto;}
}

@media (max-width: 768px) {
    .pageEditorToolbar {
        display: block;
    }

    .toolbarButtonIcon {
        display: block;
        margin: 0 auto @spacing-small auto;
        font-size: 22px;
    }

    .toolbarButtonLabel {
        font-size: 14px;
    }

    .pageEditorToolbar__breadcrumb {
        border-bottom: solid 1px @gray-1;
        max-width: auto;
        width: 100%;
        justify-content: center;
        padding: @spacing-small;
    }

    .toolbarTabs__first {
        margin-left: 0;
    }

    .toolbarTabs__actions {
        width: 120px;
        margin-left: 0;
        margin-right: 0;
    }

    .pageEditorToolbar__tabs {
        padding: 0 @spacing-small;
    }

    .toolbarTabs {
        justify-content: center;
        padding: 0;
        margin: 0;
        width: 100%;
    }

    .toolbarButton {
        width: 120px;
        font-size: 0.8em;
        padding-top: 12px;
        padding-bottom: 12px;
    }
}

@media (max-width: 638px) {
    .toolbarTabs > li {
        width: 25%;
        flex-basis: 25%;
        text-align: center;
    }
    .toolbarTabs__actions {
        width: auto;
        margin-left: 0;
        margin-right: 0;
        text-align: center;
    }

    .toolbarButton {
        width: auto;
    }

    .toolbarButtonIcon {
        font-size: 18px;
        margin: 0 auto 0 auto;
    }

    .toolbarButtonLabel {
        // font-size: 13px;
        display: none;
    }
}
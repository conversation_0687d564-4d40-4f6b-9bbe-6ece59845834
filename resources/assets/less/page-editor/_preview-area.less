.pagePreview__container {
    width: 100vw;
    height: 100%;
    max-width: 100%;
    margin: 0 auto;
    transition-duration: 0.5s;
    transition-timing-function: ease-in-out;
    transition-property: padding-left, max-width;
    // overflow: auto;
    iframe {
        margin: 0;
        padding: 0;
        height: 100%;
        width: 100%;
        border: none;
        overflow: auto;
    }
}

.pagePreview__container.tablet {
    max-width: 768px;
}

.pagePreview__container.mobile {
    max-width: 375px;
}

.pagePreview__container.desktop.editing {
    padding-left: var(--sidebar-width);
}


@media (max-width: 1000px) {
    .pagePreview__container.desktop.editing {
       padding-left: 0px !important;
    } 
}
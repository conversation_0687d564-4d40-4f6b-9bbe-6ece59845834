.pageStructure {
    height: 100%;
    width: var(--sidebar-width);
    max-width: 100vw;
    overflow: auto;
    background-color: @gray-3;
    -webkit-overflow-scrolling: touch;
    padding-bottom: 44px;
}

.pageEditor__activeBlocksList > li {
    display: flex;
    justify-content: space-between;
    align-items: center;
    width: 100%;
    min-height: 32px;
    border: solid 1px @gray-1;
    color: @primary-8;
    background-color: @gray-2;
    border-radius: 3px;
    display: flex;
    align-items: center;
    text-align: center;
    padding: 0 @spacing-small;
    margin-bottom: @spacing-medium;
    > a {
        color: @primary-8;
    }
    > div > a {
        color: @primary-8;
    }
    .draghandle {
        color: @primary-8;
        &:hover {
            cursor: grab;
        }
    }
    .widgetLibrary__listItemTitle {
        font-weight: bold;
    }
    &:hover {
        cursor: pointer;
    }
}

.activeBlocksList__icon {
    display: block;
    width: 32px;
}

.pageEditor__activeBlocksList {
    padding: @spacing-medium;
    min-height: 50%;
}

@media (max-width: 644px)
{
    .pageStructure {
        width: 100vw;
        max-width: 100vw;
    }
}
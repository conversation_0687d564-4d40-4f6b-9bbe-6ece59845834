.pageSettings {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    width: 100vw;
    height: 100%;
    display: grid;
    grid-template-columns: var(--sidebar-width) 1fr;
    background-color: @gray-2;
    .widget__navigation {
        background-color: @gray-2;
        li {border-color: @gray-3;}
        a, .btn {color: @gray-8}
    }
}

.pageSettings__sidebar {
    padding: @spacing-medium;
    overflow: auto;
    .mobileNav {
        display: none;
    }
}

.pageSettings__contentContainer {
    max-height: 100%;
    overflow: auto;
    position: relative;
}

.pageSettings__contentBody {
    overflow: auto;
    padding: @spacing-medium @spacing-large;
    background-color: #FFF;
    padding-top: 0;
    height: 100%;
}

.pageSettings__footer {
    height: 64px;
    display: flex;
    align-items: center;
    justify-content: flex-end;
    padding: 0 @spacing-large;
    background-color: #fff;
    right: 0;
    top: 0;
}

@media (max-width: 644px)
{
    .pageSettings {
        grid-template-columns: 1fr;
        grid-template-rows: 64px 1fr;
    }

    .pageSettings__sidebar {
        padding: 0 @spacing-medium;
        display: flex;
        align-items: center;
        justify-content: center;
        .mobileNav {
            display: block;
        }
        .widget__navigation {
            display: none;
        }
    }

    // .pageSettings__contentBody {
    //     padding-top: @spacing-large;
    //     padding-bottom: 64px;
    // }
    
    // .pageSettings__footer {
    //     top: auto;
    //     bottom: 0;
    // }
}
@import "../graze-css/_variables";
@import "../graze-css/_colors";
@import "_widget-library.less";
@import "_page-settings.less";
@import "_page-structure.less";
@import "_preview-area.less";
@import "_text_editor.less";
@import "_toolbar.less";
@import "_sidebar.less";
@import "_widget.less";

:root {
    --background-color: #FFF;
    --background-color-tp: rgba(255,255,255,0.96);
    --toolbar-bg-color: @gray-2;
    --toolbar-link-color: @primary-8;
    --toolbar-text-color: @gray-7;
    --toolbar-active-bg-color: @gray-3;
    --toolbar-active-link-color: @gray-10;
    --toolbar-active-text-color: @gray-10;
    --toolbar-height: 64px;

    --sidebar-bg-color:  @gray-10;
    --sidebar-text-color:  @gray-2;
    --sidebar-link-color:  @gray-2;
    --sidebar-width: 333px;
}

// :root {
//     --background-color: @gray-2;
//     --toolbar-bg-color: @gray-2;
//     --toolbar-link-color: @primary-7;
//     --toolbar-text-color: @primary-7;
//     --toolbar-active-bg-color: @gray-1;
//     --toolbar-active-link-color: @primary-7;
//     --toolbar-active-text-color: @primary-7;
//     --toolbar-height: 64px;

//     --sidebar-bg-color:  @gray-10;
//     --sidebar-text-color:  @gray-2;
//     --sidebar-link-color:  @gray-2;
//     --sidebar-width: 333px;
// }

.pageEditor {
    height: 100vh;
    width: 100vw;
    display: grid;
    grid-template-columns: 1fr;
    grid-template-rows: auto 1fr;
    // overflow: hidden;
}

.pageEditor__content, .pageEditor__previewContainer {
    position: relative;
    height: 100%;
    width: 100%;
    overflow: auto;
}

// Page Structure
.pageStructure__container {
    padding: @spacing-medium;
}

.icon-menu {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr;
    grid-gap: @spacing-medium;
    justify-content: center;
    li {
        text-align: center;
    }
    a {color: @gray-5;}
    a:hover {
        color: @primary-7;
    }
}


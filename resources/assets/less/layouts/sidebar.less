@navigation-background: #3D3D3D;
@navigation-divider: #2D2D2D;

.sidebarLayoutContainer {
  display: grid;
  grid-template-columns: 210px 1fr;

  .wrapper {
    width: 100%;
    margin-bottom: 0;
    padding-bottom: 0;
    max-height: 100vh;
    overflow: auto;
    .container {
      padding-bottom: 4rem;
    }
  }

  .toolbar {
    margin-bottom: 2rem;
  }

  .navigation {
    display: flex;
    flex-direction: column;
    border-top: 0;
    background-color:@navigation-background;
    min-height: 100vh;
    padding: 0;
    ul {
      flex: 0 1 auto;
    }
    .navigationHeader {
      flex: 0 0 auto;
      padding: 0.75rem 0;
      border-bottom: solid 1px @navigation-divider;
    }
    .navigationMain {
      flex: 0 1 auto;
      padding: 0.75rem 0;
      .dropdown-menu {
        top: 0;
        left: 100%;
      }
    }
    .navigationFooter {
      flex: 0 0 auto;
      padding: 0.5rem 0 2rem 0;
      border-top: solid 1px @navigation-divider;
      margin-top: auto;
      .dropdown-menu {
        top: auto;
        bottom: 0;
        left: 100%;
      }
    }
    .dropdown-menu::after, .dropdown-menu::before {
      display: none;
    }
  }

  .navigation-items {
    display: flex;
    flex-direction: column;
    padding: 0;
    justify-content: flex-start;
    > li {
      width: 100%;
      flex: 1 0 auto;
      margin-right: 0;
      padding: 0;
      padding-top: 0;
      border-bottom: none;
      i.fa {
        margin-right: 0.25rem;
        opacity: 0.85;
      }
    }
    > li > a {
      display: block;
      color: #FFF;
      padding: 0.75rem 0 0.75rem 1rem;
      font-size: 16px;
      line-height: initial;
      border-left: solid 6px transparent;
      &:hover, &:focus {
        background-color: #FFF;
        color: @primary;
        border-color: @primary;
      }
    }
  }

  li.navigation-logo {
    display: block;
    padding: 0;
    text-align: center;
    a {
      display: block;
      padding: 0;
    }
    .fullLogo {
      display: inline-block;
    }
    .mobileLogo {
      display: none;
    }
  }

  &.collapsed {
    grid-template-columns: 60px 1fr;
    .navigation {
      .dropdown-menu {
        position: absolute;
        text-align: left;
      }
  
      .navigation-items {
        background-color: transparent;
        > li > a {
          padding: 0.75rem 0;
          border: none;
          text-align: center;
        }
        > li > a > span {
          display: none;
        }
      }
  
      .collapse-icon {
        transform: scaleX(-1);
      }
    }

    li.navigation-logo {
      .fullLogo {
        display: none;
      }
      .mobileLogo {
        display: inline-block;
      }
    }
  }

  @media (max-width: @mobile-navigation-break) {
    grid-template-columns: 60px 1fr;

    .navigation {
      .dropdown-menu {
        position: absolute;
        text-align: left;

      }
    }

    .navigation-items {
      background-color: transparent;
      > li > a {
        padding: 0.75rem 0;
        border: none;
        text-align: center;
      }
      > li > a > span {
        display: none;
      }
    }

    .collapse-icon {
      transform: scaleX(-1);
    }

    li.navigation-logo {
      .fullLogo {
        display: none;
      }
      .mobileLogo {
        display: inline-block;
      }
    }
  }
}

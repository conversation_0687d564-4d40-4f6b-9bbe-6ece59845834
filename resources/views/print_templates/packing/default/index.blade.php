@extends('print_templates.layout')

@section('styles')
.packing-list {
    page-break-after: {{ setting('layout_page_break', 'always') }};
    break-after: {{ setting('layout_page_break', 'always') }};
}
@stop

@section('content')
@if($orders->count())
    @foreach($orders as $order)
        <div class="packing-list">

            <div id="header">
                <table>
                    <tr>
                        <td colspan="100%">
                            <div class="type">Packing List for Order #{{ $order->id }}<br />
                                <small class="fas fa-star __{{ $order->first_time_order }}">&#9733;</small>
                                {{ $order->customer_last_name }}, {{ $order->customer_first_name }}
                            </div>
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <div>{{ $order->customer_email }}<br>{{ $order->customer_phone }}</div>
                            <div>{{ $order->shipping_street }} {{ $order->shipping_street_2 }}</div>
                            {{ $order->shipping_city }}, {{ $order->shipping_state }} {{ $order->shipping_zip }}
                        </td>
                        <td class="text-center">
                            <div><b>Purchase Date</b></div>
                            {{ $order->present()->confirmedDate() }}
                            <br>
                        </td>
                        <td class="text-center">
                            <div><b>Delivery Date</b></div>
                            {{ $order->present()->pickupDate() }}
                            <br>
                        </td>
                        <td class="text-center">
                            <div><b>Location</b></div>
                            {{ $order->present()->pickupTitle() }}
                            <br>
                        </td>
                    </tr>
                </table>
            </div>
            @if($order->items->count())
                <div id="items">
                    <div style="display: flex;">
                        <div style="flex: 0 0 auto; margin-right: auto;">
                            <strong>Parcels: {{ $order->present()->totalContainers() ? $order->present()->totalContainers() : '__________'}}</strong>
                        </div>
                        <div style="flex: 0 0 auto; margin-left: auto;">
                            <strong>Order Weight: {{ weight($order->weight) }}</strong>
                        </div>    
                    </div>

                    <table style="margin-top: 20px;">
                        <thead>
                        <tr>
                            <th align="left">SKU</th>
                            <th align="left">Item Name</th>
                            <th align="left">Weight</th>
                            <th align="left">Unit</th>
                            <th align="left">Group</th>
                            <th align="left" class="location-id">Loc. ID</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($order->items as $item)
                            @if($item->is_grouped)
                                @include('print_templates.packing.default._item_row')  
                            @else
                                @for($i = 0; $i < $item->fulfilledQuantity(); $i++)
                                    @include('print_templates.packing.default._item_row', [
                                        'qty' => 1,
                                        'weight' => $item->weight / $item->qty
                                    ])
                                @endfor 
                            @endif
                        @endforeach
                        </tbody>
                    </table>
                </div>
            @else
                <p><b>This order has no items</b></p>
            @endif
            <div id="customer-notes">
                <b>Customer Notes: </b>
                {{ $order->customer_notes }}
            </div>
            <div class="packing-notes">
                <b>Packing Notes: </b>
                {{ $order->packing_notes }}
            </div>
            @if($order->first_time)
                <i class="fa fa-check"></i>
            @endif
            <div style="margin-top: 20px; text-align: center;">End of Order #{{ $order->id }}</div>
        </div>
    @endforeach
@else
    <p><b>No orders found.</b></p>
@endif
@stop
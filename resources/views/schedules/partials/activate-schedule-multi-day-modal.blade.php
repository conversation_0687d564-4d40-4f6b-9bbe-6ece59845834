@component('partials.modal', ['id' => 'activateScheduleModal'])
    @slot('header')
        Activate Schedule
    @endslot

    @slot('body')
        <form action="/admin/schedules/{{ $schedule->id }}/activate" method="POST" id="activateScheduleForm">
            <input type="hidden" name="multi_date" value="true" />
            @csrf
            @method('PUT')
            <div class="form-group">
                <label for="first_delivery_date" class="block text-sm font-medium text-gray-700">Delivery Days / Week</label>
                <div class="mt-2 daysOfWeekButtonGroup form-group">
                    <div>
                        <input type="checkbox" value="1" name="days_of_week[]" id="days_of_week_1">
                        <label for="days_of_week_1">Mon</label>
                    </div>
                    <div>
                        <input type="checkbox" value="2" name="days_of_week[]" id="days_of_week_2">
                        <label for="days_of_week_2">Tue</label>
                    </div>
                    <div>
                        <input type="checkbox" value="3" name="days_of_week[]" id="days_of_week_3">
                        <label for="days_of_week_3">Wed</label>
                    </div>
                    <div>
                        <input type="checkbox" value="4" name="days_of_week[]" id="days_of_week_4">
                        <label for="days_of_week_4">Thur</label>
                    </div>
                    <div>
                        <input type="checkbox" value="5" name="days_of_week[]" id="days_of_week_5">
                        <label for="days_of_week_5">Fri</label>
                    </div>
                    <div>
                        <input type="checkbox" value="6" name="days_of_week[]" id="days_of_week_6">
                        <label for="days_of_week_6">Sat</label>
                    </div>
                    <div>
                        <input type="checkbox" value="0" name="days_of_week[]" id="days_of_week_0">
                        <label for="days_of_week_0">Sun</label>
                    </div>
                </div>
            </div>

            <div class="mt-4">
                <label for="first_delivery_date" class="block text-sm font-medium text-gray-700">First delivery date</label>
                <p class="m-0 mt-1 text-sm text-gray-500">Delivery dates generate based on the selected date, delivery days, and delivery frequency</p>

                <div class="mt-2 flex align-items-m">
                    <x-form.pikaday-input
                            name="first_delivery_date"
                            class="form-control"
                            value="{{ request('first_delivery_date') ?? '' }}"
                            id="first_delivery_date"
                            placeholder="First delivery date"
                            tabindex="1"
                    />
                </div>
            </div>

            <div class="mt-4">
                <label for="order_cutoff" class="block text-sm font-medium text-gray-700">Order deadline</label>
                <p class="m-0 mt-1 text-sm text-gray-500">Set how many days before the delivery date ordering should close</p>

                <select id="order_cutoff" name="order_cutoff" class="mt-2 w-20 block pl-3 pr-10 py-2 text-base border-gray-300 focus:outline-none focus:ring-keppel-500 focus:border-keppel-500 sm:text-sm rounded-md">
                    {!! selectOptions(array_combine(range(0, 30), range(0, 30))) !!}
                </select>
            </div>
            @endslot
            @slot('footer')
                <button type="button" class="btn btn-alt" @click="hideModal('activateScheduleModal')">Cancel</button>
                <button type="submit" class="btn btn-action" @click="submitForm('activateScheduleForm')">Start Schedule</button>
        </form>
    @endslot
@endcomponent

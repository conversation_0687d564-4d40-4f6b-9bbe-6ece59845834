@extends('layouts.main', ['pageTitle' => 'Schedules'])

@section('toolbar-breadcrumb')
    <li>Schedules ({{ $schedules->count() }})</li>
@stop

@section('content')
    <div class="border-b border-gray-200 pb-5 sm:flex sm:items-center sm:justify-between">
        <h3 class="text-lg font-medium leading-6 text-gray-900">Schedules</h3>
        <div class="mt-3 flex sm:mt-0 sm:ml-4">
            <button type="button"  @click="showModal('createScheduleModal')" class="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 shadow-sm hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-keppel-500 focus:ring-offset-2">Create</button>
        </div>
    </div>

    @include('schedules.partials.index-table')

    @include('schedules.partials.create-schedule-modal')
@stop()
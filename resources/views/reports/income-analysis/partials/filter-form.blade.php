<div class="filterPanel" id="filterPanel">
    <div class="settingsPanel pa-0">
        <div class="settingsPanel__header flex align-items-m">
            <div class="flex-item-fill">Filter Report</div>
            <button type="button" class="btn btn-alt flex-item" @click="hidePanel('filterPanel')"><i
                        class="fas fa-times fa-lg"></i></button>
        </div>
        <div class="settingsPanel__body">
            <div class="form-group">
                <label for="order_status">Order Status</label>
                <x-form.order-status-select
                        class="form-control select2 autofocus"
                        name="order_status[]"
                        data-placeholder="Select statuses"
                        tabindex="1"
                        multiple
                        :selected="request('order_status')"
                />
            </div>

            <div class="form-group">
                <label for="date_type">Date Type</label>
                <select class="form-control" name="date_type" tabindex="1">
                    {!! selectOptions(['payment_date' => 'Payment Date','pickup_date' => 'Delivery Date'], request('date_type')) !!}
                </select>
            </div>

            <div x-data="dateRange('date_range_start', 'date_range_end')" class="form-group">
                <div class="flex items-center justify-between">
                    <label for="date_range">Date range</label>
                    <x-form.date-range-dropdown />
                </div>

                <div class="mt-1 flex space-x-2 items-center">
                    <x-form.pikaday-input
                            name="date_range[start]"
                            class="form-control"
                            value="{{ request('date_range')['start'] ?? '' }}"
                            id="date_range_start"
                            placeholder="Start date"
                            tabindex="1"
                    />

                    <x-form.pikaday-input
                            name="date_range[end]"
                            class="form-control"
                            value="{{ request('date_range')['end'] ?? '' }}"
                            id="date_range_end"
                            placeholder="End date"
                            tabindex="1"
                    />
                </div>
            </div>

            <div class="form-group">
                <label for="exported">Export Status</label>
                {{ html()->select('exported', [null => 'All', true => 'Exported', false => 'Not Exported'], request('exported'))->class('form-control')->id('exported') }}
            </div>

            <div class="form-group">
                <label for="paid">Payment Status</label>

                <x-form.payment-status-select
                        class="form-control"
                        name="paid"
                        tabindex="1"
                        placeholder="All"
                        :selected="request('paid')"
                />
            </div>

            <div class="form-group">
                <label for="order_type_id">Sales Channel</label>
                <x-form.channel-select
                        class="form-control select2"
                        name="order_type_id[]"
                        tabindex="1"
                        data-placeholder="Select sales channels"
                        multiple
                        :selected="request('order_type_id')"
                        :include_all="true"
                />
            </div>

            {{--Payment Method--}}
            <div class="form-group">
                <label for="paid">Payment Method</label>
                <x-form.payment-method-select
                        class="form-control"
                        name="payment_id"
                        tabindex="1"
                        placeholder="All"
                        :selected="request('payment_id')"
                />
            </div>

            {{--Tags--}}
            <div class="form-group">
                <label for="order_tags">Tags</label>
                <x-form.order-tag-select
                        class="form-control select2"
                        name="order_tags[]"
                        data-placeholder="Select some tags"
                        multiple
                        style="width: 100%"
                        :selected="request('order_tags')"
                />
            </div>
        </div>
        <div class="settingsPanel__footer flex align-items-m">
            <button type="submit" class="btn btn-alt mr-md" name="export" value="true"><i
                        class="fas fa-cloud-download"></i></button>
            <button type="submit" class="btn btn-action btn-block btn-lg"
                    @click="submitForm('filterReportsForm')">Filter
            </button>
        </div>
    </div>
</div>

<div class="gc-modal gc-modal-mask" id="confirmOrderModal" @click="hideModal('confirmOrderModal')">
    <div class="gc-modal-wrapper">
        <form action="{{ route('admin.orders.confirm.store', compact('order')) }}" method="POST">
            @csrf
            <div class="gc-modal-container" @click.stop>

                <div class="gc-modal-header">
                    Confirm Order
                </div>

                <div class="gc-modal-body">
                    @if(! $order->meetsOrderMinimum())
                        <div class="rounded-md bg-yellow-100 p-2 mb-4">
                            <div class="flex">
                                <div class="shrink-0">
                                    <svg class="size-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true" data-slot="icon">
                                        <path fill-rule="evenodd" d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495ZM10 5a.75.75 0 0 1 .75.75v3.5a.75.75 0 0 1-1.5 0v-3.5A.75.75 0 0 1 10 5Zm0 9a1 1 0 1 0 0-2 1 1 0 0 0 0 2Z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                <div class="ml-3">
                                    <h3 class="text-sm font-medium text-yellow-800">Order Below Minimum</h3>
                                    <div class="mt-2 text-sm text-yellow-700">
                                        <p>The order subtotal is below the ${{ money($order->getOrderMinimum()) }} minimum for this delivery method. Confirm you wish to proceed.</p>
                                    </div>
                                    <div class="mt-4">
                                        <div class="flex gap-3">
                                            <div class="flex h-6 shrink-0 items-center">
                                                <div class="group grid size-4 grid-cols-1">
                                                    <input id="confirm_below_minimum" aria-describedby="confirm_below_minimum-description" name="confirm_below_minimum" type="checkbox" class="!m-0 col-start-1 row-start-1 appearance-none rounded border border-gray-300 bg-white checked:border-keppel-600 checked:bg-keppel-600 indeterminate:border-keppel-600 indeterminate:bg-keppel-600 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600 disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto">
                                                    <svg class="pointer-events-none col-start-1 row-start-1 size-3.5 self-center justify-self-center stroke-white group-has-[:disabled]:stroke-gray-950/25" viewBox="0 0 14 14" fill="none">
                                                        <path class="opacity-0 group-has-[:checked]:opacity-100" d="M3 8L6 11L11 3.5" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                        <path class="opacity-0 group-has-[:indeterminate]:opacity-100" d="M3 7H11" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
                                                    </svg>
                                                </div>
                                            </div>
                                            <div class="text-sm/6">
                                                <label for="confirm_below_minimum" class="font-medium text-yellow-800">Confirm below the minimum</label>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    @endif

                    <div class="flex align-items-m form-group">
                        <div class="flex-item-fill mr-sm">
                            <label for="customer_first_name">First Name</label>
                            <input type="text" name="customer_first_name" value="{{ old('customer_first_name', $order->customer_first_name) }}" class="form-control" tabindex="1" required/>
                        </div>

                        <div class="flex-item-fill">
                            <label for="customer_last_name">Last Name</label>
                            <input type="text" name="customer_last_name" value="{{ old('customer_last_name', $order->customer_last_name) }}" class="form-control" tabindex="1" required/>
                        </div>
                    </div>

                    <div class="flex align-items-m form-group">
                        <div class="flex-item-fill mr-sm">
                            <label for="customer_phone">Phone</label>
                            <input type="text" name="customer_phone" value="{{ old('customer_phone', $order->customer_phone) }}" class="form-control" tabindex="1" required/>
                        </div>

                        <div class="flex-item-fill">
                            <label for="customer_email">Email</label>
                            <input type="email" name="customer_email" value="{{ old('customer_email', $order->customer_email) }}" class="form-control" tabindex="1" required/>
                        </div>
                    </div>

                    <div class="flex align-items-m form-group">
                        <div class="flex-item-fill mr-sm">
                            <label for="shipping_street">Street</label>
                            <input type="text" name="shipping_street" id="shipping_street" value="{{ old('shipping_street', $order->shipping_street) }}" class="form-control" tabindex="1"/>
                        </div>
                        <div class="flex-item-fill">
                            <label for="shipping_street_2">Street Line 2</label>
                            <input type="text" name="shipping_street_2" id="shipping_street_2" value="{{ old('shipping_street_2', $order->shipping_street_2) }}" class="form-control" tabindex="1"/>
                        </div>
                    </div>

                    <div class="flex form-group">
                        <div class="flex-item-fill mr-sm">
                            <label for="shipping_city">City</label>
                            <input type="text" name="shipping_city" id="shipping_city" value="{{ old('shipping_city', $order->shipping_city) }}" class="form-control" tabindex="1"/>
                        </div>

                        <div class="flex-item-fill mr-sm">
                            <label for="shipping_state">{{ getMessage('checkout_label_state') }}</label>
                            <x-form.state-select
                                    class="form-control"
                                    name="shipping_state"
                                    id="shipping_state"
                                    tabindex="1"
                                    placeholder="Select your state"
                                    :selected="$order->shipping_state"
                            />
                        </div>

                        <div class="flex-item-fill">
                            <label for="shipping_zip">{{ getMessage('checkout_label_zip') }}</label>
                            <input type="text" name="shipping_zip" id="shipping_zip" value="{{ old('shipping_zip', $order->shipping_zip) }}" class="form-control" tabindex="1">
                        </div>
                    </div>

                    <div class="form-group">
                        <div class="checkbox">
                            <label for="save_for_later">
                                <input type="checkbox" name="save_for_later" id="save_for_later" tabindex="1">
                                Save for future purchases
                            </label>
                        </div>
                    </div>
                    <hr>
                    <div class="form-group">
                        <label for="payment_id">Payment Method</label>
                        <x-form.payment-method-select
                                class="form-control"
                                name="payment_id"
                                tabindex="1"
                                :selected="(int) $order->payment_id"
                        />
                    </div>

                    <div class="form-group">
                        <label for="type_id">Sales Channel</label>
                        <x-form.channel-select
                                class="form-control"
                                name="type_id"
                                tabindex="1"
                                :selected="(int) $order->type_id"
                        />
                    </div>
                    <hr>
                    <div class="checkbox">
                        <label for="send_confirmation_email">
                            <input type="checkbox" name="send_confirmation_email" id="send_confirmation_email" tabindex="1" checked>
                            Send Order Confirmation Email
                        </label>
                    </div>
                </div>

                <div class="gc-modal-footer">
                    <button type="button" class="btn btn-alt" @click="hideModal('confirmOrderModal')">Cancel</button>
                    <button type="submit" class="btn btn-action" name="confirmed" value="true" tabindex="1">Confirm</button>
                </div>
            </div>
        </form>
    </div>
</div>

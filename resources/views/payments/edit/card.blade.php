@extends('layouts.main', ['pageTitle' => 'Credit Card'])

@section('content')
{{--Toolbar--}}
@section('toolbar-breadcrumb')
<li><a href="/admin/settings">Settings</a></li>
<li><a href="/admin/settings/payments">Payment Options</a></li>
<li>Credit Card</li>
@stop

<div class="panel">
    <div class="panel-body pa-0">
        <table class="table">
            <tbody>
                <tr>
                    <td>
                        <div class="flex align-items-m pa-sm">
                            <div class="flex-item mr-md">
                                <a href="/admin/settings/payments/card/stripe">
                                    <img src="/images/payment-gateways/stripe.png" alt="Stripe logo"
                                        style="width: 25px;">
                                </a>
                            </div>
                            <div class="flex-item" style="width: 150px;">
                                <a href="/admin/settings/payments/card/stripe" class="btn-alt bold">
                                    Stripe
                                </a>
                            </div>
                            <div class="flex-item push-right">
                                <a href="/admin/settings/payments/card/stripe" class="btn btn-link btn-alt">
                                    @if($activeGateway === 'stripe')
                                    <span class="label label-success">Enabled</span>
                                    @else
                                    <span class="label label-light">Disabled</span>
                                    @endif
                                </a>
                            </div>
                        </div>
                    </td>
                </tr>
            </tbody>
        </table>
    </div>
</div>

<form action="/admin/settings/payments/{{ $payment->id }}" method="POST">
    @csrf
    @method('put')
    <div class="panel">
        <div class="panel-heading flex align-items-m">
            <div class="flex-item">Credit Card Messaging</div>
        </div>
        <div class="panel-body pa-0">
            <table class="table table-striped table-settings">
                <tbody>
                    <tr>
                        <td>
                            <h2>Label</h2>
                            <p>The name of the payment method on payment select and checkout screens.</p>
                        </td>
                        <td>
                            <input type="text" name="title" value="{{ $payment->title }}" class="form-control">
                        </td>
                    </tr>
                    <tr>
                        <td>
                            <h2>Payment Instructions</h2>
                            <p>Provide additional payment instructions that your customers will see during checkout.</p>
                        </td>
                        <td>
                            <textarea 
                                name="instructions" class="form-control"
                                rows="4">{{ old('instructions', $payment->instructions) }}
                            </textarea>
                        </td>
                    </tr>
                </tbody>
            </table>        
        </div>
        <div class="panel-footer text-right">
            <button class="btn btn-action">Save</button>
        </div>
    </div>
</form>
@stop
@extends('layouts.main', ['pageTitle' => $user->full_name])

@section('toolbar-breadcrumb')
    <li>
        <a href="{{ route('admin.users.index', session('users-filtered', [])) }}">Customers</a>
    </li>
    <li>{{ $user->full_name }}</li>
@stop
@section('toolbar-buttons')
    <button class="btn btn-success mr-xs" @click="submitForm('updateResourceForm', $event)">Save</button>

    <div class="dropdown">
        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="fas fa-gear"></i> Actions <i class="fas fa-caret-down"></i>
        </button>
        <ul class="dropdown-menu pull-right">
            <li><a href="/admin/users/{{ $user->id }}/login-as-user">Log-in as Customer</a></li>
            <li><a href="#" @click="showModal('createOrderModal')">Create Order</a></li>
            <li><a href="#" @click="showModal('resetPasswordModal')">Reset Password</a></li>
            <li><a href="#" @click="showModal('deleteCustomerModal')">Delete</a></li>
        </ul>
    </div>
@stop

@section('content')
    {{--Partials--}}
    @include('users.partials.delete-user-modal')
    @include('users.partials.reset-password-modal')
    @include('users.partials.create-order-modal')

    {{--Tabs--}}

    <ul class="info-boxes">
        <li>
            <span class="info-box-heading">
                <b>{{ $user->full_name }}</b>
            </span>
            <a href="mailto:{{ $user->email }}">{{ $user->email }}</a>
        </li>
        <li>
            <span class="info-box-heading">
                <b>Delivery Method</b>
            </span>
            {{ $user->pickup ? $user->pickup['title'] : '' }}
        </li>
        <li>
            <span class="info-box-heading">
                <b>Store Credit</b>
            </span>
            &#36;{{ money($user->credit) }}
        </li>
        <li>
            <span class="info-box-heading">
                <b>Order Count</b>
            </span>
            {{ $user->orders_count }}
        </li>
        <li>
            <span class="info-box-heading">
                <b>Last Purchase</b>
            </span>
            {{ $user->present()->lastPurchase() }}
        </li>
    </ul>

    <div class="row">
        <div class="content">
            <ul class="nav">
                <li role="presentation" class="{{ activeTab('name', Request::getTab('tab', 'name')) }}">
                    <a href="{{ route('admin.users.edit', $user->id)}}?tab=name">Contact Info</a>
                </li>
                @if($user->role_id !== \App\Support\Enums\UserRole::CUSTOMER->value)
                    <li role="presentation" class="{{ activeTab('profile', Request::getTab('tab')) }}">
                        <a href="{{ route('admin.users.edit', $user->id)}}?tab=profile">Profile</a>
                    </li>
                @endif
                <li role="presentation" class="{{ activeTab('addresses', Request::getTab('tab')) }}">
                    <a href="{{ route('admin.users.edit', $user->id)}}?tab=addresses">Addresses</a>
                </li>
                <li role="presentation" class="{{ activeTab('orders', Request::getTab('tab')) }}">
                    <a href="{{ route('admin.users.edit', $user->id)}}?tab=orders">Orders</a>
                </li>
                <li role="presentation" class="{{ activeTab('notes', Request::getTab('tab')) }}">
                    <a href="{{ route('admin.users.edit', $user->id)}}?tab=notes">Notes</a>
                </li>
                <li role="presentation" class="{{ activeTab('settings', Request::getTab('tab')) }}">
                    <a href="{{ route('admin.users.edit', $user->id)}}?tab=settings">Settings</a>
                </li>
                <li role="presentation" class="{{ activeTab('credit', Request::getTab('tab')) }}">
                    <a href="{{ route('admin.users.edit', $user->id)}}?tab=credit">Credit</a>
                </li>
                <li role="presentation" class="{{ activeTab('billing', Request::getTab('tab')) }}">
                    <a href="{{ route('admin.users.edit', $user->id)}}?tab=billing">Billing</a>
                </li>
                @if(!$user->referrals->isEmpty())
                    <li role="presentation" class="{{ activeTab('referrals', Request::getTab('tab')) }}">
                        <a href="{{ route('admin.users.edit', $user->id)}}?tab=referrals">Referrals</a>
                    </li>
                @endif
                <li role="presentation" class="{{ activeTab('subscription', Request::getTab('tab')) }}">
                    <a href="{{ route('admin.users.edit', $user->id)}}?tab=subscription">Subscription</a>
                </li>
            </ul>
            @includeIf("users.partials.". request()->getTab('tab', 'name'))
        </div>
        <div class="sidebar sidebar-1 sidebar-right">
            <tags id="{{ $user->id }}" model="users"></tags>
            @if($user->referredBy)
                <ul class="info-boxes">
                    <li>
                        <span class="info-box-heading">
                            <strong><i class="fas fa-user-friends"></i> Referred By</strong>
                        </span>
                        <a href="/admin/users/{{ $user->referredBy->id }}/edit">{{ $user->referredBy->full_name }}</a>
                    </li>
                </ul>
            @endif
        </div>
    </div>
@stop

@section('modals')
    <livewire:admin.modals.add-address/>
    <livewire:admin.modals.edit-address/>
    <livewire:admin.modals.delete-address-confirmation/>
@endsection

@section('scripts')
    <script>

        document.addEventListener('DOMContentLoaded', () => {
            $('.pickup-date').daterangepicker({
                singleDatePicker: true,
                showDropdowns: true,
                parentEl: '#createOrderModal .modal-dialog',
                locale: {
                    format: 'MM/DD/YYYY'
                }
            });

            $('.start-date').daterangepicker({
                singleDatePicker: true,
                showDropdowns: true,
                locale: {
                    format: 'MM/DD/YYYY'
                }
            });

            $('#referralDateRangePicker').daterangepicker({
                timePicker: false,
                opens: 'right',
                locale: {
                    format: 'MM/D/YY'
                },
                ranges: {
                    'This Month': [moment().startOf('month'), moment().endOf('month')],
                    'Last Month': [moment().subtract(1, 'month').startOf('month'), moment().subtract(1, 'month').endOf('month')]
                }
            });

            $('#billingSameAsShipping').on('change', function() {
                if ($(this).is(':checked')) {
                    $('#billing_street').val($('#street').val());
                    $('#billing_street_2').val($('#street_2').val());
                    $('#billing_city').val($('#city').val());
                    $('#billing_state').val($('#state').val());
                    $('#billing_zip').val($('#zip').val());
                } else {
                    $('#billing_street').val('');
                    $('#billing_street_2').val('');
                    $('#billing_city').val('');
                    $('#billing_state').val('');
                    $('#billing_zip').val('');
                }
            });
        });
    </script>
    @yield('payment_scripts')
@stop

@extends('layouts.main', ['pageTitle' => $proposal->first_name.' '.$proposal->last_name])

{{--Toolbar--}}
@section('toolbar-breadcrumb')
    <li><a href="{{ route('admin.proposals.index') }}">Pickup Proposals</a></li>
    <li>{{ $proposal->full_name }}</li>
@endsection

@section('toolbar-buttons')
    <button class="btn btn-success flex-item mr-sm" @click="submitForm('proposalForm')">Save</button>

    <div class="dropdown">
        <button type="button" class="btn btn-default dropdown-toggle" data-toggle="dropdown" aria-haspopup="true" aria-expanded="false">
            <i class="fas fa-gear"></i> Actions <i class="fas fa-caret-down"></i>
        </button>
        <ul class="dropdown-menu pull-right">
            <li><a href="#" @click.prevent="showModal('deleteProposalModal')">Delete</a></li>
        </ul>
    </div>
@endsection

@section('content')
    @include('proposals.partials.delete-proposal-modal')

    <form action="{{ route('admin.proposals.update', $proposal->id) }}" method="POST" id="proposalForm">
        @csrf
        @method('put')
        <div class="row">
            <div class="content">
                <div class="panel ">
                    <div class="panel-body">
                        {{--First_name--}}
                        <div class="form-group">
                            <label for="first_name">First Name</label>
                            <input type="text" name="first_name" class="form-control" value="{{ old('first_name', $proposal->first_name) }}" />
                        </div>

                        {{--Last_name--}}
                        <div class="form-group">
                            <label for="last_name">Last Name</label>
                            <input type="text" name="last_name" class="form-control" value="{{ old('last_name', $proposal->last_name) }}" />
                        </div>

                        {{--Phone--}}
                        <div class="form-group">
                            <label for="phone">Phone</label>
                            <input type="text" name="phone" class="form-control" value="{{ old('phone', $proposal->phone) }}" />
                        </div>

                        {{--Email--}}
                        <div class="form-group">
                            <label for="email">Email</label>
                            <input type="text" name="email" class="form-control" value="{{ old('email', $proposal->email) }}" />
                        </div>
                    </div>
                </div>
                <div class="panel ">
                    <div class="panel-heading">Proposed Location</div>
                    <div class="panel-body">
                        {{--Street--}}
                        <div class="form-group">
                            <label for="street">Street</label>
                            <input type="text" name="street" class="form-control" value="{{ old('street', $proposal->street) }}" />
                        </div>
                        <div class="flex align-items-m">
                            {{--City--}}
                            <div class="form-group flex-item-fill mr-sm">
                                <label for="city">City</label>
                                <input type="text" name="city" class="form-control" value="{{ old('city', $proposal->city) }}" />
                            </div>

                            {{--State--}}
                            <div class="form-group flex-item-fill mr-sm">
                                <label for="state">State</label>
                                <x-form.state-select
                                        class="form-control"
                                        name="state"
                                        :selected="$proposal->state"
                                />
                            </div>

                            {{--Zip--}}
                            <div class="form-group flex-item-fill">
                                <label for="zip">Zip</label>
                                <input type="text" name="zip" class="form-control" value="{{ old('zip', $proposal->zip) }}" />
                            </div>
                        </div>
                        <div class="flex align-items-m">

                            <div class="form-group flex-item-fill mr-sm">
                                <label for="lat">Latitude</label>
                                <input type="text" name="lat" class="form-control" value="{{ old('latitude', $proposal->lat) }}" />
                            </div>


                            <div class="form-group flex-item-fill">
                                <label for="lng">Longitude</label>
                                <input type="text" name="lng" class="form-control" value="{{ old('longitude', $proposal->lng) }}" />
                            </div>
                        </div>

                        <label>Days Of Week</label>
                        <div class="form-group">
                            @foreach (\Carbon\Carbon::getDays() as $day)
                                <div class="checkbox">
                                    <label>
                                        <input type="checkbox" name="days_of_week[]" value="{{ $day }}" @checked(in_array($day, $proposal->days_of_week ?? []))> {{ $day }}
                                    </label>
                                </div>
                            @endforeach
                        </div>

                        <label>Parking</label>
                        <div class="form-group">
                            <div class="checkbox">
                                <label class="mr-sm">
                                    <input type="checkbox" name="truck_parking" id="truck_parking" value="1" @if($proposal->truck_parking) checked @endif> I can
                                    accommodate parking for a delivery truck
                                </label>
                                <label>
                                    <input type="checkbox" name="customer_parking" id="customer_parking" value="1" @if($proposal->customer_parking) checked @endif>
                                    I can accommodate parking for customer vehicles
                                </label>
                            </div>
                        </div>

                        <div class="form-group">
                            <label for="notes">Notes</label>
                            <textarea name="notes" class="form-control" rows="10">{{ $proposal->notes }}</textarea>
                        </div>
                    </div>
                </div>
            </div>

            <div class="sidebar sidebar-1 sidebar-right">
                <div class="panel ">
                    <div class="panel-heading">
                        Applicant's Comments
                    </div>
                    <div class="panel-body lh-2">
                        {!! $proposal->details !!}
                    </div>
                </div>
                <div class="panel ">
                    <div class="panel-body">

                        {{--status--}}
                        <div class="form-group">
                            <label for="status">Status</label>
                            <x-form.proposal-status-select
                                    class="form-control"
                                    name="status"
                                    :selected="$proposal->status"
                            />
                        </div>

                        {{--rating--}}
                        <div class="form-group">
                            <label for="status">Rating</label>
                            <x-form.proposal-rating-select
                                    class="form-control"
                                    name="rating"
                                    :selected="$proposal->rating"
                            />
                        </div>

                        {{--Created At--}}
                        <div class="form-group">
                            <label for="created_at">Date</label>
                            <input type="text" name="created_at" class="form-control" id="created_at" value="{{ old('created_at', $proposal->created_at->format('m/d/Y')) }}" />
                        </div>
                    </div>
                </div>
                <div class="panel">
                    <div class="panel-body">
                        <div style="height: 400px; width: 100%">
                            <l-map :use-global-leaflet="false" ref="map" :zoom="15" :center="[{{ $proposal->lat }}, {{ $proposal->lng }}]">
                                <l-tile-layer
                                        url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
                                        layer-type="base"
                                        name="OpenStreetMap"
                                ></l-tile-layer>
                                <l-marker :lat-lng="[{{ $proposal->lat }}, {{ $proposal->lng }}]"></l-marker>
                            </l-map>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
@endsection

@section('scripts')
    @include('partials.check-for-unsaved-changes')
@endsection

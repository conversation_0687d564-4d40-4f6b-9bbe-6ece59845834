<div class="filterPanel" id="filterPanel">
    <div class="settingsPanel pa-0">
        <div class="settingsPanel__header flex align-items-m">
            <div class="flex-item-fill">Filter Proposals</div>
            <button
                    type="button"
                    class="btn btn-alt flex-item"
                    @click="hidePanel('filterPanel')"
            ><i class="fas fa-times fa-lg"></i></button>
        </div>
        <div class="settingsPanel__body">
            <input type="hidden" name="tab" value="{{ Request::get('tab', 'list') }}">
            {{--Status--}}
            <div class="form-group">
                <label for="proposal_status">Status</label>
                <x-form.proposal-status-select
                        class="form-control autofocus"
                        name="proposal_status"
                        placeholder="All"
                        :selected="request('proposal_status')"
                />
            </div>

            {{--Rating--}}
            <div class="form-group">
                <label for="status">Rating</label>
                <x-form.proposal-rating-select
                        class="form-control"
                        name="rating"
                        placeholder="All"
                        :selected="request('rating')"
                />
            </div>

            <div class="form-group">
                <label for="state">State</label>
                <x-form.state-select
                        class="form-control"
                        name="state"
                        placeholder="All"
                        :selected="request('state')"
                />
            </div>
        </div>
        <div class="settingsPanel__footer flex align-items-m">
            <button type="submit" class="btn btn-action btn-block btn-lg" @click="submitForm('filterOrdersForm')">Filter</button>
        </div>
    </div>
</div>

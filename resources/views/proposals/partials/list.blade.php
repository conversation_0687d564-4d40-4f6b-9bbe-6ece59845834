@include('partials.saved-filters')
<div class="panel">
    <div class="panel-heading">

        <form action="{{ route('admin.proposals.index') }}" method="GET" id="filterOrdersForm" class="hidden-print">
            <div class="flex align-items-m">
                <button
                        type="button"
                        class="btn btn-white flex-item br-right-0 br--l"
                        @click.stop="showPanel('filterPanel')"
                        tabindex="1"
                >Filter
                    <span class="hide-mobile">Proposals</span>
                    <i class="fas fa-caret-down"></i></button>
                <div class="flex-item-fill">
                    <button type="submit" class="btn btn-clear btn-input-overlay"><i class="fas fa-search"></i></button>
                    <input
                            tabindex="1"
                            type="text"
                            class="form-control input-overlay-left br--r"
                            placeholder="Search by name, email, city, or state..."
                            name="proposals"
                            value="{{ Request::get('proposals') }}"
                    >
                </div>
            </div>
            @include('proposals.partials.filter-form')
        </form>
        @include('partials.applied-filters', ['filter_resource' => 'proposal'])
    </div>
    <div class="panel-body pa-0">
        <div class="table-responsive">
            <table class="table table-striped table-full table-list">
                <thead>
                <tr>
                    <th>{!! sortTable('First', 'first_name') !!}</th>
                    <th>{!! sortTable('Last', 'last_name') !!}</th>
                    <th>{!! sortTable('Phone', 'phone') !!}</th>
                    <th>{!! sortTable('City', 'city') !!}</th>
                    <th>{!! sortTable('State', 'state') !!}</th>
                    <th>{!! sortTable('Rating', 'rating') !!}</th>
                    <th>{!! sortTable('Date', 'created_at') !!}</th>
                </tr>
                </thead>
                <tbody>
                @foreach($proposals as $proposal)
                    <tr>
                        <td>
                            <a href="{{ route('admin.proposals.edit', [$proposal->id]) }}">{{ $proposal->first_name }}</a>
                        </td>
                        <td>
                            {{ $proposal->last_name }}
                        </td>
                        <td>{{ $proposal->phone }}</td>
                        <td>{{ $proposal->city }}</td>
                        <td>{{ $proposal->state }}</td>
                        <td>{!! $proposal->present()->starRating !!}</td>
                        <td>{{ $proposal->created_at->format('m/d/y')    }}</td>
                    </tr>
                @endforeach
                @if($proposals->isEmpty())
                    <tr>
                        <td colspan="100%">No pickup proposals found.</td>
                    </tr>
                @endif
                </tbody>
            </table>
        </div>
    </div>
</div>

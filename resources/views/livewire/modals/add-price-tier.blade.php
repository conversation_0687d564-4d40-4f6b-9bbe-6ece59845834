<x-modal>
    <form
            wire:submit="submit"
            class="relative transform rounded-lg bg-white text-left shadow-xl sm:mx-auto sm:my-8 sm:w-full sm:max-w-xl"
    >
        <div class="rounded-t-lg bg-white px-4 pb-4 pt-5 sm:p-6 sm:pb-4">
            <div class="sm:flex sm:items-start">
                <div class="mx-auto flex h-12 w-12 flex-shrink-0 items-center justify-center rounded-full bg-keppel-100 sm:mx-0 sm:h-10 sm:w-10">
                    <svg class="h-6 w-6 text-keppel-600" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 7.5l-9-5.25L3 7.5m18 0l-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"/>
                    </svg>
                </div>
                <div class="mt-3 text-center w-full sm:ml-4 sm:mt-0 sm:text-left">
                    <h3 class="text-base font-semibold leading-6 text-gray-900" id="modal-title">Add Price</h3>
                    <div class="mt-2">
                        <p class="text-sm text-gray-500">Add a new price tier.</p>
                    </div>
                    <div class="mt-4 grid grid-cols-1 gap-x-4 gap-y-8 sm:grid-cols-6">
                        <div class="col-span-full">
                            <label for="quantity" class="block text-sm/6 font-medium text-gray-900">Quantity</label>
                            <div class="relative mt-2 w-24">
                                <input type="text" wire:model="quantity" name="quantity" id="quantity" class="block w-full rounded-md border-0 py-1.5 ring-1 ring-inset focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 @error('quantity') pr-3 text-red-900 ring-red-300 placeholder:text-red-300 focus:ring-red-500 @else text-gray-900 ring-gray-300 placeholder:text-gray-400 focus:ring-keppel-600 @enderror" required @error('quantity') aria-invalid="true" @enderror aria-describedby="quantity-error">
                                @error('quantity')
                                <div class="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-3">
                                    <svg class="h-5 w-5 text-red-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"/>
                                    </svg>
                                </div>
                                @enderror
                            </div>
                            @error('quantity') <p class="mt-2 text-sm text-red-600" id="quantity-error">{{ $message }}</p> @enderror
                        </div>
                        <div class="col-span-full sm:col-span-3">
                            <label for="unit_price" class="block text-sm/6 font-medium text-gray-900">Price</label>
                            <div class="mt-2 grid grid-cols-1">
                                <input type="text" wire:model.number="unit_price" name="unit_price" id="unit_price" class="col-start-1 row-start-1 block w-full rounded-md border-0 pl-10 py-1.5 ring-1 ring-inset focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 @error('unit_price') pr-3 text-red-900 ring-red-300 placeholder:text-red-300 focus:ring-red-500 @else text-gray-900 ring-gray-300 placeholder:text-gray-400 focus:ring-keppel-600 @enderror" required @error('unit_price') aria-invalid="true" @enderror aria-describedby="unit_price-error">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="pointer-events-none col-start-1 row-start-1 ml-3 size-5 self-center @error('unit_price') text-red-500 @else text-gray-400 @endif sm:size-4">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                                </svg>
                            </div>
                            @error('unit_price') <p class="mt-2 text-sm text-red-600" id="unit_price-error">{{ $message }}</p> @enderror
                        </div>
                        <div class="col-span-full sm:col-span-3">
                            <label for="sale_unit_price" class="block text-sm/6 font-medium text-gray-900">Sale Price</label>
                            <div class="mt-2 grid grid-cols-1">
                                <input type="text" wire:model.number="sale_unit_price" name="sale_unit_price" id="sale_unit_price" class="col-start-1 row-start-1 block w-full rounded-md border-0 pl-10 py-1.5 ring-1 ring-inset focus:ring-2 focus:ring-inset sm:text-sm sm:leading-6 @error('sale_unit_price') pr-3 text-red-900 ring-red-300 placeholder:text-red-300 focus:ring-red-500 @else text-gray-900 ring-gray-300 placeholder:text-gray-400 focus:ring-keppel-600 @enderror" required @error('sale_unit_price') aria-invalid="true" @enderror aria-describedby="sale_unit_price-error">
                                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="pointer-events-none col-start-1 row-start-1 ml-3 size-5 self-center @error('sale_unit_price') text-red-500 @else text-gray-400 @endif sm:size-4">
                                    <path stroke-linecap="round" stroke-linejoin="round" d="M12 6v12m-3-2.818.879.659c1.171.879 3.07.879 4.242 0 1.172-.879 1.172-2.303 0-3.182C13.536 12.219 12.768 12 12 12c-.725 0-1.45-.22-2.003-.659-1.106-.879-1.106-2.303 0-3.182s2.9-.879 4.006 0l.415.33M21 12a9 9 0 1 1-18 0 9 9 0 0 1 18 0Z"/>
                                </svg>
                            </div>
                            @error('sale_unit_price') <p class="mt-2 text-sm text-red-600" id="sale_unit_price-error">{{ $message }}</p> @enderror
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="rounded-b-lg bg-gray-50 px-4 py-3 sm:flex sm:flex-row-reverse sm:px-6">
            <button type="submit" wire:loading.attr="disabled" class="inline-flex w-full justify-center rounded-md bg-keppel-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-keppel-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-keppel-600 sm:ml-3 sm:w-auto">
                Add
            </button>
            <button type="button" wire:loading.attr="disabled" wire:click="close" class="mt-3 inline-flex w-full justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:mt-0 sm:w-auto">
                Cancel
            </button>
        </div>
    </form>
</x-modal>

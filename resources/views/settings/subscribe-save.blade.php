@php use App\Services\SubscriptionSettingsService; @endphp
@extends('layouts.main', ['pageTitle' => 'Settings / Subscriptions'])

{{--Toolbar--}}
@section('toolbar-breadcrumb')
    <li><a href="/admin/settings">Settings</a></li>
    <li>Subscriptions</li>
@stop

@section('setting_toolbar')
    <button class="btn btn-success" @click="submitForm('settingsForm')">Save</button>
@stop

@section('content')

    <div class="pb-5 space-x-4 sm:flex sm:items-center sm:justify-between">
        <div>
            <div class="flex items-center">
                <h3 class="ml-2 text-lg font-medium leading-6 text-gray-900"><i class="far fa-repeat"></i> Subscriptions</h3>
            </div>
        </div>
    </div>

    <div>
        @php
            $active_tab = request()->get('tab', 'products');
        @endphp
        <div class="sm:hidden">
            <label for="tabs" class="sr-only">Select a tab</label>
            <!-- Use an "onChange" listener to redirect the user to the selected tab URL. -->
            <select id="tabs" name="tabs" onChange="changeTab(event.target.value)" class="block w-full rounded-md border-gray-300 py-2 pl-3 pr-10 text-base focus:border-keppel-500 focus:outline-none focus:ring-keppel-500 sm:text-sm">
                <option value="products" @if($active_tab === 'products') selected @endif>Products</option>
                <option value="incentives" @if($active_tab === 'incentives') selected @endif>Incentives</option>
                <option value="notifications" @if($active_tab === 'notifications') selected @endif>Notifications</option>
            </select>
        </div>
        <div class="hidden sm:block">
            <nav class="-mb-px flex space-x-8" aria-label="Tabs">
                <a href="?tab=products" class="@if($active_tab === 'products') border-keppel-500 text-keppel-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" @if($active_tab === 'products') aria-current="page" @endif>Products</a>
                <a href="?tab=incentives" class="@if($active_tab === 'incentives') border-keppel-500 text-keppel-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" @if($active_tab === 'incentives') aria-current="page" @endif>Incentives</a>
                <a href="?tab=notifications" class="@if($active_tab === 'notifications') border-keppel-500 text-keppel-600 @else border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300 @endif whitespace-nowrap py-4 px-1 border-b-2 font-medium text-sm" @if($active_tab === 'notifications') aria-current="page" @endif>Notifications</a>
            </nav>
        </div>
    </div>

    @php
        $subscription_settings_service = app(SubscriptionSettingsService::class);
    @endphp
    @includeIf("settings.subscribe-and-save." . request()->get('tab', 'products'))
@stop

@section('scripts')
    <script>
        var changeTab = function(tab) {
            window.location = window.location.origin + window.location.pathname + '?tab=' + tab;
        };
    </script>
@endsection

<template>
    <div class="tw-py-6">
        <Disclosure>
            <DisclosureButton :class="[currentView !== 'contact' ? 'tw-border-b tw-border-gray-200' : '']" as="template" @click="toggleContact">
                <button :class="['tw-text-gray-900 tw-w-full tw-pb-6 tw-text-left tw-text-lg tw-font-display tw-font-medium tw-flex tw-items-center tw-justify-between tw-cursor-pointer']" type="button">
                    Information
                    <ChevronUpIcon v-if="currentView !== 'contact'" class="tw-ml-3 tw-w-5 tw-h-5 tw-text-gray-500"/>
                    <ChevronDownIcon v-else class="tw-ml-3 tw-w-5 tw-h-5 tw-text-gray-500"/>
                </button>
            </DisclosureButton>
            <transition
                enter-active-class="tw-transition tw-duration-100 tw-ease-out"
                enter-from-class="tw-opacity-0"
                enter-to-class="tw-opacity-100"
            >
                <div v-show="currentView === 'contact'">
                    <DisclosurePanel class="tw-border-b tw-border-gray-200" static>
                        <ContactInformationForm @completed="setCurrentView"/>
                    </DisclosurePanel>
                </div>
            </transition>
        </Disclosure>
        <Disclosure>
            <DisclosureButton :class="[currentView !== 'shipping' ? 'tw-border-b tw-border-gray-200' : '']" as="template" @click="toggleShipping">
                <button :class="[cart.contactInformationIsComplete ? 'tw-text-gray-900' : 'tw-text-gray-500', 'tw-w-full tw-py-6 tw-text-left tw-text-lg tw-font-display tw-font-medium tw-flex tw-items-center tw-justify-between tw-cursor-pointer']" :disabled="!cart.contactInformationIsComplete" type="button">
                    Delivery
                    <ChevronUpIcon v-if="currentView !== 'shipping'" class="tw-ml-3 tw-w-5 tw-h-5 tw-text-gray-500"/>
                    <ChevronDownIcon v-else class="tw-ml-3 tw-w-5 tw-h-5 tw-text-gray-500"/>
                </button>
            </DisclosureButton>
            <transition
                enter-active-class="tw-transition tw-duration-100 tw-ease-out"
                enter-from-class="tw-opacity-0"
                enter-to-class="tw-opacity-100"
            >
                <div v-show="currentView === 'shipping'">
                    <DisclosurePanel class="tw-border-b tw-border-gray-200" static>
                        <ShippingInformationForm @completed="setCurrentView"/>
                    </DisclosurePanel>
                </div>
            </transition>
        </Disclosure>
        <Disclosure>
            <DisclosureButton :class="[currentView !== 'billing' ? 'tw-border-b tw-border-gray-200' : '']" as="template" @click="toggleBilling">
                <button :class="[shippingIsComplete ? 'tw-text-gray-900' : 'tw-text-gray-500', 'tw-w-full tw-py-6 tw-text-left tw-text-lg tw-font-display tw-font-medium tw-flex tw-items-center tw-justify-between tw-cursor-pointer']" :disabled=" ! shippingIsComplete" type="button">
                    Payment
                    <ChevronUpIcon v-if="currentView !== 'billing'" class="tw-ml-3 tw-w-5 tw-h-5 tw-text-gray-500"/>
                    <ChevronDownIcon v-else class="tw-ml-3 tw-w-5 tw-h-5 tw-text-gray-500"/>
                </button>
            </DisclosureButton>
            <transition
                enter-active-class="tw-transition tw-duration-100 tw-ease-out"
                enter-from-class="tw-opacity-0"
                enter-to-class="tw-opacity-100"
            >
                <div v-show="currentView === 'billing'">
                    <DisclosurePanel class="tw-border-b tw-border-gray-200" static>
                        <PaymentInformationForm @completed="setCurrentView"/>
                    </DisclosurePanel>
                </div>
            </transition>
        </Disclosure>

        <Disclosure>
            <DisclosureButton :class="[currentView !== 'review' ? 'tw-border-b tw-border-gray-200' : '']" as="template" @click="toggleReview">
                <button :class="[cart.billingInformationIsComplete ? 'tw-text-gray-900' : 'tw-text-gray-500', 'tw-w-full tw-py-6 tw-text-left tw-text-lg tw-font-display tw-font-medium tw-flex tw-items-center tw-justify-between tw-cursor-pointer']" :disabled="!cart.billingInformationIsComplete" type="button">
                    Review
                    <ChevronUpIcon v-if="currentView !== 'review'" class="tw-ml-3 tw-w-5 tw-h-5 tw-text-gray-500"/>
                    <ChevronDownIcon v-else class="tw-ml-3 tw-w-5 tw-h-5 tw-text-gray-500"/>
                </button>
            </DisclosureButton>
            <transition
                enter-active-class="tw-transition tw-duration-100 tw-ease-out"
                enter-from-class="tw-opacity-0"
                enter-to-class="tw-opacity-100"
            >
                <div v-if="currentView === 'review'">
                    <DisclosurePanel class="tw-border-b tw-border-gray-200" static>
                        <ReviewForm @completed="redirectToCheckoutComplete"/>
                    </DisclosurePanel>
                </div>
            </transition>
        </Disclosure>
    </div>
</template>

<script>
export default {
    name: 'CheckoutForm'
};
</script>

<script setup>
import { ChevronUpIcon, ChevronDownIcon } from '@heroicons/vue/outline';
import { Disclosure, DisclosurePanel, DisclosureButton } from '@headlessui/vue';
import ContactInformationForm from './ContactInformationForm.vue';
import ShippingInformationForm from './ShippingInformationForm.vue';
import PaymentInformationForm from './PaymentInformationForm.vue';
import ReviewForm from './ReviewForm.vue';
import { computed, ref, toRaw, watch } from 'vue';
import { useCartStore } from '../../stores/cart';
import { useCheckoutStore } from '../../stores/checkout';
import { useSubscriptionStore } from '../../stores/subscriptions';
import { find } from 'lodash';
import { itemSubtotal } from '../../../../../../assets/js/modules/cart.js';
import { itemPrice } from '../../composables/cart.js';
import { usePotentialSubscriptionDiscountTotal } from '../../composables/subscription.js';

const cart = useCartStore();
const checkout = useCheckoutStore();
const subscription = useSubscriptionStore();

const currentView = ref(null);

const shippingIsComplete = computed(() => {
    return ((checkout.isPickup && !checkout.require_shipping_address) || cart.shippingInformationIsComplete)
        && (checkout.available_dates.length === 0 || cart.date_id !== null);
});

const setCurrentView = () => {
    if (!cart.contactInformationIsComplete) {
        return currentView.value = 'contact';
    }

    if (!shippingIsComplete.value) {
        return currentView.value = 'shipping';
    }

    if (!cart.billingInformationIsComplete) {
        return currentView.value = 'billing';
    }

    currentView.value = 'review';
};

const productIncentive = computed(() => cart.subscription ? subscription.selectedProductIncentive(cart.subscription.product_incentive_id) : null);
const productIncentiveAsItem = computed(() => {
    if (!cart.isSubscribing || !productIncentive.value) return null;

    return {
        product: productIncentive.value,
        quantity: 1,
        weight: productIncentive.value.weight
    };
});

const productIncentiveSubtotal = computed(() => {
    if (!productIncentiveAsItem.value) return 0;
    return itemSubtotal(productIncentiveAsItem.value);
});

const subscriptionSavingsTotal = computed(() => {
    if (!cart.isSubscribing) return 0;

    // savings calculation should not include discount on product incentive subtotal
    let subtotal = cart.itemsSubtotal;

    if (!subscription.discount_incentive_applies_to_one_time_items) {
        subtotal = cartItems.value
            .filter(item => subscription.excluded_product_ids.indexOf(item.product.id) === -1)
            .reduce((previousValue, item) => previousValue + itemPrice(toRaw(item)), 0);
    }

    return usePotentialSubscriptionDiscountTotal(subtotal, subscription.discount_incentive);
});

const subtotal = computed(() => {
    return cart.itemsSubtotal + productIncentiveSubtotal.value - subscriptionSavingsTotal.value;
});

const addedShippingInfo = () => {
    if (typeof gtag === 'function') {
        gtag('event', 'add_shipping_info', {
            currency: 'USD',
            value: subtotal.value / 100,
            shipping_tier: checkout.delivery_method.title,
            items: cart.itemsAsGoogleArray
        });
    }
};

const addedBillingInfo = () => {
    const selectedPaymentMethod = computed(() => find(checkout.available_payment_options, { key: cart.billing.method }));

    if (typeof gtag === 'function') {
        gtag('event', 'add_payment_info', {
            currency: 'USD',
            value: subtotal.value / 100,
            payment_type: selectedPaymentMethod.value.title,
            items: cart.itemsAsGoogleArray
        });
    }
};

setCurrentView();

const fireInitialEvents = () => {
    if (currentView.value === 'contact' || currentView.value === 'shipping') return;

    addedShippingInfo();

    if (currentView.value === 'review') {
        setTimeout(() => {
            addedBillingInfo();
        }, 1000);
    }
};

fireInitialEvents();

watch(currentView, async (newView, oldView) => {
    if (oldView === 'shipping') {
        addedShippingInfo();
    }

    if (oldView === 'billing') {
        addedBillingInfo();
    }
});

const toggleContact = () => {
    if (!cart.contactInformationIsComplete || currentView.value === 'contact') return;

    return currentView.value = 'contact';
};

const toggleShipping = () => {
    if (!cart.contactInformationIsComplete || currentView.value === 'shipping') return;

    return currentView.value = 'shipping';
};

const toggleBilling = () => {
    if (!cart.contactInformationIsComplete || !shippingIsComplete.value || currentView.value === 'billing') return;

    return currentView.value = 'billing';
};

const toggleReview = () => {
    if (!cart.contactInformationIsComplete || !shippingIsComplete.value || !cart.billingInformationIsComplete || currentView.value === 'review') return;

    return currentView.value = 'review';
};

const redirectToCheckoutComplete = () => window.location.href = '/checkout/complete';

</script>

<template>
    <div>
        <!-- Mobile order summary -->
        <section aria-labelledby="order-heading" class="tw-bg-gray-50 tw-py-6 lg:tw-hidden">
            <Disclosure v-slot="{ open }" as="div" class="tw-max-w-lg tw-mx-auto">
                <div :class="[ ! open ? 'tw-pb-6' : '', 'tw-flex tw-items-center tw-justify-between']">
                    <h2 id="order-heading" class="tw-text-lg tw-font-semibold tw-font-display tw-text-gray-900">Your Order</h2>
                    <DisclosureButton class="tw-text-theme-action-color hover:tw-text-theme-action-color/70">
                        <span v-if="open">Hide full summary</span>
                        <span v-if="!open">Show full summary</span>
                    </DisclosureButton>
                </div>

                <transition
                    enter-active-class="transition duration-100 ease-out"
                    enter-from-class="transform opacity-0"
                    enter-to-class="transform opacity-100"
                    leave-active-class="transition duration-75 ease-out"
                    leave-from-class="transform opacity-100"
                    leave-to-class="transform opacity-0"
                >
                    <DisclosurePanel as="div">
                        <div class="tw-divide-y tw-divide-gray-200 tw-space-y-4">
                            <div class="tw-space-y-4">
                                <h2 id="summary-heading" class="tw-sr-only">Order summary</h2>
                                <div :class="[cart.isSubscribing ? 'tw-border tw-border-gray-200' : '', 'tw-rounded-md  tw-py-4']">
                                    <div v-if="cart.isSubscribing" class="tw-pb-2 tw-px-4">
                                        <div class="tw-flex">
                                            <div class="tw-flex-shrink-0 tw-mt-1 tw-ml-1">
                                                <RefreshIcon aria-hidden="true" class="tw-h-4 tw-w-4 tw-text-gray-400"/>
                                            </div>
                                            <div class="tw-ml-1 tw-flex-1">
                                                <p class="tw-font-bold tw-text-gray-700 tw-text-base">Subscription items</p>
                                                <p class="tw-text-sm tw-text-gray-600 tw-italic">Repeat
                                                    <span v-text="formattedFrequency"></span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                    <ul :class="[cart.isSubscribing ? ' tw-px-6' : '', 'tw-flex-auto tw-overflow-y-auto tw-divide-y tw-divide-gray-200']" role="list">
                                        <OrderSummaryPromoItem v-if="productIncentiveAsItem" :item="productIncentiveAsItem" class="tw-mb-1"/>
                                        <OrderSummaryItem v-for="item in standardItems" :key="item.id" :item="item" class="tw-px-4"/>
                                    </ul>
                                </div>
                                <div v-if="cart.isSubscribing && addOnItems.length > 0" class="tw-rounded-md tw-border tw-border-gray-200 tw-py-4">
                                    <div class="tw-pb-2 tw-px-4">
                                        <div class="tw-flex">
                                            <div class="tw-flex-shrink-0 tw-mt-1 tw-ml-1">
                                                <CheckCircleIcon aria-hidden="true" class="tw-h-4 tw-w-4 tw-text-gray-400"/>
                                            </div>
                                            <div class="tw-ml-1 tw-flex-1">
                                                <p class="tw-font-bold tw-text-gray-700 tw-text-base">One-time items</p>
                                                <p class="tw-text-sm tw-text-gray-600 tw-italic">Next order only</p>
                                            </div>
                                        </div>
                                    </div>
                                    <ul class="tw-flex-auto tw-overflow-y-auto tw-divide-y tw-divide-gray-200 tw-px-6" role="list">
                                        <OrderSummaryItem v-for="item in addOnItems" :key="item.id" :item="item" class="tw-px-4"/>
                                    </ul>
                                </div>
                            </div>

                            <OrderDiscountCodeForm v-if="cart.canApplyCoupon" class="tw-py-4 tw-border-b tw-border-gray-200 "/>

                            <dl class="pt-tw-border-t tw-border-gray-200 tw-text-sm tw-font-semibold tw-text-gray-500 tw-py-6 tw-space-y-6">
                                <div class="tw-flex tw-justify-between">
                                    <dt>Subtotal</dt>
                                    <dd class="tw-text-gray-900" v-text="currency.centsToDollars(subtotal)"></dd>
                                </div>

                                <div v-if="locationFeeTotal > 0" class="tw-flex tw-justify-between">
                                    <dt class="tw-flex tw-items-center">Other Fees
                                        <Popover class="tw-ml-1 tw-relative tw-flex tw-items-center">
                                            <PopoverButton>
                                                <InformationCircleIcon class="tw-text-gray-400 tw-h-4 tw-w-4"/>
                                            </PopoverButton>

                                            <PopoverPanel class="tw-absolute tw-z-20 tw-right-0 tw-mt-5 tw-top-0 tw-bg-white tw-border tw-w-screen tw-max-w-xs tw-border-gray-200 tw-p-3 tw-mt-3 tw-rounded tw-shadow-lg">
                                                <p class="tw-font-display tw-font-bold tw-text-gray-900">Other Fees</p>
                                                <dl class="tw-mt-2 tw-space-y-2">
                                                    <div v-for="fee in checkout.locationFees" :key="fee.id" class="tw-flex tw-items-center tw-justify-between tw-space-x-8">
                                                        <dt class="tw-text-gray-600" v-text="fee.title"></dt>
                                                        <dd v-text="currency.centsToDollars(fee.amount)"></dd>
                                                    </div>
                                                </dl>
                                            </PopoverPanel>
                                        </Popover>
                                    </dt>
                                    <dd class="tw-text-gray-900" v-text="currency.centsToDollars(locationFeeTotal)"></dd>
                                </div>
                                <div v-show="taxTotal > 0" class="tw-flex tw-justify-between">
                                    <dt>Taxes</dt>
                                    <dd class="tw-text-gray-900" v-text="currency.centsToDollars(taxTotal)"></dd>
                                </div>
                                <div v-show="deliveryFeeTotal > 0" class="tw-flex tw-justify-between">
                                    <dt>Delivery</dt>
                                    <dd class="tw-text-gray-900" v-text="currency.centsToDollars(deliveryFeeTotal)"></dd>
                                </div>
                                <div v-show="subscriptionSavingsTotal > 0" class="tw-flex tw-justify-between">
                                    <dt>Subscription savings (5%)</dt>
                                    <dd class="tw-text-gray-900">-
                                        <span v-text="currency.centsToDollars(subscriptionSavingsTotal)"></span>
                                    </dd>
                                </div>
                                <div v-if="cart.couponsSubtotal > 0" class="tw-flex tw-justify-between">
                                    <dt class="tw-flex">
                                        Coupon
                                        <span class="tw-ml-2 tw-rounded-full tw-bg-gray-200 tw-text-xs tw-text-gray-600 tw-py-0.5 tw-px-2 tw-tracking-wide">
                                            {{ cart.discounts.coupons[0].code }}
                                        </span>
                                    </dt>
                                    <dd class="tw-text-gray-900">-
                                        <span v-text="currency.centsToDollars(cart.couponsSubtotal)"></span>
                                    </dd>
                                </div>
                                <div v-show="creditTotal > 0" class="tw-flex tw-justify-between">
                                    <dt class="tw-flex">
                                        Credit
                                    </dt>
                                    <dd class="tw-text-gray-900">-
                                        <span v-text="currency.centsToDollars(creditTotal)"></span>
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </DisclosurePanel>
                </transition>

                <div class="tw-border-t tw-border-gray-200 tw-pt-6">
                    <div v-if=" ! open && cart.couponsSubtotal > 0" class="tw-flex tw-justify-between">
                        <dt class="">
                            <p class="tw-m-0 tw-text-xs">Coupon applied!</p>
                            <span class="tw-rounded-full tw-bg-gray-200 tw-text-xs tw-text-gray-600 tw-py-px tw-px-2 tw-tracking-wide">
                                {{ cart.discounts.coupons[0].code }}
                            </span>
                        </dt>
                        <dd class="tw-text-gray-900">-
                            <span v-text="currency.centsToDollars(cart.couponsSubtotal)"></span>
                        </dd>
                    </div>
                    <div v-else>
                        <DisclosureButton v-show=" ! open" class="tw-text-theme-action-color hover:tw-text-theme-action-color/70">
                            <span>Add discount code</span>
                        </DisclosureButton>
                    </div>

                    <p class="tw-mt-4 tw-flex tw-items-center tw-justify-between tw-text-sm tw-font-semibold tw-text-gray-900">
                        <span class="tw-text-base">Total</span>
                        <span class="tw-text-base" v-text="currency.centsToDollars(total)"></span>
                    </p>
                </div>

                <div class="tw-mt-6 tw-flex">
                    <div class="tw-flex-shrink-0">
                        <img alt="Brothers' Guarantee" class="tw-flex-none tw-w-10 tw-h-8 tw-rounded-md tw-object-center tw-object-cover" src="https://d14fky00evb1ot.cloudfront.net/sevensonsfarms/images/1726507278_66e8690e9996a.webp">
                    </div>
                    <div class="tw-ml-3">
                        <p class="tw-text-xs tw-font-medium tw-text-gray-800">
                            Your card will be charged once all items are weighed and packed. All orders are backed by our 100% satisfaction guarantee.
                        </p>
                    </div>
                </div>
            </Disclosure>
        </section>

        <!-- Order summary -->
        <section aria-labelledby="summary-heading" class="tw-hidden tw-bg-gray-50  tw-w-full tw-max-w-md tw-flex-col lg:tw-flex">
            <h2 id="summary-heading" class="tw-pt-6 tw-text-lg tw-font-semibold tw-font-display tw-text-gray-900">Order summary</h2>
            <div class="tw-divide-y tw-divide-gray-200">
                <div class="tw-py-4 tw-space-y-4">
                    <div :class="[cart.isSubscribing ? 'tw-border tw-border-gray-200  tw-py-4' : '', 'tw-rounded-md']">
                        <div v-if="cart.isSubscribing" class="tw-pb-4 tw-px-4">
                            <div class="tw-flex">
                                <div class="tw-flex-shrink-0 tw-mt-1 tw-ml-1">
                                    <RefreshIcon aria-hidden="true" class="tw-h-4 tw-w-4 tw-text-gray-400"/>
                                </div>
                                <div class="tw-ml-1 tw-flex-1">
                                    <p class="tw-font-bold tw-text-gray-700 tw-text-base">Subscription items</p>
                                    <p class="tw-text-sm tw-text-gray-500 tw-italic">Repeat
                                        <span v-text="formattedFrequency"></span>
                                    </p>
                                </div>
                            </div>
                        </div>
                        <ul :class="[cart.isSubscribing ? ' tw-px-6' : '', 'tw-flex-auto tw-overflow-y-auto tw-divide-y tw-divide-gray-200']" role="list">
                            <OrderSummaryPromoItem v-if="productIncentiveAsItem" :item="productIncentiveAsItem" class="tw-mb-1"/>
                            <OrderSummaryItem v-for="item in standardItems" :key="item.id" :item="item" class="tw-px-4"/>
                        </ul>
                    </div>
                    <div v-if="cart.isSubscribing && addOnItems.length > 0" class="tw-rounded-md tw-border tw-border-gray-200 tw-py-4">
                        <div class="tw-pb-2 tw-px-4">
                            <div class="tw-flex">
                                <div class="tw-flex-shrink-0 tw-mt-1 tw-ml-1">
                                    <CheckCircleIcon aria-hidden="true" class="tw-h-4 tw-w-4 tw-text-gray-400"/>
                                </div>
                                <div class="tw-ml-1 tw-flex-1">
                                    <p class="tw-font-bold tw-text-gray-700 tw-text-base">One-time items</p>
                                    <p class="tw-text-sm tw-text-gray-500 tw-italic">This order only</p>
                                </div>
                            </div>
                        </div>
                        <ul class="tw-flex-auto tw-overflow-y-auto tw-divide-y tw-divide-gray-200 tw-px-6" role="list">
                            <OrderSummaryItem v-for="item in addOnItems" :key="item.id" :item="item" class="tw-px-4"/>
                        </ul>
                    </div>
                </div>

                <div class="tw-flex-none tw-bg-gray-50 tw-space-y-4">
                    <OrderDiscountCodeForm v-if="cart.canApplyCoupon" class="tw-py-4 tw-border-b tw-border-gray-200 "/>

                    <dl class="tw-text-sm tw-font-semibold tw-text-gray-500 tw-py-6 tw-space-y-6">
                        <div class="tw-flex tw-justify-between">
                            <dt>Subtotal</dt>
                            <dd class="tw-text-gray-900" v-text="currency.centsToDollars(subtotal)"></dd>
                        </div>

                        <div v-if="locationFeeTotal > 0" class="tw-flex tw-justify-between">
                            <dt class="tw-flex tw-items-center">Other Fees
                                <Popover class="tw-ml-1 tw-relative tw-flex tw-items-center">
                                    <PopoverButton>
                                        <InformationCircleIcon class="tw-text-gray-400 tw-h-4 tw-w-4"/>
                                    </PopoverButton>

                                    <PopoverPanel class="tw-absolute tw-z-20 tw-right-0 tw-mt-5 tw-top-0 tw-bg-white tw-border tw-w-screen tw-max-w-xs tw-border-gray-200 tw-p-3 tw-mt-3 tw-rounded tw-shadow-lg">
                                        <p class="tw-font-display tw-font-bold tw-text-gray-900">Other Fees</p>
                                        <dl class="tw-mt-2 tw-space-y-2">
                                            <div v-for="fee in checkout.locationFees" :key="fee.id" class="tw-flex tw-items-center tw-justify-between tw-space-x-8">
                                                <dt class="tw-text-gray-600" v-text="fee.title"></dt>
                                                <dd v-text="currency.centsToDollars(fee.amount)"></dd>
                                            </div>
                                        </dl>
                                    </PopoverPanel>
                                </Popover>
                            </dt>
                            <dd class="tw-text-gray-900" v-text="currency.centsToDollars(locationFeeTotal)"></dd>
                        </div>
                        <div v-show="taxTotal > 0" class="tw-flex tw-justify-between">
                            <dt>Taxes</dt>
                            <dd class="tw-text-gray-900" v-text="currency.centsToDollars(taxTotal)"></dd>
                        </div>
                        <div v-show="deliveryFeeTotal > 0" class="tw-flex tw-justify-between">
                            <dt>Delivery</dt>
                            <dd class="tw-text-gray-900" v-text="currency.centsToDollars(deliveryFeeTotal)"></dd>
                        </div>
                        <div v-show="subscriptionSavingsTotal > 0" class="tw-flex tw-justify-between">
                            <dt>Subscription savings (5%)</dt>
                            <dd class="tw-text-gray-900">-
                                <span v-text="currency.centsToDollars(subscriptionSavingsTotal)"></span>
                            </dd>
                        </div>
                        <div v-if="cart.couponsSubtotal > 0" class="tw-flex tw-justify-between">
                            <dt class="tw-flex">
                                Coupon
                                <span class="tw-ml-2 tw-rounded-full tw-bg-gray-200 tw-text-xs tw-text-gray-600 tw-py-0.5 tw-px-2 tw-tracking-wide">
                                    {{ cart.discounts.coupons[0].code }}
                                </span>
                            </dt>
                            <dd class="tw-text-gray-900">-
                                <span v-text="currency.centsToDollars(cart.couponsSubtotal)"></span>
                            </dd>
                        </div>
                        <div v-show="creditTotal > 0" class="tw-flex tw-justify-between">
                            <dt class="tw-flex">
                                Credit
                                <!--                            <span class="tw-ml-2 tw-rounded-full tw-bg-gray-200 tw-text-xs tw-text-gray-600 tw-py-0.5 tw-px-2 tw-tracking-wide">{{ discount.code }}</span>-->
                            </dt>
                            <dd class="tw-text-gray-900">-
                                <span v-text="currency.centsToDollars(creditTotal)"></span>
                            </dd>
                        </div>
                        <div class="tw-border-t tw-border-gray-200 tw-pt-6 tw-text-gray-900">
                            <div class="tw-flex tw-items-center tw-justify-between">
                                <dt class="tw-text-base">Total</dt>
                                <dd class="tw-text-base" v-text="currency.centsToDollars(total)"></dd>
                            </div>
                            <div class="tw-mt-6 tw-flex">
                                <div class="tw-flex-shrink-0">
                                    <img alt="Brothers' Guarantee" class="tw-flex-none tw-w-16 tw-h-12 tw-rounded-md tw-object-center tw-object-cover" src="https://d14fky00evb1ot.cloudfront.net/sevensonsfarms/images/1726507278_66e8690e9996a.webp">
                                </div>
                                <div class="tw-ml-3">
                                    <p class="tw-text-sm tw-font-medium tw-text-gray-800">
                                        Your card will be charged once all items are weighed and packed. All orders are backed by our 100% satisfaction guarantee.
                                    </p>
                                </div>
                            </div>
                        </div>
                    </dl>

                </div>
            </div>

        </section>
    </div>
</template>

<script>
export default {
    name: 'OrderSummary'
};
</script>


<script setup>
import { ChevronUpIcon, RefreshIcon, CheckCircleIcon } from '@heroicons/vue/solid';
import { InformationCircleIcon } from '@heroicons/vue/outline';
import {
    Popover,
    PopoverButton,
    PopoverOverlay,
    PopoverPanel,
    Disclosure,
    DisclosurePanel,
    DisclosureButton,
    TransitionChild,
    TransitionRoot
} from '@headlessui/vue';
import { useCartStore } from '../../stores/cart';
import { useMessageStore } from '../../stores/message';
import { itemTaxTotal, deliveryFee, deliveryFeeTax, itemPrice } from '../../composables/cart';
import { itemSubtotal } from '../../../../../../assets/js/modules/cart';
import currency from '../../../../../../assets/js/modules/currency';
import OrderSummaryItem from './OrderSummaryItem.vue';
import OrderSummaryPromoItem from './OrderSummaryPromoItem.vue';
import OrderDiscountCodeForm from './OrderDiscountCodeForm.vue';
import { computed, ref, toRaw } from 'vue';
import { useSubscriptionStore } from '../../stores/subscriptions';
import { useFormattedFrequency, usePotentialSubscriptionDiscountTotal } from '../../composables/subscription';
import { useCheckoutStore } from '../../stores/checkout';
import { useUserStore } from '../../stores/user';

const cart = useCartStore();
const checkout = useCheckoutStore();
const message = useMessageStore();
const subscription = useSubscriptionStore();
const user = useUserStore();

const open = ref(false);

const cartItems = computed(() => cart.itemsArray);

const productIncentive = computed(() => cart.subscription ? subscription.selectedProductIncentive(cart.subscription.product_incentive_id) : null);

const productIncentiveAsItem = computed(() => {
    if (!cart.isSubscribing || !productIncentive.value) return null;

    return {
        product: productIncentive.value,
        quantity: 1,
        weight: productIncentive.value.weight
    };
});

const productIncentiveSubtotal = computed(() => {
    if (!productIncentiveAsItem.value) return 0;
    return itemSubtotal(productIncentiveAsItem.value);
});

const subtotal = computed(() => {
    return cart.itemsSubtotal + productIncentiveSubtotal.value;
});

const standardItems = computed(() => {
    const items = cartItems.value;

    if (!cart.isSubscribing) {
        return items;
    }

    return items.filter(item => subscription.excluded_product_ids.indexOf(item.product.id) === -1);
});

const addOnItems = computed(() => {
    const items = cartItems.value;

    if (!cart.isSubscribing) {
        return [];
    }

    return items.filter(item => subscription.excluded_product_ids.indexOf(item.product.id) !== -1);
});

const formattedFrequency = computed(() => cart.subscription ? useFormattedFrequency(cart.subscription.frequency) : null);

const subscriptionSavingsTotal = computed(() => {
    if (!cart.isSubscribing) return 0;

    // savings calculation should not include discount on product incentive subtotal
    let subtotal = cart.itemsSubtotal;

    if (!subscription.discount_incentive_applies_to_one_time_items) {
        subtotal = cartItems.value
            .filter(item => subscription.excluded_product_ids.indexOf(item.product.id) === -1)
            .reduce((previousValue, item) => previousValue + itemPrice(toRaw(item)), 0);
    }

    return usePotentialSubscriptionDiscountTotal(subtotal, subscription.discount_incentive);
});

const itemsTaxTotal = computed(() => {
    if (checkout.hasNoTax) return 0;

    return cartItems.value
        .reduce((previousValue, item) => previousValue + itemTaxTotal(toRaw(item), checkout.taxRate), 0);
});

const deliveryTaxTotal = computed(() => {
    if (!checkout.delivery_method.tax_delivery_fee) return 0;
    return deliveryFeeTax(checkout.delivery_method, toRaw(cartItems.value), toRaw(checkout.taxRate));
});

const taxTotal = computed(() => {
    if (user.exempt_from_taxes) return 0;
    return itemsTaxTotal.value + deliveryTaxTotal.value + checkout.locationFeesTaxTotal;
});

const deliveryFeeTotal = computed(() => {
    return deliveryFee(checkout.delivery_method, toRaw(cartItems.value));
});

const locationFeeTotal = computed(() => {
    if (user.exempt_from_fees) return 0;
    return checkout.locationFeesTotal;
});

const feesTotal = computed(() => {
    if (user.exempt_from_fees) return 0;
    return deliveryFeeTotal.value + checkout.locationFeesTotal;
});

const totalBeforeCredit = computed(() => {
    return subtotal.value + feesTotal.value + taxTotal.value - subscriptionSavingsTotal.value - cart.couponsSubtotal;
});

const creditTotal = computed(() => {
    if (!cart.creditTotal) return 0;
    return Math.min(totalBeforeCredit.value, cart.creditTotal);
});

const total = computed(() => totalBeforeCredit.value - creditTotal.value);
</script>

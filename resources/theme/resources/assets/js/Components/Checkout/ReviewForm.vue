<template>
    <form class="tw-pb-6 tw-space-y-8" @submit.prevent="placeOrder">
        <div class="tw-space-y-6">
            <div v-if="checkout.delivery_method && checkout.delivery_method.settings.checkout_notes" class="tw-rounded-lg tw-bg-gray-50 tw-p-4">
                <div class="tw-flex">
                    <div class="tw-shrink-0">
                        <ExclamationCircleIcon aria-hidden="true" class="tw-h-5 tw-w-5 tw-text-gray-400"/>
                    </div>
                    <div class="tw-ml-3">
                        <h3 class="tw-text-sm tw-font-semibold tw-text-gray-700">Important
                            <span v-text="checkout.isPickup ? 'Pickup' : 'Shipping'"></span> Notes</h3>
                        <div class="tw-mt-2 tw-prose tw-text-sm tw-text-gray-800" v-html="checkout.delivery_method.settings.checkout_notes"></div>
                    </div>
                </div>
            </div>

            <div>
                <h3 class="tw-sr-only">Your order information</h3>
                <h4 class="tw-sr-only">Contact and Shipping</h4>

                <dl class="tw-grid tw-grid-cols-2 tw-gap-x-6 tw-text-sm">
                    <div>
                        <dt class="tw-font-semibold tw-text-gray-900">Contact</dt>
                        <dd class="tw-mt-2 tw-text-gray-700">
                            <address class="tw-not-italic">
                                <span class="tw-block tw-break-all">
                                    <span v-text="cart.customer.first_name"></span>
                                    <span v-text="cart.customer.last_name"></span>
                                </span>
                                <span class="tw-block tw-break-all" v-text="cart.customer.email"></span>
                                <span class="tw-block" v-text="cart.customer.phone"></span>
                                <span v-if=" ! user.subscribed_to_sms_marketing_at && checkout.sms_opt_in_settings.message && checkout.sms_opt_in_settings.sms_legal_consent_message" class="tw-flex tw-items-center">
                                    <CheckCircleIcon v-if="cart.customer.opt_in_to_sms" class="tw-mr-1 tw-w-4 tw-h-4 tw-text-gray-400"/>
                                    <XCircleIcon v-else class="tw-mr-1 tw-w-4 tw-h-4 tw-text-gray-500"/>
                                    SMS Opt-in
                                </span>
                            </address>
                        </dd>
                    </div>
                    <div>
                        <dt class="tw-font-semibold tw-text-gray-900">Delivery</dt>
                        <dd class="tw-mt-2 tw-text-gray-700">
                            <p v-text="checkout.isPickup ? 'Pickup at farm' : 'Home Delivery' "></p>
                            <p><span v-text="deliveryDate"></span><span v-if="checkout.hasEstimatedDeliveryDate">*</span></p>
                            <pre v-if="pickupTime" class="tw-whitespace-pre-line tw-font-body tw-text-sm" v-html="pickupTime"></pre>
                            <p v-if="checkout.hasEstimatedDeliveryDate" class="tw-mt-1 tw-italic text-gray-500">*Estimated delivery by</p>
                        </dd>
                    </div>
                </dl>
            </div>

            <div>
                <h4 class="tw-sr-only">Shipping and Payment</h4>
                <dl class="tw-grid tw-grid-cols-2 tw-gap-x-6 tw-text-sm">
                    <div>
                        <dt class="tw-font-semibold tw-text-gray-900"><span v-text="checkout.isPickup ? 'Pickup' : 'Shipping' "></span> address</dt>
                        <dd class="tw-mt-2 tw-text-gray-700">
                            <address v-if="checkout.isPickup" class="tw-not-italic">
                                <span class="tw-block" v-text="checkout.delivery_method.display_name ? checkout.delivery_method.display_name : checkout.delivery_method.title"></span>
                                <span class="tw-block"><span v-text="checkout.delivery_method.street"></span>
                                    <span v-text="checkout.delivery_method.street_2"></span></span>
                                <span class="tw-block"><span v-text="checkout.delivery_method.city"></span>,
                                    <span v-text="checkout.delivery_method.state"></span> <span v-text="checkout.delivery_method.zip"></span></span>
                            </address>
                            <address v-else class="tw-not-italic">
                                <span class="tw-block"><span v-text="cart.customer.first_name"></span> <span v-text="cart.customer.last_name"></span></span>
                                <span class="tw-block"><span v-text="cart.shipping.street"></span> <span v-text="cart.shipping.street_2"></span></span>
                                <span class="tw-block"><span v-text="cart.shipping.city"></span>, <span v-text="cart.shipping.state"></span>
                                    &nbsp;<span v-text="cart.shipping.zip"></span></span>
                            </address>
                        </dd>
                    </div>
                    <div v-if="paymentMethod">
                        <dt class="tw-font-semibold tw-text-gray-900">Payment Method</dt>
                        <dd class="tw-mt-2 tw-text-gray-700">
                            <p v-text="paymentMethod.title"></p>
                            <div v-if="selectedCard" class="tw-flex tw-space-x-2">
                                <div>
                                    <p>
                                        <span aria-hidden="true">•••• </span><span class="tw-sr-only">Ending in </span><span v-text="selectedCard.last_four"></span>
                                    </p>
                                </div>
                                <div>
                                    <p class="tw-text-gray-500">Exp {{ String(selectedCard.exp_month).padStart(2, '0') }}/{{ String(selectedCard.exp_year).slice(-2) }}</p>
                                </div>
                            </div>
                        </dd>
                    </div>
                </dl>
            </div>
        </div>
        <div v-show="cart.is_gift" class="tw-bg-theme-brand-color/10 tw-p-4 tw-rounded-md">
            <h4 class="tw-mt-0 tw-text-sm tw-font-semibold tw-text-gray-900">Gift Information</h4>
            <dl class="tw-mt-2 tw-text-sm">
                <div>
                    <dt class="tw-block tw-text-sm font-semibold text-gray-700">Recipient</dt>
                    <dd class="tw-text-gray-700">
                        <p class="tw-m-0" v-text="cart.recipient_email"></p>
                    </dd>
                </div>
                <div class="tw-mt-4">
                    <dt class="tw-block tw-text-sm font-medium text-gray-700">Message</dt>
                    <dd class="tw-text-gray-700">
                        <p class="tw-m-0" v-text="cart.recipient_notes"></p>
                    </dd>
                </div>
            </dl>
        </div>
        <div>
            <label class="tw-sr-only" for="notes">Notes</label>
            <textarea id="notes" v-model="cart.notes" :placeholder="checkout.notes_placeholder" class="focus:tw-ring-theme-action-color focus:tw-border-theme-action-color tw-block tw-w-full tw-shadow-sm  placeholder:tw-text-gray-400 placeholder:tw-italic sm:tw-text-sm tw-border-gray-300 tw-rounded-md" maxlength="1000" name="notes" rows="3"/>
            <p id="notes-description" class="tw-mt-2 tw-text-sm tw-text-gray-500"><span v-text="noteCharactersRemaining"></span> characters remaining</p>
        </div>

        <p v-if="confirmationError" class="tw-my-2 tw-text-sm tw-text-red-500" v-text="confirmationError"></p>

        <button
            :class="[
                canSubmit ? 'tw-bg-theme-action-color tw-text-white hover:tw-bg-theme-action-color/70 focus:tw-ring-theme-action-color ' : 'tw-bg-gray-100 tw-text-gray-500 tw-cursor-not-allowed',
                'tw-mt-6 tw-w-full tw-cursor-pointer tw-border tw-border-transparent tw-rounded-md tw-shadow-sm tw-py-2 tw-px-4 tw-text-sm tw-font-semibold focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-offset-2'
            ]"
            :disabled=" ! canSubmit"
            type="submit"
            @click="placeOrder"
        >
            <span v-if="placingOrder" class="tw-inline-flex tw-justify-center">
                <svg class="tw-animate-spin tw--ml-1 tw-mr-3 tw-h-5 tw-w-5 tw-text-white" fill="none" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg">
                    <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="tw-opacity-75" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z" fill="currentColor"></path>
                </svg>
                Placing...
            </span>
            <span v-else>Place Order</span>
        </button>
    </form>
</template>

<script>
export default {
    name: 'ReviewForm'
};
</script>

<script setup>
import { CheckCircleIcon, XCircleIcon, ExclamationCircleIcon } from '@heroicons/vue/outline';
import axios from 'axios';
import { computed, ref, toRaw } from 'vue';
import { useCartStore } from '../../stores/cart';
import { useUserStore } from '../../stores/user';
import dayjs from 'dayjs';
import { find } from 'lodash';
import { useCheckoutStore } from '../../stores/checkout';

const emit = defineEmits(['completed']);

const cart = useCartStore();
const checkout = useCheckoutStore();
const user = useUserStore();

const placingOrder = ref(false);
const confirmationError = ref(null);

const shippingIsComplete = computed(() => {
    return ((checkout.isPickup && !checkout.require_shipping_address) || cart.shippingInformationIsComplete) && (checkout.available_dates.length === 0 || cart.date_id !== null);
});

const canSubmit = computed(() => {
    return !placingOrder.value
        && cart.contactInformationIsComplete
        && shippingIsComplete.value
        && cart.billingInformationIsComplete;
});

const noteCharactersRemaining = computed(() => Math.max(1000 - cart.notes.length, 0));

const deliveryDate = computed(() => {
    const date = find(checkout.available_dates, { id: cart.date_id });

    return date ? dayjs(date.date).format('dddd, MMM D, YYYY') : 'Date TBA';
});

const pickupTime = computed(() => checkout.delivery_method.pickup_times);

const paymentMethod = computed(() => find(checkout.available_payment_options, { key: cart.billing.method }));

const selectedCard = computed(() => {
    if (!paymentMethod.value || paymentMethod.value.key !== 'card') return null;

    return find(user.cards, { id: cart.billing.source_id });
});


const placeOrder = () => {
    if (placingOrder.value) return;

    placingOrder.value = true;
    confirmationError.value = null;

    const cartToConfirm = { ...toRaw(cart.$state) };

    cartToConfirm.items = Object.values(toRaw(cart.items)).map(item => ({
        product_id: item.product.id,
        quantity: item.quantity
    }));

    axios.post('/api/carts/confirm', cartToConfirm)
        .then(response => {
            emit('completed');
        })
        .catch(error => {
            placingOrder.value = false;

            let message = 'There was an error processing your order. Please try again later.';

            if (error.response.status === 409) {
                message = error.response.data.message;
            }

            confirmationError.value = message;
        });
};
</script>

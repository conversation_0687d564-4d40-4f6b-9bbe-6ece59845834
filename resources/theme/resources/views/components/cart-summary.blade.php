@props(['cart', 'detailed' => false, 'divided' => false])

@php
    /** @var \App\Contracts\Cartable $cart */
@endphp
<dl class="tw-m-0 tw-space-y-4">
    <div class="tw-flex tw-items-center tw-justify-between">
        <dt class="tw-font-normal tw-text-sm tw-text-gray-600">Subtotal</dt>
        <dd class="tw-font-normal tw-text-sm tw-font-medium tw-text-gray-900">${{ money($cart->cartSubtotal()) }}</dd>
    </div>
    <div class="tw-flex tw-items-center tw-justify-between {{ $divided ? 'tw-border-t tw-border-gray-200 tw-pt-4' : '' }}">
        <dt class="tw-font-normal tw-text-sm tw-text-gray-600">Delivery</dt>
        <dd class="tw-font-normal tw-text-sm tw-font-medium tw-text-gray-900">${{ money($cart->cartDeliveryTotal()) }}</dd>
    </div>
    @if ($cart->isRecurring() && $cart->cartSubscriptionSavingsTotal() > 0)
        <div class="tw-flex tw-items-center tw-justify-between {{ $divided ? 'tw-border-t tw-border-gray-200 tw-pt-4' : '' }}">
            <dt class="tw-font-normal tw-text-sm tw-text-gray-600">Subscription savings</dt>
            <dd class="tw-font-normal tw-text-sm tw-font-medium tw-text-gray-900">(${{ money($cart->cartSubscriptionSavingsTotal()) }})</dd>
        </div>
    @endif
</dl>

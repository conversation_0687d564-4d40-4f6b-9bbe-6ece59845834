@props(['page_id', 'widget'])

@php
    $padding_top = match ($widget['settings']['padding']['top'] ?? '') {
        'sm' => 'tw-pt-6 lg:tw-pt-10',
        'md' => 'tw-pt-12 lg:tw-pt-16',
        'lg' => 'tw-pt-24 lg:tw-pt-32',
        'xl' => 'tw-pt-32 lg:tw-pt-48',
        default => '',
    };

    $padding_bottom = match ($widget['settings']['padding']['bottom'] ?? '') {
        'sm' => 'tw-pb-6 lg:tw-pb-10',
        'md' => 'tw-pb-12 lg:tw-pb-16',
        'lg' => 'tw-pb-24 lg:tw-pb-32',
        'xl' => 'tw-pb-32 lg:tw-pb-48',
        default => '',
    };

    $max_width = match ($widget['settings']['max_width'] ?? '') {
        'sm' => 'tw-max-w-lg',
        'md' => 'tw-max-w-4xl',
        'lg' => 'tw-max-w-6xl',
        'xl' => 'tw-max-w-7xl',
        default => '',
    };

    $order = $openOrder;
    $products = ($widget['settings']['product_ids'] ?? false)
        ? app(\App\Repositories\StoreRepository::class)->getRelatedProducts($widget['settings']['product_ids'])
        : collect();

    $store_service = app(\App\Services\StoreService::class);
    $confirm_delivery_method = is_null(
        app(\App\Services\StoreService::class)
            ->deliveryMethod(request()->cookie('shopping_delivery_method_id'))
    );
@endphp

<section @if(!empty($widget['settings']['html_id'] ?? '')) id="{{ $widget['settings']['html_id'] }}" @endif class="tw-w-full" style="background-color: {{ $widget['settings']['background']['color'] ?? '#ffffff' }};">
    <div class="tw-mx-auto tw-w-full tw-px-4 {{ $max_width }} sm:tw-px-6 lg:tw-px-8 {{ $padding_top }} {{ $padding_bottom }}">
        <div class="tw-mx-auto tw-my-8 tw-grid tw-grid-cols-1 tw-gap-y-12 @if($products->count() === 1) tw-max-w-sm md:tw-grid-cols-1 md:tw-gap-x-8 lg:tw-grid-cols-1 xl:tw-gap-x-10 @elseif($products->count() === 2) tw-max-w-3xl md:tw-grid-cols-2 md:tw-gap-x-8 lg:tw-grid-cols-2 xl:tw-gap-x-10 @else md:tw-grid-cols-2 md:tw-gap-x-8 lg:tw-grid-cols-3 xl:tw-gap-x-10 @endif">
            @foreach($products as $index => $product)
                <x-theme::product-card
                        :product="$product"
                        :order="$order"
                        :cta_label="$widget['settings']['cta']['label'] ?? null"
                        :cta_action="$widget['settings']['cta']['action'] ?? null"
                        :list_details="[
                            'item_category' => null,
                            'item_category2' => null,
                            'item_list_id' => 'page_'. $page_id.'_widget_' . $widget['id'],
                            'item_list_name' => 'Page Product Widget'
                        ]"
                        :index="$index"
                        :confirm_delivery_method="$confirm_delivery_method"
                />
            @endforeach
        </div>
    </div>

</section>

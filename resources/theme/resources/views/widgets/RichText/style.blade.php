.textWidget--{{ $widget->id }} {
    --tw-prose-headings: {{ $widget->getColor('text_color', 'var(--text_color)') }};
    --tw-prose-p: {{ $widget->getColor('text_color', 'var(--text_color)') }};
    --tw-prose-a: {{ $widget->getColor('link_color', 'var(--link_color)') }};
    --tw-prose-body: {{ $widget->setting('text_color', '#777') }};
    --tw-prose-bullets: {{ $widget->setting('text_color', '#777') }};
    max-width: {{ $widget->setting('width', 1366) }}px;
    padding-top: {{ $widget->setting('paddingTop', 60) }}px; 
    padding-bottom: {{ $widget->setting('paddingBottom', 60) }}px; 
    line-height: {{ $widget->setting('line_height', '1.75') }}; 
    background-color: {{ $widget->getColor('background', 'var(--background_color)') }};
    color: {{ $widget->getColor('text_color', 'var(--text_color)') }};
}

.textWidget--{{ $widget->id }} a {
    color: {{ $widget->getColor('link_color', 'var(--link_color)') }};
    text-decoration: none;
}

.textWidget--{{ $widget->id }} a:hover {
    color: {{ $widget->getColor('link_color', 'var(--link_color)') }};
    text-decoration: underline;
}

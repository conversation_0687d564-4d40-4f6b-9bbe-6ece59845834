@php
    /** @var App\Models\Widget $widget */
    /** @var App\Models\Pickup|null $delivery_method */
    /** @var int|null $pricing_group_id */

    $products = $themeService->getFeaturedProducts($widget, $delivery_method?->id, $price_group_id);
@endphp
<div class="tw-mx-auto tw-my-8 tw-grid tw-grid-cols-1 tw-gap-y-12 @if($products->count() === 1) tw-max-w-sm tw-mx-auto md:tw-grid-cols-1 md:tw-gap-x-8 lg:tw-grid-cols-1 xl:tw-gap-x-10 @elseif($products->count() === 2) tw-max-w-3xl tw-mx-auto md:tw-grid-cols-2 md:tw-gap-x-8 lg:tw-grid-cols-2 xl:tw-gap-x-10 @else md:tw-grid-cols-2 md:tw-gap-x-8 lg:tw-grid-cols-3 xl:tw-gap-x-10 @endif featuredProductsWidget__products">
    @foreach($products as $product)
        @php
            /** @var \App\Models\Product $product */
        @endphp
        <section
                itemscope itemtype="https://schema.org/Product"
                itemid="{{ url('/store/product/'.$product->slug) }}"
                id="product_{{ $product->slug }}"
                class="tw-flex tw-flex-col tw-w-full tw-bg-white tw-rounded-lg tw-shadow-xl"
        >
            <div class="tw-w-full tw-relative">
                <div class="tw-relative tw-group tw-w-full">
                    <a href="/store/product/{{ $product->slug }}"
                       title="{{ $product->title }}"
                       class="tw-relative productListing__photoLink--grid"
                    >
                        @if($product->calloutMessage())
                            <div class="tw-absolute tw--rotate-3 tw-z-10 tw-top-0 tw-left-0 tw--mt-1 tw--ml-2 tw-border tw-rounded-md {{ $callout_border_class }} tw-bg-white tw-shadow-lg productListing__calloutTag @if($product->isOnSale()) productListing__saleCalloutTag @else productListing__standardCalloutTag @endif">
                                <div class="tw-pl-3 tw-pr-4 tw-py-1 tw-bg-white tw-rounded-md ">
                                    <div class="tw-flex tw-items-center">
                                        <p class="tw-m-0 tw-whitespace-nowrap tw-text-xs {{ $text_class }}">{{ $product->calloutMessage() }}</p>
                                    </div>
                                </div>
                            </div>
                        @endif

                        <div class="tw-aspect-h-3 tw-aspect-w-4 tw-w-full tw-group">
                            @if($product->mainPhoto)
                                <img src="{{ $product->mainPhoto->path }}" alt="{{ $product->title }}" itemprop="image"
                                     class="tw-transition-opacity group-hover:tw-opacity-75 tw-rounded-t-lg tw-h-full tw-w-full tw-object-cover tw-object-center">
                            @else
                                <div class="tw-h-full tw-w-full">
                                    <img src="{{ \App\Models\Media::s3ToCloudfront(theme('logo_src')) }}"
                                         alt="{{ $product->title }}"
                                         class="tw-absolute tw-top-1/2 tw-left-1/2 tw-transform tw--translate-x-1/2 tw--translate-y-1/2 tw-w-1/2 ">
                                    <div class="tw-h-full tw-w-full tw-object-cover tw-object-center tw-rounded-t-lg tw-bg-gray-900 tw-opacity-10 group-hover:tw-opacity-30"></div>
                                </div>
                            @endif
                        </div>

                        @if($widget->setting('action_event', 'add_to_cart') === 'add_to_cart')
                            <div class="tw-hidden tw-absolute tw-bottom-0 tw-w-full tw-p-4 lg:tw-block ">
                                <button type="button"
                                        class="tw-relative tw-z-10 tw-w-full tw-rounded-md tw-bg-white tw-transition-opacity tw-bg-opacity-75 tw-px-4 tw-py-2 tw-text-sm tw-text-gray-900 tw-opacity-0 focus:tw-opacity-100 group-hover:tw-opacity-100">
                                    View Details
                                    <span class="tw-sr-only">, {{ $product->title }}</span>
                                </button>
                            </div>
                        @endif
                    </a>
                </div>
            </div>

            <div class="tw-relative tw-flex-1 tw-mt-4 tw-px-4">
                @if($product->isOnSale())
                    @php($show_unit_pricing = $product->isPricedByWeight() && setting('show_price_per_pound', true))

                    <div class="tw-mb-1 tw--mt-2">
                        @if ($product->isGiftCard())
                            <p class="tw-m-0 tw-inline-block tw-text-xs {{ $text_class }} productListing__saleSavings- productListing__saleSavings--grid">
                                &#36;{{ money($product->getSavings()) }} bonus cash
                            </p>
                        @else
                            <p class="tw-m-0 tw-leading-4 tw-inline-block tw-text-xs {{ $text_class }} productListing__saleSavings- productListing__saleSavings--grid">
                                @if($product->isBundle())
                                    Bundle Savings
                                @else
                                    Save
                                @endif
                                @if($show_unit_pricing)
                                    &#36;{{ money($product->getUnitSavings()) }}
                                    /{{ __("messages.uom." . setting('weight_uom', 'pounds')) }}.
                                @else
                                    &#36;{{ money($product->getSavings()) }}
                                @endif
                            </p>
                        @endif
                    </div>
                @endif

                <a href="/store/product/{{ $product->slug }}"
                   title="{{ $product->title }}"
                   class="tw-relative productListing__photoLink--grid"
                >
                    <p class="tw-m-0 tw-leading-6 tw-font-medium tw-text-gray-700 hover:tw-text-gray-700"
                       itemprop="name">
                        {{ $product->title }}
                    </p>
                </a>
                <div itemprop="description">
                    @if ( ! empty($product->unit_description))
                        <p class="tw-m-0 tw-mt-1 tw-leading-4 tw-text-sm tw-text-gray-500">{!! $product->unit_description !!}</p>
                    @endif
                    <meta itemprop="sku" content="{{ $product->sku }}" />
                </div>

                @if ( ! empty($product->vendor_id))
                    <div itemprop="brand" itemscope itemtype="http://schema.org/Brand"
                         class="tw-mb-2 tw-mt-1 tw-text-sm tw-text-theme-link-color productPage__vendorLink--grid">
                        {!! $product->present()->vendorLink() !!}
                    </div>
                @endif

                @if($widget->setting('action_event', 'add_to_cart') === 'add_to_cart')
                    <a href="/store/product/{{ $product->slug }}"
                       title="{{ $product->title }}"
                       class="tw-mt-4 tw-block tw-text-sm tw-text-gray-700 lg:tw-hidden"
                    >
                        <span class="tw-m-0 tw-text-sm tw-font-medium ">View Details
                            <span aria-hidden="true">→</span>
                        </span>
                    </a>
                @endif
            </div>

            <div class="tw-flex tw-rounded-lg">
                <div class="tw-w-full tw-flex tw-flex-col">
                    <div class="tw-flex-1">
                        <div class="tw-flex tw-flex-col productListing__priceContainer--grid"
                             itemprop="offers"
                             itemscope
                             itemtype="https://schema.org/Offer"
                        >
                            <!-- Start Price Section -->
                            <div class="tw-px-4 tw-mt-4 tw-pb-2  productListing__price--grid">
                                @if(auth()->guest() && $widget->setting('hide_price'))
                                    <div class="tw-px-4 tw--ml-1">
                                        @include('theme::store._partials.price-gate')
                                    </div>
                                @else
                                    @include('theme::widgets.FeaturedProducts.price')
                                @endif
                            </div>
                            <!-- End Price Section -->
                        </div>
                    </div>
                </div>
            </div>
        </section>
    @endforeach
</div>

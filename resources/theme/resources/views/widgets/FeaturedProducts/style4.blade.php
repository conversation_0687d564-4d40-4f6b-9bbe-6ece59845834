@php
    /** @var App\Models\Widget $widget */
    /** @var App\Models\Pickup|null $delivery_method */
    /** @var int|null $pricing_group_id */

    $product = null;
    $comparable_products = $themeService->getFeaturedProducts($widget, $delivery_method?->id, $price_group_id);

    if($comparable_products->isNotEmpty()) {
        $product = $comparable_products[0];
    }
@endphp

@if(!is_null($product))
    <div class="tw-mx-auto tw-my-8 tw-grid tw-grid-cols-1 tw-gap-y-12 tw-max-w-sm tw-mx-auto md:tw-grid-cols-1 md:tw-gap-x-8 lg:tw-grid-cols-1 xl:tw-gap-x-10 featuredProductsWidget__products">
        <section
                itemscope itemtype="https://schema.org/Product"
                itemid="{{ url('/store/product/'.$product->slug) }}"
                id="product_{{ $product->slug }}"
                class="tw-flex tw-flex-col tw-w-full tw-bg-white tw-rounded-lg tw-shadow-xl"
        >
            <div class="tw-rounded-lg featuredProductsWidget__productContainer">
                <div class="tw-w-full tw-relative">
                    <div class="tw-relative tw-w-full tw-rounded-t-lg">
                        <a href="/store/product/{{ $product->slug }}"
                           title="{{ $product->title }}"
                           class="tw-relative productListing__photoLink--grid"
                        >
                            <div class="tw-aspect-h-3 tw-aspect-w-4 tw-w-full tw-group">
                                @if($product->mainPhoto)
                                    <img src="{{ $product->mainPhoto->path }}" alt="{{ $product->title }}"
                                         itemprop="image"
                                         class="tw-transition-opacity group-hover:tw-opacity-75 tw-rounded-t-lg tw-h-full tw-w-full tw-object-cover tw-object-center featuredProductsWidget__productPhoto">
                                @else
                                    <div class="tw-h-full tw-w-full">
                                        <img src="{{ \App\Models\Media::s3ToCloudfront(theme('logo_src')) }}"
                                             alt="{{ $product->title }}"
                                             class="tw-absolute tw-top-1/2 tw-left-1/2 tw-transform tw--translate-x-1/2 tw--translate-y-1/2 tw-w-1/2 ">
                                        <div class="tw-h-full tw-w-full tw-object-cover tw-object-center tw-rounded-t-lg tw-bg-gray-900 tw-opacity-10 group-hover:tw-opacity-30"></div>
                                    </div>
                                @endif
                            </div>
                        </a>
                    </div>
                </div>

                @if(auth()->check() && $comparable_products->isNotEmpty() && $widget->setting('action_event', 'show_details') === 'add_to_cart')
                    <livewire:theme.add-product-by-comparison-button
                            :product="$product"
                            :comparable_products="$comparable_products"
                            :cart_label="$widget->setting('cta_text', $product->cartActionLabel())"
                            :has_subscription="$has_subscription"
                            :has_order=" ! is_null($openOrder)"
                            :cta_classes="['tw-px-3 tw-py-2 tw-rounded-t-none tw-rounded-b-lg tw-text-sm tw-font-semibold']"
                            :confirm_delivery_method="is_null($delivery_method)"
                    />
                @endif
            </div>
        </section>
    </div>
@endif

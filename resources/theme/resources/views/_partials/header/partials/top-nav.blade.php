@php
    $logo_src = theme('logo_src');
    $mobile_logo_src = theme('mobile_logo_src');
@endphp
<header class="tw-bg-theme-header-bg-color">
    <div class="tw-mx-auto tw-max-w-7xl tw-px-2 sm:tw-px-4 lg:tw-divide-y lg:tw-divide-gray-700 lg:tw-px-8">
        <div class="tw-relative tw-flex tw-h-14 tw-justify-between">
            <div class="tw-relative tw-flex-1 tw-z-10 tw-flex tw-px-2 lg:tw-px-0 md:tw-flex-none ">
                <div x-data class="tw-flex tw-shrink-0 tw-items-center">
                    <button x-on:click="$dispatch('open-flyout-menu')" type="button"
                            class="tw-relative tw-rounded-md tw-bg-transparent tw-text-theme-auxiliary-link-color tw-mr-3 lg:tw-hidden">
                        <span class="tw-absolute tw--inset-0.5"></span>
                        <span class="tw-sr-only">Open menu</span>
                        <div class="tw-flex tw-items-center tw-space-x-2">
                            <svg class="tw-h-6 tw-w-6 " fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                 stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round"
                                      d="M3.75 6.75h16.5M3.75 12h16.5m-16.5 5.25h16.5"></path>
                            </svg>
                        </div>
                    </button>
                    <a href="{{ theme('logo_url', '/') }}" class="tw-hidden md:tw-block">
                        <span class="tw-sr-only">{{ setting('farm_name') }}</span>
                        <img src="{{ $logo_src }}" class="tw-h-8 tw-w-auto" alt="{{ setting('farm_name') }} Logo">
                    </a>
                </div>
            </div>
            <div class="tw-relative tw-z-10 tw-flex tw-mx-auto tw-flex-1 tw-items-center tw-justify-center tw-px-2">
                <div class="tw-w-full sm:tw-max-w-sm">
                    <button class="tw-hidden tw-w-full tw-bg-theme-order-status-bg-color tw-rounded-lg tw-cursor-text tw-border-0 tw-py-2 tw-px-3 tw-text-white tw-shadow-inner tw-ring-1 tw-ring-inset tw-ring-theme-brand-color focus:tw-ring-2 focus:tw-ring-inset focus:tw-ring-theme-action-color sm:tw-text-sm sm:tw-leading-6 md:tw-block" x-on:click="$dispatch('product-search-toggled')">
                        <span class="tw-flex tw-items-center tw-overflow-hidden tw-space-x-2">
                            <svg class="tw-h-5 tw-w-5 tw-text-theme-gray-700" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                <path fill-rule="evenodd"
                                      d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
                                      clip-rule="evenodd"
                                ></path>
                            </svg>
                            <span class="tw-inline-block tw-whitespace-nowrap">What can we help you find?</span>
                        </span>
                    </button>
                    <div class="tw-flex tw-justify-center md:tw-hidden">
                        <a href="{{ theme('logo_url', '/') }}" class="tw-inline-block">
                            <span class="tw-sr-only">{{ setting('farm_name') }}</span>
                            <img src="{{ $logo_src }}" class="tw-h-8 tw-w-auto" alt="{{ setting('farm_name') }} Logo">
                        </a>
                    </div>
                </div>
            </div>
            <div class="tw-relative tw-flex-1 tw-z-10 tw-max-w-sm tw-space-x-0.5 tw-ml-4 tw-flex tw-justify-end tw-items-center md:tw-flex-none">

                <!-- Profile dropdown -->
                <div class="tw-relative tw-shrink-0">
                    <div x-data="{ isOpen: false }" class="tw-relative" x-on:click.away="isOpen = false">
                        <!-- Account menu toggle -->
                        <button type="button" x-on:click="isOpen = !isOpen"
                                class="tw-inline-flex tw-items-center tw-p-2 tw-text-white tw-rounded-lg hover:tw-text-gray-100 hover:tw-text-theme-main-navigation-link-color-hover hover:tw-bg-theme-order-status-bg-color/60"
                                :aria-expanded="isOpen"
                        >
                            <span class="tw-absolute tw--inset-0.5"></span>
                            <span class="tw-sr-only">Open account menu</span>
                            <div class="tw-flex tw-items-center tw-space-x-2 tw-text-sm">
                                <svg class="tw-h-6 tw-w-6" xmlns="http://www.w3.org/2000/svg" fill="none"
                                     viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                    <path stroke-linecap="round" stroke-linejoin="round"
                                          d="M17.982 18.725A7.488 7.488 0 0012 15.75a7.488 7.488 0 00-5.982 2.975m11.963 0a9 9 0 10-11.963 0m11.963 0A8.966 8.966 0 0112 21a8.966 8.966 0 01-5.982-2.275M15 9.75a3 3 0 11-6 0 3 3 0 016 0z"/>
                                </svg>
                            </div>
                        </button>

                        <!-- Account menu popover -->
                        <div
                                x-show="isOpen"
                                x-cloak
                                x-transition:enter=""
                                x-transition:enter-start=""
                                x-transition:enter-end=""
                                x-transition:leave="tw-transition-opacity tw-ease-in tw-duration-100"
                                x-transition:leave-start="tw-opacity-100"
                                x-transition:leave-end="tw-opacity-0"
                                class="tw-absolute tw-right-0 tw-z-10 tw-mt-2 tw-w-64 tw-origin-top-right tw-divide-y tw-divide-gray-200 tw-overflow-hidden tw-rounded-md tw-bg-white tw-shadow-lg tw-ring-1 tw-ring-black tw-ring-opacity-5 focus:tw-outline-none"
                                tabindex="-1"
                        >
                            @if(auth()->check())
                                <div>
                                    <div class="tw-border-t-4 tw-border-theme-action-color">
                                        <div class="tw-py-3 tw-px-4 tw-text-sm tw-bg-gray-100 tw-border-b tw-border-200">
                                            <div class="tw-font-medium tw-text-gray-900">{{ auth()->user()->full_name }}</div>
                                            <div class="tw-text-xs tw-text-gray-500">{{ auth()->user()->email }}</div>
                                        </div>
                                    </div>

                                    <div class="tw-border-b tw-border-gray-200">
                                        <a href="{{ url('/account') }}"
                                           class="tw-no-underline tw-py-2 tw-px-4 tw-text-gray-500 tw-text-sm tw-block hover:tw-bg-gray-100 ">{{ __('messages.edit_profile') }}</a>
                                        @if($has_subscription)
                                            <a href="{{ route('customers.recurring.edit') }}"
                                               class="tw-no-underline tw-py-2 tw-px-4 tw-text-gray-500 tw-text-sm tw-block hover:tw-bg-gray-100">Manage Subscription</a>
                                        @elseif($has_inactive_subscription)
                                            <a href="{{ route('customers.recurring.edit') }}"
                                               class="tw-no-underline tw-py-2 tw-px-4 tw-text-gray-500 tw-text-sm tw-block hover:tw-bg-gray-100">Resume Subscription</a>
                                        @else
                                            <a href="{{ url('subscribe-save') }}"
                                               class="tw-no-underline tw-w-full tw-py-2 tw-px-4 tw-inline-flex tw-items-center tw-justify-between tw-text-gray-500 tw-text-sm tw-block hover:tw-bg-gray-100">
                                                <div>
                                                    Subscribe & Save
                                                </div>
                                                <div>
                                                    <span class="tw-inline-flex tw-items-center tw-rounded-md tw-bg-theme-action-color/10 tw-px-2 tw-py-1 tw-text-xs tw-font-medium tw-text-theme-action-color tw-ring-1 tw-ring-inset tw-ring-theme-action-color/30">Learn more</span>
                                                </div>
                                            </a>
                                        @endif
                                        <a href="{{ url('/account/gift-card') }}"
                                           class="tw-no-underline tw-py-2 tw-px-4 tw-text-gray-500 tw-text-sm tw-block hover:tw-bg-gray-100 ">{{ __('messages.redeem_gift_card') }}</a>
                                        <a href="{{ route('customer.orders') }}"
                                           class="tw-no-underline tw-py-2 tw-px-4 tw-text-gray-500 tw-text-sm tw-block hover:tw-bg-gray-100 ">{{ __('messages.order_history') }}</a>
                                        <a href="{{ url('/account/referrals') }}"
                                           class="tw-no-underline tw-py-2 tw-px-4 tw-text-gray-500 tw-text-sm tw-block hover:tw-bg-gray-100 ">{{ __('messages.referrals') }}</a>
                                        <a href="{{ url('customer-support') }}"
                                           class="tw-no-underline tw-py-2 tw-px-4 tw-text-gray-500 tw-text-sm tw-block hover:tw-bg-gray-100 ">Help</a>
                                    </div>
                                    <div>
                                        <form method="POST" action="{{ route('logout') }}">
                                            @csrf
                                            <button type="submit"
                                                    class="tw-w-full tw-text-left tw-no-underline tw-py-4 tw-px-4 tw-text-gray-500 tw-text-sm tw-block hover:tw-bg-gray-100">{{ __('messages.sign_out') }}</button>
                                        </form>
                                    </div>
                                </div>
                            @else
                                <div class="tw-py-4 tw-px-3">
                                    <a href="{{ route('login') }}"
                                       class="tw-no-underline tw-flex tw-w-full tw-justify-center tw-rounded-md tw-bg-theme-action-color tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-leading-6 tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color/70 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-action-color">Sign
                                        in</a>

                                    <p class="tw-m-0 tw-mt-4 tw-text-center tw-text-sm tw-text-gray-500">
                                        Not a customer?
                                        <a href="{{ route('register') }}"
                                           class="tw-block tw-font-semibold tw-leading-6 tw-text-theme-action-color hover:tw-text-theme-action-color/70">Create
                                            an account</a>
                                    </p>
                                </div>
                            @endif

                        </div>
                    </div>


                </div>

                <!-- Cart -->
                @if($has_subscription)
                    <button x-on:click="$dispatch('openPanel', { title: 'Subscription', component: 'theme.subscription-side-panel' });"
                            class="tw-m-0 tw-p-2 tw-relative tw-group tw-cursor-pointer hover:tw-no-underline tw-flex tw-items-center tw-rounded-lg hover:tw-text-gray-100 hover:tw-text-theme-main-navigation-link-color-hover hover:tw-bg-theme-order-status-bg-color/60">
                        <svg class="tw-h-6 tw-w-6 tw-flex-shrink-0 tw-text-theme-auxiliary-link-color"
                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                             stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                  d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 00-16.536-1.84M7.5 14.25L5.106 5.272M6 20.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm12.75 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z"/>
                        </svg>

                        <span class="tw-absolute tw-top-0 tw-right-0 tw-mt-0.5 tw-mr-0.5 tw-rounded-full tw-h-4 tw-w-4 tw-flex tw-items-center tw-justify-center tw-text-[10px] tw-font-semibold tw-bg-theme-auxiliary-link-color tw-text-theme-header-bg-color">
                            <livewire:theme.subscription-item-count/>
                        </span>
                        <span class="tw-sr-only">items in subscription, view subscription</span>
                    </button>
                @elseif( ! is_null($order))
                    <button x-on:click="$dispatch('openPanel', { title: 'Order', component: 'theme.order-side-panel' });"
                            class="tw-m-0 tw-p-2 tw-relative tw-group tw-cursor-pointer hover:tw-no-underline tw-flex tw-items-center tw-rounded-lg hover:tw-text-gray-100 hover:tw-text-theme-main-navigation-link-color-hover hover:tw-bg-theme-order-status-bg-color/60">

                        <svg class="tw-h-6 tw-w-6 tw-flex-shrink-0 tw-text-theme-auxiliary-link-color"
                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                             stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                  d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 00-16.536-1.84M7.5 14.25L5.106 5.272M6 20.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm12.75 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z"/>
                        </svg>

                        <span class="tw-absolute tw-top-0 tw-right-0 tw-mt-0.5 tw-mr-0.5 tw-rounded-full tw-h-4 tw-w-4 tw-flex tw-items-center tw-justify-center tw-text-[10px] tw-font-semibold tw-bg-theme-auxiliary-link-color tw-text-theme-header-bg-color">
                            <livewire:theme.order-item-count/>
                        </span>
                        <span class="tw-sr-only">items in order, view order</span>
                    </button>
                @else
                    <button
                            x-on:click="$dispatch('openPanel', { title: 'Shopping cart', component: 'theme.cart-side-panel' });"
                            class="tw-m-0 tw-p-2 tw-relative tw-group tw-cursor-pointer hover:tw-no-underline tw-flex tw-items-center tw-rounded-lg hover:tw-text-gray-100 hover:tw-text-theme-main-navigation-link-color-hover hover:tw-bg-theme-order-status-bg-color/60">
                        <svg class="tw-h-6 tw-w-6 tw-flex-shrink-0 tw-text-theme-auxiliary-link-color"
                             xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                             stroke-width="1.5" stroke="currentColor">
                            <path stroke-linecap="round" stroke-linejoin="round"
                                  d="M2.25 3h1.386c.51 0 .955.343 1.087.835l.383 1.437M7.5 14.25a3 3 0 00-3 3h15.75m-12.75-3h11.218c1.121-2.3 2.1-4.684 2.924-7.138a60.114 60.114 0 00-16.536-1.84M7.5 14.25L5.106 5.272M6 20.25a.75.75 0 11-1.5 0 .75.75 0 011.5 0zm12.75 0a.75.75 0 11-1.5 0 .75.75 0 011.5 0z"/>
                        </svg>

                        @if(auth()->check())
                            <span class="tw-absolute tw-top-0 tw-right-0 tw-mt-0.5 tw-mr-0.5 tw-rounded-full tw-h-4 tw-w-4 tw-flex tw-items-center tw-justify-center tw-text-[10px] tw-font-semibold tw-bg-theme-auxiliary-link-color tw-text-theme-header-bg-color">
                                <livewire:theme.cart-item-count/>
                            </span>
                        @endif
                        <span class="tw-sr-only">items in cart, view cart</span>
                    </button>
                @endif
            </div>
        </div>
    </div>

    <!-- Mobile menu, show/hide based on menu state. -->
    <div class="md:tw-hidden">
        <div class="tw-mx-auto tw-max-w-7xl tw-pb-2 tw-px-2 sm:tw-px-4">
            <button class="tw-w-full tw-text-sm tw-bg-theme-order-status-bg-color tw-rounded-lg tw-cursor-text tw-border-0 tw-py-2 tw-px-3 tw-text-white tw-shadow-inner tw-ring-1 tw-ring-inset tw-ring-theme-brand-color focus:tw-ring-2 focus:tw-ring-inset focus:tw-ring-theme-action-color sm:tw-text-sm sm:tw-leading-6" x-on:click="$dispatch('product-search-toggled')">
                <span class="tw-flex tw-items-center tw-overflow-hidden tw-space-x-2">
                    <svg class="tw-h-5 tw-w-5 tw-text-gray-200" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                        <path fill-rule="evenodd"
                              d="M9 3.5a5.5 5.5 0 100 11 5.5 5.5 0 000-11zM2 9a7 7 0 1112.452 4.391l3.328 3.329a.75.75 0 11-1.06 1.06l-3.329-3.328A7 7 0 012 9z"
                              clip-rule="evenodd"
                        ></path>
                    </svg>
                    <span class="tw-inline-block tw-whitespace-nowrap">What can we help you find?</span>
                </span>
            </button>
        </div>
    </div>
</header>

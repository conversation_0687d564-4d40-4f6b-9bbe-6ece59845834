<div x-data="{ show: false }" x-on:open-flyout-menu.window="show = true" x-cloak class="tw-relative tw-z-50 tw-pointer-events-none" aria-labelledby="slide-over-title" role="dialog"
     aria-modal="true">
    <!-- Background backdrop -->
    <div
            x-show="show"
            x-cloak
            x-transition:enter="tw-transition-opacity tw-ease-in-out tw-duration-300"
            x-transition:enter-start="tw-opacity-0"
            x-transition:enter-end="tw-opacity-100"
            x-transition:leave="tw-transition-opacity tw-ease-in-out tw-duration-300"
            x-transition:leave-start="tw-opacity-100"
            x-transition:leave-end="tw-opacity-0"
            class="tw-fixed tw-inset-0 tw-bg-gray-700 tw-bg-opacity-75 tw-transition-opacity tw-pointer-events-none"
            :class="{ 'tw-pointer-events-none': ! show, 'tw-pointer-events-auto': show }"
    ></div>

    <div class="tw-pointer-events-none tw-fixed tw-inset-0 tw-overflow-hidden">
        <div class="tw-pointer-events-none tw-absolute tw-inset-0 tw-overflow-hidden">
            <div class="tw-pointer-events-none tw-fixed tw-inset-y-0 tw-left-0 tw-flex tw-max-w-full tw-pr-10">
                <!-- Slide-over panel -->
                <div
                        x-show="show"
                        x-cloak
                        x-transition:enter="tw-transition tw-transform tw-ease-in-out tw-duration-300"
                        x-transition:enter-start="tw--translate-x-full"
                        x-transition:enter-end="tw-translate-x-0"
                        x-transition:leave="tw-transition  tw-transform tw-ease-in-out tw-duration-300"
                        x-transition:leave-start="tw-translate-x-0"
                        x-transition:leave-end="tw--translate-x-full"
                        class="tw-pointer-events-auto tw-relative tw-w-screen tw-max-w-xs"
                        @click.away="show = false"
                >
                    <!-- Close button -->
                    <div
                            x-show="show"
                            x-cloak
                            x-transition:enter="tw-ease-in-out tw-duration-300"
                            x-transition:enter-start="tw-opacity-0"
                            x-transition:enter-end="tw-opacity-100"
                            x-transition:leave="tw-ease-in-out tw-duration-300"
                            x-transition:leave-start="tw-opacity-100"
                            x-transition:leave-end="tw-opacity-0"
                            class="tw-absolute tw-right-0 tw-top-0 tw--mr-8 tw-flex tw-pl-2 tw-pt-4 sm:tw--mr-10 sm:tw-pl-4"
                    >
                        <button type="button" @click="show = false"
                                class="tw-relative tw-rounded-md tw-text-gray-300 hover:tw-text-white focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-white">
                            <span class="tw-absolute tw--inset-2.5"></span>
                            <span class="tw-sr-only">Close panel</span>
                            <svg class="tw-h-6 tw-w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5"
                                 stroke="currentColor" aria-hidden="true">
                                <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                            </svg>
                        </button>
                    </div>

                    <!-- Panel content -->
                    <div class="tw-flex tw-h-full tw-flex-col tw-overflow-y-auto tw-pb-16 tw-bg-white tw-shadow-xl">
                        <h2 class="tw-sr-only" id="slide-over-title">Store menu</h2>

                        <!-- Panel header -->
                        <div class="tw-bg-theme-header-bg-color tw-py-3 tw-px-4 sm:tw-px-6">
                            <div class="tw-flex tw-items-center">
                                @if(theme('logo_src', false))
                                    <a href="{{ theme('logo_url', '/') }}">
                                        <span class="tw-sr-only">{{ setting('farm_name') }}</span>
                                        <img src="{{ \App\Models\Media::s3ToCloudfront(theme('logo_src')) }}"
                                             class="tw-h-8 tw-w-auto"
                                             alt="{{ setting('farm_name') }} Logo">
                                    </a>
                                @else
                                    <a href="/"
                                       class="tw-no-underline tw-text-theme-link-color">{{ setting('farm_name') }}</a>
                                @endif
                            </div>
                        </div>

                        <!-- Desktop Store menu -->
                        <div
                                class="tw-hidden tw-pt-3 tw-px-4 sm:tw-px-6 lg:tw-block">
                            <div>
                                <div class="tw-flex tw-items-center tw-justify-between tw-py-3 tw-px-4 tw-rounded-md tw-w-full">
                                    <p class="tw-m-0  tw-text-base tw-font-semibold tw-text-gray-900">Store</p>
                                    <a href="{{ route('store.index') }}"
                                       class=" tw-text-sm tw-text-gray-700 tw-underline hover:tw-text-gray-500">
                                        Shop All
                                    </a>
                                </div>

                                @foreach($store_menu->items as $index => $menu_item)
                                    @include('theme::_partials.header.partials.store-menu-item', compact('menu_item'))
                                @endforeach

                            </div>
                        </div>

                        <!-- Mobile store menu -->
                        <div x-data="{ storeMenuIsExpanded: false }"
                             class="tw-border-b tw-border-gray-200 tw-pt-3 tw-px-4 sm:tw-px-6 lg:tw-hidden">
                            <div class="tw-flex tw-items-center tw-justify-between tw-py-3 tw-px-4 tw-rounded-md tw-w-full">
                                <p class="tw-m-0  tw-text-base tw-font-semibold tw-text-gray-900">Store</p>
                                <a href="{{ route('store.index') }}"
                                   class=" tw-text-sm tw-text-gray-700 tw-underline hover:tw-text-gray-500">
                                    Shop All
                                </a>
                            </div>
                            <div class="tw-relative tw-pb-12">
                                @foreach($store_menu->items as $index => $menu_item)
                                    <div @if($index > 5) x-show="storeMenuIsExpanded" x-collapse @endif>
                                        @include('theme::_partials.header.partials.store-menu-item', compact('menu_item'))
                                    </div>
                                @endforeach
                                @if($store_menu->items->count() > 6)
                                    <div class="tw-absolute tw-bottom-0 tw-left-0 tw-w-full">
                                        <button @click="storeMenuIsExpanded = !storeMenuIsExpanded" type="button"
                                                class="tw-group tw-flex tw-text-sm tw-items-center tw-w-full tw-text-left tw-rounded-md tw-py-3 tw-px-4 tw-gap-x-3"
                                                aria-controls="sub-menu-{{ $menu_item->id }}"
                                                :aria-expanded="storeMenuIsExpanded">
                                            <span class="tw-text-sm tw-font-semibold tw-leading-6 tw-text-theme-link-color group-hover:tw-text-theme-link-color/70">
                                                See
                                                <span
                                                        x-text="storeMenuIsExpanded ? 'less' : 'more'"></span>
                                            </span>
                                            <svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 16 16"
                                                 fill="currentColor"
                                                 class="tw-text-theme-link-color/70 tw-h-4 tw-w-4 tw-shrink-0 tw-transition-transform tw-duration-75  group-hover:tw-text-theme-link-color/50"
                                                 :class="{ 'tw-rotate-180': storeMenuIsExpanded }">
                                                <path fill-rule="evenodd"
                                                      d="M4.22 6.22a.75.75 0 0 1 1.06 0L8 8.94l2.72-2.72a.75.75 0 1 1 1.06 1.06l-3.25 3.25a.75.75 0 0 1-1.06 0L4.22 7.28a.75.75 0 0 1 0-1.06Z"
                                                      clip-rule="evenodd"/>
                                            </svg>

                                        </button>
                                    </div>
                                @endif
                            </div>
                        </div>

                        <!-- Main menu -->
                        <div class="tw-pt-3 tw-px-4 sm:tw-px-6 lg:tw-hidden">
                            @foreach($main_menu->items as $main_menu_item)
                                @php
                                    /** @var \App\Models\MenuItem $main_menu_item */
                                @endphp
                                <div x-data="{ isExpanded: false }">
                                    @if($main_menu_item->isSubmenu())
                                        <button @click="isExpanded = !isExpanded" type="button"
                                                class="tw-flex tw-items-center tw-w-full tw-text-left tw-rounded-md tw-py-3 tw-px-4 tw-gap-x-3 hover:tw-bg-gray-100 sm:tw-text-sm"
                                                aria-controls="sub-menu-{{ $main_menu_item->id }}"
                                                :aria-expanded="isExpanded">
                                            <span class="tw-flex-1">{{ $main_menu_item->getLabel() }}</span>
                                            <svg class="tw-text-gray-400 tw-h-5 tw-w-5 tw-shrink-0 tw-transition-transform tw-duration-75"
                                                 :class="{ 'tw-rotate-90 tw-text-gray-500': isExpanded, 'tw-text-gray-400': isExpanded }"
                                                 viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                <path fill-rule="evenodd"
                                                      d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                                                      clip-rule="evenodd"></path>
                                            </svg>
                                        </button>
                                        <ul style="display: none;" x-show="isExpanded" class="tw-mt-1 tw-pl-4"
                                            id="sub-menu-{{ $main_menu_item->id }}">
                                            @foreach($main_menu_item->submenu->items as $sub_menu_item)
                                                @php
                                                    /** @var \App\Models\MenuItem $sub_menu_item */
                                                @endphp
                                                <li>
                                                    <a href="{{ $sub_menu_item->getUrl() }}"
                                                       class="tw-no-underline tw-text-gray-700 tw-block tw-rounded-md tw-py-3 tw-px-4 hover:tw-bg-gray-100 sm:tw-text-sm">{{ $sub_menu_item->getLabel() }}</a>
                                                </li>
                                            @endforeach
                                        </ul>
                                    @else
                                        <a href="{{ $main_menu_item->getUrl() }}"
                                           class="tw-no-underline tw-text-gray-700 tw-block tw-rounded-md tw-py-3 tw-px-4 hover:tw-bg-gray-100 sm:tw-text-sm">{{ $main_menu_item->getLabel() }}</a>
                                    @endif
                                </div>
                            @endforeach

                            <div x-data="{ isExpanded: false }">
                                <button @click="isExpanded = !isExpanded" type="button"
                                        class="tw-flex tw-items-center tw-w-full tw-text-left tw-rounded-md tw-py-3 tw-px-4 tw-gap-x-3 hover:tw-bg-gray-100 sm:tw-text-sm"
                                        aria-controls="sub-menu-help"
                                        :aria-expanded="isExpanded">
                                    <span class="tw-flex-1">Help</span>
                                    <svg class="tw-text-gray-400 tw-h-5 tw-w-5 tw-shrink-0 tw-transition-transform tw-duration-75"
                                         :class="{ 'tw-rotate-90 tw-text-gray-500': isExpanded, 'tw-text-gray-400': isExpanded }"
                                         viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd"
                                              d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z"
                                              clip-rule="evenodd"></path>
                                    </svg>
                                </button>
                                <ul style="display: none;" x-show="isExpanded" class="tw-mt-1 tw-pl-4"
                                    id="sub-menu-help">

                                    <li>
                                        <a href="{{ url('/customer-support') }}" class="tw-no-underline tw-text-gray-700 tw-block tw-rounded-md tw-py-3 tw-px-4 hover:tw-bg-gray-100 sm:tw-text-sm">
                                            Contact Us
                                        </a>
                                        <button type="button" id="accessibilityTriggerId" class="tw-text-gray-700 tw-block tw-w-full tw-text-left tw-rounded-md tw-py-3 tw-px-4 hover:tw-bg-gray-100 sm:tw-text-sm">
                                            Accessibility
                                        </button>
                                        <button type="button" x-on:click="window.openZendeskWidget(); open = false;" class="tw-text-gray-700 tw-block tw-w-full tw-text-left tw-rounded-md tw-py-3 tw-px-4 hover:tw-bg-gray-100 sm:tw-text-sm">
                                            Customer FAQs
                                        </button>
                                    </li>
                                </ul>

                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

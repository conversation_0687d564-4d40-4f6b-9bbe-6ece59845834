<form action="{{ $action ?? '/account/cards' }}" method="POST" id="payment-form">
    @isset($redirect)
        <input type="hidden" name="redirect" value="{{ $redirect }}"/>
    @endisset
    <input type="hidden" name="payment_id" value="1">
    @csrf

    <div class="inline-group" id="name_on_card_group">
        <div>
            <label class="control-label" for="name_on_card">Name on card: </label>
        </div>
        <div>
            <input
                    name="cardholder_name"
                    class="form-control required"
                    id="cardholder_name"
                    value="{{ auth()->user()->full_name }}"
                    tabindex="1"
            />
        </div>
    </div>

    <div class="inline-group">
        <div>
            <label for="card-element">
                Credit or debit card:
            </label>
        </div>
        <div id="card-element" class="form-control">
            <!-- a Stripe Element will be inserted here. -->
        </div>
    </div>

    <!-- Used to display form errors -->
    <div id="card-errors" role="alert"></div>

    @if(auth()->user()->hasCard())
        <div class="form-group text-right">
            <div class="checkbox">
                <label>
                    <input type="checkbox" name="make_default" value="1">
                    Make default
                </label>
            </div>
        </div>
    @else
        <input type="hidden" name="make_default" value="1">
    @endif

    <div class="text-right">
        <button type="submit" class="btn btn-action" id="payment-form-button" tabindex="1">Add card</button>
    </div>
</form>

:root {
--brand_color: {!! app('theme')->setting('brand_color', '#34B393') !!};
--brand_color_inverted: {!! app('theme')->setting('brand_color_inverted', '#FFFFFF') !!};
--background_color: {{ app('theme')->setting('body_bg', '#FFF') }};
--text_color: {{ app('theme')->setting('body_text_color', '#3d3d3d') }};
--link_color: {{ app('theme')->setting('link_color', 'var(--brand_color)') }};
}

body {
background-color: var(--background_color);
color: var(--text_color);
}

.h1, .h2, .h3, .h4, .h5, .h6, h1, h2, h3, h4, h5, h6, legend {
font-family: {!! app('theme')->setting('heading_font', 'Helvetica Neue, Helvetica, Arial, sans-serif') !!};
}

body, p, label, .paragraph {
font-family: {!! app('theme')->setting('paragraph_font', 'Helvetica Neue, Helvetica, Arial, sans-serif') !!};
font-size: {!! app('theme')->setting('paragraph_font_size', '16px') !!};
}

a {
color: var(--link_color);
}

a:hover {
text-decoration: underline;
}

.brand {
background-color: {!! app('theme')->setting('brand_color', '#34B393') !!};
color: {!! app('theme')->setting('brand_color_inverted', '#FFFFFF') !!};
border-color: {!! app('theme')->setting('brand_color', '#34B393') !!};
}

.text-brand {
color: {!! app('theme')->setting('brand_color', '#34B393') !!};
}

.text-action {
color: {!! app('theme')->setting('action_color', '#34B393') !!};
}

.brand-inverted {
background-color: {!! app('theme')->setting('brand_color_inverted', '#FFFFFF') !!};
color: {!! app('theme')->setting('brand_color', '#34B393') !!};
border-color: {!! app('theme')->setting('brand_color_inverted', '#FFFFFF') !!};
}

.action {
background-color: {!! app('theme')->setting('action_color', '#CF3D2E') !!};
color: {!! app('theme')->setting('action_color_inverted', '#FFFFFF') !!};
border-color: {!! app('theme')->setting('action_color', '#CF3D2E') !!};
}

.action-inverted {
background-color: {!! app('theme')->setting('action_color_inverted', '#FFFFFF') !!};
color: {!! app('theme')->setting('action_color', '#CF3D2E') !!};
border-color: {!! app('theme')->setting('action_color_inverted', '#FFFFFF') !!};
}

/* SiteHeader */

.announcement_bar {
background-color: {{ app('theme')->setting('announcement_bar_bg_color', '#FFF')  }};
color: {{ app('theme')->setting('announcement_bar_text_color', '#3d3d3d')  }};
}

.announcement_bar a {
color: {{ app('theme')->setting('announcement_bar_link_color', '#3d3d3d')  }};
}

.siteHeader {
background-color: {!! app('theme')->setting('header_bg_color', '#EEEEEE') !!};
color: {!! app('theme')->setting('main_navigation_link_color', '#CF3D2E') !!};
border-{!! app('theme')->setting('header_border_position', 'bottom') !!}-style: solid;
border-{!! app('theme')->setting('header_border_position', 'bottom') !!}-width: {!! app('theme')->setting('header_border_size', '0') !!};
border-{!! app('theme')->setting('header_border_position', 'bottom') !!}-color: {!! app('theme')->setting('header_border_color', 'transparent') !!};
}

.siteHeader__container {
max-width: {{ app('theme')->setting('header_width', '1366px') }};
}

/* MainNavigation */

.mainNavigation, .mobileNav {
background-color: {{ app('theme')->setting('main_navigation_bg', '#EEEEEE')  }};
}

.mainNavigation__list {
text-align: {{ app('theme')->setting('main_navigation_link_alignment', 'center')  }} !important;
}

.siteHeader__container--style4 {
text-align: {{ app('theme')->setting('main_navigation_link_alignment', 'center')  }};
}

.mainNavigation__list > li > a {
font-size: {{ app('theme')->setting('main_navigation_link_font_size', '14px')  }};
}

/*Mobile Nav*/
.mobileNav > li > a {
color: {{ app('theme')->setting('main_navigation_link_color', '#4d4d4d')  }};
background-color: {{ app('theme')->setting('main_navigation_link_bg_color', '#EEEEEE')  }};
}

.mobileNav > li > a:hover {
background-color: {{ adjustBrightness(app('theme')->setting('main_navigation_link_bg_color', '#EEEEEE'), -40)  }};
}

.mobileNav > li > a:focus {
background-color: {{ adjustBrightness(app('theme')->setting('main_navigation_link_bg_color', '#EEEEEE'), -40)  }};
}

@if(app('theme')->setting('main_navigation_link_hover_effect') == 'inverted')
    /* Inverted Link Effect */
    .mainNavigation__list > li > a {
    color: {{ app('theme')->setting('main_navigation_link_color', '#4d4d4d')  }};
    background-color: transparent;
    }
    .mainNavigation__list > li > a:hover {
    color: {{ app('theme')->setting('main_navigation_link_color_hover', '#4d4d4d')  }};
    background-color: {{ app('theme')->setting('main_navigation_link_color', '#EEEEEE')  }};
    }

    .mobileNav > li > a {
    color: {{ app('theme')->setting('main_navigation_link_color', '#4d4d4d')  }};
    background-color: transparent;
    }

    .mobileNav > li > a:hover {
    color: {{ app('theme')->setting('main_navigation_link_color_hover', '#4d4d4d')  }};
    background-color: {{ app('theme')->setting('main_navigation_link_color', '#EEEEEE')  }};
    }

    .mobileNav > li > a:focus {
    color: {{ app('theme')->setting('main_navigation_link_color_hover', '#4d4d4d')  }};
    background-color: {{ app('theme')->setting('main_navigation_link_color', '#EEEEEE')  }};
    }

@elseif(app('theme')->setting('main_navigation_link_hover_effect') == 'underline')
    /* Underline Link Effect */
    .mainNavigation__list > li > a {
    color: {{ app('theme')->setting('main_navigation_link_color', '#4d4d4d')  }};
    background-color: transparent;
    border-bottom: solid 2px transparent;
    }
    .mainNavigation__list > li > a:hover {
    color: {{ app('theme')->setting('main_navigation_link_color_hover', '#4d4d4d')  }};
    background-color: transparent;
    border-bottom: solid 2px {{ app('theme')->setting('main_navigation_link_color_hover', '#4d4d4d')  }};
    }

    .mobileNav > li > a {
    color: {{ app('theme')->setting('main_navigation_link_color', '#4d4d4d')  }};
    background-color: transparent;
    border-bottom: solid 2px transparent;
    }

    .mobileNav > li > a:hover {
    color: {{ app('theme')->setting('main_navigation_link_color_hover', '#4d4d4d')  }};
    background-color: transparent;
    border-bottom: solid 2px {{ app('theme')->setting('main_navigation_link_color_hover', '#4d4d4d')  }};
    }

    .mobileNav > li > a:focus {
    color: {{ app('theme')->setting('main_navigation_link_color_hover', '#4d4d4d')  }};
    background-color: transparent;
    border-bottom: solid 2px {{ app('theme')->setting('main_navigation_link_color_hover', '#4d4d4d')  }};
    }

@elseif(app('theme')->setting('main_navigation_link_hover_effect') == 'custom')
    /* Custom Link Colors */
    .mainNavigation__list > li > a {
    color: {{ app('theme')->setting('main_navigation_link_color', '#4d4d4d')  }};
    background-color: {{ app('theme')->setting('main_navigation_link_bg_color', '#EEEEEE')  }};
    }
    .mainNavigation__list > li > a:hover {
    color: {{ app('theme')->setting('main_navigation_link_color_hover', '#4d4d4d')  }};
    background-color: {{ app('theme')->setting('main_navigation_link_bg_color_hover', '#EEEEEE')  }};
    }

    .mobileNav > li > a {
    color: {{ app('theme')->setting('main_navigation_link_color', '#4d4d4d')  }};
    background-color: {{ app('theme')->setting('main_navigation_link_bg_color', '#EEEEEE')  }};
    }

    .mobileNav > li > a:hover {
    color: {{ app('theme')->setting('main_navigation_link_color_hover', '#4d4d4d')  }};
    background-color: {{ app('theme')->setting('main_navigation_link_bg_color_hover', '#EEEEEE')  }};
    }

    .mobileNav > li > a:focus {
    color: {{ app('theme')->setting('main_navigation_link_color_hover', '#4d4d4d')  }};
    background-color: {{ app('theme')->setting('main_navigation_link_bg_color_hover', '#EEEEEE')  }};
    }
@else
    .mainNavigation__list > li > a {
    color: {{ app('theme')->setting('main_navigation_link_color', '#4d4d4d')  }};
    background-color: {{ app('theme')->setting('main_navigation_bg', '#EEEEEE')  }};
    }
    .mainNavigation__list > li > a:hover {
    color: {{ app('theme')->setting('main_navigation_link_color_hover', '#4d4d4d') }};
    background-color: {{ app('theme')->setting('main_navigation_bg', '#EEEEEE')  }};
    }

    .mobileNav > li > a {
    color: {{ app('theme')->setting('main_navigation_link_color', '#4d4d4d')  }};
    background-color: {{ app('theme')->setting('main_navigation_bg', '#EEEEEE')  }};
    }

    .mobileNav > li > a:hover {
    color: {{ app('theme')->setting('main_navigation_link_color_hover', '#4d4d4d') }};
    background-color: {{ app('theme')->setting('main_navigation_bg', '#EEEEEE')  }};
    }

    .mobileNav > li > a:focus {
    color: {{ app('theme')->setting('main_navigation_link_color_hover', '#4d4d4d') }};
    background-color: {{ app('theme')->setting('main_navigation_bg', '#EEEEEE')  }};
    }
@endif

@if(app('theme')->setting('main_navigation_link_transition'))
    .mainNavigation__list > li > a {
    transition: all 0.2s ease-in-out;
    }
@endif

/* Auxiliary Navigation (Use this for the account menu, sign up cta, cart, etc,) */

.auxiliaryMenu--block, .auxiliaryMenu__container {
background-color: {!! app('theme')->setting('auxiliary_bg_color', 'transparent') !!};
@if(app('theme')->setting('auxiliary_menu_style') == 'block')
    border-bottom-color: {!! app('theme')->setting('auxiliary_border_color', 'transparent') !!}
@else
    border-bottom: none;
@endif
}

.auxiliaryMenu {
max-width: {{ app('theme')->setting('header_width', '1366px') }};
}

@if(app('theme')->setting('auxiliary_link_color'))
    .auxiliaryMenu > li > a {
    color: {{ app('theme')->setting('auxiliary_link_color') }};
    }
    .auxiliaryMenu > li > a:hover, .auxiliaryMenu > li > a:focus {
    text-decoration: underline;
    }
@else
    @if(app('theme')->setting('header_style') == 'style3' && app('theme')->setting('auxiliary_menu_style') == 'block')
        .auxiliaryMenu > li > a {
        @if(colorIsDark(app('theme')->setting('auxiliary_bg_color', '#EEEEEE')))
            color: {{ adjustBrightness(app('theme')->setting('auxiliary_bg_color', '#EEEEEE'), 175) }};
        @else
            color: {{ adjustBrightness(app('theme')->setting('auxiliary_bg_color', '#EEEEEE'), -175) }};
        @endif
        }
        .auxiliaryMenu > li > a:hover, .auxiliaryMenu > li > a:focus {
        @if(colorIsDark(app('theme')->setting('auxiliary_bg_color', '#EEEEEE')))
            color: {{ adjustBrightness(app('theme')->setting('auxiliary_bg_color', '#EEEEEE'), 135) }};
        @else
            color: {{ adjustBrightness(app('theme')->setting('auxiliary_bg_color', '#EEEEEE'), -135) }};
        @endif
        }
    @else
        .auxiliaryMenu > li > a {
        @if(colorIsDark(app('theme')->setting('header_bg_color', '#EEEEEE')))
            color: {{ adjustBrightness(app('theme')->setting('header_bg_color', '#EEEEEE'), 175) }};
        @else
            color: {{ adjustBrightness(app('theme')->setting('header_bg_color', '#EEEEEE'), -175) }};
        @endif
        }

        .auxiliaryMenu > li > a:hover, .auxiliaryMenu > li > a:focus {
        @if(colorIsDark(app('theme')->setting('header_bg_color', '#EEEEEE')))
            color: {{ adjustBrightness(app('theme')->setting('header_bg_color', '#EEEEEE'), 135) }};
        @else
            color: {{ adjustBrightness(app('theme')->setting('header_bg_color', '#EEEEEE'), -135) }};
        @endif
        }
    @endif
@endif

.order__delivery_threshold_difference, .order__delivery_threshold_difference-met {
background-color: {{ app('theme')->setting('order_status_bg', '#f8f8f8')  }};
color: {{ app('theme')->setting('order_status_color', '#777')  }};
}

/* OrderStatus */
.orderStatusBar {
background-color: {{ app('theme')->setting('order_status_bg', '#f8f8f8')  }};
}

.orderStatus__container {
color: {{ app('theme')->setting('order_status_color', '#777')  }};
}

.orderStatus__container a {
border-bottom-color: {{ adjustBrightness(app('theme')->setting('order_status_bg', '#f8f8f8'), 50) }};
color: {{ app('theme')->setting('order_status_color', '#777') }};
}

.orderStatus__container a:hover {
border-bottom-color: {{ app('theme')->setting('order_status_color', '#777') }};
}

.orderStatus__mobileToggle {
color: {{ app('theme')->setting('order_status_color', '#777')  }};
}

.orderStatus__mobileToggle a {
border-bottom-color: {{ adjustBrightness(app('theme')->setting('order_status_bg', '#f8f8f8'), 50) }};
color: {{ app('theme')->setting('order_status_color', '#777') }};
}

.orderStatus__mobileToggle a:hover {
border-bottom-color: {{ app('theme')->setting('order_status_color', '#777') }};
}

/* Logo */

.logo {
color: {{ app('theme')->setting('main_navigation_link_color', '#CF3D2E')  }};
font-size: {{ app('theme')->setting('main_navigation_link_font_size', '12px') }};
padding: {{ app('theme')->setting('logo_padding', '10') }}px;
}

.logo__img {
max-height: {{ app('theme')->setting('logo_height', '100') }}px;
}

{!! $theme->generateMobileLogo() !!}

.siteFooter {
background-color: {{ app('theme')->setting('footer_bg', '#2d2d2d')  }};
color: {{ app('theme')->setting('footer_text_color', '#777') }};
}

.siteFooter .tw-prose {
--tw-prose-headings: {{ app('theme')->setting('footer_text_color', '#777') }};
--tw-prose-body: {{ app('theme')->setting('footer_text_color', '#777') }};
--tw-prose-p: {{ app('theme')->setting('footer_text_color', '#777') }};
--tw-prose-a: {{ app('theme')->setting('footer_link_color', '#FFFFFF') }};
--tw-prose-bullets: {{ app('theme')->setting('footer_text_color', '#777') }};
}

.siteFooter h1, .siteFooter h2, .siteFooter h3, .siteFooter h4, .siteFooter h5, .siteFooter h6 {
color: {{ app('theme')->setting('footer_text_color', '#777') }};
}

.siteFooter a {
--tw-prose-a: {{ app('theme')->setting('footer_link_color', '#FFFFFF') }};
color: {{ app('theme')->setting('footer_link_color', '#FFFFFF') }};
}

.siteFooter a:hover, .siteFooter a:focus {
color: {{ adjustBrightness(app('theme')->setting('footer_link_color', '#FFFFFF'), -20) }};
}

.siteFooter img {
margin-left: auto;
margin-right: auto;
}

.backToTopLink__container {
background-color: {{ adjustBrightness(app('theme')->setting('footer_bg', '#2d2d2d'), 20)  }};
}

.backToTopLink__container a {
color: {{ app('theme')->setting('footer_link_color', '#FFFFFF') }};
}

/* Buttons */
.btn-brand, .btn-default, .btn-primary {
background-color: {{ app('theme')->setting('brand_color', $variables['default_brand_color'])  }} !important;
color: {{ app('theme')->setting('brand_color_inverted', $variables['default_brand_color_inverted'])  }} !important;
}

.btn-brand-inverted {
background-color: {!! app('theme')->setting('brand_color_inverted', '#FFFFFF') !!};
color: {!! app('theme')->setting('brand_color', '#34B393') !!};
border: solid 1px {!! app('theme')->setting('brand_color', '#FFFFFF') !!};
}

.btn-brand-inverted:not([disabled]):hover {
background-color: {!! app('theme')->setting('brand_color', '#FFFFFF') !!};
color: {!! app('theme')->setting('brand_color_inverted', '#34B393') !!};
border: solid 1px {!! app('theme')->setting('brand_color', '#FFFFFF') !!};
}

{{-- .btn-outline {
	background-color: transparent;
	border-color: {{ adjustBrightness(app('theme')->setting('brand_color', $variables['default_brand_color']), -20)  }};
	color: {{ app('theme')->setting('brand_color_inverted', $variables['default_brand_color_inverted'])  }};
} --}}

.btn-brand:not([disabled]):hover, .btn-default:not([disabled]):hover, .btn-primary:not([disabled]):hover {
@if(colorIsDark(app('theme')->setting('brand_color', $variables['default_brand_color'])))
    background-color: {{ adjustBrightness(app('theme')->setting('brand_color', $variables['default_brand_color']), 40)  }} !important;
    border-color: {{ adjustBrightness(app('theme')->setting('brand_color', $variables['default_brand_color']), 40)  }} !important;
@else
    background-color: {{ adjustBrightness(app('theme')->setting('brand_color', $variables['default_brand_color']), -40)  }} !important;
    border-color: {{ adjustBrightness(app('theme')->setting('brand_color', $variables['default_brand_color']), -40)  }} !important;
@endif
color: {{ app('theme')->setting('brand_color_inverted', $variables['default_brand_color_inverted'])  }} !important;
}

.btn-danger, .btn-action, .cta {
background-color: {{ app('theme')->setting('action_color', $variables['default_action_color'])  }} !important;
border-color: {{ adjustBrightness(app('theme')->setting('action_color', $variables['default_action_color']), -20)  }} !important;
color: {{ app('theme')->setting('action_color_inverted', $variables['default_action_color_inverted'])  }} !important;
}
.btn-danger:not([disabled]):hover, .btn-action:not([disabled]):hover, .cta:not([disabled]):hover {
@if(colorIsDark(app('theme')->setting('action_color', $variables['default_action_color'])))
    background-color: {{ adjustBrightness(app('theme')->setting('action_color', $variables['default_action_color']), 40)  }} !important;
    border-color: {{ adjustBrightness(app('theme')->setting('action_color', $variables['default_action_color']), 40)  }} !important;
@else
    background-color: {{ adjustBrightness(app('theme')->setting('action_color', $variables['default_action_color']), -40)  }} !important;
    border-color: {{ adjustBrightness(app('theme')->setting('action_color', $variables['default_action_color']), -40)  }} !important;
@endif

color: {{ app('theme')->setting('action_color_inverted', $variables['default_action_color_inverted'])  }} !important;
}

.btn-action-inverted {
background-color: {!! app('theme')->setting('action_color_inverted', '#FFFFFF') !!};
color: {!! app('theme')->setting('action_color', '#34B393') !!};
border: solid 1px {!! app('theme')->setting('action_color', '#FFFFFF') !!};
}

.btn-action-inverted:not([disabled]):hover {
background-color: {!! app('theme')->setting('action_color', '#FFFFFF') !!};
color: {!! app('theme')->setting('action_color_inverted', '#34B393') !!};
border: solid 1px {!! app('theme')->setting('action_color', '#FFFFFF') !!};
}

.bullet, .confirmation__step-number {
background-color: {{ app('theme')->setting('brand_color', $variables['default_brand_color'])  }};
color: {{ app('theme')->setting('brand_color_inverted', $variables['default_brand_color_inverted'])  }};
}

/* Store Menu */
.storeIndex__sideBarList--style2 {
background-color: {{ app('theme')->setting('store_menu_bg', '#f8f8f8')  }} !important;
border-color: {{ app('theme')->setting('store_menu_bg', '#f8f8f8')  }} !important;
color: {{ app('theme')->setting('store_menu_color', '#777')  }};
}

.storeSearch__input--style2 {
border-color: {{ app('theme')->setting('store_menu_bg', '#f8f8f8')  }} !important;
}

.storeIndex__sideBarList--style2 > li > a {
color: {{ app('theme')->setting('store_menu_color', '#777')  }};
border-color: {{ adjustBrightness(app('theme')->setting('store_menu_bg', '#f8f8f8'), -10)  }} !important;
}

.storeIndex__sideBarList--style2 > li > a:hover {
color: {{ app('theme')->setting('store_menu_color', '#777')  }};
background-color: {{ adjustBrightness(app('theme')->setting('store_menu_bg', '#f8f8f8'), -10)  }} !important;
}

.storeSearch__button--style2 {
color: {{ app('theme')->setting('store_menu_color', '#777')  }};
}

.banner__protocols li:before {
color: {{ app('theme')->setting('brand_color', $variables['default_brand_color']) }}
}

/*Custom Styles*/
{!! $theme->custom_css !!}

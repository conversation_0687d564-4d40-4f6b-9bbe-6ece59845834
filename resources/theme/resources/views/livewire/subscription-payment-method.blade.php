@php
    /** @var \App\Models\RecurringOrder $subscription */
    /** @var \App\Models\Order|null $current_order */
@endphp
<div class="tw-mt-4 tw-bg-gray-50 tw-rounded-lg">
    @if( ! is_null($current_order))
        @php
            $payment_method = $current_order->paymentMethod;
        @endphp
        <div class="tw-px-4 tw-py-5 sm:tw-p-6">
            <h3 class="tw-text-base tw-font-semibold tw-leading-6 tw-text-gray-900">{{ $payment_method?->title ?? 'Payment method not selected' }}</h3>
            <div class="tw-mt-2 tw-max-w-xl tw-text-sm tw-text-gray-500">
                <p>{{ $payment_method?->instructions ?? '' }}</p>
            </div>
            @if($payment_method?->isCard())
                <form wire:submit.prevent="updatePaymentSource" method="POST" class="tw-m-0">
                    <div class="tw-mt-4">
                        <label for="payment_source_id" class="tw-block tw-text-sm tw-font-medium tw-leading-6 tw-text-gray-900">
                            Credit/Debit card
                        </label>
                        <select id="payment_source_id" wire:model="payment_source_id" class="tw-mt-2 tw-block tw-w-full tw-rounded-md tw-border-0 tw-py-1.5 tw-pl-3 tw-pr-10 tw-text-gray-900 tw-ring-1 tw-ring-inset tw-ring-gray-300 focus:tw-ring-2 focus:tw-ring-theme-action-color sm:tw-text-sm sm:tw-leading-6">
                            @foreach($subscription->customer->stripePaymentMethods() as $available_card)
                                @php /** @var \App\Billing\Gateway\PaymentMethod $available_card */ @endphp

                                <option value="{{ $available_card->id }}">
                                    {{ str($available_card->brand)->ucfirst() }} ending in {{ $available_card->last_four }}
                                </option>
                            @endforeach
                        </select>
                    </div>
                    <div class="tw-mt-5">
                        <button type="submit" class="tw-inline-flex tw-items-center tw-rounded-md tw-bg-white tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-gray-900 tw-shadow-sm tw-ring-1 tw-ring-inset tw-ring-gray-300 hover:tw-bg-gray-50">
                            Update
                        </button>
                    </div>
                </form>
            @endif
        </div>
    @else

        @php
            $payment_method = $subscription->upcomingPaymentMethod();
        @endphp
        <div class="tw-px-4 tw-py-5 sm:tw-p-6">
            <h3 class="tw-text-base tw-font-semibold tw-leading-6 tw-text-gray-900">{{ $payment_method?->title ?? 'Payment method not selected' }}</h3>
            <div class="tw-mt-2 tw-max-w-xl tw-text-sm tw-text-gray-500">
                <p>{{ $payment_method?->instructions ?? '' }}</p>
            </div>
        </div>
    @endif
</div>

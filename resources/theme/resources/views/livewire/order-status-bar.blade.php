@php
    /** @var \App\Models\Pickup|null $current_delivery_method */
    /** @var App\Models\RecurringOrder|null $subscription */
    /** @var App\Models\Order|null $order */
@endphp

<div class="tw-bg-theme-order-status-bg-color tw-py-2 orderStatusBar">
    <div class="tw-relative tw-mx-auto tw-max-w-7xl tw-px-4 sm:tw-px-6 lg:tw-px-8">
        <div class="tw-flex tw-items-center tw-justify-center">
            <div class="tw-text-sm tw-text-theme-order-status-color">
                @if(auth()->guest() && is_null($current_delivery_method))
                    <a href="{{ route('login') }}" class="tw-text-sm tw-font-semibold tw-text-theme-order-status-color">Sign
                        in</a> or <a href="{{ route('register') }}"
                                     class="tw-text-sm tw-font-semibold tw-text-theme-order-status-color">Sign up</a>
                @else
                    @if($order)
                        <p class="tw-m-0 tw-text-sm tw-leading-6 tw-text-white">
                            Your @if( ! is_null($subscription))
                                subscription
                            @endif order will be delivered on
                            <time datetime="{{ $order->pickup_date->format('Y-m-d') }}">{{ $order->pickup_date->format('D, M jS') }}</time>
                            . <a href="{{ route('customer.orders.show', [$order]) }}"
                                 class="tw-whitespace-nowrap tw-text-white tw-font-semibold">View your order&nbsp;<span
                                        aria-hidden="true">&rarr;
                                </span>
                            </a>
                        </p>
                    @elseif($subscription)
                        <p class="tw-m-0 tw-text-sm tw-leading-6 tw-text-white">
                            Your next delivery is scheduled for
                            <time datetime="{{ $subscription->ready_at->format('Y-m-d') }}">{{ $subscription->ready_at->format('D, M jS') }}.</time>
                            <a href="{{ route('customers.recurring.edit') }}"
                               class="tw-whitespace-nowrap  tw-text-white tw-font-semibold">Manage subscription&nbsp;<span aria-hidden="true">&rarr;</span>
                            </a>
                        </p>
                    @else
                        @php
                            /** @var \App\OrderWindow|null $active_order_window */
                            $active_order_window = $current_delivery_method?->activeOrderWindow();
                        @endphp
                        @if(is_null($active_order_window))
                            Ordering is currently closed. <a
                                    href="{{ url("/locations/{$current_delivery_method?->slug}#schedule") }}"
                                    class="tw-font-semibold tw-text-theme-order-status-color">See schedule</a>
                        @else
                            Delivery on
                            <time class="tw-font-semibold" datetime="{{ $active_order_window->deliveryDatetime()->format('Y-m-d') }}">
                                {{ $active_order_window->deliveryDatetime()->format('D, M jS') }}</time>. Order within
                            <span class="tw-font-semibold">
                                {{ $active_order_window->deadlineDatetime()->diffForHumans(syntax: \Carbon\CarbonInterface::DIFF_ABSOLUTE) }}.
                            </span>
                        @endif
                    @endif
                @endif

            </div>
        </div>
    </div>
</div>

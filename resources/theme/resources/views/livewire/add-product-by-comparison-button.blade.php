@php
    /** @var App\Models\Product $product */
    /** @var Illuminate\Support\Collection $comparable_products */
    /** @var string $cart_label */
    /** @var bool|null $rounded */
    /** @var bool $confirm_delivery_method */
@endphp

<form
        class="tw-px-2 tw-pb-2 productListing__productAddToCartForm--grid"
        id="shoppingCartForm_{{ $product->slug }}"
>
    <div>
        <label for="location" class="tw-sr-only">Location</label>
        <select wire:model.lazy="selected_variant_id" name="product_id" class="tw-mt-2 tw-block tw-w-full tw-rounded-md tw-border-0 tw-py-1.5 tw-pl-3 tw-pr-10 tw-text-gray-900 tw-ring-1 tw-ring-inset tw-ring-gray-300 focus:tw-ring-2 focus:tw-ring-indigo-600 sm:tw-text-sm sm:tw-leading-6">
            @foreach($comparable_products as $variant)
                <option value="{{ $variant->id }}">{{ $variant->title }}</option>
            @endforeach
        </select>
    </div>

    <div class="tw-mt-2 featuredProductsWidget_cta--style2">
        <button
                type="button"
                class="@if($cta_classes ?? false) {{ $cta_classes }} @else tw-px-3 tw-py-2 tw-rounded-none tw-rounded-b-lg tw-text-sm tw-font-semibold @endif btn btn-action btn-block"
                wire:click="add"
                wire:loading.attr="disabled"
                wire:target="add"
        >
            <span wire:loading.remove wire:target="add">
                {{ $cart_label }}
            </span>
            <span wire:loading wire:target="add" style="display:none;">Adding...</span>
        </button>
    </div>
</form>


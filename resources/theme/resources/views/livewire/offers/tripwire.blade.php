<div
        x-data="{ open: false }"
        x-init="$watch('open', value => {
            if (value && typeof gtag !== 'undefined') {
                gtag('event', 'view_promotion', {
                    creative_name: 'first_order_discount',
                    creative_slot: 'tripwire_footer',
                    promotion_name: '20% Off First $175 Order',
                    promotion_id: '20%_off_first_$175_order'
                });
            }
        })"
        class="tw-relative tw-z-30"
        aria-labelledby="modal-title"
        role="dialog"
        aria-modal="true"
>
    <div x-show="open"
         x-cloak
         x-transition:enter="tw-ease-out tw-duration-200"
         x-transition:enter-start="tw-opacity-0"
         x-transition:enter-end="tw-opacity-100"
         x-transition:leave="tw-ease-in tw-duration-100"
         x-transition:leave-start="tw-opacity-100"
         x-transition:leave-end="tw-opacity-0"
         class="tw-fixed tw-inset-0 tw-bg-gray-500/50 tw-transition-opacity"
         aria-hidden="true"
    ></div>
    <div x-on:click.away="open = false" class="tw-fixed tw-inset-x-0 tw-bottom-0 tw-bg-gray-50 tw-p-4 tw-pb-6 tw-ring-4 tw-ring-theme-action-color/85 lg:tw-px-6">
        <div x-on:click="open = !open" class="tw-relative tw-cursor-pointer">
            <div class="tw-absolute tw-right-0 tw-top-0 lg:tw-pr-2 lg:tw-pt-2">
                <button type="button" class="tw-rounded-md tw-bg-gray-50 tw-text-gray-400 hover:tw-text-gray-500 focus:tw-outline-none">
                    <span class="tw-sr-only">Close</span>
                    <svg class="tw-size-6 tw-transition tw-duration-100" x-bind:class="{ 'tw-rotate-180': open }" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 15.75 7.5-7.5 7.5 7.5"/>
                    </svg>
                </button>
            </div>
            <div class="tw-max-w-5xl tw-mx-auto tw-flex tw-flex-col tw-items-center tw-text-center">
                <h5 class="tw-m-0 tw-text-sm tw-font-body tw-font-bold tw-uppercase tw-text-theme-action-color lg:tw-text-base">
                    First order bonus
                </h5>
                <p class="tw-m-0 tw-mt-2 tw-font-display tw-text-2xl tw-text-theme-action-color lg:tw-text-4xl">
                    Get $20 off <br class="lg:tw-hidden"/> your first order!
                </p>
                <div x-show="open" x-cloak class="tw-mt-2 ">
                    <p class="tw-text-base tw-max-w-xl tw-mx-auto">The discount will be automatically applied at checkout to orders of $175 and above—no code needed.</p>

                    <div class="tw-mt-3 ">
                        <form wire:submit.prevent="submit" class="tw-max-w-md tw-mx-auto tw-flex tw-space-x-3">
                            <input type="hidden" wire:model="username" name="username" class="tw-hidden">
                            <input type="hidden" wire:model="timestamp" name="timestamp" class="tw-hidden"/>
                            @error('username')
                            <p class="m-0 mt-2 text-sm text-red-600" id="first-name-error">There was an error. Please try again.</p>
                            @enderror
                            @error('timestamp')
                            <p class="m-0 mt-2 text-sm text-red-600" id="first-name-error">There was an error. Please try again.</p>
                            @enderror

                            <div class="tw-flex-auto">
                                <label for="email" class="tw-sr-only">Email</label>
                                <div class="tw-grid tw-grid-cols-1">
                                    <input type="email" x-on:click.stop name="email" wire:model="email" id="email" class="tw-col-start-1 tw-row-start-1 tw-block tw-w-full tw-rounded-md tw-bg-white tw-py-1.5 tw-pl-10 tw-pr-3 tw-text-base tw-text-gray-900 tw-outline tw-outline-1 tw--outline-offset-1 tw-outline-gray-300 placeholder:tw-text-gray-400 focus:tw-outline focus:tw-outline-2 focus:tw--outline-offset-2 focus:tw-outline-theme-action-color sm:tw-pl-9 sm:tw-text-sm/6" required placeholder="Email">
                                    <svg class="tw-pointer-events-none tw-col-start-1 tw-row-start-1 tw-ml-3 tw-size-5 tw-self-center tw-text-gray-400 sm:tw-size-4" viewBox="0 0 16 16" fill="currentColor" aria-hidden="true" data-slot="icon">
                                        <path d="M2.5 3A1.5 1.5 0 0 0 1 4.5v.793c.026.009.051.02.076.032L7.674 8.51c.206.1.446.1.652 0l6.598-3.185A.755.755 0 0 1 15 5.293V4.5A1.5 1.5 0 0 0 13.5 3h-11Z"></path>
                                        <path d="M15 6.954 8.978 9.86a2.25 2.25 0 0 1-1.956 0L1 6.954V11.5A1.5 1.5 0 0 0 2.5 13h11a1.5 1.5 0 0 0 1.5-1.5V6.954Z"></path>
                                    </svg>
                                </div>
                            </div>

                            <button type="submit" wire:click.prevent.stop="submit" wire:loading.attr="disabled" wire:target="submit" class="tw-rounded-md tw-bg-theme-action-color tw-px-6 tw-py-1 tw-text-base/6 tw-font-semibold tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color/90 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-action-color">
                                <span wire:loading.remove wire:target="submit">Claim</span>
                                <svg wire:loading.inline wire:target="submit" class="tw-animate-spin tw-h-4 tw-w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                    <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                    <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                </svg>
                            </button>
                        </form>
                        @error('email')
                        <p class="tw-m-0 tw-mt-2 tw-text-sm tw-text-red-600" id="email-error">{{ $message }}</p>
                        @enderror
                        <p class="tw-m-0 tw-mt-3 tw-max-w-2xl tw-mx-auto tw-text-xs tw-text-gray-500">
                            *Offer valid for new customers only. Cannot be combined with any other offer. Offer not valid on prior purchases or gift cards. Offer valid until {{ today()->addWeeks(2)->format('m/d/Y') }}. Seven Sons Farms reserves the right to cancel this offer at any time. Value based on non-customer registering and placing an order within two (2) weeks of claiming the offer.
                            By entering my email address above, I opt in to receive marketing emails from Seven Sons Farms. You can unsubscribe at any time.<br/>
                            <a href="{{ route('terms-of-service') }}">Terms of Service</a> and <a href="{{ route('privacy-policy') }}">Privacy Policy</a>
                        </p>
                    </div>

                </div>
            </div>
        </div>
    </div>
</div>

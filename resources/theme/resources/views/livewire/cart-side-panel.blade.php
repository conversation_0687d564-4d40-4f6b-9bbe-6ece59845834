@php
    /**
     * @var \App\Contracts\Cartable|null $cart
     * @var \App\Models\Pickup|null $delivery_method
     * @var \App\Services\SubscriptionSettingsService $subscription_settings_service
     */
    $is_open = is_null($delivery_method) || $delivery_method->isCurrentlyAcceptingOrders();
@endphp

<div class="tw-h-full tw-bg-white tw-flex tw-flex-col tw-pointer-events-auto tw-w-screen tw-max-w-md">
    @if(!is_null($cart) && auth()->check())
        <div class="tw-flex-1 tw-overflow-hidden tw-flex tw-flex-col tw-bg-white tw-shadow-xl">
            <div class="tw-flex-1 tw-overflow-y-auto tw-py-4 tw-px-4 sm:tw-px-6">

                @if( ! $cart->cartIsEmpty())
                    <div>
                        <div class="tw-w-full tw-pb-1 tw-flex tw-items-center tw-justify-between tw-text-theme-brand-color">
                            <h2 class="tw-m-0 tw-text-lg tw-font-body tw-font-semibold" id="slide-over-title">Cart ({{ $cart->itemCount() }})</h2>
                            <div class="tw-ml-3 tw-flex tw-h-7 tw-items-center">
                                <button type="button" @click="open = false" class="tw--m-2 tw-p-2 tw-text-theme-brand-color/80 hover:tw-text-theme-brand-color/60">
                                    <span class="tw-sr-only">Close panel</span>
                                    <svg class="tw-h-6 tw-w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"/>
                                    </svg>
                                </button>
                            </div>
                        </div>

                        @if($is_open && $delivery_method?->display_cart_shipping_calculator && $delivery_method?->deliveryFeeHasCap())
                            <x-theme::free-shipping-calculator
                                    :cart="$cart"
                                    :delivery_method="$delivery_method"
                            />
                        @endif


                    </div>

                    <div class="tw-border-t tw-border-gray-200 tw-flow-root">
                        @if ( ! $is_open)
                            <div class="tw-mb-4">
                                <x-theme::delivery-method-closed :delivery_method="$delivery_method"/>
                            </div>
                        @endif

                        @if($is_open && $cart->cartIsEligibleForSubscription())
                            <div class="tw-pt-1">
                                <x-theme::subscribe-and-save-cart-toggle
                                        :cart="$cart"
                                        :subscription_settings_service="$subscription_settings_service"
                                />
                            </div>
                        @endif
                        <livewire:theme.cart-items :cart="$cart"/>
                    </div>
                @else
                    <div class="tw-py-6">
                        <x-theme::empty-cart :cart="$cart" :centered="true"/>
                    </div>
                @endif
            </div>
        </div>

        @if( ! $cart->cartIsEmpty())
            @php
                $subtotal = $cart->cartSubtotal();

                $welcome_savings = 0;

                if ($cart->hasConditionalCouponApplied(config('grazecart.welcome_coupon_code')) && $subtotal >= 17500) {
                    $welcome_savings = 2000; // $20 off for welcome coupon
                }

                $subtotal_with_savings = $subtotal - $welcome_savings -  ($cart->isRecurring() ? $cart->cartSubscriptionSavingsTotal() : 0);
            @endphp
            <div class="tw-bg-white">


                <div class="tw-relative tw-border-t tw-px-4 sm:tw-px-6 ">

                    @if($cart->hasConditionalCouponApplied(config('grazecart.welcome_coupon_code')))
                        <div class="tw-px-4 tw-pt-3 tw-space-y-1.5 tw-text-center">
                            <p class="tw-m-0 tw-text-xs">
                                @if($subtotal < 17500)
                                    Spend ${{ money(17500 - $subtotal) }} more for
                                    <span class="tw-font-semibold">$20 off</span>. Expires in
                                    <span class="tw-font-semibold">{{ today()->diffForHumans($cart?->cartCustomer()->created_at->addWeeks(2), \Carbon\CarbonInterface::DIFF_ABSOLUTE) }}</span>.
                                @else
                                    You earned <span class="tw-font-semibold">$20 off!</span>
                                @endif
                            </p>
                            <div class="tw-w-full tw-relative tw-bg-gray-200 tw-rounded-md tw-h-1.5">
                                <div class="tw-absolute tw-inset-0">
                                    <div class="tw-h-full tw-rounded-md tw-bg-theme-announcement-bar-bg-color tw-max-w-full" style="width: {{ ($subtotal / 17500) * 100 }}%"></div>
                                </div>
                            </div>
                        </div>
                    @endif
                    <div class="tw-relative tw-z-10 tw-bg-white tw-border-gray-200 tw-cursor-pointer tw-pb-6 hover:tw-text-gray-700">
                        <div @click="is_showing_total_details = ! is_showing_total_details">
                            <div class="tw-pt-3 tw-flex tw-justify-between">
                                <div class="tw-flex tw-items-center tw-space-x-2 tw-text-lg tw-font-medium tw-text-gray-900">
                                    <p class="tw-m-0">Subtotal</p>
                                </div>
                                <div class="tw-flex tw-text-lg tw-items-baseline tw-space-x-2">
                                    @if($subtotal !== $subtotal_with_savings)
                                        <p class="tw-m-0 tw-line-through tw-text-gray-500 tw-text-sm">
                                            ${{ money($subtotal) }}
                                        </p>
                                    @endif
                                    <p class="tw-m-0 @if($subtotal !== $subtotal_with_savings) tw-text-keppel-600 tw-font-semibold @endif">
                                        ${{ money($subtotal_with_savings) }}
                                    </p>
                                </div>

                            </div>
                        </div>

                        <div class="tw-pt-3 tw-space-y-6">
                            @if($is_open)
                                @if( ! $cart->meetsOrderMinimum())
                                    <button type="button" class="tw-cursor-not-allowed tw-w-full tw-items-center tw-justify-center tw-rounded-md tw-border tw-border-transparent tw-bg-theme-action-color/70 tw-px-3 tw-py-2 tw-font-medium tw-text-white tw-shadow-sm">
                                        &#36;{{ money($delivery_method?->min_customer_orders) }} minimum to checkout<br>
                                        <span class="tw-text-xs tw-text-white tw-font-light">(This helps us keep your shipment frozen)</span>
                                    </button>
                                @else
                                    <a href="{{ route('checkout.index') }}" class="tw-no-underline">
                                        <button type="button" class="tw-w-full tw-flex tw-items-center tw-justify-center tw-rounded-md tw-border tw-border-transparent tw-bg-theme-action-color tw-px-3 tw-py-2 tw-text-base tw-font-medium tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color/70">
                                            Checkout
                                        </button>
                                    </a>
                                @endif

                                <div class="tw-mt-3 tw-flex tw-justify-center tw-text-center  tw-text-gray-500">
                                    <button type="button" @click="open = false" class="tw-font-medium tw-text-sm tw-text-theme-action-color hover:tw-text-theme-action-color/70">
                                        Keep Shopping
                                        <span aria-hidden="true"> &rarr;</span>
                                    </button>
                                </div>
                            @else
                                <button type="button" class="tw-cursor-not-allowed tw-w-full tw-flex tw-items-center tw-justify-center tw-rounded-md tw-border tw-border-transparent tw-bg-theme-action-color/70 tw-px-6 tw-py-3 tw-text-base tw-font-medium tw-text-white tw-shadow-sm">
                                    Ordering closed
                                </button>
                                <div class="tw-mt-6 tw-flex tw-justify-center tw-text-center tw-text-sm tw-text-gray-500">
                                    <p class="tw-m-0">
                                        <button type="button" @click="open = false" class="tw-font-medium tw-text-theme-action-color hover:tw-text-theme-action-color/70">
                                            {{ __('messages.cart.keep_shopping') }}
                                            <span aria-hidden="true"> &rarr;</span>
                                        </button>
                                    </p>
                                </div>
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        @endif
    @else
        <div class="tw-flex-1 tw-overflow-hidden tw-flex tw-flex-col tw-bg-white tw-shadow-xl">
            <div class="tw-flex-1 tw-overflow-y-auto tw-py-6 tw-px-4 sm:tw-px-6">
                <div class="tw-py-6">
                    <x-theme::empty-cart :centered="true"/>
                </div>
            </div>
        </div>
    @endif
</div>


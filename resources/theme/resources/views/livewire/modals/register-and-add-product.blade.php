<x-theme::modal class="tw-w-full">
    <form wire:submit.prevent="submit"
          x-trap="open"
          class="tw-relative tw-transform tw-overflow-hidden tw-rounded-lg tw-bg-white tw-text-left tw-shadow-xl sm:tw-my-8 sm:tw-w-full sm:tw-max-w-md sm:tw-mx-auto"
    >
        <div class="tw-bg-white tw-px-4 tw-pb-4 tw-pt-5 sm:tw-p-6 sm:tw-pb-4">
            <div class="tw-py-2 tw-text-gray-700">
                <header class="tw-block tw-text-center">
                    <h1 class="tw-mx-0 tw-mt-0 tw-mb-3 tw-text-3xl tw-font-medium tw-font-display">
                        Register
                    </h1>

                    <div class="tw-mx-auto">
                        <img src="{{ \App\Models\Media::s3ToCloudfront('https://s3.amazonaws.com/sevensonsfarms/sevensonsfarms/images/1690321497_64c042590e1db.png') }}" width=500px>
                    </div>

                    <p class="tw-m-0 tw-mt-2 tw-text-base tw-max-w-xl tw-mx-auto">The discount will be automatically applied at checkout to orders of $175 and above—no code needed.</p>
                </header>

                <form wire:submit.prevent="submit" method="POST" class="tw-mt-5">
                    @csrf
                    <div class="tw-hidden">
                        <input type="text" wire:model="username" autocomplete="off" tabindex="-1" class="tw-hidden">
                        <input type="text" wire:model="timestamp" autocomplete="off" tabindex="-1" class="tw-hidden">
                    </div>
                    <div class="tw-mt-4">
                        <label for="postal_code" class="tw-sr-only">@lang('Postal code')</label>
                        <div class="tw-relative tw-rounded-md tw-shadow-sm">
                            <input type="text" wire:model="postal_code" id="postal_code" required autofocus class="tw-block tw-w-full tw-rounded-md tw-border-0 tw-py-1.5 tw-text-gray-900 tw-ring-1 tw-ring-inset tw-ring-gray-300 placeholder:tw-text-gray-400 focus:tw-ring-2 focus:tw-ring-inset focus:tw-ring-theme-action-color sm:tw-text-sm sm:tw-leading-6 @error('postal_code') tw-pr-10 tw-text-red-900 tw-ring-red-300 placeholder:tw-text-red-300 focus:tw-ring-red-500 @enderror" placeholder="@lang('Postal code')" @error('postal_code') aria-invalid="true" aria-describedby="postal_code-error" @enderror>
                            @error('postal_code')
                            <div class="tw-pointer-events-none tw-absolute tw-inset-y-0 tw-right-0 tw-flex tw-items-center tw-pr-3">
                                <svg class="tw-h-5 tw-w-5 tw-text-red-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            @enderror
                        </div>
                        @error('postal_code') <p class="tw-mt-2 tw-text-left tw-text-sm tw-text-red-600" id="postal_code-error">{{ $message }}</p> @enderror
                    </div>
                    <div class="tw-mt-4">
                        <label for="email" class="tw-sr-only">@lang('Email address')</label>
                        <div class="tw-relative tw-rounded-md tw-shadow-sm">
                            <input type="email" wire:model="email" id="email" required autofocus class="tw-block tw-w-full tw-rounded-md tw-border-0 tw-py-1.5 tw-text-gray-900 tw-ring-1 tw-ring-inset tw-ring-gray-300 placeholder:tw-text-gray-400 focus:tw-ring-2 focus:tw-ring-inset focus:tw-ring-theme-action-color sm:tw-text-sm sm:tw-leading-6 @error('email') tw-pr-10 tw-text-red-900 tw-ring-red-300 placeholder:tw-text-red-300 focus:tw-ring-red-500 @enderror" placeholder="@lang('Email address')" @error('email') aria-invalid="true" aria-describedby="email-error" @enderror>
                            @error('email')
                            <div class="tw-pointer-events-none tw-absolute tw-inset-y-0 tw-right-0 tw-flex tw-items-center tw-pr-3">
                                <svg class="tw-h-5 tw-w-5 tw-text-red-500" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                    <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                </svg>
                            </div>
                            @enderror
                        </div>
                        @error('email') <p class="tw-mt-2 tw-text-left tw-text-sm tw-text-red-600" id="email-error">{{ $message }}</p> @enderror
                    </div>


                    <div class="tw-mt-4">
                        <button type="submit" wire:click.prevent="submit" wire:loading.attr="disabled" wire:loading.class="tw-cursor-not-allowed tw-opacity-75" class="tw-w-full tw-rounded-md tw-bg-theme-action-color tw-px-3 tw-py-2 tw-text-base tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-action-color">
                            Sign Up & Shop
                            <span wire:loading.inline wire:target="submit">...</span>
                        </button>

                        <div class="tw-relative tw-mt-4">
                            <div class="tw-absolute tw-inset-0 tw-flex tw-items-center" aria-hidden="true">
                                <div class="tw-w-full tw-border-t tw-border-gray-200"></div>
                            </div>
                            <div class="tw-relative tw-flex tw-justify-center tw-text-sm/6 tw-font-medium">
                                <a href="{{ route('login') }}" class="tw-bg-white tw-px-6 ">Or Login Instead</a>
                            </div>
                        </div>
                    </div>
                </form>

                <p class="tw-m-0 tw-mt-3 tw-max-w-2xl tw-text-center tw-mx-auto tw-text-xs tw-text-gray-500">
                    *Offer valid for new customers only. Cannot be combined with any other offer. Offer not valid on prior purchases or gift cards. Offer valid until {{ today()->addWeeks(2)->format('m/d/Y') }}. Seven Sons Farms reserves the right to cancel this offer at any time. Value based on non-customer registering and placing an order within two (2) weeks of claiming the offer.
                    By entering my email address above, I opt in to receive marketing emails from Seven Sons Farms. You can unsubscribe at any time.<br/>
                    <a href="{{ route('terms-of-service') }}">Terms of Service</a> and <a href="{{ route('privacy-policy') }}">Privacy Policy</a>
                </p>

                <div class="tw-mt-4 tw-text-center">

                    <i><span style="font-size: 12px">We promise to never sell your information.</span></i>
                </div>
            </div>
        </div>
    </form>
</x-theme::modal>


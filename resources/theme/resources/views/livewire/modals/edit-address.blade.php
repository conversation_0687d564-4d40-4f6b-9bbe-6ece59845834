@php
    /** @var App\Models\Address $address */
@endphp
<x-theme::modal>
    <form
            x-data="googleAutocomplete(
                '{{ config('services.google.places_js_api_key') }}',
                $refs.autocompleteGoogle
            )"
            x-trap="open"
            @google-place-changed="event => {
                $wire.street = event.detail.street ?? '';
                $wire.city = event.detail.city ?? '';
                $wire.state = event.detail.state ?? '';
                $wire.postal_code = event.detail.postal_code ?? '';
             }"
            wire:submit="submit"
            class="tw-relative tw-transform tw-rounded-lg tw-bg-white tw-text-left tw-shadow-xl sm:tw-my-8 sm:tw-w-full sm:tw-max-w-2xl"
    >
        <div class="tw-rounded-t-lg tw-bg-white tw-px-4 tw-pb-4 tw-pt-5 sm:tw-p-6 sm:tw-pb-4">
            <div class="sm:tw-flex sm:tw-items-start">
                <div class="tw-mx-auto tw-flex tw-h-12 tw-w-12 tw-flex-shrink-0 tw-items-center tw-justify-center tw-rounded-full tw-bg-theme-brand-color/25 sm:tw-mx-0 sm:tw-h-10 sm:tw-w-10">
                    <svg class="tw-h-6 tw-w-6 tw-text-theme-brand-color" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" d="M21 7.5l-9-5.25L3 7.5m18 0l-9 5.25m9-5.25v9l-9 5.25M3 7.5l9 5.25M3 7.5v9l9 5.25m0-9v9"></path>
                    </svg>
                </div>
                <div class="tw-mt-3 tw-text-center sm:tw-ml-4 sm:tw-mt-0 sm:tw-text-left">
                    <h3 class="tw-text-base tw-font-semibold tw-leading-6 tw-text-gray-900" id="modal-title">Edit address</h3>
                    <div class="tw-mt-2">
                        <p class="tw-text-sm tw-text-gray-500">Update your existing address.</p>
                    </div>
                    <div class="tw-text-left tw-mt-10 tw-grid tw-grid-cols-1 tw-gap-x-6 tw-gap-y-8 sm:tw-grid-cols-6">
                        <div class="tw-col-span-full sm:tw-col-span-4">
                            <label for="street" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700">Street</label>
                            <div class="tw-relative tw-mt-1 tw-rounded-md tw-shadow-sm">
                                <input type="text" x-ref="autocompleteGoogle" wire:model="street" name="street" id="street" class="tw-block tw-w-full tw-rounded-md tw-border tw-border-gray-300 focus:tw-outline-none focus:tw-border-keppel-500 focus:tw-ring-keppel-500 sm:tw-text-sm tw-@error('street') tw-border-red-300 tw-pr-10 tw-text-red-900 tw-placeholder-red-300 focus:tw-border-red-500 focus:tw-ring-red-500 tw-@enderror" aria-invalid="true" aria-describedby="street-error">
                                @error('street')
                                <div class="tw-pointer-events-none tw-absolute tw-inset-y-0 tw-right-0 tw-flex tw-items-center tw-pr-3">
                                    <!-- Heroicon name: mini/exclamation-circle -->
                                    <svg class="tw-h-5 tw-w-5 tw-text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                @enderror
                            </div>
                            @error('street')
                            <p class="tw-m-0 tw-mt-2 tw-text-sm tw-text-red-600" id="street-error">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="tw-col-span-full sm:tw-col-span-2">
                            <label for="street_2" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700">Apt, unit, etc.</label>
                            <div class="tw-relative tw-mt-1 tw-rounded-md tw-shadow-sm">
                                <input type="text" wire:model="street_2" name="street_2" id="street_2" class="tw-block tw-w-full tw-rounded-md tw-border tw-border-gray-300 focus:tw-outline-none focus:tw-border-keppel-500 focus:tw-ring-keppel-500 sm:tw-text-sm tw-@error('street_2') tw-border-red-300 tw-pr-10 tw-text-red-900 tw-placeholder-red-300 focus:tw-border-red-500 focus:tw-ring-red-500 tw-@enderror" aria-invalid="true" aria-describedby="street_2-error">
                                @error('street_2')
                                <div class="tw-pointer-events-none tw-absolute tw-inset-y-0 tw-right-0 tw-flex tw-items-center tw-pr-3">
                                    <!-- Heroicon name: mini/exclamation-circle -->
                                    <svg class="tw-h-5 tw-w-5 tw-text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                @enderror
                            </div>
                            @error('street_2')
                            <p class="tw-m-0 tw-mt-2 tw-text-sm tw-text-red-600" id="street_2-error">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="sm:tw-col-span-2 sm:tw-col-start-1">
                            <label for="city" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700">City</label>
                            <div class="tw-relative tw-mt-1 tw-rounded-md tw-shadow-sm">
                                <input type="text" wire:model="city" name="city" id="city" class="tw-block tw-w-full tw-rounded-md tw-border tw-border-gray-300 focus:tw-outline-none focus:tw-border-keppel-500 focus:tw-ring-keppel-500 sm:tw-text-sm tw-@error('city') tw-border-red-300 tw-pr-10 tw-text-red-900 tw-placeholder-red-300 focus:tw-border-red-500 focus:tw-ring-red-500 tw-@enderror" aria-invalid="true" aria-describedby="city-error">
                                @error('city')
                                <div class="tw-pointer-events-none tw-absolute tw-inset-y-0 tw-right-0 tw-flex tw-items-center tw-pr-3">
                                    <!-- Heroicon name: mini/exclamation-circle -->
                                    <svg class="tw-h-5 tw-w-5 tw-text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                @enderror
                            </div>
                            @error('city')
                            <p class="tw-m-0 tw-mt-2 tw-text-sm tw-text-red-600" id="city-error">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="sm:tw-col-span-2">
                            <label for="state" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700">State</label>
                            <div class="tw-relative tw-mt-1 tw-rounded-md tw-shadow-sm">
                                <select wire:model.lazy="state" id="state" name="state" class="tw-block tw-w-full tw-rounded-md tw-border tw-border-gray-300 focus:tw-outline-none focus:tw-border-keppel-500 focus:tw-ring-keppel-500 sm:tw-text-sm tw-@error('state') tw-border-red-300 tw-pr-10 tw-text-red-900 tw-placeholder-red-300 focus:tw-border-red-500 focus:tw-ring-red-500 tw-@enderror" aria-invalid="true" aria-describedby="state-error">
                                    <option value="">Select state...</option>
                                    @foreach(config('grazecart.states') as $abbr => $state)
                                        <option value="{{ $abbr }}">{{ $state }}</option>
                                    @endforeach
                                    @foreach(config('grazecart.provinces') as $abbr => $state)
                                        <option value="{{ $abbr }}">{{ $state }}</option>
                                    @endforeach
                                </select>
                                @error('state')
                                <div class="tw-pointer-events-none tw-absolute tw-inset-y-0 tw-right-0 tw-flex tw-items-center tw-pr-3">
                                    <!-- Heroicon name: mini/exclamation-circle -->
                                    <svg class="tw-h-5 tw-w-5 tw-text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                @enderror
                            </div>
                            @error('state')
                            <p class="tw-m-0 tw-mt-2 tw-text-sm tw-text-red-600" id="state-error">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="sm:tw-col-span-2">
                            <label for="postal_code" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700">Postal code</label>
                            <div class="tw-relative tw-mt-1 tw-rounded-md tw-shadow-sm">
                                <input type="text" wire:model="postal_code" name="postal_code" id="postal_code" class="tw-block tw-w-full tw-rounded-md tw-border tw-border-gray-300 focus:tw-outline-none focus:tw-border-keppel-500 focus:tw-ring-keppel-500 sm:tw-text-sm tw-@error('postal_code') tw-border-red-300 tw-pr-10 tw-text-red-900 tw-placeholder-red-300 focus:tw-border-red-500 focus:tw-ring-red-500 tw-@enderror" aria-invalid="true" aria-describedby="postal_code-error">
                                @error('postal_code')
                                <div class="tw-pointer-events-none tw-absolute tw-inset-y-0 tw-right-0 tw-flex tw-items-center tw-pr-3">
                                    <!-- Heroicon name: mini/exclamation-circle -->
                                    <svg class="tw-h-5 tw-w-5 tw-text-red-500" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                        <path fill-rule="evenodd" d="M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-8-5a.75.75 0 01.75.75v4.5a.75.75 0 01-1.5 0v-4.5A.75.75 0 0110 5zm0 10a1 1 0 100-2 1 1 0 000 2z" clip-rule="evenodd"></path>
                                    </svg>
                                </div>
                                @enderror
                            </div>
                            @error('postal_code')
                            <p class="tw-m-0 tw-mt-2 tw-text-sm tw-text-red-600" id="postal_code-error">{{ $message }}</p>
                            @enderror
                        </div>

                        <div class="tw-col-span-full">
                            <div class="tw-relative tw-flex tw-items-start">
                                <div class="tw-flex tw-h-6 tw-items-center">
                                    <input id="is_default" wire:model="is_default" aria-describedby="is_default-description" name="is_default" type="checkbox" class="tw-h-4 tw-w-4 tw-rounded tw-border-gray-300 tw-text-keppel-600 focus:tw-ring-keppel-600">
                                </div>
                                <div class="tw-ml-3 tw-leading-6">
                                    <label for="is_default" class="tw-m-0 tw-font-medium tw-text-sm tw-text-gray-900">Default</label>
                                    <p id="is_default-description" class="tw-m-0 tw-text-sm tw-text-gray-500 ">Use this when starting a new order</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="tw-rounded-b-lg tw-bg-gray-50 tw-px-4 tw-py-3 sm:tw-flex sm:tw-flex-row-reverse sm:tw-px-6">
            <button type="submit" wire:loading.attr="disabled" wire:target="submit" class="tw-inline-flex tw-w-full tw-justify-center tw-rounded-md tw-bg-theme-action-color tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color/70 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-action-color sm:tw-ml-3 sm:tw-w-auto">
                <span wire:loading.remove wire:target="submit">Save</span>
                <svg wire:loading.inline wire:target="submit" class="tw-animate-spin tw-h-4 tw-w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
            </button>
            <button type="button" wire:loading.attr="disabled" wire:target="submit" wire:click="close" class="tw-mt-3 tw-inline-flex tw-w-full tw-justify-center tw-rounded-md tw-bg-white tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-gray-900 tw-shadow-sm tw-ring-1 tw-ring-inset tw-ring-gray-300 hover:tw-bg-gray-50 sm:tw-mt-0 sm:tw-w-auto">
                Cancel
            </button>
        </div>
    </form>
</x-theme::modal>

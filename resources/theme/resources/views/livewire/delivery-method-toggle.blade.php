@php
    /** @var \App\Models\Pickup $current_delivery_method */
    /** @var App\Models\RecurringOrder|null $subscription */
    /** @var App\Models\Order|null $order */
    /** @var App\Services\Geocoding\GeocodedAddress|null $geocoded_address */
@endphp
<div
        x-data="{
            open: @entangle('open').live,
            searching: @entangle('searching'),
            selected_delivery_method_id: @entangle('selected_delivery_method_id'),
            delivery_method_type: @entangle('delivery_method_type'),
            delivery_is_available: @entangle('delivery_is_available'),
            selected_address_id: @entangle('selected_address_id'),
            view() { this.open = true; },
            close() { this.open = false; this.loadingSidePanel = false; $wire.close(); },
            toggle() { this.open ? this.close() : this.view(); },
            loadingSidePanel: false
        }"
        @click.away="open ? close() : null"
        class="tw-w-full tw-block tw-text-left sm:tw-w-auto sm:tw-relative"
>
    <div class="tw-w-full">
        <button type="button" @click="toggle()" class="tw-relative tw-w-full tw-group tw-py-1.5 tw-rounded-lg tw-flex tw-items-center tw-gap-x-2 tw-text-sm tw-text-theme-main-navigation-link-color lg:tw-py-1" :aria-expanded="open">
            <span class="tw-absolute tw-z-0 tw-top-0 tw-left-0 tw-w-full tw-h-full tw-pointer-events-none tw-bg-gray-700 tw-rounded-lg tw-bg-opacity-0 lg:group-hover:tw-bg-opacity-10"></span>

            @if(is_null($current_delivery_method))
                <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="tw-size-4">
                    <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"></path>
                </svg>
                <span class="tw-flex-1 tw-text-left tw-block">
                    Confirm we deliver to you</span>
            @else
                <div class="tw-flex-1 tw-flex tw-items-center tw-space-x-2">

                    <div class="tw-text-white">
                        <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="tw-w-4 tw-h-4">
                            <path stroke-linecap="round" stroke-linejoin="round" d="M15 10.5a3 3 0 1 1-6 0 3 3 0 0 1 6 0Z"/>
                            <path stroke-linecap="round" stroke-linejoin="round" d="M19.5 10.5c0 7.142-7.5 11.25-7.5 11.25S4.5 17.642 4.5 10.5a7.5 7.5 0 1 1 15 0Z"/>
                        </svg>

                    </div>
                    <div class="tw-flex-1 tw-flex tw-items-center tw-space-x-1">
                        <div class="tw-block">
                            <div class="tw-text-left tw-flex tw-items-center tw-space-x-1">
                                <span class="tw-block tw-text-xs">{{ $current_delivery_method->isPickup()  ? 'Pickup in' : 'Ship to' }}</span>
                                <span class="tw-block tw-text-xs tw-font-semibold">{{ $postal_code }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            @endif

            <svg class="tw-h-4 tw-w-4 tw-transform tw-duration-100" :class="{ 'tw-rotate-180': open }" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                <path fill-rule="evenodd" d="M5.23 7.21a.75.75 0 011.06.02L10 11.168l3.71-3.938a.75.75 0 111.08 1.04l-4.25 4.5a.75.75 0 01-1.08 0l-4.25-4.5a.75.75 0 01.02-1.06z" clip-rule="evenodd"></path>
            </svg>
        </button>
    </div>

    <!--
      Dropdown menu, show/hide based on menu state.
    -->
    <div x-cloak
         x-show="open"
         x-transition:enter="tw-transition tw-ease-out tw-duration-100"
         x-transition:enter-start="tw-transform tw-opacity-0 tw-scale-95"
         x-transition:enter-end="tw-transform tw-opacity-100 tw-scale-100"
         x-transition:leave="tw-transition tw-ease-in tw-duration-75"
         x-transition:leave-start="tw-transform tw-opacity-100 tw-scale-100"
         x-transition:leave-end="tw-transform tw-opacity-0 tw-scale-95"
         class="tw-absolute tw-z-30 tw-left-2 tw-right-2 tw-origin-top-left tw-rounded-lg tw-overflow-hidden tw-bg-white tw-shadow-lg focus:tw-outline-none sm:tw-left-auto sm:tw-right-auto sm:tw-w-80 lg:tw-mt-1.5"
         role="menu"
         aria-orientation="vertical"
         aria-labelledby="menu-button"
         tabindex="-1"
    >
        @if($open)
            @if(is_null($order) && is_null($subscription))
                <div class="tw-px-4 tw-py-5 sm:tw-p-6" role="none">
                    <div x-show="delivery_method_type === 'delivery'" class="tw-relative tw-w-full" x-trap="open">

                        @if(!is_null(auth()->user()) && $has_addresses)
                            @if(is_null($addresses))
                                @php
                                    $current_address = $this->current_address;
                                @endphp
                                <div>
                                    <h3 class="tw-m-0 tw-text-base tw-font-semibold tw-leading-6 tw-text-gray-900">Shipping address</h3>
                                    <div class="tw-mt-2 tw-bg-gray-50 sm:tw-rounded-lg">
                                        <div class="tw-px-2 tw-py-3 sm:tw-p-4">

                                            <p class="tw-m-0 tw-block tw-text-sm tw-font-semibold tw-text-gray-900">
                                                {{ $current_address?->street }}
                                            </p>
                                            @if($current_address?->location?->street_2 ?? false)
                                                <p class="tw-m-0 tw-block tw-text-xs tw-font-normal tw-text-gray-500">
                                                    {{ $current_address?->location->street_2 }}
                                                </p>
                                            @endif
                                            <p class="tw-m-0 tw-block tw-text-xs tw-font-normal tw-text-gray-500">
                                                {{ $current_address?->city }}, {{ $current_address?->state }} {{ $current_address?->postal_code }}
                                            </p>

                                            <div class="tw-mt-5">
                                                <button type="button"
                                                        wire:click="changeAddress"
                                                        class="tw-inline-flex tw-items-center tw-rounded-md tw-bg-white tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-gray-900 tw-shadow-sm tw-ring-1 tw-ring-inset tw-ring-gray-300 hover:tw-bg-gray-50"
                                                >
                                                    <span wire:loading.remove wire:target="changeAddress">Change address</span>
                                                    <svg wire:loading.inline wire:target="changeAddress" class="tw-animate-spin tw-h-4 tw-w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                        <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                        <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                                    </svg>
                                                </button>
                                            </div>
                                        </div>


                                    </div>
                                    <div class="tw-mt-2 ">
                                        <p class="tw-m-0 tw-text-xs tw-text-gray-500">Your shipping address is used to provide
                                            <span class="tw-font-semibold">the most up-to-date</span>
                                            product and delivery information.
                                        </p>
                                    </div>
                                </div>
                            @else
                                <h3 class="tw-text-base tw-font-semibold tw-leading-6 tw-text-gray-900">Shipping addresses</h3>
                                <fieldset class="tw-m-0 tw-mt-4 " aria-label="Shipping address">
                                    <div class="tw-space-y-2 tw-rounded-md tw-bg-white">
                                        @foreach($addresses as $address)
                                            @php /** @var \App\Models\Address $address */ @endphp

                                            <label aria-label="{{ $address->formatted() }}"
                                                   class="tw-relative tw-block tw-group tw-cursor-pointer tw-rounded-lg tw-border tw-pr-4 tw-py-2.5 tw-shadow-sm focus:tw-outline-none sm:tw-flex sm:tw-justify-between" :class="{ 'tw-bg-theme-action-color/10 focus:tw-border-theme-action-color focus:tw-ring-2 focus:tw-ring-theme-action-color': selected_address_id === {{ $address->id }}, 'tw-bg-white hover:tw-bg-gray-50 focus:tw-border-gray-300': selected_address_id !== {{ $address->id }} }">
                                                <!-- Not Checked: "hidden" -->
                                                <svg class="tw-absolute tw-top-0 tw-left-0 tw-mt-3 tw-ml-3 tw-h-5 tw-w-5 tw-transition" :class="{ 'tw-text-theme-action-color': selected_address_id === {{ $address->id }}, 'tw-hidden group-hover:tw-block group-hover:tw-text-gray-300': selected_address_id !== {{ $address->id }} }" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.857-9.809a.75.75 0 00-1.214-.882l-3.483 4.79-1.88-1.88a.75.75 0 10-1.06 1.061l2.5 2.5a.75.75 0 001.137-.089l4-5.5z" clip-rule="evenodd"/>
                                                </svg>
                                                <input id="address-{{ $address->id }}" x-model.number="selected_address_id" value="{{ $address->id }}" name="delivery-method" type="radio" @checked($address->id === $current_delivery_method?->id)  class="tw-sr-only" aria-labelledby="address-{{ $address->id }}-label" aria-describedby="address-{{ $address->id }}-description-0">
                                                <span class="tw-pl-8 tw-flex tw-items-center">
                                                    <span class="tw-pl-2 tw-flex tw-flex-col tw-text-sm">
                                                        <span id="address-{{ $address->id }}-description-0">
                                                            <p class="tw-m-0 tw-font-semibold tw-text-sm tw-text-gray-900">{{ $address->street }}</p>
                                                            @if($address->location?->street_2)
                                                                <p class="tw-m-0 tw-font-normal tw-text-xs tw-text-gray-500">{{ $address->location->street_2 }}</p>
                                                            @endif
                                                            <p class="tw-m-0 tw-font-normal tw-text-xs tw-text-gray-500">{{ $address->city }},
                                                                {{ $address->state }} {{ $address->postal_code }}</p>
                                                        </span>
                                                    </span>
                                                </span>
                                                <span class="tw-pointer-events-none tw-absolute tw--inset-px tw-rounded-lg" :class="{ 'tw-border tw-border-theme-action-color tw-ring-2 tw-ring-theme-action-color': selected_address_id === {{ $address->id }}, 'tw-border-2 tw-border-transparent': selected_address_id !== {{ $address->id }} }" aria-hidden="true"></span>
                                            </label>
                                        @endforeach
                                    </div>
                                </fieldset>
                                <button type="button" @click="$dispatch('open-modal-add-address', { redirect_after_submit: false })" class="tw-mt-2 tw-relative tw-block tw-w-full tw-rounded-lg tw-transition tw-border-2 tw-border-dashed tw-border-gray-300 tw-px-3 tw-py-4 tw-text-center hover:tw-border-gray-500 hover:tw-bg-gray-100 focus:tw-outline-none">
                                    <span class="tw-block tw-text-sm tw-font-semibold tw-text-gray-900">Add a new address</span>
                                </button>

                                @if($addresses?->isNotEmpty() ?? false)
                                    <button type="button"
                                            wire:click="confirmAddressChange"
                                            :disabled="selected_address_id === null"
                                            class="tw-mt-4 tw-block tw-w-full tw-px-3 tw-py-2 tw-rounded-md tw-text-center tw-text-sm tw-font-semibold tw-text-white tw-shadow-sm focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-action-color"
                                            :class="{ 'tw-bg-theme-action-color/70 tw-cursor-not-allowed': selected_address_id === null, 'tw-bg-theme-action-color hover:tw-bg-theme-action-color/70': selected_address_id !== null }"
                                    >
                                        <span wire:loading.remove wire:target="confirmAddressChange">Save</span>
                                        <svg wire:loading.inline wire:target="confirmAddressChange" class="tw-animate-spin tw-h-4 tw-w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                            <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                            <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                        </svg>
                                    </button>
                                @endif
                            @endif
                        @else
                            <div x-show="delivery_is_available === true" class="tw-pt-5 tw-relative tw-bg-white tw-w-full sm:tw-pt-6">
                                <button type="button" @click="close()" class="tw-absolute tw-top-0 tw-right-0 tw-rounded-md tw-text-gray-300 hover:tw-text-gray-500 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-white">
                                    <span class="tw-absolute tw--inset-2.5"></span>
                                    <span class="tw-sr-only">Close</span>
                                    <svg class="tw-h-6 tw-w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                                    </svg>
                                </button>
                                <div>
                                    <div class="tw-mx-auto tw-flex tw-h-12 tw-w-12 tw-items-center tw-justify-center tw-rounded-full tw-bg-theme-brand-color/20">
                                        <svg class="tw-h-6 tw-w-6 tw-text-theme-brand-color" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M4.5 12.75l6 6 9-13.5"></path>
                                        </svg>
                                    </div>
                                    <div class="tw-mt-3 tw-text-center sm:tw-mt-5">
                                        <h3 class="tw-text-base tw-font-semibold tw-leading-6 tw-text-gray-900" id="modal-title">
                                            Delivery available</h3>
                                        <div class="tw-mt-2">
                                            @if($geocoded_city && $postal_code)
                                                <p class="tw-m-0 tw-text-sm tw-text-gray-500">Great! We're going to deliver your next order to
                                                    <strong>{{ $geocoded_city }}, {{ $postal_code }}</strong>!
                                                </p>
                                            @else
                                                <p class="tw-m-0 tw-text-sm tw-text-gray-500">Great! We're going to deliver your next order to
                                                    <strong>{{ $postal_code }}</strong>!</p>
                                            @endif
                                            {{--                                    <p class="tw-m-0 tw-mt-1 tw-text-sm tw-font-semibold tw-text-theme-action-color">Start--}}
                                            {{--                                        shopping!</p>--}}
                                        </div>
                                    </div>
                                </div>
                                <div class="tw-mt-5 sm:tw-mt-6">
                                    <button type="button" @click="delivery_is_available = null" class="tw-mx-auto tw-px-4 tw-border tw-border-gray-200 tw-rounded-md tw-flex tw-items-center tw-justify-center tw-py-2 tw-text-sm tw-font-semibold tw-leading-6 tw-text-gray-500 hover:tw-text-gray-600 hover:tw-bg-gray-50">
                                        Change postal code
                                    </button>
                                </div>
                            </div>

                            <div x-show="delivery_is_available === false">
                                <div class="tw-pt-5 tw-relative tw-bg-white tw-w-full sm:tw-pt-6">
                                    <button type="button" @click="close()" class="tw-absolute tw-top-0 tw-right-0 tw-rounded-md tw-text-gray-300 hover:tw-text-gray-500 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-white">
                                        <span class="tw-absolute tw--inset-2.5"></span>
                                        <span class="tw-sr-only">Close</span>
                                        <svg class="tw-h-6 tw-w-6" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" aria-hidden="true">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18L18 6M6 6l12 12"></path>
                                        </svg>
                                    </button>
                                    <div class="tw-mx-auto tw-flex tw-h-12 tw-w-12 tw-items-center tw-justify-center tw-rounded-full tw-bg-theme-action-color/20">
                                        <svg class="tw-h-6 tw-w-6 tw-text-theme-action-color" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M6 18 18 6M6 6l12 12"/>
                                        </svg>
                                    </div>
                                    <div class="tw-mt-3 tw-text-center sm:tw-mt-5">
                                        <h3 class="tw-text-base tw-font-semibold tw-leading-6 tw-text-gray-900" id="modal-title">
                                            Delivery unavailable</h3>
                                        <div class="tw-mt-2">
                                            @if($geocoded_city && $postal_code)
                                                <p class="tw-m-0 tw-text-sm tw-text-gray-500">Sorry, we don't yet deliver
                                                    to {{ $geocoded_city }}, {{ $postal_code }}!
                                                </p>
                                            @else
                                                <p class="tw-m-0 tw-text-sm tw-text-gray-500">Sorry, we couldn't find that postal
                                                    code!</p>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                                <div class="tw-mt-5 sm:tw-mt-6">
                                    <button type="button" @click="delivery_is_available = null" class="tw-w-full tw-flex tw-items-center tw-justify-center tw-py-2 tw-text-sm tw-font-semibold tw-leading-6 tw-text-theme-link-color hover:tw-text-theme-link-color/70">
                                        Try another postal code
                                    </button>
                                </div>
                            </div>

                            <div x-show="delivery_is_available === null">
                                <div class="tw-flex tw-items-center">
                                    <div class="tw-text-theme-brand-color">
                                        <svg class="tw-w-6 tw-h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                            <path stroke-linecap="round" stroke-linejoin="round" d="M8.25 18.75a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h6m-9 0H3.375a1.125 1.125 0 0 1-1.125-1.125V14.25m17.25 4.5a1.5 1.5 0 0 1-3 0m3 0a1.5 1.5 0 0 0-3 0m3 0h1.125c.621 0 1.129-.504 1.09-1.124a17.902 17.902 0 0 0-3.213-9.193 2.056 2.056 0 0 0-1.58-.86H14.25M16.5 18.75h-2.25m0-11.177v-.958c0-.568-.422-1.048-.987-1.106a48.554 48.554 0 0 0-10.026 0 1.106 1.106 0 0 0-.987 1.106v7.635m12-6.677v6.677m0 4.5v-4.5m0 0h-12"/>
                                        </svg>

                                    </div>
                                    <p class="tw-m-0 tw-ml-2 tw-text-base tw-font-semibold">Check delivery availability</p>
                                </div>
                                <div class="tw-mt-4 tw-w-full ">
                                    <div class="tw-flex tw-items-center tw-space-x-2">
                                        <div class="tw-flex-1">
                                            <label for="postal_code" class="tw-sr-only">Postal code</label>
                                            <input type="text" wire:model="postal_code" wire:keyup.prevent.enter="searchPostalCode" name="postal_code" id="postal_code" placeholder="Postal code" autocomplete="postal_code" class="tw-block tw-w-full tw-rounded-md tw-border-0 tw-py-1.5 tw-text-gray-900 tw-shadow-sm tw-ring-1 tw-ring-inset tw-ring-gray-300 placeholder:tw-text-gray-400 focus:tw-ring-2 focus:tw-ring-inset focus:tw-ring-theme-action-color sm:tw-text-sm sm:tw-leading-6">
                                        </div>
                                        <button type="button" wire:click="searchPostalCode" class="tw-inline-flex tw-items-center tw-justify-center tw-rounded-md tw-bg-theme-action-color tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-white tw-shadow-sm hover:tw-bg-theme-action-color/70 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-action-color ">
                                            <span wire:loading.remove wire:target="searchPostalCode">Check</span>
                                            <svg wire:loading.inline wire:target="searchPostalCode" class="tw-animate-spin tw-h-5 tw-w-5 tw-flex-none tw-text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                                                <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                                                <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                                            </svg>
                                        </button>
                                    </div>

                                    <p class="tw-m-0 tw-mt-2 tw-text-xs tw-text-gray-500 tw-text-center">Your postal code helps
                                        us
                                        to provide
                                        <span class="tw-font-semibold">the most up-to-date</span>
                                        product and delivery information.
                                    </p>
                                </div>
                            </div>
                        @endif
                        @if(is_null($addresses))
                            <p class="tw-m-0 tw-mt-3 tw-pt-3 tw-border-t tw-border-gray-200 tw-text-center tw-text-sm tw-text-gray-500">
                                Nearby?
                                <!-- space -->
                                <button wire:click="setTypeAsPickup" class="tw-font-semibold tw-leading-6 tw-text-theme-link-color hover:tw-text-theme-link-color/70">
                                    Pickup at our Indiana farm
                                </button>
                            </p>
                        @endif
                    </div>

                    <div>
                        <div x-show="delivery_method_type === 'pickup'"
                                {{--                            type="button"--}}
                                {{--                            @click="$dispatch('openPanel', { title: 'Delivery Method', component: 'theme.address-side-panel', params: { delivery_method_type: 'pickup', postal_code: @js($current_delivery_method?->zip ?? auth()->user()?->zip) } }); loadingSidePanel = true"--}}
                                {{--                         class="tw-p-4 tw-bg-white tw-rounded-md tw-w-full tw-flex tw-items-center tw-justify-between  hover:tw-bg-gray-50"--}}
                        >
                            <div class="tw-flex tw-min-w-0 tw-gap-x-2">
                                <div class="tw-min-w-0 tw-flex-auto tw-text-left">
                                    <div class="tw-flex">
                                        <div class="tw-text-theme-brand-color">
                                            <svg class="tw-w-6 tw-h-6" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                <path stroke-linecap="round" stroke-linejoin="round" d="M13.5 21v-7.5a.75.75 0 0 1 .75-.75h3a.75.75 0 0 1 .75.75V21m-4.5 0H2.36m11.14 0H18m0 0h3.64m-1.39 0V9.349M3.75 21V9.349m0 0a3.001 3.001 0 0 0 3.75-.615A2.993 2.993 0 0 0 9.75 9.75c.896 0 1.7-.393 2.25-1.016a2.993 2.993 0 0 0 2.25 1.016c.896 0 1.7-.393 2.25-1.015a3.001 3.001 0 0 0 3.75.614m-16.5 0a3.004 3.004 0 0 1-.621-4.72l1.189-1.19A1.5 1.5 0 0 1 5.378 3h13.243a1.5 1.5 0 0 1 1.06.44l1.19 1.189a3 3 0 0 1-.621 4.72M6.75 18h3.75a.75.75 0 0 0 .75-.75V13.5a.75.75 0 0 0-.75-.75H6.75a.75.75 0 0 0-.75.75v3.75c0 .414.336.75.75.75Z"/>
                                            </svg>
                                        </div>
                                        <div class="tw-ml-3">
                                            <p class="tw-m-0 tw-text-base tw-font-semibold">{{ $current_delivery_method?->present()->title() }}</p>
                                            <p class="tw-m-0 tw-mt-1 tw-flex tw-text-sm tw-text-gray-500 tw-truncate">
                                                {{ $current_delivery_method?->street }}
                                            </p>
                                            <p class="tw-m-0 tw-flex tw-text-sm tw-text-gray-500 tw-truncate">
                                                {{ $current_delivery_method?->city }},
                                                {{ $current_delivery_method?->state }} {{ $current_delivery_method?->zip }}
                                            </p>

                                        </div>
                                    </div>

                                </div>
                            </div>
                            <p class="tw-m-0 tw-mt-3 tw-pt-3 tw-border-t tw-border-gray-200 tw-text-center tw-text-sm tw-text-gray-500">
                                Need it shipped?
                                <!-- space -->
                                <button wire:click="setTypeAsDelivery" class="tw-font-semibold tw-leading-6 tw-text-theme-link-color hover:tw-text-theme-link-color/70">
                                    Check availability
                                </button>
                            </p>

                            {{--                        <div class="tw-shrink-0">--}}
                            {{--                            <svg x-show="! loadingSidePanel && delivery_method_type === 'pickup'" class="tw-h-5 tw-w-5 tw-flex-none tw-text-gray-300" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">--}}
                            {{--                                <path fill-rule="evenodd" d="M7.21 14.77a.75.75 0 01.02-1.06L11.168 10 7.23 6.29a.75.75 0 111.04-1.08l4.5 4.25a.75.75 0 010 1.08l-4.5 4.25a.75.75 0 01-1.06-.02z" clip-rule="evenodd"></path>--}}
                            {{--                            </svg>--}}
                            {{--                            <svg x-show="loadingSidePanel && delivery_method_type === 'pickup'" class="tw-animate-spin tw-h-5 tw-w-5 tw-flex-none tw-text-gray-300" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">--}}
                            {{--                                <circle class="tw-opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>--}}
                            {{--                                <path class="tw-opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>--}}
                            {{--                            </svg>--}}
                            {{--                        </div>--}}
                        </div>
                    </div>
                </div>
            @else
                <div class="tw-px-4 tw-py-3 tw-bg-white tw-rounded-lg" role="none">
                    @if( ! is_null($order))
                        <p class="tw-m-0 tw-font-semibold tw-text-gray-900">Order</p>

                        <dl class="tw-m-0 tw-mt-2 tw-text-sm">
                            <div class="tw-flex tw-items-center tw-justify-between">
                                <dt class="tw-font-normal tw-text-gray-500">Order number&nbsp;</dt>
                                <dd class="tw-font-medium">{{ $order->id }}</dd>
                            </div>

                            <div class="tw-flex tw-items-center tw-justify-between">
                                <dt class="tw-font-normal tw-text-gray-500">{{ $order->pickup->isPickup() ? 'Pickup date' : 'Delivery date' }}
                                    &nbsp;
                                </dt>
                                <dd class="tw-font-medium tw-text-gray-900">
                                    <time datetime="{{ $order->pickup_date->format('Y-m-d') }}">{{ $order->pickup_date->format('M j, Y') }}</time>
                                </dd>
                            </div>
                            <div class="tw-flex tw-items-center tw-justify-between">
                                <dt class="tw-font-normal tw-text-gray-500">Edit until&nbsp;</dt>
                                <dd class="tw-font-medium tw-text-gray-900">
                                    <time datetime="{{ $order->deadlineDatetime()->format('Y-m-d H:i:s') }}">{{ $order->deadlineDatetime()->format('M j, Y') }}</time>
                                </dd>
                            </div>
                            <div class="tw-mt-4">
                                <dt class="tw-font-normal tw-text-gray-500">{{ $order->pickup->isPickup() ? 'Pickup' : 'Shipping' }}
                                    address&nbsp;
                                </dt>
                                @if($order->pickup->isPickup())
                                    <dd class="tw-font-medium tw-text-gray-900">{{ $order->pickup->display_name ?? $order->pickup->title }}
                                        <br/>{{ $order->pickup->street }} @if($order->pickup->street_2)
                                            , {{ $order->pickup->street_2 }}
                                        @endif
                                        <br/>{{ $order->pickup->city }}
                                        , {{ $order->pickup->state }} {{ $order->pickup->zip }}
                                    </dd>
                                @else
                                    <dd class="tw-font-medium tw-text-gray-900">{{ $order->customer_first_name }} {{ $order->customer_last_name }}
                                        <br/>{{ $order->shipping_street }} @if($order->shipping_street_2)
                                            <br/> {{ $order->shipping_street_2 }}
                                        @endif
                                        <br/>{{ $order->shipping_city }}
                                        , {{ $order->shipping_state }} {{ $order->shipping_zip }}
                                    </dd>
                                @endif
                            </div>
                        </dl>
                        <div class="tw-mt-4 tw-border-t tw-border-gray-200 tw-pt-2">
                            <a href="{{ route('customer.orders.show', [$order->id]) }}" class="tw-text-sm tw-font-medium tw-text-theme-link-color hover:tw-text-theme-link-color/70">
                                View details
                                <span aria-hidden="true"> →</span>
                            </a>
                        </div>

                    @elseif(!is_null($subscription))
                        <p class="tw-m-0 tw-font-semibold tw-text-gray-900">Subscription</p>
                        <dl class="tw-m-0 tw-mt-2 tw-text-sm">
                            <div class="tw-flex tw-items-center tw-justify-between">
                                <dt class="tw-font-normal tw-text-gray-500">Next delivery&nbsp;</dt>
                                <dd class="tw-font-medium tw-text-gray-900">
                                    <time datetime="{{ $subscription->ready_at->format('Y-m-d') }}">{{ $subscription->ready_at->format('D, M jS') }}</time>
                                </dd>
                            </div>

                            <div class="tw-flex tw-items-center tw-justify-between">
                                <dt class="tw-font-normal tw-text-gray-500">Frequency&nbsp;</dt>
                                <dd class="tw-font-medium  tw-text-gray-900">
                                    {{ $subscription->formattedReorderFrequency() }}
                                </dd>
                            </div>

                            <div class="tw-mt-4">
                                <dt class="tw-font-normal tw-text-gray-500">{{ $subscription->fulfillment->isPickup() ? 'Pickup' : 'Shipping' }}
                                    address&nbsp;
                                </dt>
                                @if($subscription->fulfillment->isPickup())
                                    <dd class="tw-font-medium tw-text-gray-900">{{ $subscription->fulfillment->display_name ?? $subscription->fulfillment->title }}
                                        <br/>{{ $subscription->fulfillment->street }} @if($subscription->fulfillment->street_2)
                                            , {{ $subscription->fulfillment->street_2 }}
                                        @endif
                                        <br/>{{ $subscription->fulfillment->city }}
                                        , {{ $subscription->fulfillment->state }} {{ $subscription->fulfillment->zip }}
                                    </dd>
                                @else
                                    @php
                                        $address = $subscription->customer->defaultShippingAttributes();
                                    @endphp

                                    <dd class="tw-font-medium tw-text-gray-900">{{ $subscription->customer->full_name }}
                                        <br/>{{ $address['street'] }} @if($address['street_2'])
                                            <br/> {{ $address['street_2'] }}
                                        @endif
                                        <br/>{{ $address['city'] }}, {{ $address['state'] }} {{ $address['postal_code'] }}
                                    </dd>
                                @endif
                            </div>
                        </dl>
                        <div class="tw-mt-4 tw-border-t tw-border-gray-200 tw-pt-2">
                            <a href="{{ route('customers.recurring.edit') }}" class="tw-text-sm tw-font-medium tw-text-theme-link-color hover:tw-text-theme-link-color/70">
                                Manage subscription
                                <span aria-hidden="true"> →</span>
                            </a>
                        </div>
                    @endif
                </div>
            @endif
        @endif
    </div>
</div>


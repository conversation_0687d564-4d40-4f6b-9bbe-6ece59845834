@extends('theme::_layouts.main', [
    'pageTitle' => 'Create Your Account',
    'showLogo' => true,
    'messageType' => 'registration_message',
    'show_tripwire' => false,
])

@section('pageMetaTags')
    <!-- Twitter Card data -->
    <meta name="twitter:card" content="summary">
    <meta name="twitter:title" content="Create Your Account">
    <meta name="twitter:description" content="{{ setting('farm_name') }}">
    <meta name="twitter:image" content="{{ setting('share_image', theme('logo_src')) }}">

    <!-- Open Graph data -->
    <meta property="og:title" content="Create Your Account"/>
    <meta property="og:type" content="article"/>
    <meta property="og:url" content="{{ request()->fullUrl() }}"/>
    <meta property="og:image" content="{{ setting('share_image', theme('logo_src')) }}"/>
    <meta property="og:description" content="{{ setting('farm_name') }}"/>
    <meta property="og:site_name" content="{{ setting('farm_name') }}"/>
@endsection

@section('content')
    <livewire:theme.authentication.registration/>
@endsection

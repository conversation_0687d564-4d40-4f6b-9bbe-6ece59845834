@php
    /** @var App\Contracts\Cartable $cart */
@endphp
<table class="checkout_summaryTable mb-3">
    <tr>
        <td class="text-left">Items:</td>
        <td class="text-right">&#36;{{ money($cart->cartSubtotal()) }}</td>
    </tr>
    <tr>
        <td class="text-left">Fees:</td>
        <td class="text-right">&#36;{{ money($cart->cartLocationFeeTotal()) }}</td>
    </tr>
    <tr>
        <td class="text-left">Tax:</td>
        <td class="text-right">&#36;{{ money($cart->cartTaxTotal()) }}</td>
    </tr>

    @foreach($cart->cartCoupons() as $coupon)
        @php /** @var \App\Cart\Coupon $coupon */ @endphp
        <tr class="checkout_summaryTable__coupon">
            <td class="text-left">Coupon:</td>
            <td class="text-right">
                <i class="fa fa-tag"></i> {{ $coupon->name }}
                <div class="text-muted">-&#36;{{ money($coupon->amount) }}</div>
            </td>
        </tr>
    @endforeach

    @if($cart->cartStoreCreditTotal() > 0)
        <tr>
            <td class="text-left">Credit:</td>
            <td class="text-right">-&#36;{{ money($cart->cartStoreCreditTotal()) }}</td>
        </tr>
    @endif

    <tr class="checkout_orderTotal">
        <td class="text-left">&ast;Total:</td>
        <td class="text-right">&#36;<span class="order__total">{{ money($cart->cartTotal()) }}</span></td>
    </tr>
</table>

@extends('theme::_layouts.main', [
	'pageTitle' => 'Recipes'
])

@section('content')

    <section class="pageContainer recipesIndex">
        <h1 class="recipesIndex__heading h1">Recipes</h1>

        <form x-data="{ submit() { $el.submit(); } }" class="form-inline text-right" action="{{ route('recipes.index') }}" method="GET">
            <label>Filter by tag:</label>
            <x-theme::form.recipe-tag-select
                    class="form-control"
                    name="tag"
                    id="recipeFilterSelect"
                    placeholder="Show All"
                    :selected="request('tag')"
                    x-on:change="submit"
            />
        </form>
        <div class="tw-reset">
            <div class="tw-mx-auto tw-mt-12 tw-grid tw-max-w-2xl tw-grid-cols-1 tw-gap-x-8 tw-gap-y-20 lg:tw-mx-0 lg:tw-max-w-none lg:tw-grid-cols-3 blog__grid">
                @foreach($recipes as $recipe)
                    <x-theme::recipe-card :recipe="$recipe"/>
                @endforeach
                @if($recipes->isEmpty())
                    <p>There were no recipes found.</p>
                @endif
            </div>
        </div>
        <div class="text-center">
            {!! $recipes->appends(['q' => request()->get('q'), 'tag' => request()->get('tag')])->render() !!}
        </div>
    </section>
@endsection

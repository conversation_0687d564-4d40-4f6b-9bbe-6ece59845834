<div>
    @php
        /** @var \App\Models\RecurringOrder $subscription */
        /** @var \App\Models\Order|null $in_transit_order */

        $delivery_method = $subscription->fulfillment;
        $subscription_settings = app(App\Services\SubscriptionSettingsService::class);
        $current_order = null;

        if ( ! $subscription->trashed()) {
            $current_order = $subscription->currentOrder;
        }

        if (is_null($in_transit_order) && ! is_null($current_order) && ! $current_order->isNew()) {
            $in_transit_order = $current_order;
        }

        if ( ! is_null($in_transit_order)) {
            $next_deadline_date = $in_transit_order->deadlineDatetime();
            $next_delivery_date = $in_transit_order->pickupDatetime();

            $order_window = $subscription->nextOrderWindow();
            $soonest_order_window = $order_window;
            $upcoming_delivery_date = $order_window?->deliveryDatetime();
            $upcoming_deadline_date = $order_window?->deadlineDatetime();
        } elseif ( ! is_null($current_order)) {
            $next_deadline_date = $current_order->deadlineDatetime();
            $next_delivery_date = $current_order->pickupDatetime();

            $order_window = $subscription->nextOrderWindow($next_delivery_date);
            $soonest_order_window = $subscription->fulfillment->activeOrderWindow();
            $upcoming_delivery_date = $order_window?->deliveryDatetime();
        } else {
            $order_window = $subscription->nextOrderWindow();
            $next_deadline_date = $order_window?->deadlineDatetime();
            $next_delivery_date = $order_window?->deliveryDatetime();

            $order_window = $subscription->nextOrderWindow($next_delivery_date);
            $soonest_order_window = $subscription->fulfillment->activeOrderWindow();
            $upcoming_delivery_date = $order_window?->deliveryDatetime();
        }

        $selected_promo_item = $subscription->promoItem();
    @endphp

    <div x-data="{ currentTab: 'next', editingFrequency: false }" class="tw-reset tw-bg-white">
        <div class="tw-mx-auto tw-max-w-2xl tw-px-4 tw-pt-8 tw-pb-12 sm:tw-px-6 lg:tw-max-w-7xl lg:tw-px-8">

            <div class="tw--ml-1 tw-flex tw-flex-center tw-text-theme-link-color/70">
                <svg class="tw-w-5 tw-h-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M12.79 5.23a.75.75 0 01-.02 1.06L8.832 10l3.938 3.71a.75.75 0 11-1.04 1.08l-4.5-4.25a.75.75 0 010-1.08l4.5-4.25a.75.75 0 011.06.02z" clip-rule="evenodd"/>
                </svg>
                <a href="{{ route('customer.profile') }}" class="tw-ml-2 tw-text-theme-link-color tw-no-underline">My account</a>
            </div>

            <main>
                <div class="tw-mt-8 tw-space-y-20 sm:tw-space-y-24">
                    <!-- Start Order information -->
                    <div>
                        <!-- Start Order tabs -->
                        <div class="tw-border-b tw-border-gray-200">

                            <div class="sm:tw-flex sm:tw-items-baseline">
                                <h3 class="tw-text-lg tw-font-semibold tw-leading-6 tw-text-gray-900">Orders</h3>
                                <div class="tw-mt-4 sm:tw-ml-10 sm:tw-mt-0">
                                    <nav class="tw--mb-px tw-flex tw-space-x-8">
                                        <a
                                                @click="currentTab = 'next'"
                                                class="tw-border-theme-brand-color/80 tw-text-theme-brand-color tw-no-underline tw-cursor-pointer tw-border-b-2 tw-px-1 tw-pb-4 tw-text-sm tw-font-medium"
                                                :class="{
                                                    'tw-border-theme-brand-color/80 tw-text-theme-brand-color': currentTab === 'next',
                                                    'tw-border-transparent tw-text-gray-500 hover:tw-border-gray-300 hover:tw-text-gray-700': currentTab === 'upcoming',
                                                }"
                                                :aria-current="currentTab === 'next' ? 'page' : false"
                                        >
                                            {{ $next_delivery_date?->format('M jS, Y') ?? ($delivery_method->isDeliveryZone() ? 'Next delivery' : 'Next pickup') }}
                                        </a>
                                        <a
                                                @click="currentTab = 'upcoming'"
                                                class="tw-border-transparent tw-text-gray-500 hover:tw-border-gray-300 hover:tw-text-gray-700 tw-no-underline tw-cursor-pointer tw-border-b-2 tw-px-1 tw-pb-4 tw-text-sm tw-font-medium"
                                                :class="{
                                                    'tw-border-theme-brand-color/80 tw-text-theme-brand-color': currentTab === 'upcoming',
                                                    'tw-border-transparent tw-text-gray-500 hover:tw-border-gray-300 hover:tw-text-gray-700': currentTab === 'next',
                                                }"
                                                :aria-current="currentTab === 'upcoming' ? 'page' : false"
                                        >
                                            {{ $upcoming_delivery_date?->format('M jS, Y') ?? ($delivery_method->isDeliveryZone() ? 'Upcoming delivery' : 'Upcoming pickup') }}
                                        </a>
                                    </nav>
                                </div>
                            </div>
                        </div>
                        <!-- End Order tabs -->

                        <!-- Start Next Order -->
                        <div x-show="currentTab === 'next'">
                            <div class="tw-pt-6 tw-bg-white">
                                <div class="sm:tw-flex sm:tw-items-baseline sm:tw-justify-between">
                                    <div class="tw-flex tw-items-center">
                                        <div>
                                            <div class="tw-w-12 tw-border-2 tw-border-gray-700 tw-rounded tw-flex tw-flex-col">
                                                <div class="tw-bg-gray-700 tw-text-white tw-text-center tw-text-xs">
                                                    {{  $next_delivery_date?->format('D') }}
                                                </div>
                                                <div class="tw-text-center tw-py-px">
                                                    <p class="tw-m-0 tw-font-semibold tw-text-base tw-leading-5">{{  $next_delivery_date?->format('j') }}</p>
                                                    <p class="tw-m-0 tw-text-xs">{{  $next_delivery_date?->format('M') }}</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="tw-pl-4">
                                            <h3 id="message-heading" class="tw-m-0 tw-text-xl tw-font-semibold tw-leading-6 tw-text-gray-900">
                                                Order @if( ! is_null($in_transit_order))
                                                    processed
                                                @elseif(! is_null($current_order))
                                                    placed
                                                @else
                                                    scheduled
                                                @endif
                                            </h3>
                                            @if($in_transit_order?->deadlineHasPassed() ?? true)
                                                <p class="tw-m-0 tw-mt-1 tw-truncate tw-text-sm tw-text-gray-500">
                                                    @if( ! is_null($in_transit_order))
                                                        Editing ended
                                                    @else
                                                        Edit until
                                                    @endif {{ $next_deadline_date?->format('M d | h:iA') ?? 'TBA' }}
                                                </p>
                                            @endif
                                        </div>
                                    </div>

                                    @if(is_null($in_transit_order))
                                        <div class="tw-mt-4 tw-flex tw-items-center tw-justify-between sm:tw-ml-6 sm:tw-mt-0 sm:tw-flex-shrink-0 sm:tw-justify-start">
                                            <div class="tw-relative sm:tw-ml-3 tw-inline-block tw-text-left">
                                                <div class="tw-flex tw-justify-between">
                                                    <div x-data="" class="tw-flex tw-items-center tw-space-x-2 tw-text-sm">
                                                        <button type="button" x-on:click="$dispatch('legacy-modal-opened', { id: 'getItSoonerModal' })" class="tw-flex tw-items-center tw-justify-center tw-rounded-md tw-border tw-border-gray-300 tw-bg-white tw-px-2.5 tw-py-2 tw-text-sm tw-font-medium tw-text-gray-700 tw-shadow-sm hover:tw-bg-gray-50 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-theme-action-color/50 focus:tw-ring-offset-2">
                                                            Send it now
                                                        </button>
                                                        <button type="button" x-on:click="$dispatch('legacy-modal-opened', { id: 'skipRecurringOrderModal' })" class="tw-flex tw-items-center tw-justify-center tw-rounded-md tw-border tw-border-gray-300 tw-bg-white tw-px-2.5 tw-py-2 tw-text-sm tw-font-medium tw-text-gray-700 tw-shadow-sm hover:tw-bg-gray-50 focus:tw-outline-none focus:tw-ring-2 focus:tw-ring-theme-action-color/50 focus:tw-ring-offset-2">
                                                            Skip
                                                        </button>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    @endif
                                </div>

                                @if( ! is_null($in_transit_order))
                                    <p class="tw-m-0 tw-mt-6 tw-text-base tw-text-gray-700">Your order is being
                                        processed. @if($in_transit_order?->deadlineHasPassed() ?? true)
                                            {{ $next_deadline_date?->format('D, F jS') ?? 'TBA' }} was the last day to make changes.
                                        @endif</p>
                                @elseif( ! is_null($current_order))
                                    <p class="tw-m-0 tw-mt-6 tw-text-base tw-text-gray-700">Your order has been placed. You can make changes to it
                                        until {{ $next_deadline_date?->format('D, F jS') ?? 'TBA' }}.</p>
                                @else
                                    <p class="tw-m-0 tw-mt-6 tw-text-base tw-text-gray-700">Your next order is scheduled. You can make changes to it
                                        until {{ $next_deadline_date?->format('D, F jS') ?? 'TBA' }}.</p>
                                @endif
                            </div>

                            <div class="tw-mt-6 lg:tw-grid lg:tw-grid-cols-12 lg:tw-items-start lg:tw-gap-x-12 xl:tw-gap-x-16">
                                <section aria-labelledby="cart-heading" class="tw-bg-white tw-h-full tw-pr-8 lg:tw-pr-0 lg:tw-col-span-7">
                                    @if($current_order?->hasUnfulfilledItems())
                                        <x-theme::unfulfilled-order-item-alert :order="$current_order"/>
                                    @endif
                                    <h2 id="cart-heading" class="tw-sr-only">Items in your subscription</h2>
                                    @if( ! is_null($current_order))
                                        <livewire:theme.order-items :order="$current_order"/>
                                    @else
                                        <livewire:theme.subscription-items :subscription="$subscription" :show_promotion_change="$available_promo_items->isNotEmpty()"/>
                                    @endif
                                </section>

                                <!-- Subscription summary -->
                                <section aria-labelledby="summary-heading" class="tw-bg-gray-50 tw-h-full tw-mt-8 lg:tw-col-span-5 lg:tw-mt-0">
                                    <div class="tw-rounded-lg tw-bg-gray-50 tw-px-4 tw-py-6 sm:tw-p-6 lg:tw-p-8">
                                        @if( ! is_null($current_order))
                                            <h2 id="summary-heading" class="tw-text-lg tw-font-medium tw-text-gray-900">Order #{{ $current_order->id }}
                                                summary</h2>

                                            <div class="tw-mt-6">
                                                <x-theme::order-summary :order="$current_order"/>

                                                <div class="tw-mt-6 tw-flex tw-items-center tw-justify-between tw-border-t tw-border-gray-200 tw-pt-4">
                                                    <dt class="tw-text-base tw-font-medium tw-text-gray-900">Total</dt>
                                                    <dd class="tw-text-base tw-font-medium tw-text-gray-900">${{ money($current_order->total) }}</dd>
                                                </div>
                                            </div>

                                            @if($current_order->canBeModified())
                                                <div class="tw-mt-6 tw-flex tw-justify-center tw-text-center tw-text-sm tw-text-gray-500">
                                                    <a href="{{ route('store.index') }}" class="tw-m-0 tw-w-full tw-no-underline">
                                                        <button type="button" class="tw-block tw-w-full tw-px-3 tw-py-2 tw-rounded-lg tw-text-sm tw-font-semibold btn btn-brand">
                                                            Add items to order
                                                            <span aria-hidden="true"> &rarr;</span>
                                                        </button>
                                                    </a>
                                                </div>
                                            @endif

                                        @else
                                            <h2 id="summary-heading" class="tw-text-lg tw-font-medium tw-text-gray-900">Subscription summary</h2>

                                            <div class="tw-mt-6">
                                                <x-theme::subscription-summary :subscription="$subscription"/>

                                                <div class="tw-mt-6 tw-flex tw-items-center tw-justify-between tw-border-t tw-border-gray-200 tw-pt-4">
                                                    <dt class="tw-text-base tw-font-medium tw-text-gray-900">Total</dt>
                                                    <dd class="tw-text-base tw-font-medium tw-text-gray-900">${{ money($subscription->total()) }}</dd>
                                                </div>
                                            </div>

                                            <div class="tw-mt-6 tw-flex tw-justify-center tw-text-center tw-text-sm tw-text-gray-500">
                                                <a href="{{ route('store.index') }}" class="tw-m-0">
                                                    <button type="button" @click="open = false" class="tw-font-medium tw-text-theme-link-color hover:tw-text-theme-link-color/70">
                                                        Add items to subscription
                                                        <span aria-hidden="true"> &rarr;</span>
                                                    </button>
                                                </a>
                                            </div>
                                        @endif
                                    </div>

                                    <livewire:theme.subscription-payment-method :subscription_id="$subscription->id" :current_order_id="$current_order?->id"/>

                                </section>
                            </div>
                        </div>
                        <!-- End Next Order -->

                        <!-- Start Upcoming Order -->
                        <div x-show="currentTab === 'upcoming'">
                            <div class="tw-pt-6 tw-bg-white">
                                <div class="sm:tw-flex sm:tw-items-baseline sm:tw-justify-between">
                                    <div class="tw-flex tw-items-center">
                                        <div>
                                            <div class="tw-w-12 tw-border-2 tw-border-gray-700 tw-rounded tw-flex tw-flex-col">
                                                <div class="tw-bg-gray-700 tw-text-white tw-text-center tw-text-xs">
                                                    {{  $upcoming_delivery_date?->format('D') }}
                                                </div>
                                                <div class="tw-text-center tw-py-px">
                                                    <p class="tw-m-0 tw-font-semibold tw-text-base tw-leading-5">{{  $upcoming_delivery_date?->format('j') }}</p>
                                                    <p class="tw-m-0 tw-text-xs">{{  $upcoming_delivery_date?->format('M') }}</p>
                                                </div>
                                            </div>
                                        </div>

                                        <div class="tw-pl-4">
                                            <h3 id="message-heading" class="tw-m-0 tw-text-xl tw-font-semibold tw-leading-6 tw-text-gray-900">
                                                Order scheduled
                                            </h3>
                                            <p class="tw-m-0 tw-mt-1 tw-truncate tw-text-sm tw-text-gray-500">
                                                @if( ! is_null($in_transit_order) && ! $in_transit_order->deadlineHasPassed() && ! $in_transit_order->isNew())
                                                    Edit starting {{ $next_deadline_date?->copy()->addSecond()->startOfHour()->format('M d | h:iA') ?? 'TBA' }}
                                                @elseif( ! is_null($in_transit_order))
                                                    Edit until {{ $upcoming_deadline_date?->copy()->addSecond()->startOfHour()->format('M d | h:iA') ?? 'TBA' }}
                                                @else
                                                    Edit starting {{ $next_deadline_date?->copy()->addSecond()->startOfHour()->format('M d | h:iA') ?? 'TBA' }}
                                                @endif
                                            </p>
                                        </div>
                                    </div>
                                </div>

                                @if( ! is_null($in_transit_order))
                                    <p class="tw-m-0 tw-mt-6 tw-text-base tw-text-gray-700">An order is scheduled. You can make changes to it
                                        until {{ $upcoming_deadline_date?->format('D, F jS') ?? 'TBA' }}.</p>
                                @else
                                    <p class="tw-m-0 tw-mt-6 tw-text-base tw-text-gray-700">An order is scheduled. You can make changes to it
                                        starting {{ $next_deadline_date?->copy()->addSecond()->startOfHour()->format('D, F jS') ?? 'TBA' }}.</p>
                                @endif
                            </div>
                            <div class="tw-pt-6 lg:tw-grid lg:tw-grid-cols-12 lg:tw-items-start lg:tw-gap-x-12 xl:tw-gap-x-16">
                                <section aria-labelledby="cart-heading" class="lg:tw-col-span-7">
                                    <h2 id="cart-heading" class="tw-sr-only">Items in your subscription</h2>
                                    <livewire:theme.subscription-items :subscription="$subscription" :show_promotion_change="$available_promo_items->isNotEmpty()" :can_be_modified=" ! is_null($in_transit_order) && $in_transit_order->deadlineHasPassed()"/>
                                </section>

                                <!-- Subscription summary -->
                                <section aria-labelledby="summary-heading" class="tw-bg-gray-50 tw-mt-8 lg:tw-col-span-5 lg:tw-mt-0">
                                    <div class="tw-rounded-lg tw-bg-gray-50 tw-px-4 tw-py-6 sm:tw-p-6 lg:tw-p-8">
                                        <h2 id="summary-heading" class="tw-text-lg tw-font-medium tw-text-gray-900">Subscription summary</h2>

                                        <div class="tw-mt-6">
                                            <x-theme::subscription-summary :subscription="$subscription"/>

                                            <div class="tw-mt-6 tw-flex tw-items-center tw-justify-between tw-border-t tw-border-gray-200 tw-pt-4">
                                                <dt class="tw-text-base tw-font-medium tw-text-gray-900">Total</dt>
                                                <dd class="tw-text-base tw-font-medium tw-text-gray-900">${{ money($subscription->total()) }}</dd>
                                            </div>
                                        </div>

                                        <div class="tw-mt-6 tw-flex tw-justify-center tw-text-center tw-text-sm tw-text-gray-500">
                                            <a href="{{ route('store.index') }}" class="tw-m-0">
                                                <button type="button" @click="open = false" class="tw-font-medium tw-text-theme-link-color hover:tw-text-theme-link-color/70">
                                                    Add items to subscription
                                                    <span aria-hidden="true"> &rarr;</span>
                                                </button>
                                            </a>
                                        </div>
                                    </div>

                                    <livewire:theme.subscription-payment-method :subscription_id="$subscription->id" :current_order_id="$current_order?->id"/>
                                </section>
                            </div>

                        </div>
                        <!-- End Upcoming Order -->
                    </div>
                    <!-- End Order information -->

                    <!-- Start Subscription information -->
                    <div class="tw-mx-auto tw-max-w-2xl tw-space-y-16 sm:tw-space-y-20 lg:tw-mx-0 lg:tw-max-w-none">
                        <div class="tw-bg-white tw-p-6 tw-border tw-border-gray-200 tw-rounded-md tw-shadow">
                            <h2 class="tw-m-0 tw-text-lg tw-font-semibold tw-leading-7 tw-text-gray-900">Subscription details</h2>
                            <p class="tw-m-0 tw-mt-1 tw-text-sm tw-leading-6 tw-text-gray-500">This information is used when generating your subscription
                                orders.</p>

                            <dl class="tw-mt-6 tw-space-y-6 tw-divide-y tw-divide-gray-100 tw-border-t tw-border-gray-200 tw-text-sm tw-leading-6">
                                <div class="tw-pt-6 sm:tw-flex">
                                    <dt class="tw-font-medium tw-text-gray-900 sm:tw-w-64 sm:tw-flex-none sm:tw-pr-6">Delivery method</dt>
                                    <dd class="tw-mt-1 sm:tw-mt-0">
                                        <div class="tw-text-gray-900">
                                            @if($delivery_method->isDeliveryZone())
                                                <div>Home delivery</div>
                                                @php($address = $subscription->customer->addresses()->default()->first())
                                                <address class="tw-mt-2 tw-text-gray-500">
                                                    {{ $subscription->customer->full_name }}
                                                    {{ $address?->street }} @if($address?->location->street_2)
                                                        , {{ $address?->location->street_2 }}
                                                    @endif <br>
                                                    {{ $address?->city }}, {{ $address?->state }} {{ $address?->postal_code }}
                                                </address>
                                            @else
                                                Pickup
                                                <address class="tw-mt-2 tw-text-gray-500">
                                                    {{ $delivery_method->present()->title() }}<br>
                                                    {{ $delivery_method->street }} @if($delivery_method->street_2)
                                                        , {{ $delivery_method->street_2 }}
                                                    @endif <br>
                                                    {{ $delivery_method->city }}, {{ $delivery_method->state }} {{ $delivery_method->zip }}
                                                </address>
                                            @endif
                                        </div>
                                        <div class="tw-mt-2 tw-flex">
                                            <div class="tw-flex-shrink-0">
                                                <svg class="tw-h-5 tw-w-5 tw-text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                    <path stroke-linecap="round" stroke-linejoin="round" d="M11.25 11.25l.041-.02a.75.75 0 011.063.852l-.708 2.836a.75.75 0 001.063.853l.041-.021M21 12a9 9 0 11-18 0 9 9 0 0118 0zm-9-3.75h.008v.008H12V8.25z"/>
                                                </svg>

                                            </div>
                                            <div class="tw-ml-3 tw-flex-1 md:tw-flex md:tw-justify-between">
                                                <p class="tw-text-sm tw-text-gray-700">Please contact us to update your delivery method.</p>
                                            </div>
                                        </div>

                                    </dd>
                                </div>
                                <div class="tw-pt-6 sm:tw-flex">
                                    <dt class="tw-font-medium tw-text-gray-900 sm:tw-w-64 sm:tw-flex-none sm:tw-pr-6">Frequency</dt>
                                    <dd class="tw-mt-1 tw-w-full sm:tw-mt-0">
                                        <div x-show=" ! editingFrequency" class="tw-flex tw-justify-between tw-gap-x-6 sm:tw-flex-auto">
                                            <div class="tw-text-gray-900">{{ $subscription->formattedReorderFrequency() }}</div>
                                            <button @click="editingFrequency = true" type="button" class="tw-font-semibold tw-text-theme-link-color hover:tw-text-theme-link-color/70">
                                                Update
                                            </button>
                                        </div>

                                        <form style="display:none;" x-show="editingFrequency" action="{{ route('customers.recurring.frequency.update') }}" method="POST" id="update_frequency" class="tw-m-0 tw-flex tw-justify-between tw-gap-x-6 sm:tw-flex-auto">
                                            @csrf
                                            @method('PUT')
                                            <div>
                                                <label for="reorder_frequency" class="tw-sr-only">{{ $delivery_method->isDeliveryZone() ? 'Delivery' : 'Pickup' }}
                                                    frequency</label>
                                                <select id="reorder_frequency" name="reorder_frequency" class="tw-block tw-w-full tw-rounded-md tw-border-0 tw-py-1.5 tw-pl-3 tw-pr-10 tw-text-gray-900 tw-ring-1 tw-ring-inset tw-ring-gray-300 focus:tw-ring-2 focus:tw-ring-theme-action-color sm:tw-text-sm sm:tw-leading-6">
                                                    @foreach ($subscription->reorderOptions() as $available_reorder_frequency)
                                                        @isset(App\Models\Schedule::$reorderFrequencies[$available_reorder_frequency])
                                                            <option value="{{ $available_reorder_frequency }}" {{ $subscription->reorder_frequency == $available_reorder_frequency ? "selected" : "" }} >
                                                                {{ App\Models\Schedule::$reorderFrequencies[$available_reorder_frequency] }}
                                                            </option>
                                                        @endisset
                                                    @endforeach
                                                </select>
                                            </div>
                                            <button @click="editingFrequency = false" type="submit" class="tw-font-semibold tw-text-theme-link-color hover:tw-text-theme-link-color/70">
                                                Save
                                            </button>
                                        </form>
                                    </dd>
                                </div>
                                <div class="tw-pt-6 sm:tw-flex">

                                    <dt class="tw-font-medium tw-text-gray-900 sm:tw-w-64 sm:tw-flex-none sm:tw-pr-6">Notifications</dt>
                                    <dd class="tw-mt-1 tw-flex tw-justify-between tw-gap-x-6 sm:tw-mt-0 sm:tw-flex-auto">
                                        <div class="tw-space-y-3 tw-text-gray-900">
                                            <div class="tw-flex tw-items-center">
                                                <div class="tw-flex-shrink-0">
                                                    <svg class="tw-w-5 tw-h-5 tw-text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M21.75 6.75v10.5a2.25 2.25 0 01-2.25 2.25h-15a2.25 2.25 0 01-2.25-2.25V6.75m19.5 0A2.25 2.25 0 0019.5 4.5h-15a2.25 2.25 0 00-2.25 2.25m19.5 0v.243a2.25 2.25 0 01-1.07 1.916l-7.5 4.615a2.25 2.25 0 01-2.36 0L3.32 8.91a2.25 2.25 0 01-1.07-1.916V6.75"/>
                                                    </svg>
                                                </div>

                                                <p class="tw-m-0 tw-ml-3 tw-truncate tw-text-sm tw-font-medium">{{ $subscription->customer->email }}</p>
                                            </div>
                                            <div class="tw-flex tw-items-center">
                                                <div class="tw-flex-shrink-0">
                                                    <svg class="tw-w-5 tw-h-5 tw-text-gray-400" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor">
                                                        <path stroke-linecap="round" stroke-linejoin="round" d="M20.25 8.511c.884.284 1.5 1.128 1.5 2.097v4.286c0 1.136-.847 2.1-1.98 2.193-.34.027-.68.052-1.02.072v3.091l-3-3c-1.354 0-2.694-.055-4.02-.163a2.115 2.115 0 01-.825-.242m9.345-8.334a2.126 2.126 0 00-.476-.095 48.64 48.64 0 00-8.048 0c-1.131.094-1.976 1.057-1.976 2.192v4.286c0 .837.46 1.58 1.155 1.951m9.345-8.334V6.637c0-1.621-1.152-3.026-2.76-3.235A48.455 48.455 0 0011.25 3c-2.115 0-4.198.137-6.24.402-1.608.209-2.76 1.614-2.76 3.235v6.226c0 1.621 1.152 3.026 2.76 3.235.577.075 1.157.14 1.74.194V21l4.155-4.155"/>
                                                    </svg>
                                                </div>
                                                <p class="tw-m-0 tw-ml-3 tw-truncate tw-text-sm tw-font-medium">{{ $subscription->customer->phone }}</p>
                                            </div>
                                        </div>
                                        <a href="{{ route('customer.profile') }}" class="tw-no-underline tw-font-semibold tw-text-theme-link-color hover:tw-text-theme-link-color/70">Update</a>
                                    </dd>
                                </div>
                            </dl>
                        </div>
                    </div>
                    <!-- End Subscription information -->

                </div>

                <!-- Start help information -->
                <div class="tw-mt-6 tw-pt-6 tw-pb-5 tw-bg-white sm:tw-mt-12">
                    <div class="tw--ml-4 tw--mt-4 tw-flex tw-flex-wrap tw-items-center tw-justify-between sm:tw-flex-nowrap">
                        <div class="tw-ml-4 tw-mt-4">
                            <h3 class="tw-text-base tw-font-semibold tw-leading-6 tw-text-gray-900">Need help?</h3>
                            <p class="tw-mt-1 tw-text-sm tw-text-gray-500">Please
                                <a href="{{ route('page.contact') }}" class="tw-font-semibold tw-text-theme-link-color tw-no-underline hover:tw-text-theme-link-color/70">contact
                                    us</a> if you need to update your delivery method, address, or if you just have some questions about your subscription.</p>
                        </div>
                        <div class="tw-ml-4 tw-mt-4 tw-flex-shrink-0">
                            <button type="button" x-on:click="$dispatch('legacy-modal-opened', { id: 'cancelRecurringOrder' })" class="tw-relative tw-inline-flex tw-items-center tw-rounded-md tw-border tw-border-gray-300 tw-bg-white tw-px-3 tw-py-2 tw-text-sm tw-font-semibold tw-text-gray-700 tw-shadow-sm hover:tw-bg-gray-50 focus-visible:tw-outline focus-visible:tw-outline-2 focus-visible:tw-outline-offset-2 focus-visible:tw-outline-theme-action-color/60">
                                Cancel subscription
                            </button>
                        </div>
                    </div>
                </div>
                <!-- End help information -->
            </main>
        </div>
    </div>

    @include('theme::customers.partials.skip_next_order_delivery_modal', [
        'subscription' => $subscription,
        'order' => $subscription->currentOrder,
        'next_deadline_date' => $next_delivery_date
    ])

    @include('theme::customers.partials.cancel_recurring_order', [
        'subscription' => $subscription,
        'selectedPromotionItem' => $selected_promo_item
    ])

    @include('theme::customers.partials.cancellation_feedback_modal', [
        'subscription' => $subscription,
        'order' => $subscription->currentOrder
    ])

    @if($subscription->currentOrder)
        <x-theme::legacy-modal id="getItSoonerModal">
            <form action="/account/recurring-orders/get-it-sooner" method="POST">
                @csrf
                @method('PUT')
                <input type="hidden" name="order_id" value="{{ $subscription->currentOrder?->id }}">
                @include('theme::customers.partials.get_it_sooner_modal')
            </form>
        </x-theme::legacy-modal>
    @else
        <x-theme::legacy-modal id="getItSoonerModal">
            <form action="{{ route('subscriptions.update', [$subscription]) }}" method="POST">
                @csrf
                @method('PATCH')
                @include('theme::customers.partials.get_it_sooner_modal')
            </form>
        </x-theme::legacy-modal>
    @endif
</div>


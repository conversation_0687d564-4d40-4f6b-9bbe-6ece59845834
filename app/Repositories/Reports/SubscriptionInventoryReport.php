<?php

namespace App\Repositories\Reports;

use App\Models\Filters\SubscriptionInventoryFilter;
use App\Models\RecurringOrderItem;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

class SubscriptionInventoryReport
{
    /**
     * @return Collection<int, \stdClass>
     */
    public function handle(array $filters): Collection
    {
        return DB::query()
            ->selectRaw('
                bundled_product_id as product_id,
                products.title as product_title,
                products.sku as product_sku,
                SUM(bundled.bundled_qty) as on_order,
                products.inventory as current_inventory
            ')
            ->fromSub($this->subQuery($filters), 'bundled')
            ->join('products', 'products.id', '=', 'bundled.bundled_product_id')
            ->groupByRaw('bundled.bundled_product_id')
            ->orderByRaw('product_sku, product_title')
            ->get();
    }

    /**
     * @return Builder<RecurringOrderItem>
     */
    private function subQuery(array $filters): Builder
    {
        return RecurringOrderItem::query()
            ->selectRaw('
                CASE WHEN bundle_product.product_id IS NOT NULL THEN
                    bundle_product.product_id
                ELSE
                    recurring_order_items.product_id
                END AS bundled_product_id,
                CASE WHEN bundle_product.product_id IS NOT NULL THEN
                    (recurring_order_items.qty * bundle_product.qty)
                ELSE
                    recurring_order_items.qty
                END AS bundled_qty
            ')
            ->join('recurring_orders', 'recurring_orders.id', '=', 'recurring_order_items.order_id')
            ->join('products', 'products.id', '=', 'recurring_order_items.product_id')
            ->leftJoin('bundle_product', 'bundle_product.bundle_id', '=', 'recurring_order_items.product_id')
            ->filter($filters, SubscriptionInventoryFilter::class);
    }
}

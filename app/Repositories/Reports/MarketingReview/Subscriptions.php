<?php

namespace App\Repositories\Reports\MarketingReview;

use App\Models\RecurringOrder;
use App\Models\User;
use App\Repositories\Reports\MarketingReview\Concerns\OrderScope;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class Subscriptions
{
    use OrderScope;

    public function handle(Carbon $start, Carbon $end): array
    {
        $base_query =  $this->applyScope(DB::table('orders')
            ->selectRaw('COUNT(*) AS order_count, SUM(total) AS order_total')
            ->whereBetween('pickup_date', [$start, $end])
        );

        $total_order_result = $base_query->first();

        $subscription_order_count = $base_query
            ->whereNotNull('blueprint_id')
            ->first();

        $deleted_customer_id = User::deletedCustomer()->id;

        return [
            'start_date' => $start,
            'end_date' => $end,
            // TODO: check why this is different than snapshot
            'total_subscriber_count' => RecurringOrder::withTrashed()
                ->whereNot('customer_id', $deleted_customer_id)
                ->where('created_at', '<=', $end)
                ->where(function ($query) use ($end) {
                    return $query
                        ->whereNull('deleted_at')
                        ->orWhere('deleted_at', '>', $end);
                })
                ->count(),
            'new_subscription_count' => RecurringOrder::query()
                ->whereNot('customer_id', $deleted_customer_id)
                ->whereBetween('created_at', [$start, $end])
                ->count(),
            'churn_count' => RecurringOrder::onlyTrashed()
                ->whereNot('customer_id', $deleted_customer_id)
                ->whereBetween('deleted_at', [$start, $end])
                ->count(),
            'total_order_count' => $total_order_result->order_count,
            'total_order_value' => $total_order_result->order_total,
            'subscription_order_count' => $subscription_order_count->order_count,
            'subscription_order_value' => $subscription_order_count->order_total,
            'subscription_conversion_rate' => $this->subscriptionConversionRate($start, $end),
            'subscriber_engagement_rate' => $this->subscriberEngagementRate($start, $end),
            'churn_rate' => RecurringOrder::onlyTrashed()
                    ->whereNot('customer_id', $deleted_customer_id)
                    ->whereBetween('deleted_at', [$start, $end])
                    ->count() / RecurringOrder::withTrashed()
                    ->whereNot('customer_id', $deleted_customer_id)
                    ->where(function ($query) use ($start) {
                        return $query
                            ->whereNull('deleted_at')
                            ->orWhere('deleted_at', '>', $start);
                    })
                    ->count(),
        ];
    }

    protected function subscriptionConversionRate(Carbon $start, Carbon $end): float
    {
        $result = $this->applyScope(DB::table('orders')
            ->selectRaw('
                COUNT(CASE WHEN blueprint_id IS NOT NULL THEN 1 END) AS subscription_count,
                COUNT(CASE WHEN blueprint_id IS NULL THEN 1 END) AS non_subscription_count'
            )
            ->where('first_time_order', true)
            ->whereBetween('pickup_date', [$start, $end])
        )
            ->first();

        $total_first_time_orders = (int) ($result->subscription_count + $result->non_subscription_count);
        if ($total_first_time_orders === 0) {
            return 0;
        }

        return round($result->subscription_count / $total_first_time_orders, 2);
    }

    protected function subscriberEngagementRate(Carbon $start, Carbon $end): float
    {
        $subscriber_ids = RecurringOrder::query()
            ->withTrashed()
            ->where(function ($query) use ($start) {
                return $query
                    ->whereNull('deleted_at')
                    ->orWhere('deleted_at', '>', $start);
            })
            ->whereNot('customer_id', User::deletedCustomer()->id)
            ->groupBy('customer_id')
            ->pluck('customer_id');

        if ($subscriber_ids->isEmpty()) {
            return 0;
        }

        $result = $this->applyScope(DB::table('orders')
            ->selectRaw('COUNT(*) AS order_count')
            ->whereBetween('pickup_date', [$start, $end])
            ->whereIn('customer_id', $subscriber_ids)
        )
            ->first();

        return round($result->order_count / $subscriber_ids->count(), 2);
    }
}

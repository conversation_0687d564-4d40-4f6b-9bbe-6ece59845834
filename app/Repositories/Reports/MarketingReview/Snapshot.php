<?php

namespace App\Repositories\Reports\MarketingReview;

use App\Repositories\Reports\MarketingReview\Concerns\OrderScope;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;

class Snapshot
{
    use OrderScope;

    public function handle(Carbon $end): array
    {
        $result = DB::table(function ($sub_query) use ($end) {
            return $this->applyScope(
                $sub_query
                    ->selectRaw('customer_id, COUNT(*) AS order_count, SUM(total) AS order_total')
                    ->from('orders')
                    ->where('pickup_date', '>', $end->copy()->subMonthsNoOverflow(12))
                    ->where('pickup_date', '<=', $end)
                    ->groupBy('customer_id')
                );
            }, 'sub')
            ->selectRaw('count(customer_id) AS active_customers, cast(avg(order_count) as DECIMAL(5,2)) AS average_order_count, cast((sum(order_total) / sum(order_count)) as DECIMAL(8,0)) AS average_order_value')
            ->first();

        return [
            'end_date' => $end,
            'active_customers' => (int) $result->active_customers,
            'average_customer_order_count' => round($result->average_order_count ?? 0, 2),
            'average_order_value' => (int) round($result->average_order_value ?? 0)
        ];
    }
}

<?php

namespace App\Models;

use App\Actions\Cart\ConfirmOrderWithCart;
use App\Actions\Order\AddItem;
use App\Actions\Order\AddPromoItem;
use App\Actions\Order\AddRecurringItem;
use App\Actions\Order\ApplyCoupon;
use App\Actions\Order\Cancel;
use App\Actions\Order\Confirm;
use App\Actions\Order\ConfirmRecurring;
use App\Actions\Order\Remove;
use App\Actions\Order\RemovePromoItems;
use App\Actions\Order\SetItemQuantity;
use App\Actions\Order\Skip;
use App\Actions\Order\UpdateOrderFulfillmentLocation;
use App\Actions\ProcessOrder;
use App\Cart\Cart;
use App\Commerce\Calculator;
use App\Contracts\Cartable;
use App\Events\Order\OrderWasPaid;
use App\Exceptions\BackOrderException;
use App\Exceptions\CouponInvalidException;
use App\Exceptions\ExclusivityException;
use App\Exceptions\PickupNotFoundException;
use App\Exceptions\ProductNotFoundException;
use App\Exceptions\QuantityAdjustmentException;
use App\Models\Concerns\IsCartable;
use App\OrderWindow;
use App\Presenters\OrderPresenter;
use App\Services\SettingsService;
use App\Services\SubscriptionSettingsService;
use App\Support\Enums\OrderStatus;
use App\Support\Enums\ProductType;
use App\Traits\HasTags;
use Bugsnag\BugsnagLaravel\Facades\Bugsnag;
use Carbon\Carbon;
use Carbon\CarbonInterface;
use Carbon\CarbonInterval;
use Database\Factories\OrderFactory;
use Eloquent;
use EloquentFilter\Filterable;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Notifications\DatabaseNotificationCollection;
use Illuminate\Notifications\Notifiable;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Laracasts\Presenter\PresentableTrait;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;

/**
 * App\Models\Order
 *
 * @property int $id
 * @property string|null $tracking_id
 * @property int $type_id
 * @property bool|null $is_recurring
 * @property int|null $blueprint_id
 * @property int $order_number
 * @property int $customer_id
 * @property int $staff_id
 * @property string|null $accounting_id
 * @property int $total
 * @property int|null $payments_subtotal
 * @property int $original_total
 * @property int $subtotal
 * @property int $original_subtotal
 * @property int $tax
 * @property int $original_tax
 * @property float $tax_rate
 * @property float $weight
 * @property string $original_weight
 * @property int $order_discount
 * @property int $subscription_savings
 * @property int $coupon_subtotal
 * @property string $discount_type
 * @property int $fees_subtotal
 * @property int $original_fees_subtotal
 * @property int $credit_applied
 * @property int $delivery_rate
 * @property int $delivery_fee_type
 * @property int $delivery_fee
 * @property string|null $delivery_fee_cap
 * @property int $schedule_id
 * @property int $pickup_id
 * @property int|null $date_id
 * @property int $status_id
 * @property \Illuminate\Support\Carbon|null $deadline_date
 * @property \Illuminate\Support\Carbon|null $pickup_date
 * @property \Illuminate\Support\Carbon|null $original_pickup_date
 * @property string $customer_first_name
 * @property string $customer_last_name
 * @property string $customer_email
 * @property string|null $customer_email_alt
 * @property string $customer_phone
 * @property string $customer_phone_2
 * @property string $shipping_street
 * @property string $shipping_street_2
 * @property string $shipping_city
 * @property string $shipping_state
 * @property string $shipping_zip
 * @property string|null $shipping_country
 * @property string $billing_street
 * @property string $billing_street_2
 * @property string $billing_city
 * @property string $billing_state
 * @property string $billing_zip
 * @property string|null $billing_country
 * @property string $customer_notes
 * @property string $packing_notes
 * @property string $invoice_notes
 * @property string $payment_notes
 * @property int $flagged
 * @property int $fulfillment_error
 * @property int $packed
 * @property bool $canceled
 * @property \Illuminate\Support\Carbon|null $canceled_at
 * @property \Illuminate\Support\Carbon|null $skipped_at
 * @property bool $confirmed
 * @property \Illuminate\Support\Carbon|null $confirmed_date
 * @property \Illuminate\Support\Carbon|null $due_date
 * @property string|null $payment_terms
 * @property bool $processed
 * @property string|null $processed_date
 * @property int $payment_id
 * @property int|null $payment_source_id
 * @property bool $paid
 * @property \Illuminate\Support\Carbon|null $payment_date
 * @property int $containers
 * @property int $containers_2
 * @property int $exported
 * @property int $picked_up
 * @property int $last_modified_by
 * @property int $first_time_order
 * @property string|null $packed_email_sent_at
 * @property string|null $confirmation_email_sent_at
 * @property string|null $processed_email_sent_at
 * @property int $created_year
 * @property int $created_month
 * @property int $created_day
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property string|null $recipient_email
 * @property string $recipient_notes
 * @property-read bool $is_paid
 * @property-read bool $is_not_paid
 * @property-read RecurringOrder|null $blueprint
 * @property-read \Illuminate\Database\Eloquent\Collection|OrderItem[] $cartItems
 * @property-read int|null $cart_items_count
 * @property-read User|null $customer
 * @property-read Date|null $date
 * @property-read \Illuminate\Database\Eloquent\Collection|Coupon[] $discounts
 * @property-read int|null $discounts_count
 * @property-read \Illuminate\Database\Eloquent\Collection|OrderFee[] $fees
 * @property-read int|null $fees_count
 * @property-read string $confirmed_date_formatted
 * @property-read string $credit_applied_formatted
 * @property-read string $customer_full_name
 * @property-read Carbon|null $delivery_date
 * @property-read string $delivery_fee_formatted
 * @property-read string $delivery_rate_formatted
 * @property-read string $fees_subtotal_formatted
 * @property-read string $order_discount_formatted
 * @property-read string $payment_date_formatted
 * @property-read string $subscription_savings_formatted
 * @property-read string $subtotal_formatted
 * @property-read string $tax_formatted
 * @property-read int $total_due
 * @property-read string $total_formatted
 * @property-read \Illuminate\Database\Eloquent\Collection|GiftCertificate[] $giftCards
 * @property-read int|null $gift_cards_count
 * @property-read \Illuminate\Database\Eloquent\Collection|OrderItem[] $items
 * @property-read int|null $items_count
 * @property-read DatabaseNotificationCollection|DatabaseNotification[] $notifications
 * @property-read int|null $notifications_count
 * @property-read Payment|null $paymentMethod
 * @property-read \Illuminate\Database\Eloquent\Collection|OrderPayment[] $payments
 * @property-read int|null $payments_count
 * @property-read Pickup|null $pickup
 * @property-read Schedule|null $schedule
 * @property-read User|null $staff
 * @property-read \Illuminate\Database\Eloquent\Collection|Tag[] $tags
 * @property-read int|null $tags_count
 * @property-read User|null $updatedBy
 * @method static Builder|Order forEmail()
 * @method static Builder|Order history(int $userId)
 * @method static Builder|Order newModelQuery()
 * @method static Builder|Order newQuery()
 * @method static Builder|Order newest()
 * @method static Builder|Order oneTime()
 * @method static Builder|Order open()
 * @method static Builder|Order ownedBy(int $userId)
 * @method static Builder|Order query()
 * @method static Builder|Order recurring()
 * @method static Builder|Order subscription()
 * @method static Builder|Order unconfirmed()
 * @method static Builder|Order undecidedRecurringCart()
 * @method static Builder|Order whereAccountingId($value)
 * @method static Builder|Order whereBillingCity($value)
 * @method static Builder|Order whereBillingCountry($value)
 * @method static Builder|Order whereBillingState($value)
 * @method static Builder|Order whereBillingStreet($value)
 * @method static Builder|Order whereBillingStreet2($value)
 * @method static Builder|Order whereBillingZip($value)
 * @method static Builder|Order whereBlueprintId($value)
 * @method static Builder|Order whereCanceled($value)
 * @method static Builder|Order whereCanceledAt($value)
 * @method static Builder|Order whereConfirmationEmailSentAt($value)
 * @method static Builder|Order whereConfirmed($value)
 * @method static Builder|Order whereConfirmedDate($value)
 * @method static Builder|Order whereContainers($value)
 * @method static Builder|Order whereContainers2($value)
 * @method static Builder|Order whereCouponSubtotal($value)
 * @method static Builder|Order whereCreatedAt($value)
 * @method static Builder|Order whereCreatedDay($value)
 * @method static Builder|Order whereCreatedMonth($value)
 * @method static Builder|Order whereCreatedYear($value)
 * @method static Builder|Order whereCreditApplied($value)
 * @method static Builder|Order whereCustomerEmail($value)
 * @method static Builder|Order whereCustomerEmailAlt($value)
 * @method static Builder|Order whereCustomerFirstName($value)
 * @method static Builder|Order whereCustomerId($value)
 * @method static Builder|Order whereCustomerLastName($value)
 * @method static Builder|Order whereCustomerNotes($value)
 * @method static Builder|Order whereCustomerPhone($value)
 * @method static Builder|Order whereCustomerPhone2($value)
 * @method static Builder|Order whereDateId($value)
 * @method static Builder|Order whereDeadlineDate($value)
 * @method static Builder|Order whereDeliveryFee($value)
 * @method static Builder|Order whereDeliveryFeeCap($value)
 * @method static Builder|Order whereDeliveryFeeType($value)
 * @method static Builder|Order whereDeliveryRate($value)
 * @method static Builder|Order whereDiscountType($value)
 * @method static Builder|Order whereDueDate($value)
 * @method static Builder|Order whereExported($value)
 * @method static Builder|Order whereFeesSubtotal($value)
 * @method static Builder|Order whereFirstTimeOrder($value)
 * @method static Builder|Order whereFlagged($value)
 * @method static Builder|Order whereFulfillmentError($value)
 * @method static Builder|Order whereId($value)
 * @method static Builder|Order whereInvoiceNotes($value)
 * @method static Builder|Order whereIsRecurring($value)
 * @method static Builder|Order whereLastModifiedBy($value)
 * @method static Builder|Order whereOrderDiscount($value)
 * @method static Builder|Order whereOrderNumber($value)
 * @method static Builder|Order whereOriginalFeesSubtotal($value)
 * @method static Builder|Order whereOriginalPickupDate($value)
 * @method static Builder|Order whereOriginalSubtotal($value)
 * @method static Builder|Order whereOriginalTax($value)
 * @method static Builder|Order whereOriginalTotal($value)
 * @method static Builder|Order whereOriginalWeight($value)
 * @method static Builder|Order wherePacked($value)
 * @method static Builder|Order wherePackedEmailSentAt($value)
 * @method static Builder|Order wherePackingNotes($value)
 * @method static Builder|Order wherePaid($value)
 * @method static Builder|Order wherePaymentDate($value)
 * @method static Builder|Order wherePaymentId($value)
 * @method static Builder|Order wherePaymentNotes($value)
 * @method static Builder|Order wherePaymentSourceId($value)
 * @method static Builder|Order wherePaymentTerms($value)
 * @method static Builder|Order wherePaymentsSubtotal($value)
 * @method static Builder|Order wherePickedUp($value)
 * @method static Builder|Order wherePickupDate($value)
 * @method static Builder|Order wherePickupId($value)
 * @method static Builder|Order whereProcessed($value)
 * @method static Builder|Order whereProcessedDate($value)
 * @method static Builder|Order whereProcessedEmailSentAt($value)
 * @method static Builder|Order whereScheduleId($value)
 * @method static Builder|Order whereShippingCity($value)
 * @method static Builder|Order whereShippingCountry($value)
 * @method static Builder|Order whereShippingState($value)
 * @method static Builder|Order whereShippingStreet($value)
 * @method static Builder|Order whereShippingStreet2($value)
 * @method static Builder|Order whereShippingZip($value)
 * @method static Builder|Order whereSkippedAt($value)
 * @method static Builder|Order whereStaffId($value)
 * @method static Builder|Order whereStatusId($value)
 * @method static Builder|Order whereSubscriptionSavings($value)
 * @method static Builder|Order whereSubtotal($value)
 * @method static Builder|Order whereTax($value)
 * @method static Builder|Order whereTaxRate($value)
 * @method static Builder|Order whereTotal($value)
 * @method static Builder|Order whereTrackingId($value)
 * @method static Builder|Order whereTypeId($value)
 * @method static Builder|Order whereUpdatedAt($value)
 * @method static Builder|Order whereWeight($value)
 * @property-read \Illuminate\Database\Eloquent\Collection<int, Event> $events
 * @property-read int|null $events_count
 * @property-read Product|null $subscription_product_incentive
 * @property-read \Illuminate\Database\Eloquent\Collection<int, OrderItem> $rawItems
 * @property-read int|null $raw_items_count
 * @property-read \Illuminate\Database\Eloquent\Collection<int, OrderItem> $giftCardItems
 * @property-read int|null $gift_card_items_count
 * @method static Builder|Order confirmed()
 * @method static Builder|Order confirmedBetween(Carbon $start_date, Carbon $end_date)
 * @method static OrderFactory factory($count = null, $state = [])
 * @method static Builder|Order filter(array $input = [], $filter = null)
 * @method static Builder|Order paginateFilter($perPage = null, $columns = [], $pageName = 'page', $page = null)
 * @method static Builder|Order readyForAutoConfirmation()
 * @method static Builder|Order simplePaginateFilter($perPage = null, $columns = [], $pageName = 'page', $page = null)
 * @method static Builder|Order since(CarbonInterface $date)
 * @method static Builder|Order whereBeginsWith($column, $value, $boolean = 'and')
 * @method static Builder|Order whereEndsWith($column, $value, $boolean = 'and')
 * @method static Builder|Order whereLike($column, $value, $boolean = 'and')
 * @mixin Eloquent
 */
class Order extends Model implements Cartable
{
    use HasFactory, Filterable, HasTags, Notifiable, IsCartable;
    use PresentableTrait {
        present as protected presentTrait;
    }

    protected $presenter = OrderPresenter::class;

    protected $guarded = [];
    protected $appends = [
        'customer_full_name', 'subtotal_formatted', 'tax_formatted', 'total_formatted',
        'order_discount_formatted', 'delivery_fee_formatted', 'delivery_rate_formatted',
        'fees_subtotal_formatted', 'credit_applied_formatted', 'confirmed_date_formatted',
        'total_due', 'subscription_savings_formatted', 'delivery_date', 'is_paid'
    ];

    public static function createForUser(User $user, array $params = []): Order
    {
        if (is_null($user->pickup)) {
            throw new PickupNotFoundException;
        }

        $params = collect($params);

        $date_id = $params->get('date_id');
        $deadline_date = $params->get('deadline_date');
        $pickup_date = $params->get('pickup_date');

        if (is_null($deadline_date) && is_null($pickup_date)) {
            if ($order_window = $user->pickup->activeOrderWindow()) {
                $date_id = $order_window->dateId();
                $deadline_date = $order_window->deadlineDatetime();
                $pickup_date = $order_window->deliveryDatetime();
            }
        }

        $orderAttributes = array_merge([
            'is_recurring' => null,
            'type_id' => $params->get('type_id', $user->pickup->setting('sales_channel', 1)),
            'payment_id' => $params->get('payment_id', $user->setting('default_payment_method', 0)),
            'payment_source_id' => $user->checkout_card_id ?? null,
            'schedule_id' => $user->pickup->schedule_id ?: 0,
            'pickup_id' => $user->pickup->id,
            'date_id' => $date_id,
            'deadline_date' => $deadline_date,
            'pickup_date' => $pickup_date,
            'original_pickup_date' => $pickup_date,
            'customer_id' => $user->id,
            'customer_first_name' => $user->first_name,
            'customer_last_name' => $user->last_name,
            'customer_phone' => $user->phone,
            'customer_email' => $user->email,
            'billing_street' => $user->billing_street,
            'billing_street_2' => $user->billing_street_2,
            'billing_city' => $user->billing_city,
            'billing_state' => $user->billing_state,
            'billing_zip' => $user->billing_zip ?? '',
            'first_time_order' => $user->orders()->where(['confirmed' => true, 'canceled' => false])->doesntExist(),
            'confirmed' => false,
            'packed' => false,
            'paid' => false,
            'status_id' => 1
        ], self::defaultShippingAttributes($user));

        $order = Order::create($orderAttributes);

        $order->updateTotals();

        return $order;
    }

    public function deadlineDatetime(): ?Carbon
    {
        if ( ! $this->deadline_date) {
            return null;
        }

        $deadline_hour = app(SettingsService::class)->deadlineHour();

        if ($deadline_hour === 24) {
            return $this->deadline_date->copy()->endOfDay();
        }

        return $this->deadline_date->copy()->setTime($deadline_hour, 0);
    }

    public static function defaultShippingAttributes(User $user): array
    {
        $attributes = $user->defaultShippingAttributes();

        return [
            'shipping_street' => $attributes['street'] ?? '',
            'shipping_street_2' => $attributes['street_2'] ?? '',
            'shipping_city' => $attributes['city'] ?? '',
            'shipping_state' => $attributes['state'] ?? '',
            'shipping_zip' => $attributes['postal_code'] ?? '',
            'shipping_country' => $attributes['country'] ?? '',
        ];
    }

    public function updateTotals(): Order
    {
        /** @var Order $order */
        $order = $this->calculate()->totals();
        // TODO: move persistence out of calculator and to places that call this function

        if ($order->isDirty()) {
            $order->save();
        }

        return $order;
    }

    public function calculate(): Calculator
    {
        $this->load('items.product', 'pickup.fees', 'fees', 'discounts', 'payments.refunds');
        return new Calculator($this);
    }

    public static function relationsToLoad(): array
    {
        return [
            'pickup' => function ($q) {
                $q->select([
                    'id', 'slug', 'title', 'display_name', 'schedule_id', 'status_id', 'fulfillment_type', 'pricing_group_id',
                    'payment_methods', 'street', 'street_2', 'city', 'state', 'zip', 'display_cart_shipping_calculator',
                    'apply_limit', 'delivery_fee_cap', 'delivery_rate', 'delivery_total_threshold', 'settings'
                ]);
            },
            'customer' => fn($q) => $q->select(['id', 'first_name', 'last_name', 'role_id', 'email', 'pricing_group_id', 'credit']),
        ];
    }

    protected static function booted()
    {
        static::saving(function ($order) {
            if ($order->isDirty('pickup_date')) {
                $order->calculatePackDeadline();
            }
        });
    }

    public function calculatePackDeadline(): void
    {
        if ( ! $this->pickup_date) {
            return;
        }

        $this->pack_deadline_at = $this->pickup_date
            ->copy()
            ->endOfDay()
            ->subHours($this->pickup?->schedule?->totalPackDeadlineHours() ?? 0);
    }

    /**
     * @throws Exception
     */
    public function addPromoItem(Product $product): OrderItem
    {
        return $this->addItem($product, 1, 'promo');
    }

    /**
     * @throws ExclusivityException
     * @throws BackOrderException
     */
    public function addItem(Product $product, int $qty, string $type = 'standard', bool $force = false): OrderItem
    {
        return app(AddItem::class)->handle($this, $product, $qty, $type, $force);
    }

    public function present(): OrderPresenter
    {
        return $this->presentTrait();
    }

    public function routeNotificationForMail(): string
    {
        return $this->customer_email ?: $this->customer->email;
    }

    public function routeNotificationForTwilio(): ?string
    {
        $util = PhoneNumberUtil::getInstance();

        try {
            $parsed_number = $util->parse($this->customer_phone ?: $this->customer->phone, "US");

            if ($util->isValidNumber($parsed_number)) {
                return $util->format($parsed_number, PhoneNumberFormat::E164);
            }
        } catch (Exception $e) { /* fail silently */
        }

        return null;
    }

    /**
     * @throws Exception
     */
    public function addRecurringItem(RecurringOrderItem $subscription_item): OrderItem
    {
        return app(AddRecurringItem::class)->handle($this, $subscription_item);
    }

    /**
     * @throws BackOrderException|ProductNotFoundException|QuantityAdjustmentException
     */
    public function updateItemQuantity(OrderItem $item, int $qty, bool $force = false): ?OrderItem
    {
        return app(SetItemQuantity::class)->handle($this, $item, $qty, $force);
    }

    public function removeItem(OrderItem $item): self
    {
        $item->delete();

        if ($item->product->isGiftCard()) {
            GiftCertificate::where(['order_item_id' => $item->id])->delete();
        }

        if ( ! $this->isConfirmed()) {
            return $this;
        }

        $item->releaseProductInventory();

        Event::create([
            'model_type' => Order::class,
            'model_id' => $this->id,
            'description' => "{$item->title} was removed from the order",
            'event_id' => 'order_item_removed',
            'user_id' => auth()->id(),
            'created_at' => now(),
            'metadata' => json_encode([
                'item_id' => $item->id,
                'qty' => $item->qty
            ])
        ]);

        return $this;
    }

    public function isConfirmed()
    {
        return $this->confirmed ?? false;
    }

    public function findPromotionalItems(): Collection
    {
        return $this->items->filter(fn($item) => $item->type === 'promo');
    }

    public function getFreeItemSavings(): int
    {
        return $this->getFreeItems()->sum(function (OrderItem $item) {
            return $item->product ? $item->product->getRegularPrice() : 0;
        });
    }

    public function getFreeItems(): Collection
    {
        return $this->items->filter(function (OrderItem $item) {
            return $item->subtotal <= 0 && $item->type === 'promo';
        });
    }

    public function confirm(array $params = []): Order
    {
        if ($this->isConfirmed()) {
            return $this;
        }

        $class = Confirm::class;

        if ($this->isFromBlueprint()) {
            $class = ConfirmRecurring::class;
        }

        return app()->make($class)->handle($this, $params);
    }

    public function isFromBlueprint(): bool
    {
        return ! is_null($this->blueprint_id);
    }

    public function confirmWithCart(Cart $cart): Order
    {
        return app(ConfirmOrderWithCart::class)->handle($this, $cart);
    }

    public function remove(): void
    {
        app(Remove::class)->handle($this);
    }

    public function skipDays(int $days): RecurringOrder
    {
        return app(Skip::class)->handle(
            $this,
            $this->pickup_date->copy()->addDays($days)
        );
    }

    public function rescheduleTo(Carbon $new_delivery_date): RecurringOrder
    {
        return app(Skip::class)->handle($this, $new_delivery_date);
    }

    public function nextRecurringOrderPickupDate(): ?Carbon
    {
        if (is_null($this->blueprint)) return null;

        return $this->pickup_date
            ->copy()
            ->addDays($this->blueprint->reorder_frequency ?? 7);
    }

    /**
     * @throws Exception
     */
    public function process(): array
    {
        return app(ProcessOrder::class)->handle($this);
    }

    public function markAsPaid(): void
    {
        if ($this->is_not_paid && $this->isConfirmed()) {
            $this->paid = true;
            $this->payment_date = now();
            $this->save();

            OrderWasPaid::dispatch($this);
        }
    }

    public function canAcceptCredit(): bool
    {
        return $this->is_not_paid
            && $this->isConfirmed()
            && $this->hasNotBeenProcessed()
            && !$this->isCanceled()
            && $this->items->doesntContain(fn($item) => $item->product->isGiftCard());
    }

    public function hasNotBeenProcessed(): bool
    {
        return !$this->hasBeenProcessed();
    }

    public function hasBeenProcessed(): bool
    {
        return $this->processed ?? false;
    }

    public function isCanceled(): bool
    {
        return $this->status_id === OrderStatus::canceled()
            || $this->canceled
            || ! is_null($this->canceled_at);
    }

    public function getCreditableBalance(): int
    {
        return $this->total - $this->credit_applied;
    }

    public function idempotencyKey(?int $amount = null): string
    {
        $key = now()->format('y-m-d-h-i');
        $amountKey = $amount ?? $this->total;
        return $this->id . '_' . $key . '_' . $amountKey;
    }

    public function getOutOfStockItems(): Collection
    {
        return $this->items->reject(function ($item) {
            return $item->product->hasAmountAvailable($item->qty);
        });
    }

    public function hasDeliveryBetween(array $range): bool
    {
        return $this->getDeliveryDate() >= $range['start']
            && $this->getDeliveryDate() <= $range['end'];
    }

    public function getDeliveryDate(): ?Carbon
    {
        return $this->isConfirmed()
            ? $this->pickup_date
            : $this->pickup->activeOrderWindow()?->deliveryDatetime();
    }

    public function creditCanBeApplied(): bool
    {
        return $this->total - $this->credit_applied > 0;
    }

    /**
     * @param array|int $status
     */
    public function doesNotHaveStatus($status): bool
    {
        return $this->hasStatus($status) === false;
    }

    /**
     * @param array|int $status
     */
    public function hasStatus($status): bool
    {
        return is_array($status)
            ? in_array($this->status_id, $status)
            : $this->status_id === $status;
    }

    public function hasSchedule(): bool
    {
        return $this->pickup->schedule_id !== 0;
    }

    public function hasSecondaryEmail(): bool
    {
        return ! empty($this->secondaryEmail());
    }

    public function secondaryEmail(): ?string
    {
        return $this->customer_email_alt;
    }

    public function cancelIfEmpty(): self
    {
        if ($this->items->isEmpty() && $this->isConfirmed()) {
            $this->cancel();
        }

        return $this;
    }

    public function cancel(): Order
    {
        return app(Cancel::class)->handle($this);
    }

    /**
     * @throws ExclusivityException
     */
    public function updateFulfillmentOption(Pickup $pickup, bool $removeExcluded = false): self
    {
        return app(UpdateOrderFulfillmentLocation::class)->handle($this, $pickup, $removeExcluded);
    }

    /**
     * @return OrderItem|false
     */
    public function productsDoNotMeetOrderMinimum()
    {
        foreach ($this->items as $item) {
            if ($item->product->hasOrderMinimum() && $this->subtotal < $item->product->orderMinimum()) {
                return $item;
            }
        }

        return false;
    }

    public function applyFulfillmentFees(Pickup $pickup): self
    {
        DB::table('order_fees')->where('order_id', $this->id)->delete();

        $pickup->fees->each(function ($fee) {
            OrderFee::create([
                'order_id' => $this->id,
                'title' => $fee->title,
                'qty' => 1,
                'amount' => $fee->amount,
                'taxable' => $fee->taxable,
                'apply_limit' => $fee->apply_limit,
                'threshold' => $fee->threshold,
                'cap' => $fee->cap,
                'subtotal' => $fee->amount,
            ]);
        });

        return $this;
    }

    /**
     * @throws CouponInvalidException
     */
    public function applyCoupon(Coupon $coupon): Coupon
    {
        return app(ApplyCoupon::class)->handle($this, $coupon);
    }

    /**
     * @param Coupon|int $coupon
     */
    public function removeCoupon($coupon): self
    {
        if (! $coupon instanceof Coupon) {
            $coupon = Coupon::find($coupon);
        }

        $this->discounts()->detach($coupon->id);
        $coupon->total_uses = $coupon->total_uses - 1;
        $coupon->save();

        return $this;
    }

    /**
     * @return BelongsToMany<Coupon, $this>
     */
    public function discounts(): BelongsToMany
    {
        return $this->belongsToMany(Coupon::class)->withPivot('user_id', 'savings');
    }

    public function removeAllCoupons(): self
    {
        $this->discounts()->detach();
        return $this;
    }

    public function deliveryFeeMeetsCap($deliveryFee): bool
    {
        return $this->pickup->apply_limit
            && $this->subtotal >= $this->pickup->delivery_total_threshold
            && $deliveryFee > $this->pickup->delivery_fee_cap;
    }

    public function isShipping(): bool
    {
        return $this->pickup->isDeliveryZone();
    }

    public function inventoryTimingDatetime(): ?Carbon
    {
        $deadline = $this->deadlineDatetime();

        if (! $deadline) {
            return null;
        }

        return $deadline->copy()
            ->subDays(app(SubscriptionSettingsService::class)->inventoryManagementDayCount());
    }

    public function deadlineReminderDatetime(): ?Carbon
    {
        $deadline = $this->deadlineDatetime();

        if (! $deadline
            || is_null($this->blueprint)
            || is_null($this->blueprint->schedule)
        ) {
            return null;
        }

        $schedule = $this->blueprint->schedule;

        $days_before = (int) setting('recurring_orders_deadline_days_before', 1);
        $hours_before = (int) setting('recurring_orders_deadline_hours_before', 0);

        if (! $schedule->usesGlobalMailSubscriptionDeadlineReminderSetting()) {
            $days_before = $schedule->subscription_reminder_days;
            $hours_before = $schedule->subscription_reminder_hour;
        }

        return $deadline->copy()->subHours(
            (int) CarbonInterval::days($days_before ?? 0)->addHours($hours_before ?? 0)->totalHours
        );
    }

    public function deadlineEndTime(): ?string
    {
        if (! $this->deadline_date) {
            return null;
        }

        return app(SettingsService::class)->formattedDeadlineEndTime();
    }

    public function getPricingGroup(): int
    {
        if ($this->customer->pricing_group_id ?? null) {
            return $this->customer->pricing_group_id;
        } elseif ($this->pickup->pricing_group_id ?? null) {
            return $this->pickup->pricing_group_id;
        }

        return 0;
    }

    public function getItemsByType(array $options): Collection
    {
        if ($this->cartItems->isEmpty()) {
            return collect();
        }

        return $this->cartItems->filter(function ($item) use ($options) {
            return in_array($item->type, $options);
        });
    }

    public function scopeForEmail(Builder $query): Builder
    {
        return $query->with([
            'items.product', 'fees', 'discounts', 'pickup', 'customer', 'paymentMethod'
        ]);
    }

    public function scopeConfirmedBetween(Builder $query, Carbon $start_date, Carbon $end_date)
    {
        return $query
            ->whereDate('confirmed_date', '>=', $start_date->format('Y-m-d'))
            ->whereDate('confirmed_date', '<=', $end_date->format('Y-m-d'));
    }

    public function scopeOpen(Builder $query): Builder
    {
        return $query
            ->whereHas('pickup', fn(Builder $query) =>
                /** @var Builder<Pickup> $query */
                /** @phpstan-ignore-next-line */
                $query->isActive()->whereNot('fulfillment_type', Pickup::FULFILLMENT_TYPE_VIRTUAL)
            )
            ->where(['confirmed' => true, 'canceled' => false, 'canceled_at' => null])
            ->whereNotIn('status_id', [OrderStatus::canceled(), OrderStatus::completed(),  OrderStatus::pickedUp()])
            ->whereNotNull('deadline_date')
            ->whereDate('deadline_date', '>=', today())
            ->where(function (Builder $query) {
                return $query
                    // subscription
                    ->whereNotNull('blueprint_id')
                    // one time with modifications
                    ->when(
                        app(SettingsService::class)->allowsOrderModification(),
                        fn(Builder $query) => $query->orWhereNull('blueprint_id')
                    );
            })
            ->orderBy('deadline_date')
            ->orderBy('created_at');
    }

    public function scopeReadyForAutoConfirmation(Builder $query)
    {
        /** @var Builder<Order> $query */
        return $query->subscription()
            ->where([
                'confirmed' => false,
                'canceled' => false
            ])
            ->where('pickup_date', '>=', today())
            ->where('deadline_date', '<=', app(SubscriptionSettingsService::class)->autoConfirmationDeadlineDate());
    }

    public function canBeModified(): bool
    {
        if ( ! $this->isNew() || $this->is_paid) {
            return false;
        }

        if ( ! $this->isConfirmed()) {
            return true;
        }

        if (is_null($this->pickup_date) || is_null($this->deadline_date)) {
            return false;
        }

        if (! $this->isFromBlueprint() && ! app(SettingsService::class)->allowsOrderModification()) {
            return false;
        }

        return ! $this->deadlineHasPassed();
    }

    public function isNew(): bool
    {
        return $this->status_id === OrderStatus::confirmed();
    }

    public function deadlineHasPassed(): bool
    {
        $deadline = $this->deadlineDatetime();

        if (! $deadline) {
            return false;
        }

        return $deadline < now();
    }

    public function scopeHistory(Builder $query, int $userId)
    {
        return $query
            ->where('customer_id', $userId)
            ->where('confirmed', true)
            ->with(['pickup' => fn($q) => $q->withTrashed()])
            ->orderBy('confirmed_date', 'desc')
            ->orderBy('id', 'desc');
    }

    public function scopeRecurring($query)
    {
        return $query->where('is_recurring', true);
    }

    public function scopeSubscription(Builder $query): Builder
    {
        return $query->whereNotNull('blueprint_id');
    }

    public function scopeOneTime(Builder $query): Builder
    {
        return $query->whereNull('blueprint_id');
    }

    public function scopeNewest(Builder $query): Builder
    {
        return $query->with('pickup')
            ->select(['id', 'pickup_id', 'total', 'customer_first_name', 'customer_last_name'])
            ->where('confirmed', true)
            ->where('status_id', 1)
            ->where('confirmed_date', '>', today()->subWeek())
            ->orderBy('confirmed_date', 'desc');
    }

    public function scopeOwnedBy(Builder $query, int $userId): Builder
    {
        return $query->where('customer_id', $userId);
    }

    public function status(): ?string
    {
        return OrderStatus::get($this->status_id);
    }

    public function cartItems(): HasMany
    {
        return $this->hasMany(OrderItem::class)->selectRaw('order_items.id, order_items.order_id, order_items.product_id, order_items.title, order_items.type,
                order_items.qty,order_items.weight, order_items.original_weight, order_items.unit_price, products.sale as on_sale,
                order_items.original_unit_price, order_items.unit_of_issue, order_items.subtotal,order_items.taxable, order_items.store_price, order_items.created_at,order_items.updated_at,
                order_items.tax, order_items.discount, order_items.discount_type, order_items.stock_status, products.sku, products.type_id, products.fulfillment_instructions,
                products.custom_sort, products.inventory, products.track_inventory, products.inventory_type, products.is_grouped, products.is_bundle as bundle,
                vendors.title as vendor, vendors.id as vendor_id, gift_certificates.code, gift_certificates.active, gift_certificates.redeemed')
            ->join('products', 'products.id', '=', 'order_items.product_id')
            ->leftJoin('vendors', 'vendors.id', '=', 'products.vendor_id')
            ->leftJoin('gift_certificates', 'gift_certificates.order_item_id', '=', 'order_items.id')
            ->orderBy('order_items.type', 'desc')
            ->orderBy('order_items.created_at');
    }

    public function consolidatedItems(): HasMany
    {
        return $this->hasMany(OrderItem::class)
            ->selectRaw('
                order_items.*,
                products.title,
                products.fulfillment_instructions,
                products.custom_sort,
                products.sku,
                products.inventory_type,
                packing_groups.position,
                CASE WHEN bundle_product.product_id IS NOT NULL THEN
                    bundle_product.product_id
                ELSE
                    order_items.product_id
                END AS consolidated_product_id,
                CASE WHEN bundle_product.product_id IS NOT NULL THEN
                    (order_items.qty * bundle_product.qty)
                ELSE
                    order_items.qty
                END AS consolidated_qty,
                CASE WHEN bundle_product.product_id IS NOT NULL THEN
                    (order_items.fulfilled_qty * bundle_product.qty)
                ELSE
                    order_items.fulfilled_qty
                END AS consolidated_fulfilled_qty
            ')
            ->leftJoin('bundle_product', 'bundle_product.bundle_id', '=', 'order_items.product_id')
            ->join('products', function($join){
                $join->on('products.id', '=', \DB::raw('
                    CASE WHEN bundle_product.product_id IS NOT NULL THEN bundle_product.product_id
                    ELSE order_items.product_id
                    END
                '));
            })
            ->leftJoin('packing_groups', 'packing_groups.id', '=', 'products.inventory_type');
    }

    /**
     * @return HasMany<OrderFee, $this>
     */
    public function fees(): HasMany
    {
        return $this->hasMany(OrderFee::class)->select([
            'id', 'order_id', 'title', 'amount', 'subtotal', 'qty',
            'taxable', 'tax', 'apply_limit', 'threshold', 'cap'
        ]);
    }

    /**
     * @return HasMany<OrderPayment, $this>
     */
    public function payments(): HasMany
    {
        return $this->hasMany(OrderPayment::class)->withTrashed();
    }

    /**
     * @return HasOne<User, $this>
     */
    public function staff(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'staff_id');
    }

    /**
     * @return BelongsTo<RecurringOrder, $this>
     */
    public function blueprint(): BelongsTo
    {
        return $this->belongsTo(RecurringOrder::class, 'blueprint_id', 'id')
            ->withTrashed();
    }

    /**
     * @return BelongsTo<Order, $this>
     */
    public function customerOrders(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'customer_id', 'customer_id');
    }

    /**
     * @return HasManyThrough<GiftCertificate, OrderItem, $this>
     */
    public function giftCards(): HasManyThrough
    {
        return $this->hasManyThrough(GiftCertificate::class, OrderItem::class);
    }

    /**
     * @return HasOne<User, $this>
     */
    public function customer(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'customer_id');
    }

    /**
     * @return HasOne<Payment, $this>
     */
    public function paymentMethod(): HasOne
    {
        return $this->hasOne(Payment::class, 'id', 'payment_id');
    }

    /**
     * @return HasOne<Card, $this>
     */
    public function paymentSource(): HasOne
    {
        return $this->hasOne(Card::class, 'id', 'payment_source_id');
    }

    /**
     * @return HasOne<Pickup, $this>
     */
    public function pickup(): HasOne
    {
        return $this->hasOne(Pickup::class, 'id', 'pickup_id')
            ->withVirtual()
            ->withTrashed();
    }

    /**
     * @return HasOne<Date, $this>
     */
    public function date(): HasOne
    {
        return $this->hasOne(Date::class, 'id', 'date_id');
    }

    /**
     * @return HasOne<User, $this>
     */
    public function updatedBy(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'last_modified_by');
    }

    /**
     * @return MorphMany<Event, $this>
     */
    public function events(): MorphMany
    {
        return $this->morphMany(Event::class, 'model');
    }

    public function hasAddress(): bool
    {
        return strlen($this->shipping_street) > 0
            && strlen($this->shipping_city) > 0
            && strlen($this->shipping_state) > 0
            && strlen($this->shipping_zip) > 0;
    }

    public function hasPayment(): bool
    {
        if (is_null($this->paymentMethod)) {
            return false;
        }
        if ($this->paymentMethod->isDisabled()) {
            return false;
        }
        if (count($this->pickup->payment_methods) && !in_array($this->payment_id, $this->pickup->payment_methods)) {
            return false;
        }
        if ($this->paymentMethod->isCard() && !$this->customer->hasCard()) {
            return false;
        }

        return true;
    }

    public function isProcessing(): bool
    {
        return $this->status_id === OrderStatus::processing();
    }

    public function isPacked(): bool
    {
        return $this->status_id === OrderStatus::packed();
    }

    public function isPickedUp(): bool
    {
        return $this->status_id === OrderStatus::pickedUp();
    }

    public function isCompleted(): bool
    {
        return $this->status_id === OrderStatus::completed();
    }

    public function isOnHold(): bool
    {
        return $this->status_id === OrderStatus::onHold();
    }

    public function shouldProcessWithCreditCard(): bool
    {
        return setting('process_order_charge', false)
            && $this->paidWithCreditCard()
            && $this->is_not_paid
            && $this->total_due >= 50;
    }

    public function paidWithCreditCard(): bool
    {
        return $this->paymentMethod && $this->paymentMethod->isCard();
    }

    public function isWholesale(): bool
    {
        return $this->type_id === 2;
    }

    public function isWholesaleCustomer(): bool
    {
        if ($this->customer) {
            return $this->customer->setting('pricing_tier', 'retail') == 'wholesale' ? true : false;
        }

        return false;
    }

    public function orderWindowIsClosed(): bool
    {
        return !$this->orderWindowIsOpen();
    }

    public function orderWindowIsOpen(): bool
    {
        // If the order status is not new, or the fulfillment status
        // is coming soon then ordering is closed.
        if (!$this->isNew() || $this->pickup->isComingSoon()) {
            return false;
        }

        // If there is no schedule assigned to the fulfillment
        // option then ordering is always open.
        if (!$this->pickup->schedule_id) {
            return true;
        }

        // If the order is confirmed we check the dates assigned to the order.
        // Otherwise, we check the dates on the fulfillment option.
        return $this->isConfirmed()
            ? $this->hasOpenModificationWindow()
            : $this->pickup->isCurrentlyAcceptingOrders();
    }

    public function hasOpenModificationWindow(): bool
    {
        if (is_null($this->deadline_date)) {
            return false;
        }

        return ! $this->deadlineHasPassed();
    }

    public function getCustomerName(): string
    {
        return $this->customer_first_name . ' ' . $this->customer_last_name;
    }

    public function hasPickupPoint(): bool
    {
        return ! is_null($this->pickup);
    }

    public function getDeliveryRate(): int
    {
        if ($this->hasNoPickupPoint()) {
            return 0;
        }

        return $this->pickup->delivery_rate;
    }

    public function hasNoPickupPoint(): bool
    {
        return is_null($this->pickup);
    }

    /**
     * Check to see if order meets the minimum required.
     */
    public function meetsOrderMinimum(): bool
    {
        if ($this->pickup) {
            return $this->subtotal >= $this->pickup->min_customer_orders;
        }

        return true;
    }

    public function getOrderMinimum(): int
    {
        return $this->pickup ? $this->pickup->min_customer_orders : 0;
    }

    public function hasUnfulfilledItems(): bool
    {
        return $this->items
            ->contains(fn (OrderItem $item) => $item->isUnfulfilled());
    }

    public function isStandAlone(): bool
    {
        return !$this->is_recurring;
    }

    public function markAsRecurring(): bool
    {
        $this->is_recurring = true;
        return $this->save();
    }

    public function markAsStandAlone(): bool
    {
        $this->is_recurring = false;
        return $this->save();
    }

    public function setSubtotalAttribute($value): void
    {
        $this->attributes['subtotal'] = max($value, 0);
    }

    public function setTotalAttribute($value): void
    {
        $this->attributes['total'] = max($value, 0);
    }

    public function getCustomerLastNameAttribute($value): string
    {
        return str_replace(['{', '}'], ['', ''], $value);
    }

    public function getCustomerFirstNameAttribute($value): string
    {
        return str_replace(['{', '}'], ['', ''], $value);
    }

    public function getCustomerFullNameAttribute(): string
    {
        return $this->customer_first_name . ' ' . $this->customer_last_name;
    }

    public function getSubtotalFormattedAttribute(): string
    {
        return money($this->subtotal);
    }

    public function getOrderDiscountFormattedAttribute(): string
    {
        return money($this->order_discount, '');
    }

    public function getFeesSubtotalFormattedAttribute(): string
    {
        return money($this->fees_subtotal);
    }

    public function getCreditAppliedFormattedAttribute(): string
    {
        return money($this->credit_applied);
    }

    public function getTaxFormattedAttribute(): string
    {
        return money($this->tax);
    }

    public function getTotalFormattedAttribute(): string
    {
        return money($this->total);
    }

    public function getDeliveryRateFormattedAttribute(): string
    {
        return money($this->delivery_rate);
    }

    public function getDeliveryFeeFormattedAttribute(): string
    {
        return money($this->delivery_fee);
    }

    public function deliveryFee()
    {
        if ($this->meetsFreeDelivery()) {
            return 0;
        }

        if ($this->isUnconfirmed() && $this->pickup) {
            $deliveryRate = $this->pickup->delivery_rate;
            $deliveryType = $this->pickup->setting('delivery_fee_type', 1);
        } else {
            $deliveryRate = $this->delivery_rate;
            $deliveryType = $this->delivery_fee_type;
        }

        if ($deliveryType == 2) { // Fixed
            return $deliveryRate;
        }

        return $deliveryRate * $this->weight; // The default is to calculate by weight;
    }

    public function meetsFreeDelivery(): bool
    {
        if (empty($this->pickup)) {
            return false;
        }

        if (empty($this->pickup->apply_limit) || $this->pickup->delivery_fee_cap > 0) {
            return false;
        }

        return $this->subtotal >= $this->pickup->delivery_total_threshold;
    }

    public function isUnconfirmed(): bool
    {
        return !$this->isConfirmed();
    }

    public function getPaymentsSubtotalAttribute(?int $value)
    {
        return $this->is_paid && is_null($value) ? $this->total : $value;
    }

    public function getTotalDueAttribute(): int
    {
        return $this->is_paid
            ? 0
            : $this->total - $this->payments_subtotal;
    }

    public function getConfirmedDateFormattedAttribute(): string
    {
        return $this->confirmed_date ? $this->confirmed_date->format('m/d/y') : '';
    }

    public function getPaymentDateFormattedAttribute(): string
    {
        return $this->payment_date ? $this->payment_date->format('m/d/y') : 'N/A';
    }

    public function getSubscriptionSavingsFormattedAttribute(): string
    {
        return money($this->subscription_savings);
    }

    public function getDeliveryDateAttribute(): ?Carbon
    {
        return $this->pickup_date;
    }

    public function getSubtotal(): int
    {
        $subtotal = $this->subtotal + $this->fees_subtotal + $this->tax;
        $discounts = $this->order_discount + $this->coupon_subtotal;

        $total = $subtotal - $discounts;

        return max($total, 0);
    }

    public function getRecurringOrderSavings(): int
    {
        $settings = app(SubscriptionSettingsService::class);

        if ( ( ! $this->isRecurring() && ! $this->isFromBlueprint())) {
            return 0;
        }

        if ($this->is_paid) {
            return $this->subscription_savings ?? 0;
        }

        return $this->calculateRecurringOrderSavings();
    }

    public function calculateRecurringOrderSavings(): int
    {
        if ( ! $this->isRecurring() && ! $this->isFromBlueprint()) return 0;

        return $this->rawRecurringOrderSavings();
    }

    public function rawRecurringOrderSavings(): int
    {
        $this->load(['items.product']);

        $subtotal = $this->items
            ->filter(fn (OrderItem $item) => $item->isEligibleForSubscriptionSavings())
            ->sum('subtotal');

        return (int) round(($subtotal * ( (float) app(SubscriptionSettingsService::class)->discountIncentive() / 100)));
    }

    public function totalSavingsOnSubscription(): float
    {
        return $this->rawRecurringOrderSavings();
    }

    public function formatItemsForGoogleAnalytics(): string
    {
        return json_encode($this->items->map(function ($item) {
            return [
                'id' => $item->product_id,
                'name' => $item->title,
                'quantity' => $item->qty,
                'price' => '' . money($item->price, '') . ''
            ];
        }));
    }

    public function getItSoonerDates(): Collection
    {
        $bluePrint = RecurringOrder::find($this->blueprint_id);

        if (is_null($bluePrint)) {
            return collect();
        }

        return $this->pickup->activeOrderWindowCollection($this->pickup->schedule?->ordering_in_advance);
    }

    public function releaseInventory(): self
    {
        $this->items->each(fn(OrderItem $item) => $item->releaseProductInventory());

        return $this;
    }

    public function refundCustomerCredit(): self
    {
        if (!$this->customer || $this->credit_applied === 0) {
            return $this;
        }

        $this->customer->applyCredit($this->credit_applied, 'Order #' . $this->id . ' canceled');
        $this->removeCredit($this->credit_applied);

        return $this;
    }

    public function applyCredit(int $credit, ?string $reason = null): self
    {
        if ($credit <= 0) {
            return $this;
        }

        DB::transaction(function () use ($credit, $reason) {
            $this->credit_applied += $credit;
            $this->save();

            $this->recordEvent('credit_applied', [
                'amount' => $credit,
            ], $reason);
        });

        return $this;
    }

    public function recordEvent(string $event_id, array $metadata, ?string $description = null): void
    {
        Event::create([
            'model_type' => Order::class,
            'model_id' => $this->id,
            'description' => $description,
            'event_id' => $event_id,
            'user_id' => auth()->user()->id ?? $this->customer?->id,
            'metadata' => json_encode($metadata)
        ]);
    }

    public function removeCredit(int $credit, ?string $reason = null): self
    {
        if ($credit <= 0) {
            return $this;
        }

        DB::transaction(function () use ($credit, $reason) {
            $this->credit_applied = max($this->credit_applied - $credit, 0);
            $this->save();

            $this->recordEvent('credit_removed', [
                'amount' => $credit,
            ], $reason);
        });

        return $this;
    }

    public function decrementCustomerOrderCount(): self
    {
        if (! $this->customer) {
            return $this;
        }

        $this->customer->decrementOrderCount($this->isRecurring() || $this->isFromBlueprint());

        return $this;
    }

    public function addTag(string $tagTitle): self
    {
        $tag = Tag::firstOrCreate([
            'title' => $tagTitle,
            'type' => Tag::type('order')
        ]);

        $this->tags()->syncWithoutDetaching([$tag->id]);

        return $this;
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(Tag::class)->withTimestamps();
    }

    public function pickupHasPassed(): bool
    {
        $pickup = $this->pickupDatetime();

        return ! is_null($pickup) && $pickup < now();
    }

    public function pickupDatetime(): ?Carbon
    {
        if (! $this->pickup_date) {
            return null;
        }

        return $this->pickup_date->copy()->endOfDay();
    }

    public function scopeUnconfirmed(Builder $query): Builder
    {
        return $query->where('confirmed', false);
    }

    public function scopeConfirmed(Builder $query): Builder
    {
        return $query->where('confirmed', true);
    }

    public function scopeUndecidedRecurringCart(Builder $query): Builder
    {
        return $query->unconfirmed()
            ->recurring()
            ->whereNull('blueprint_id');
    }

    public function schedule(): BelongsTo
    {
        return $this->belongsTo(Schedule::class);
    }

    public function isCustomersFirst(): bool
    {
        return Order::query()
            ->where('customer_id', $this->customer_id)
            ->where('id', '<>', $this->id)
            ->count() === 0;
    }

    public function isFirstSubscriptionOrder(): bool
    {
        return Order::query()
            ->where('customer_id', $this->customer_id)
            ->where('blueprint_id', $this->blueprint_id)
            ->count() === 1;
    }

    public function containsPackingGroups(array $packing_group_ids): bool
    {
        return $this->items
            ->filter(fn(OrderItem $item) => in_array($item->product->inventory_type, $packing_group_ids))
            ->isNotEmpty();
    }

    public function getSubscriptionProductIncentiveAttribute(): ?Product
    {
        return $this->findPromotionalItem()?->product;
    }

    public function findPromotionalItem(): ?OrderItem
    {
        return $this->items->filter(fn($item) => $item->type === 'promo')->first();
    }

    public function openDeliveryDates(): Collection
    {
        $orderingInAdvance = null;

        $schedule = $this->pickup->schedule;

        if ( ! is_null($schedule) && $schedule->ordering_in_advance >= 1) {
            $orderingInAdvance = min($schedule->ordering_in_advance, 365);
        }

        return $this->pickup
            ->activeOrderWindowCollection($orderingInAdvance)
            ->map(function (OrderWindow $order_window) {
                return [
                    'id' => $order_window->dateId(),
                    'date' => $order_window->deliveryDatetime()
                ];
            })
            ->values();
    }

    public function changePurchaseType(string $type, ?int $frequency = null, ?int $product_incentive_id = null): Order
    {
        if ($type === 'one_time_purchase' && $this->isFromBlueprint()) {
            Bugsnag::notifyException(new Exception("Order on blueprint being set to one time purchase: Order {$this->id}"));
        }

        if ($type === 'one_time_purchase') {
            $this->is_recurring = null;
            app(RemovePromoItems::class)->handle($this);
            session(['subscription_frequency' => null]);
            $this->updateTotals();
        }

        if ($type === 'recurring') {
            $this->is_recurring = true;
            app(AddPromoItem::class)->handle($this, $product_incentive_id);
            session(['subscription_frequency' => $frequency]);
            $this->updateTotals();
        }

        return $this;
    }

    public function companyName(): string
    {
        return $this->customer && $this->customer->company_name
            ? $this->customer->company_name
            : $this->customer_first_name . ' ' . $this->customer_last_name;
    }

    public function hasTaxableDeliveryFee(): bool
    {
        return $this->pickup && $this->pickup->tax_delivery_fee;
    }

    public function customerAccountingId(): string
    {
        if ($this->customer?->accounting_id) {
            return $this->customer->accounting_id;
        }

        $first_name =  $this->customer_first_name ?: 'N/A';
        $last_name = $this->customer_last_name ?: 'N/A';
        $email = $this->customer_email;

        if ($this->customer) {
            $first_name = $this->customer->first_name ?: $first_name;
            $last_name = $this->customer->last_name ?: $last_name;
            $email = $this->customer->email ?: $email;
        }

        return "{$first_name} {$last_name} ({$email})";
    }

    public function formattedTrackingId(): ?string
    {
        return $this->tracking_id
            ? substr($this->tracking_id, 0, 30)
            : null;
    }

    public function formattedPackingNotes(): string
    {
        return substr($this->packing_notes, 0, 4000);
    }

    public function formattedInvoiceNotes(): string
    {
        return substr($this->invoice_notes, 0, 1000);
    }

    public function taxRate(): float
    {
        if ( ! $this->pickup) {
            return 0.0;
        }

        return $this->pickup->tax_rate ?? 0.0;
    }

    public function paymentReference(): ?string
    {
        if ($this->paymentMethod && $this->paymentMethod->isCard()) {
            return count($this->payments) ? substr($this->payments->first()->payment_id, 0, 21) : null;
        }

        return substr($this->payment_notes, 0, 21);
    }

    /**
     * @return HasMany<OrderItem, $this>
     */
    public function giftCardItems(): HasMany
    {
        return $this->items()->whereHas('product', function (Builder $query) {
            return $query->where('type_id', ProductType::GIFT_CARD->value);
        });
    }

    /**
     * @return HasMany<OrderItem, $this>
     */
    public function items(): HasMany
    {
        $orderBy = 'unit_of_issue';
        $orderBy2 = 'order_items.id';
        $sort = 'desc';
        $sort2 = 'desc';

        $sortOrder = setting('order_items_sort_order');
        if ($sortOrder) {
            /** @var array<int, string> $ordering */
            $ordering = explode('-', $sortOrder);
            $orderBy = $ordering[0] ?? 'products.inventory_type';
            $sort = $ordering[1] ?? 'desc';
        }

        $sortOrder2 = setting('order_items_sort_order_2');

        if ($sortOrder2) {
            /** @var array<int, string> $ordering */
            $ordering = explode('-', $sortOrder2);
            $orderBy2 = $ordering[0] ?? 'order_items.id';
            $sort2 = $ordering[1] ?? 'desc';
        }

        return $this->hasMany(OrderItem::class)
            ->selectRaw('order_items.id, order_items.order_id, order_items.product_id, order_items.title, order_items.type,
                order_items.qty,order_items.weight, order_items.original_weight, order_items.unit_price, products.sale as on_sale,
                order_items.original_unit_price, order_items.unit_of_issue, order_items.subtotal,order_items.taxable, order_items.store_price, order_items.created_at,order_items.updated_at,
                order_items.tax, order_items.discount, order_items.discount_type, order_items.stock_status, fulfilled_qty, products.sku, products.type_id, products.fulfillment_instructions,
                products.custom_sort, products.inventory, products.track_inventory, products.inventory_type, products.is_grouped, products.is_bundle as bundle,
                vendors.title as vendor, vendors.id as vendor_id, gift_certificates.code, gift_certificates.active, gift_certificates.redeemed')
            ->join('products', 'products.id', '=', 'order_items.product_id')
            ->leftJoin('vendors', 'vendors.id', '=', 'products.vendor_id')
            ->leftJoin('gift_certificates', 'gift_certificates.order_item_id', '=', 'order_items.id')
            ->orderBy($orderBy, $sort)
            ->orderBy($orderBy2, $sort2)
            ->orderBy('order_items.id');
    }

    public function hasSubscriptionEligibleItems(): bool
    {
        return $this->subscriptionItems()
            ->filter(fn(OrderItem $item) => $item->type !== 'promo')
            ->isNotEmpty();
    }

    public function hasOnlyVirtualGiftCards(): bool
    {
        return $this->rawItems()
            ->with('product')
            ->select(['id', 'product_id'])
            ->get()
            ->filter(fn(OrderItem $item) => $item->product->isGiftCard() && $item->isFulfilledVirtually())
            ->isNotEmpty();
    }

    /**
     * @return HasMany<OrderItem, $this>
     */
    public function rawItems(): HasMany
    {
        return $this->hasMany(OrderItem::class)
            ->orderBy('order_items.created_at');
    }

    public function isFulfilledVirtually(): bool
    {
        return $this->rawItems()
            ->with('product')
            ->select(['id', 'product_id'])
            ->get()
            ->filter(fn(OrderItem $item) => $item->isFulfilledVirtually())
            ->isNotEmpty();
    }

    public function isGift(): bool
    {
        return ! empty($this->recipient_email);
    }

    public function isFirstTimeOrder(): bool
    {
        return (bool) $this->first_time_order;
    }

    public function scopeSince(Builder $builder, CarbonInterface $date)
    {
        return $builder->where('confirmed_date', '>=', $date);
    }

    public function oneTimeConfirmationEmailTemplate(): ?Template
    {
        $product = null;

        if ($this->status_id === OrderStatus::preOrder()) {
            $product = $this->rawItems()->first()?->product;
        }

        return Template::find($product?->setting('confirmation_email_template_id'))
            ?? Template::find($this->pickup->setting('email_order_confirmation_template'))
            ?? Template::find(setting('email_order_confirmation_template'));
    }

    public function nextSubscriptionOrderWindow(int $reorder_frequency): ?OrderWindow
    {
        return $this->pickup->schedule->dates()
            ->whereDate('pickup_date', '>=', $this->pickup_date->copy()->addDays($reorder_frequency))
            ->first()
            ?->toOrderWindows();
    }

    protected function isNotPaid(): Attribute
    {
        return Attribute::make(
            get: fn () => !$this->is_paid,
        );
    }

    protected function isPaid(): Attribute
    {
        return Attribute::make(
            get: fn () => $this->paid ?? false,
        );
    }

    protected function casts(): array
    {
        return [
            'payment_date' => 'datetime',
            'pickup_date' => 'datetime',
            'original_pickup_date' => 'datetime',
            'deadline_date' => 'datetime',
            'due_date' => 'datetime',
            'confirmed_date' => 'datetime',
            'canceled_at' => 'datetime',
            'skipped_at' => 'datetime',
            'weight' => 'float',
            'tax_rate' => 'float',
            'canceled' => 'boolean',
            'paid' => 'boolean',
            'confirmed' => 'boolean',
            'processed' => 'boolean',
            'is_recurring' => 'boolean',
            'pack_deadline_at' => 'datetime',
        ];
    }
}

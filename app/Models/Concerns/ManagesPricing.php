<?php

namespace App\Models\Concerns;

use App\Models\Price;
use App\Models\Product;

/**
 * @mixin \App\Models\Product
 */
trait ManagesPricing
{
    public function getGroupPriceAttribute(int $quantity = 1): int
    {
        return $this->getUnitPrice($quantity);
    }

    public function getUnitPrice(int $quantity = 1): int
    {
        list($base_unit_price, $base_sale_unit_price) = $this->basePricesForQuantity($quantity);

        $base_price = $this->isOnSale() ? $base_sale_unit_price : $base_unit_price;

        return $this->applyPriceGroup($base_price);
    }

    protected function basePricesForQuantity(int $quantity): array
    {
        // price backup logic - use product record
        $unit_price = $this->unit_price ?? 0;
        $sale_unit_price = $this->sale_unit_price ?? 0;

        $price = $this->priceForQuantity($quantity);

        if ($price) {
            $unit_price = $price->unit_price ?? $unit_price;
            $sale_unit_price = $price->sale_unit_price ?? $sale_unit_price;
        }

        return [$unit_price, $sale_unit_price];
    }

    protected function priceForQuantity(int $quantity): ?Price
    {
        if ($quantity === 1) {
            return $this->defaultPrice;
        }
        
        return $this->prices()
            ->where('quantity', '<=', $quantity)
            ->orderBy('quantity', 'desc')
            ->first();
    }

    public function isOnSale(): bool
    {
        return (bool) $this->sale;
    }

    protected function applyPriceGroup(int $base_price): int
    {
        if ($this->isGiftCard() || is_null($this->price)) {
            return $base_price;
        }

        if ($this->price->group?->isFixed()) {
            return $this->price->unit_price;
        }

        if ($this->price->group?->isPercentageIncrease()) {
            return (int) round($base_price + (($base_price * $this->price->group->amount) / 100));
        }

        if ($this->price->group?->isPercentageDecrease()) {
            return (int) round($base_price - (($base_price * $this->price->group->amount) / 100));
        }

        return $base_price;
    }

    public function getUnitSavings(int $quantity = 1): int
    {
        return $this->getRegularUnitPrice($quantity) - $this->getUnitPrice($quantity);
    }

    public function getRegularUnitPrice(int $quantity = 1): int
    {
        list($base_unit_price, $base_sale_unit_price) = $this->basePricesForQuantity($quantity);

        return $this->applyPriceGroup($base_unit_price);
    }

    public function getSavings(int $quantity = 1): int
    {
        return $this->getRegularPrice($quantity) - $this->getPrice($quantity);
    }

    public function getRegularPrice(int $quantity = 1): int
    {
        return $this->isPricedByWeight()
            ? (int) round($this->getRegularUnitPrice($quantity) * $this->weight)
            : $this->getRegularUnitPrice($quantity);
    }

    public function isPricedByWeight(): bool
    {
        return $this->unit_of_issue === 'weight';
    }

    public function getPrice(int $quantity = 1): int
    {
        return $this->isPricedByWeight()
            ? ((int) round($this->getUnitPrice($quantity) * $this->weight))
            : $this->getUnitPrice($quantity);
    }

    public function getBundleSavings(int $quantity = 1): int
    {
        return $this->getRegularPrice($quantity) - $this->getPrice($quantity);
    }

    public function getBundleSavingForProduct(Product $product, int $quantity = 1): int
    {
        return $this->getUnBundledProductPrice($product, $quantity) - $this->getPrice($quantity);
    }

    public function getUnBundledProductPrice(?Product $baseProduct = null, int $quantity = 1): int
    {
        if (!count($this->bundle) || !$this->isBundle()) {
            return 0;
        }

        $base_product = $this->bundle
            ->first(fn ($bundledProduct) => $bundledProduct->id === $baseProduct->id);

        if (is_null($base_product)) {
            return 0;
        }

        return $baseProduct->getPrice($quantity) * $base_product->getRelationValue('pivot')->qty;
    }

    public function isFree(int $quantity = 1): bool
    {
        return $this->getPrice($quantity) <= 0;
    }

    public function cost(): int
    {
        return $this->isPricedByWeight()
            ? (int) round($this->item_cost * $this->weight)
            : (int) $this->item_cost;
    }
}

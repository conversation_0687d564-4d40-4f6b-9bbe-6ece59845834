<?php

namespace App\Models\Concerns;

use App\Cart\Item;
use App\Cart\Subscription;
use App\Contracts\Cartable;
use App\Exceptions\BackOrderException;
use App\Exceptions\CouponInvalidException;
use App\Exceptions\ExclusivityException;
use App\Models\Card;
use App\Models\Coupon;
use App\Models\Date;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\ProductPriceGroup;
use App\Models\User;
use App\Services\SettingsService;
use App\Services\SubscriptionSettingsService;
use App\Traits\PickupDateSelectBuilder;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;

trait IsCartable
{
    use PickupDateSelectBuilder, HasCartQuantitiesByProductIdMap;

    public function addProduct(Product $product, int $quantity = 1): Item
    {
        $item = $this->addItem($product, $quantity);

        $this->updateTotals();

        return $item->toCartItem();
    }

    public function cartId(): ?int
    {
        return $this->id;
    }

    public function cartIsEmpty(): bool
    {
        return once(function () {
            return $this->rawItems()
                    ->whereNot('type', 'promo')
                    ->count() === 0;
        });
    }

    public function cartPricingGroup(): ?ProductPriceGroup
    {
        return once(function () {
            $group_id = $this->cartPricingGroupId();

            if (is_null($group_id)) {
                return null;
            }

            return ProductPriceGroup::find($group_id);
        });
    }

    public function cartPricingGroupId(): ?int
    {
        return once(function () {
            $customer = $this->cartCustomer();

            if ($customer?->pricing_group_id) {
                return (int) $customer->pricing_group_id;
            }

            $location = $this->cartLocation();

            if ($location?->pricing_group_id) {
                return (int) $location->pricing_group_id;
            }

            return null;
        });
    }

    public function cartCustomer(): ?User
    {
        return once(fn() => $this->customer);
    }

    public function cartLocation(): ?Pickup
    {
        return once(fn() => $this->pickup);
    }

    public function cartDate(): ?Date
    {
        return $this->date;
    }

    /**
     * @throws ExclusivityException
     * @throws BackOrderException
     */
    public function addItemToCart(Item $item): Cartable
    {
        $this->addItem($item->product, $item->quantity);
        return $this;
    }

    public function incrementCartItemQuantity($id): Cartable
    {
        $item = $this->items()->find($id);

        if (is_null($item)) {
            return $this;
        }

        $this->updateItemQuantity($item, $item->qty + 1);

        $this->updateTotals();

        return $this;
    }

    public function decrementCartItemQuantity($id): Cartable
    {
        $item = $this->items()->find($id);

        if (is_null($item)) {
            return $this;
        }

        $new_quantity = $item->qty - 1;

        if ($new_quantity <= 0) {
            return $this->removeCartItem($id);
        }

        $this->updateItemQuantity($item, $new_quantity);

        $this->updateTotals();

        return $this;
    }

    public function removeCartItem($id): Cartable
    {
        $item = $this->items()->find($id);

        if (is_null($item)) {
            return $this;
        }

        $this->removeItem($item);

        if ($this->isRecurring() && ! $this->cartIsEligibleForSubscription()) {
            $this->setCartAsOneTimePurchase();
        }

        $this->updateTotals();

        return $this;
    }

    public function removeItem($id): void
    {
        /** @var OrderItem $item */
        $item = OrderItem::find($id);

        Order::find($item->order_id)
            ->removeItem($item);
    }

    public function isRecurring(): bool
    {
        return (bool) $this->is_recurring;
    }

    public function cartIsEligibleForSubscription(): bool
    {
        if ($this->isFromBlueprint()) {
            return true;
        }

        return ! ($this->cartCustomer()?->hasRecurringOrder() ?? false)
            && $this->cartLocation()?->schedule?->isRepeating()
            && count($this->cartLocation()->schedule->reorder_frequency) > 0
            && $this->hasSubscriptionEligibleItems();
    }

    public function setCartAsOneTimePurchase(): Cartable
    {
        $this->changePurchaseType('one_time_purchase');
        return $this;
    }

    /**
     * @throws ExclusivityException
     */
    public function updateCartLocation(Pickup $location, bool $remove_excluded_items = false): Cartable
    {
        if ( ! $this->exists()) {
            $this->setRelation('pickup', $location);
            return $this;
        }

        $this->updateFulfillmentOption($location, $remove_excluded_items);

        $this->updateTotals();

        return $this;
    }

    public function updateCartDate(?Date $date): Cartable
    {
        $date_id = null;
        $deadline_date = null;
        $pickup_date = null;

        if ( ! is_null($date)) {
            $order_window = $date->toOrderWindows();
            $date_id = $date->id;
            $deadline_date = $order_window->deadlineDatetime();
            $pickup_date = $order_window->deliveryDatetime();
        }

        $this->date_id = $date_id;
        $this->deadline_date = $deadline_date;
        $this->pickup_date = $pickup_date;
        $this->original_pickup_date = $pickup_date;

        $this->save();

        return $this;
    }

    public function setCartAsSubscriptionPurchase(?int $frequency = null, ?int $product_incentive_id = null): Cartable
    {
        $this->changePurchaseType(
            'recurring',
                $frequency ?? $this->cartLocation()?->schedule?->defaultSubscriptionFrequency(),
            $product_incentive_id
        );
        return $this;
    }

    /**
     * @throws CouponInvalidException
     */
    public function applyCouponToCart(Coupon $coupon): Cartable
    {
        $this->applyCoupon($coupon);
        return $this;
    }

    public function applyConditionalCouponToCart(Coupon $coupon): Cartable
    {
        // Not supported
        return $this;
    }

    public function hasCouponApplied(string $code): bool
    {
        $coupon = Coupon::where('code', $code)->first();

        if (is_null($coupon)) {
            return false;
        }

        return $this->discounts()->where('coupon_id', $coupon->id)->exists();
    }

    public function hasConditionalCouponApplied(string $code): bool
    {
        // Not supported
        return false;
    }

    public function removeCouponFromCart(string $code): Cartable
    {
        $coupon = Coupon::where('code', $code)->first();

        if (is_null($coupon)) {
            return $this;
        }

        return $this->removeCoupon($coupon)->updateTotals();
    }

    public function removeConditionalCouponFromCart(string $code): Cartable
    {
        // Not supported
        return $this;
    }

    public function cartCoupons(): Collection
    {
        return $this->discounts()
            ->get()
            ->map(fn(Coupon $coupon) => new \App\Cart\Coupon(
                name: $coupon->description,
                amount: $coupon->savings ?? 0,
                code: $coupon->code
            ));
    }

    public function cartConditionalCoupons(): Collection
    {
        return collect();
    }

    public function cartTotal(): int
    {
        return max(0, $this->cartTotalBeforeStoreCredit() - $this->cartStoreCreditTotal());
    }

    public function cartTotalBeforeStoreCredit(): int
    {
        return $this->cartSubtotal()
            + $this->cartLocationFeeTotal()
            + $this->cartDeliveryTotal()
            + $this->cartTaxTotal()
            - $this->cartSubscriptionSavingsTotal()
            - $this->cartCouponTotal();
    }

    public function cartSubtotal(): int
    {
        return $this->rawItems()
            ->with(['product.price' => fn($q) => $q->where('group_id', $this->cartPricingGroupId() ?? 0)])
            ->get()
            ->sum('subtotal');
    }

    public function cartLocationFeeTotal(): int
    {
        if ($this->cartCustomer()->exemptFromFees()) {
            return 0;
        }

        return $this->cartLocation()?->fees()->sum('amount');
    }

    public function cartDeliveryTotal(): int
    {
        if ($this->cartCustomer()->exemptFromFees()) {
            return 0;
        }

        $location = $this->cartLocation();

        $amount = $location->delivery_rate;

        if ($location->deliveryFeeByWeight()) {
            $amount = (int) round($amount * $this->cartWeight());
        }

        if ($location->deliveryFeeHasCap() && $this->cartSubtotal() >= $location->deliveryCapThreshold()) {
            $amount = $location->deliveryCap();
        }

        return $amount;
    }

    public function cartWeight(): float
    {
        return once(fn() => $this->rawItems()->sum('weight'));
    }

    public function cartTaxTotal(): int
    {
        $location = $this->cartLocation();

        $rate = $location->tax_rate ?? 0;

        if ( ! $rate || $this->cartCustomer()?->exemptFromTax()) {
            return 0;
        }

        $total = $this->cartItemTaxTotal($rate) + $this->locationFeeTaxTotal($rate);

        if ($location->deliveryFeeIsTaxed()) {
            $total += $this->cartDeliveryTaxTotal($rate);
        }

        return $total;
    }

    private function cartItemTaxTotal(float $rate): int
    {
        $taxable_subtotal = $this->rawItems()
            ->with('product')
            ->get()
            ->filter(fn(OrderItem $item) => (bool) $item->product->taxable)
            ->sum('subtotal');

        return (int) round($taxable_subtotal * $rate);
    }

    private function locationFeeTaxTotal(float $rate): int
    {
        $taxable_subtotal = $this->cartLocation()
            ->fees()
            ->where(['taxable' => true])
            ->sum('amount');

        return (int) round($taxable_subtotal * $rate);
    }

    private function cartDeliveryTaxTotal(float $rate): int
    {
        return (int) round($this->cartDeliveryTotal() * $rate);
    }

    public function cartSubscriptionSavingsTotal(): int
    {
        if ($this->purchaseType() === Cartable::ONE_TIME_PURCHASE) {
            return 0;
        }

        return $this->cartPotentialSubscriptionSavingsTotal();
    }

    public function purchaseType(): int
    {
        return $this->isRecurring()
            ? Cartable::SUBSCRIPTION_PURCHASE
            : Cartable::ONE_TIME_PURCHASE;
    }

    public function cartPotentialSubscriptionSavingsTotal(): int
    {
        return $this->rawRecurringOrderSavings();
    }

    public function cartCouponTotal(): int
    {
        return $this->discounts()->sum('savings');
    }

    public function cartStoreCreditTotal(): int
    {
        return $this->cartCustomer()->credit;
    }


    // NEW UNDER HERE

    /**
     * @throws Exception
     */
    public function setItemQuantity($id, int $quantity): Item
    {
        /** @var OrderItem|null $item */
        $item = $this->items()->find($id);

        if (is_null($item) && $quantity !== 0) {
            throw new Exception('Item '.$id.' could not be found on order '.$this->id);
        }

        $this->updateItemQuantity($item, $quantity);

        return $item->fresh(['product'])->toCartItem();
    }

    public function updateLocation(Pickup $location, bool $remove_excluded_products = false): Cartable
    {
        return $this->updateFulfillmentOption($location, $remove_excluded_products);
    }

    public function setAsOneTimePurchase(): Cartable
    {
        return $this->changePurchaseType('one_time_purchase');
    }

    public function setAsSubscriptionPurchase(?int $frequency = null, ?int $product_incentive_id = null): Cartable
    {
        return $this->changePurchaseType('recurring', $frequency, $product_incentive_id);
    }

    public function oneTimeItems(): Collection
    {
        return $this->rawItems()
            ->with(['product.price' => fn($q) => $q->where('group_id', $this->cartPricingGroupId() ?? 0)])
            ->get()
            ->filter(fn(OrderItem $item) => app(SubscriptionSettingsService::class)->excludedProductIds()->contains($item->product_id));
    }

    public function subscriptionItems(): Collection
    {
        return $this->rawItems()
            ->with(['product.price' => fn($q) => $q->where('group_id', $this->cartPricingGroupId() ?? 0)])
            ->get()
            ->filter(fn(OrderItem $item) => app(SubscriptionSettingsService::class)->excludedProductIds()->doesntContain($item->product_id));
    }

    public function cartPotentialSubscriptionProductValue(): int
    {
        $id = $this->cartSubscriptionProductIncentive()->id
            ?? app(SubscriptionSettingsService::class)->defaultProductIncentiveId();

        if (is_null($id)) return 0;

        $product = Product::query()
            ->select(Product::attributesForCart())
            ->with([
                'price' => fn($q) => $q->where('group_id', $this->cartPricingGroupId() ?? 0)
            ])
            ->find($id);

        if (is_null($product)) return 0;

        return $product->getRegularUnitPrice() - $product->getUnitPrice();
    }

    public function cartSubscriptionProductIncentive(): ?Product
    {
        return $this->rawItems()
            ->where('type', 'promo')
            ->with(['product.price' => fn($q) => $q->where('group_id', $this->cartPricingGroupId() ?? 0)])
            ->first()
            ?->product;
    }

    public function toCartArray(): array
    {
        return [
            'type' =>  'order',
            'id' =>  $this->id,
            'purchase_type' => $this->purchaseType() === Cartable::SUBSCRIPTION_PURCHASE
                ? 'recurring'
                : 'one_time_purchase',
            'date_id' =>  null,
            'delivery_method_id' =>  $this->pickup?->id,
            'items' =>  $this->rawItems()
                ->with([
                    'product' =>  fn($q) => $q->select(Product::attributesForCart()),
                    'product.price' => fn($q) => $q->where('group_id', $this->cartPricingGroupId() ?? 0)
                ])
                ->get()
                ->filter(fn(OrderItem $item) => $item->type !== 'promo')
                ->map(function (OrderItem $item) {

                    // This prevents bundle products from being a part of the cart array
                    $item->product->setVisible(array_merge(Product::attributesForCart(),['price']));

                    return [
                        'id' => $item->id,
                        'product' => $item->product->toArray(),
                        'quantity' => $item->qty,
                        'price' => $item->price,
                        'weight' => $item->weight(),
                    ];
                })
                ->toArray(),
            'subscription' => $this->cartSubscription()?->toArray(),
            'discounts' => [
                'coupons' => $this->discounts
                    ->map(fn (Coupon $coupon) => $coupon->toCartCoupon()->toArray())
                    ->toArray(),
                'gift_card' =>  [
                    'name' =>  '',
                    'code' =>  '',
                    'amount' =>  0,
                ],
                'store_credit' =>  [
                    'amount' =>  min($this->customer->credit ?? 0, $this->getSubtotal()),
                ]
            ],
            'customer' => [
                'id' => $this->customer_id,
                'first_name' => $this->customer?->first_name,
                'last_name' => $this->customer?->last_name,
                'email' => $this->customer?->email,
                'phone' => $this->customer?->phone,
                'save_for_later' => true,
                'opt_in_to_sms' => ! is_null($this->customer?->subscribed_to_sms_marketing_at),
                'subscribed_to_sms' => ! is_null($this->customer?->subscribed_to_sms_marketing_at),
            ],
            'shipping' => [
                'street' => $this->shipping_street ?: $this->customer?->street,
                'street_2' => $this->shipping_street_2 ?: $this->customer?->street_2,
                'city' => $this->shipping_city ?: $this->customer?->city,
                'state' => $this->shipping_state ?: $this->customer?->state,
                'zip' => $this->shipping_zip ?: $this->customer?->zip,
                'country' => $this->customer->country
                    ?? app(SettingsService::class)->farmCountry(),
                'save_for_later' => true
            ],
            'billing' => [
                'method' => null,
                'source_id' => $this->customer?->checkout_card_id ?: null,
                'save_for_later' => true
            ]
        ];
    }

    public function cartSubscription(): ?Subscription
    {
        if ($this->purchaseType() === Cartable::ONE_TIME_PURCHASE) {
            return null;
        }

        return new Subscription(
            frequency: session('subscription_frequency')
                ?? $this->cartLocation()?->schedule?->defaultSubscriptionFrequency(),
            product_incentive_id: $this->rawItems()
                ->where('type', 'promo')
                ->first()
                ->product_id
                ?? app(SubscriptionSettingsService::class)->defaultProductIncentiveId()
        );
    }

    public function cartProductsUnderRequiredOrderMinimum(): Collection
    {
        $subtotal = $this->subtotal;

        return $this->itemsInCart()
            ->map(function (Item $item) use ($subtotal) {
                $product_minimum = $item->product->setting('order_minimum');
                if (is_null($product_minimum) || $subtotal >= formatCurrencyForDB($product_minimum)) {
                    return null;
                }

                return $item->product;
            })
            ->filter()
            ->sortByDesc(fn(Product $product) => $product->setting('order_minimum'));
    }

    /** @return Collection<Item> */
    public function itemsInCart(): Collection
    {
        return once(function () {
            return $this->rawItems()
                ->with([
                    'product' =>  fn($q) => $q->select(Product::attributesForCart()),
                    'product.price' => fn($q) => $q->where('group_id', $this->cartPricingGroupId() ?? 0)
                ])
                ->whereNot('type', 'promo')
                ->get()
                ->map(fn (OrderItem $item) => $item->toCartItem());
        });
    }

    public function itemCount(): int
    {
        return $this->itemsInCart()->sum(fn(Item $item) => $item->quantity);
    }

    public function cartItemsWithoutAvailableInventory(): Collection
    {
        return $this->itemsInCart()
            ->map(fn(Item $item) => [
                'item' => $item,
                'inventory' => [
                    'is_unlimited' => $item->product->doesNotTrackInventory(),
                    'available' => ! $item->product->doesNotTrackInventory()
                        ? $item->product->remainingInventory()
                        : null
                ]
            ])
            ->filter(function (array $item) {
                return ! $item['inventory']['is_unlimited']
                    && ! is_infinite($item['inventory']['available'])
                    && ($item['inventory']['available'] - $item['item']->quantity) < 0;
            });
    }

    public function hasValidSubscription(): bool
    {
        $subscription = $this->cartSubscription();

        return ! is_null($subscription?->frequency)
            && (
                ! is_null($subscription->product_incentive_id)
                || ! app(SubscriptionSettingsService::class)->hasProductIncentive()
            );
    }

    public function hasContactInfo(): bool
    {
        $contact = $this->getContactInfo();

        return strlen($contact['first_name']) > 0
            && strlen($contact['last_name']) > 0
            && strlen($contact['phone']) > 0;
    }

    public function getContactInfo(): array
    {
        return [
            'first_name' => $this->customer_first_name,
            'last_name' => $this->customer_last_name,
            'email' => $this->customer->email,
            'phone' => $this->customer_phone,
        ];
    }

    public function hasShippingInfo(): bool
    {
        $shipping = $this->getShippingInfo();

        return strlen($shipping['street']) > 0
            && strlen($shipping['city']) > 0
            && strlen($shipping['state']) > 0
            && strlen($shipping['zip']) > 0;
    }

    public function getShippingInfo(): array
    {
        return [
            'street' => $this->shipping_street,
            'street_2' => $this->shipping_street_2,
            'city' => $this->shipping_city,
            'state' => $this->shipping_state,
            'zip' => $this->shipping_zip,
            'country' => $this->shipping_country,
            'save_for_later' => true,
        ];
    }

    public function hasSelectedBillingMethod(): bool
    {
        $billing_method = $this->cartBillingMethod();

        if (is_null($billing_method) || $billing_method->isDisabled()) {
            return false;
        }

        $billing_methods = $this->cartLocation()?->payment_methods;

        if (empty($billing_methods)) {
            $billing_methods = Payment::enabled()->pluck('id')->toArray();
        }

        return in_array($billing_method->id, $billing_methods);
    }

    public function cartBillingMethod(): ?Payment
    {
        $this->load('paymentMethod');

        return $this->paymentMethod;
    }

    public function cartBillingSource(): ?Card
    {
        return $this->cartCustomer()
            ?->cards()
            ->find($this->payment_source_id);
    }

    public function cartSubscriptionProductIncentiveItem(): ?Item
    {
        $product = $this->cartSubscriptionProductIncentive();

        if (is_null($product)) return null;

        return new Item(id: 'promo', product: $product);
    }

    public function cartMinimum(): int
    {
        return 0;
    }

    public function setContactInfo(array $contact_info): Cartable
    {
        $this->update([
            'customer_first_name' => $contact_info['first_name'] ?? $this->customer_first_name,
            'customer_last_name' => $contact_info['last_name'] ?? $this->customer_last_name,
            'customer_phone' => $contact_info['phone'] ?? $this->customer_phone,
        ]);

        return $this;
    }

    public function setShippingInfo(array $shipping_info): Cartable
    {
        $this->update([
            'shipping_street' => $shipping_info['street'] ?? $this->shipping_street,
            'shipping_street_2' => $shipping_info['street_2'] ?? $this->shipping_street_2,
            'shipping_city' => $shipping_info['city'] ?? $this->shipping_city,
            'shipping_state' => $shipping_info['state'] ?? $this->shipping_state,
            'shipping_zip' => $shipping_info['zip'] ?? $this->shipping_zip,
        ]);

        return $this;
    }

    public function getBillingInfo(): array
    {
        return [
            'method' => $this->paymentMethod?->key,
            'payment_source_id' => $this->payment_source_id,
        ];
    }

    public function setBillingInfo(array $billing_info): Cartable
    {
        $this->update([
            'payment_id' => isset($billing_info['method'])
                ? (Payment::where('key', $billing_info['method'])->first()->id ?? $this->payment_id)
                : $this->payment_id,
            'payment_source_id' => $billing_info['source_id'] ?? $this->payment_source_id,
        ]);

        $this->load('paymentMethod');

        return $this;
    }

    public function getCustomerNotes(): ?string
    {
        return $this->customer_notes;
    }

    public function belongsInDeliveryZone(?string $zip = null, ?string $state = null): bool
    {
        $delivery_method = $this->cartLocation();

        if (is_null($delivery_method) || ! $delivery_method->isDeliveryZone()) {
            return true;
        }

        if (DB::table('pickup_zips')
            ->where([
                'pickup_id' => $delivery_method->id,
                'zip' => formatPostalCode($zip ?? $this->shipping_zip)
            ])
            ->exists()
        ) {
            return true;
        }

        if (DB::table('pickup_states')
            ->where([
                'pickup_id' => $delivery_method->id,
                'state' => trim($state ?? $this->shipping_state)
            ])
            ->exists()
        ) {
            return true;
        }

        return false;
    }

    public function removeOutOfStockItems(bool $shouldUpdateQuantity = false): self
    {
        foreach ($this->itemsInCart() as $item) {
            if ( ! $item->product->canAddAmountToCart($item->quantity, $this)) {
                $remaining_inventory = $item->product->remainingInventory();
                if ($remaining_inventory === 0 || ! $shouldUpdateQuantity) {
                    $this->removeCartItem($item->id);
                } else {
                    $this->updateCartItemQuantity($item->id, $remaining_inventory);
                }
            }
        }

        $this->updateTotals();

        return $this;
    }

    public function updateCartItemQuantity($id, int $quantity): Cartable
    {
        $item = $this->items()->find($id);

        if (is_null($item)) {
            return $this;
        }

        $this->updateItemQuantity($item, $quantity);

        $this->updateTotals();

        return $this;
    }

    public function cartProductIds(): Collection
    {
        return $this->rawItems()->pluck('product_id');
    }
}

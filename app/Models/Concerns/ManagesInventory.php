<?php

namespace App\Models\Concerns;

use App\Contracts\Cartable;
use App\Models\Product;

/**
 * @mixin \App\Models\Product
 */
trait ManagesInventory
{
    public const TRACK_INVENTORY_OPTIONS = [
        'yes' => 'Track inventory',
        'bundle' => 'Track inventory on bundle',
        'no' => 'Do not track inventory'
    ];

    public function getOutOfStockMessage(): string
    {
        return "There is not enough {$this->title} left in stock.";
    }

    public function isBelowReorderThreshold()
    {
        if ($this->tracksInventoryByBundleItem()) {
            return $this->bundle
                ->filter(fn(Product $product) => $product->isBelowReorderThreshold())
                ->isNotEmpty();
        }

        return $this->onSiteInventory() - $this->reorderThreshold() <= 0;
    }

    public function tracksInventoryByBundleItem(): bool
    {
        return $this->isBundle()
            && $this->inventoryTrackingMethod() === 'bundle';
    }

    public function isBundle(): bool
    {
        return $this->is_bundle ?? false;
    }

    public function inventoryTrackingMethod(): string
    {
        return $this->track_inventory;
    }

    public function onSiteInventory(): int
    {
        return $this->inventory ?? 0;
    }

    public function reorderThreshold(): int
    {
        return $this->stock_out_inventory ?? 0;
    }

    public function canAddAmountToCart(int $amount, Cartable $cart): bool
    {
        if ($this->doesNotTrackInventory()) {
            return true;
        }

        $current_cart_quantities = $cart->quantitiesByProductId();

        if ($this->tracksStandardInventory()) {
            return $this->hasAmountAvailable($amount + $current_cart_quantities->get($this->id, 0));
        }


        return $this->bundle
            ->reject(fn(Product $bundle_product) => $bundle_product->doesNotTrackInventory())
            ->filter(function(Product $bundle_product) use ($amount, $current_cart_quantities) {
                $amount_to_add = $bundle_product->getRelationValue('pivot')->qty * $amount;
                $amount_in_cart = $current_cart_quantities->get($bundle_product->id, 0);
                return ! $bundle_product->hasAmountAvailable($amount_to_add + $amount_in_cart);
            })
            ->isEmpty();

    }

    public function doesNotTrackInventory(): bool
    {
        return $this->inventoryTrackingMethod() === 'no';
    }

    public function tracksStandardInventory(): bool
    {
        return $this->inventoryTrackingMethod() === 'yes';
    }

    public function hasAmountAvailable($amount, bool $with_threshold_check = true): bool
    {
        if ($this->doesNotTrackInventory()) {
            return true;
        }

        $available = $with_threshold_check
            ? $this->inventoryAvailableForPurchase()
            : $this->totalAvailableInventory();

        if (is_infinite($available)) {
            return true;
        }

        return $amount <= $available;
    }

    public function inventoryAvailableForPurchase(bool $check_backorder = true): float|int
    {
        if ($this->doesNotTrackInventory() || ($check_backorder && $this->canBeBackOrdered())) {
            return log(0);
        }

        if ($this->tracksStandardInventory()) {
            return $this->totalAvailableInventory($check_backorder) - $this->subscriptionReserveInventory();
        }

        $tracked_products = $this->bundle->reject(fn(Product $product) => $product->doesNotTrackInventory());

        if ($tracked_products->isEmpty()) {
            return log(0);
        }

        return $tracked_products
            ->map(fn(Product $product) => (int) floor($product->inventoryAvailableForPurchase($check_backorder) / $product->getRelationValue('pivot')->qty))
            ->min()
            ?? 0;
    }

    private function canBeBackOrdered(): bool
    {
        return auth()->user()?->canBackOrder() ?? false;
    }

    private function totalAvailableInventory(bool $check_backorder = true): float|int
    {
        if ($this->doesNotTrackInventory() || ($check_backorder && $this->canBeBackOrdered())) {
            return log(0);
        }

        if ($this->tracksStandardInventory()) {
            return $this->onSiteInventory() + $this->offSiteInventory();
        }

        $tracked_products = $this->bundle->reject(fn(Product $product) => $product->doesNotTrackInventory());

        if ($tracked_products->isEmpty()) {
            return log(0);
        }
        
        return $tracked_products
            ->map(fn(Product $product) => (int) floor($product->totalAvailableInventory($check_backorder) / $product->getRelationValue('pivot')->qty))
            ->min()
            ?? 0;
    }

    public function offSiteInventory(): int
    {
        return $this->other_inventory ?? 0;
    }

    public function subscriptionReserveInventory(): int
    {
        return $this->oos_threshold_inventory ?? 0;
    }

    public function decrementInventory(int $amount): Product
    {
        if ($this->doesNotTrackInventory()) {
            return $this;
        }

        if ($this->tracksInventoryByBundleItem()) {
            return $this->decrementBundleInventory($amount);
        }

        $this->decrement('inventory', abs($amount));

        return $this;
    }

    private function decrementBundleInventory(int $amount): Product
    {
        foreach ($this->bundle as $bundled_product) {
            /** @var Product $bundled_product  */

            // Avoid an infinite loop.
            if ($bundled_product->isBundle()) continue;

            $bundle_amount = $bundled_product->getRelationValue('pivot')->qty * $amount;
            $bundled_product->decrementInventory($bundle_amount);
        }

        return $this;
    }

    public function incrementInventory(int $amount): Product
    {
        if ($this->doesNotTrackInventory()) {
            return $this;
        }

        if ($this->tracksInventoryByBundleItem()) {
            return $this->incrementBundleInventory($amount);
        }

        $this->increment('inventory', abs($amount));

        return $this;
    }

    private function incrementBundleInventory(int $amount): Product
    {
        foreach ($this->bundle as $bundled_product) {
            /** @var Product $bundled_product  */

            // Avoid an infinite loop.
            if ($bundled_product->isBundle()) continue;

            $bundle_amount = $bundled_product->getRelationValue('pivot')->qty * $amount;
            $bundled_product->incrementInventory($bundle_amount);
        }

        return $this;
    }

    public function isOutOfStock()
    {
        return once(function () {
            $available = $this->inventoryAvailableForPurchase();

            if (is_infinite($available)) {
                return false;
            }

            return $available <= 0;
        });
    }

    public function remainingInventory($with_threshold_check = true): float|int
    {
        return $with_threshold_check
            ? $this->inventoryAvailableForPurchase()
            : $this->totalAvailableInventory();
    }
}

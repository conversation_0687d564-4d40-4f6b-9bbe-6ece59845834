<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use App\Traits\MapIdsToTitleTrait;
use App\Traits\SeoTrait;
use Cviebrock\EloquentSluggable\Sluggable;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;

/**
 * App\Models\Protocol
 *
 * @property int $id
 * @property string $title
 * @property string $slug
 * @property string $description
 * @property string $cover_photo
 * @property int|null $seo_visibility
 * @property int $sort
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Product[] $products
 * @property-read int|null $products_count
 * @method static \Illuminate\Database\Eloquent\Builder|Protocol findSimilarSlugs(string $attribute, array $config, string $slug)
 * @method static \Illuminate\Database\Eloquent\Builder|Protocol newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Protocol newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Protocol query()
 * @method static \Illuminate\Database\Eloquent\Builder|Protocol whereCoverPhoto($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Protocol whereDescription($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Protocol whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Protocol whereSeoVisibility($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Protocol whereSlug($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Protocol whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Protocol whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Protocol withUniqueSlugConstraints(\Illuminate\Database\Eloquent\Model $model, string $attribute, array $config, string $slug)
 * @method static \Database\Factories\ProtocolFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class Protocol extends Model
{
    use HasFactory;

    use Sluggable, MapIdsToTitleTrait, SeoTrait;

    /**
     * Return the sluggable configuration array for this model.
     */
    public function sluggable(): array
    {
        return [
            'slug' => [
                'source' => 'title'
            ]
        ];
    }

    public $timestamps = false;

    protected static $mapIdsToTitleCacheKey = 'protocol';

    protected $guarded = [];

    public function products(): BelongsToMany
    {
        return $this->belongsToMany(Product::class);
    }
}

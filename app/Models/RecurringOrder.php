<?php

namespace App\Models;

use App\Actions\Order\SyncBlueprintWithOrder;
use App\Actions\Subscription\AddItem;
use App\Actions\Subscription\SetItemQuantity;
use App\Actions\Subscription\SyncSubscriptionDatetimes;
use App\Events\Subscription\RecurringOrderCanceled;
use App\Events\Subscription\SubscriptionWasExpedited;
use App\Events\Subscription\SubscriptionWasSkipped;
use App\Exceptions\BackOrderException;
use App\Exceptions\ExclusivityException;
use App\Exceptions\ProductNotFoundException;
use App\Jobs\GenerateSubscriptionOrder;
use App\OrderWindow;
use App\Services\SubscriptionSettingsService;
use App\Support\Enums\OrderStatus;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Once;

/**
 * App\Models\RecurringOrder
 *
 * @property int $id
 * @property int $customer_id
 * @property int|null $fulfillment_id
 * @property int|null $last_order_id
 * @property int|null $schedule_id
 * @property int|null $reorder_frequency
 * @property int $skip_count
 * @property \Illuminate\Support\Carbon|null $next_deadline
 * @property \Illuminate\Support\Carbon|null $next_delivery
 * @property string|null $last_reminder_sent_at
 * @property int|null $updated_by
 * @property \Illuminate\Support\Carbon|null $paused_at
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property \Illuminate\Support\Carbon|null $deleted_at
 * @property-read \App\Models\Order|null $currentOrder
 * @property-read \App\Models\User|null $customer
 * @property-read \App\Models\Pickup|null $fulfillment
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\RecurringOrderItem[] $items
 * @property-read int|null $items_count
 * @property-read \Illuminate\Database\Eloquent\Collection|\App\Models\Order[] $orders
 * @property-read int|null $orders_count
 * @property-read \App\Models\Order|null $pendingOrder
 * @property-read \App\Models\Schedule|null $schedule
 * @method static Builder|RecurringOrder newModelQuery()
 * @method static Builder|RecurringOrder newQuery()
 * @method static \Illuminate\Database\Query\Builder|RecurringOrder onlyTrashed()
 * @method static Builder|RecurringOrder query()
 * @method static Builder|RecurringOrder whereCreatedAt($value)
 * @method static Builder|RecurringOrder whereCustomerId($value)
 * @method static Builder|RecurringOrder whereDeletedAt($value)
 * @method static Builder|RecurringOrder whereFulfillmentId($value)
 * @method static Builder|RecurringOrder whereId($value)
 * @method static Builder|RecurringOrder whereLastOrderId($value)
 * @method static Builder|RecurringOrder whereLastReminderSentAt($value)
 * @method static Builder|RecurringOrder whereNextDeadline($value)
 * @method static Builder|RecurringOrder whereNextDelivery($value)
 * @method static Builder|RecurringOrder wherePausedAt($value)
 * @method static Builder|RecurringOrder whereReorderFrequency($value)
 * @method static Builder|RecurringOrder whereScheduleId($value)
 * @method static Builder|RecurringOrder whereSkipCount($value)
 * @method static Builder|RecurringOrder whereUpdatedAt($value)
 * @method static Builder|RecurringOrder whereUpdatedBy($value)
 * @method static Builder|RecurringOrder deliveryDateBetween(?Carbon $start, ?Carbon $start)
 * @method static \Illuminate\Database\Query\Builder|RecurringOrder withTrashed()
 * @method static \Illuminate\Database\Query\Builder|RecurringOrder withoutTrashed()
 * @property-read \App\Models\Order|null $previousOrder
 * @method static \Database\Factories\RecurringOrderFactory factory($count = null, $state = [])
 * @property \Illuminate\Support\Carbon|null $generate_at
 * @property \Illuminate\Support\Carbon|null $ready_at
 * @method static Builder|RecurringOrder whereGenerateAt($value)
 * @method static Builder|RecurringOrder whereReadyAt($value)
 * @mixin \Eloquent
 */
class RecurringOrder extends Model
{
    use HasFactory;

    use SoftDeletes;

    protected $guarded = [];

    public function routeNotificationForMail(): ?string
    {
        return $this->currentOrder?->routeNotificationForMail();
    }

    public function routeNotificationForTwilio(): ?string
    {
        return $this->currentOrder?->routeNotificationForTwilio();
    }

    /**
     * @return HasOne<User, $this>
     */
    public function customer(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'customer_id');
    }

    /**
     * @return BelongsTo<Order, $this>
     */
    public function pendingOrder(): BelongsTo
    {
        return $this->belongsTo(Order::class, 'last_order_id');
    }

    /**
     * @return HasOne<Pickup, $this>
     */
    public function fulfillment(): HasOne
    {
        return $this->hasOne(Pickup::class, 'id', 'fulfillment_id')->withTrashed();
    }

    /*
     * @return HasOne<Schedule, $this>
     */
    public function schedule(): HasOne
    {
        return $this->hasOne(Schedule::class, 'id', 'schedule_id');
    }

    /**
     * @return HasOne<Order, $this>
     */
    public function currentOrder(): HasOne
    {
        return $this->hasOne(Order::class, 'blueprint_id')
            ->ofMany(
                ['id' => 'MAX'],
                function (Builder $query) {
                    $query->whereDate('pickup_date', '>=', today())
                        ->where('canceled', false);
                }
            );
    }

    public function previousOrder(): HasOne
    {
        return $this->hasOne(Order::class, 'blueprint_id')
            ->ofMany(
                ['id' => 'MAX'],
                function (Builder $query) {
                    $query->whereDate('pickup_date', '<', today());
                }
            );
    }

    public function pricingGroup(): ?ProductPriceGroup
    {
        $pricing_group_id = $this->pricingGroupId();

        if (is_null($pricing_group_id)) {
            return null;
        }

        return ProductPriceGroup::find($pricing_group_id);
    }

    public function pricingGroupId(): ?int
    {
        $group_id = null;

        if ($this->customer->pricing_group_id ?? null) {
            $group_id = $this->customer->pricing_group_id;
        } elseif ($this->fulfillment->pricing_group_id ?? null) {
            $group_id = $this->fulfillment->pricing_group_id;
        }

        return $group_id;
    }

    public function reorderOptionsWithLabels() : array
    {
        return collect($this->reorderOptions())
            ->mapWithKeys(fn($option) => [
                $option => ucwords(__('messages.recurring.reorder_frequencies_formatted.' . $option))
            ])
            ->toArray();
    }

    public function reorderOptions(): array
    {
        return [7,14,28,42,56];
    }

    public function loadItems()
    {
        $this->load([
            'items.product.price' => fn($q) => $q->where('group_id', $this->pricingGroupId() ?? 0)
        ]);

        return $this;
    }

    public function getNextDeadlineDate(): Carbon
    {
        return $this->next_deadline->addDays($this->reorder_frequency);
    }

    public function getNextDeliveryDate(): Carbon
    {
        return $this->next_delivery->addDays($this->reorder_frequency);
    }

    public function inventoryTimingDatetime(): ?Carbon
    {
        /** @var Order|null $current_order */
        $current_order = $this->currentOrder;

        if (is_null($current_order) || $current_order->isCanceled() || $current_order->isConfirmed()) {
            return null;
        }

        return $current_order->inventoryTimingDatetime()?->copy();
    }

    public function deadlineReminderDatetime(): ?Carbon
    {
        /** @var Order|null $current_order */
        $current_order = $this->currentOrder;

        if (is_null($current_order) || $current_order->isCanceled()) {
            return null;
        }

        return $current_order->deadlineReminderDatetime()?->copy();
    }

    public function deadlineDatetime(): ?Carbon
    {
        /** @var Order|null $current_order */
        $current_order = $this->currentOrder;


        if (is_null($current_order) || $current_order->isCanceled()) {
            return null;
        }

        return $current_order->deadlineDatetime()?->copy();
    }

    public function hasPendingOrder(Carbon $deadline): bool
    {
        return $this->orders()
            ->whereDate('deadline_date', '>=', $deadline)
            ->exists();
    }

    /**
     * @return HasMany<Order, $this>
     */
    public function orders(): HasMany
    {
        return $this->hasMany(Order::class, 'blueprint_id');
    }

    /**
     * @return HasMany<Event, $this>
     */
    public function events(): HasMany
    {
        return $this->hasMany(Event::class, 'model_id')
            ->where('model_type', RecurringOrder::class)
            ->whereIn('event_id', [SubscriptionWasExpedited::class, SubscriptionWasSkipped::class]);
    }

    public function cancel(): RecurringOrder
    {
        $this->orders()
            ->where([
                'confirmed' => false,
                'is_recurring' => true,
            ])
            ->delete();

        $this->orders()
            ->where([
                'confirmed' => true,
                'canceled' => false,
                'status_id' => OrderStatus::confirmed(),
            ])
            ->whereDate('deadline_date', '>=', today())
            ->get()
            ->reject(fn(Order $order) => $order->deadlineHasPassed())
            ->each->cancel();

        $this->delete();

        event(new RecurringOrderCanceled($this));

         Once::flush();

        return $this;
    }

    public function nextPickupDate(): ?\Illuminate\Support\Carbon
    {
        if ( ! is_null($this->ready_at)) {
            return $this->ready_at->copy();
        }

        if ($this->currentOrder) {
            return $this->currentOrder->pickup_date->copy()->addDays($this->reorder_frequency);
        }

        if ($this->previousOrder) {
            $date = $this->previousOrder->pickup_date->copy()->addDays($this->reorder_frequency);

            if ($date > today()) {
                return $date;
            }
        }

        return  today()->copy()->addDays($this->reorder_frequency);
    }

    public function pickup(): ?Pickup
    {
        if ($this->currentOrder) {
            return $this->currentOrder->pickup;
        }

        if ($this->previousOrder) {
            return $this->previousOrder->pickup;
        }

        return null;
    }

    public function findPromotionalItem(): ?RecurringOrderItem
    {
        return $this->items()->where('type', 'promo')->first();
    }

    /**
     * @return HasMany<RecurringOrderItem, $this>
     */
    public function items(): HasMany
    {
        return $this->hasMany(RecurringOrderItem::class, 'order_id', 'id');
    }

    public function addPromoItem(Product $product, int $quantity = 1): ?RecurringOrderItem
    {
        return $this->addItem($product, $quantity, 'promo');
    }

    /**
     * @throws ExclusivityException
     * @throws BackOrderException
     */
    public function addItem(Product $product, int $quantity = 1, string $type = 'recurring', bool $force = false): ?RecurringOrderItem
    {
        return app(AddItem::class)->handle($this, $product, $quantity, $type, $force);
    }

    /**
     * @throws BackOrderException
     * @throws ProductNotFoundException
     */
    public function updateItemQuantity(RecurringOrderItem $item, int $quantity, bool $force = false): ?RecurringOrderItem
    {
        return app(SetItemQuantity::class)->handle($this, $item, $quantity, $force);
    }

    public function total(): int
    {
        return max(0, $this->totalBeforeStoreCredit() - $this->storeCreditTotal());
    }

    public function totalBeforeStoreCredit(): int
    {
        return $this->subtotal()
            + $this->locationFeeTotal()
            + $this->deliveryTotal()
            + $this->taxTotal()
            - $this->subscriptionSavingsTotal()
            - $this->couponTotal();
    }

    public function subtotal(): int
    {
        return $this->items()
            ->with(['product.price' => fn($q) => $q->where('group_id', $this->pricingGroupId() ?? 0)])
            ->get()
            ->sum(fn(RecurringOrderItem $item) => $item->subtotal());
    }

    public function locationFeeTotal(): int
    {
        if ($this->customer?->exemptFromFees()) {
            return 0;
        }

        return $this->fulfillment?->fees()->sum('amount') ?? 0;
    }

    public function deliveryTotal(): int
    {
        $location = $this->fulfillment;

        if (is_null($location) || ($this->customer?->exemptFromFees() ?? false)) {
            return 0;
        }

        return $location->deliveryFeeForSubscription($this);
    }

    public function taxTotal(): int
    {
        if ($this->customer?->exemptFromTax()) {
            return 0;
        }

        $tax_rate = $this->fulfillment->tax_rate ?? 0;

        if ($tax_rate <= 0) {
            return 0;
        }

        $taxable_subtotal = $this->items()
            ->with(['product.price' => fn($q) => $q->where('group_id', $this->pricingGroupId() ?? 0)])
            ->get()
            ->filter(fn(RecurringOrderItem $item) => $item->product->isTaxable())
            ->sum(fn(RecurringOrderItem $item) => $item->subtotal());

        if ($this->productIncentiveItem()?->product->isTaxable() ?? false) {
            $taxable_subtotal += $this->productIncentiveItem()->subtotal();
        }

        $taxable_fee_total = $this->locationTaxableFeeTotal();
        if ($this->customer?->exemptFromFees()) {
            $taxable_fee_total = 0;
        }

        return (int) round($taxable_subtotal * $tax_rate)
            + (int) round($taxable_fee_total * $tax_rate)
            + $this->deliveryTaxTotal();
    }

    public function productIncentiveItem(): ?RecurringOrderItem
    {
        return $this->items()
            ->where('type', 'promo')
            ->with(['product.price' => fn($q) => $q->where('group_id', $this->pricingGroupId() ?? 0)])
            ->first();
    }

    private function locationTaxableFeeTotal(): int
    {
        return $this->fulfillment
            ?->fees()
            ->where('taxable', true)
            ->sum('amount')
            ?? 0;
    }

    private function deliveryTaxTotal(): int
    {
        $location = $this->fulfillment;

        if (is_null($location)) {
            return 0;
        }

        $tax_rate = $location->tax_rate ?? 0;

        if ($tax_rate <= 0 || ! $location->tax_delivery_fee) {
            return 0;
        }

        return (int) round($this->deliveryTotal() * $tax_rate);
    }

    public function subscriptionSavingsTotal(): int
    {
        return $this->potentialSubscriptionSavingsTotal();
    }

    public function potentialSubscriptionSavingsTotal(): int
    {
        $eligible_subtotal = $this->items()
            ->with(['product.price' => fn($q) => $q->where('group_id', $this->pricingGroupId() ?? 0)])
            ->get()
            ->filter(fn(RecurringOrderItem $item) => $item->isEligibleForSubscriptionSavings())
            ->sum(fn(RecurringOrderItem $item) => $item->subtotal());

        return (int) round($eligible_subtotal * (app(SubscriptionSettingsService::class)->discountIncentive() / 100));
    }

    public function couponTotal(): int
    {
        return $this->coupons()->sum('amount');
    }

    public function coupons(): \Illuminate\Support\Collection
    {
        return collect();
    }

    public function storeCreditTotal(): int
    {
        return $this->customer->credit ?? 0;
    }

    public function productIncentive(): ?Product
    {
        return $this->productIncentiveItem()?->product;
    }

    public function oneTimeItems(): \Illuminate\Support\Collection
    {
        return $this->items()
            ->where(['type' => 'addon'])
            ->with(['product.price' => fn($q) => $q->where('group_id', $this->pricingGroupId() ?? 0)])
            ->get();
    }

    public function subscriptionItems(): \Illuminate\Support\Collection
    {
        return $this->items()
            ->where(['type' => 'recurring'])
            ->with(['product.price' => fn($q) => $q->where('group_id', $this->pricingGroupId() ?? 0)])
            ->get();
    }

    public function potentialSubscriptionProductValue(): int
    {
        $id = $this->promoItem()?->product_id;

        if (is_null($id)) return 0;

        $product = Product::with([
            'price' => fn($q) => $q->where('group_id', $this->pricingGroupId() ?? 0)
        ])->find($id);

        if (is_null($product)) return 0;

        return $product->getRegularUnitPrice() - $product->getUnitPrice();
    }

    public function promoItem(): ?RecurringOrderItem
    {
        return $this->items()
            ->where(['type' => 'promo'])
            ->with(['product.price' => fn($q) => $q->where('group_id', $this->pricingGroupId() ?? 0)])
            ->first();
    }

    public function formattedReorderFrequency(): string
    {
        return match ((int) $this->reorder_frequency) {
            56 => 'Every 8 weeks',
            42 => 'Every 6 weeks',
            28 => 'Every 4 weeks',
            14 => 'Every 2 weeks',
            default => 'Every week',
        };
    }

    public function generateNextOrder(bool $synchonous = false): void
    {
        if (is_null($this->ready_at)) return;

        if ($synchonous) {
            GenerateSubscriptionOrder::dispatchSync(
                subscription_id: $this->id,
                ready_at: $this->ready_at
            );
        } else {
            GenerateSubscriptionOrder::dispatch(
                subscription_id: $this->id,
                ready_at: $this->ready_at
            );
        }


        $next_date = $this->pickupSchedule()?->dates()
            ->where('pickup_date', '>=', $this->ready_at->copy()->addDays($this->reorder_frequency))
            ->first();

        $this->ready_at = $next_date?->pickup_date;
        $this->generate_at = $next_date?->toOrderWindows()->generatesAtDatetime();
        $this->save();
    }

    public function pickupSchedule(): ?Schedule
    {
        return $this->fulfillment?->schedule;
    }

    public function readyAtDatetime(): ?Carbon
    {
        if (is_null($this->ready_at)) return null;

        return $this->pickupSchedule()
            ?->dates()
            ->where('pickup_date', '>=', $this->ready_at)
            ->first()
            ?->toOrderWindows()
            ->readyAtDatetime();
    }

    public function migrate(): void
    {
        if ( ! is_null($this->ready_at) && ! is_null($this->generate_at)) return;

        /** @var Order|null $last_order */
        $last_order = $this->orders()->whereNotNull('pickup_date')->latest('pickup_date')->first();

        if (is_null($last_order)) return;

        $next_date = $this->pickupSchedule()?->dates()
            ->where('pickup_date', '>=', $last_order->pickup_date->copy()->addDays($this->reorder_frequency))
            ->first();

        if (is_null($next_date)) return;

        $this->ready_at = $next_date->pickup_date;
        $this->generate_at = $next_date->toOrderWindows()->generatesAtDatetime();
        $this->save();

        app(SyncBlueprintWithOrder::class)->handle($this, $last_order);
    }

    public function nextOrderWindow(?Carbon $current_pickup_date = null): ?OrderWindow
    {
        if (is_null($current_pickup_date) && is_null($this->ready_at)) {
            return null;
        }

        $next_pickup_date = ! is_null($current_pickup_date)
            ? $current_pickup_date->copy()->addDays($this->reorder_frequency)
            : $this->ready_at->copy();

        return $this->pickupSchedule()?->dates()
            ->where('pickup_date', '>=', $next_pickup_date->startOfDay())
            ->first()
            ?->toOrderWindows();
    }

    public function upcomingPaymentMethod(): ?Payment
    {
        $payment_method = $this->currentOrder?->paymentMethod;

        if (is_null($payment_method)) {
            $payment_method = Payment::find($this->customer->setting('default_payment_method', 0));
        }

        return $payment_method;
    }

    public function upcomingPaymentSourceId(): ?string
    {
        $payment_source_id = $this->currentOrder?->paymentSource?->source_id;

        if (is_null($payment_source_id)) {
            $payment_source_id = $this->customer->defaultCard()->value('source_id');
        }

        return $payment_source_id;
    }

    public function skipDays(int $days): RecurringOrder
    {
        $this->skip_count++;
        $this->save();

        $old_delivery_date = $this->ready_at->copy();
        $new_delivery_date = $old_delivery_date->copy()->addDays($days);

        $updated_subscription = app(SyncSubscriptionDatetimes::class)
            ->handle($this, $new_delivery_date);

        event(new SubscriptionWasSkipped(
            subscription: $this,
            old_delivery_date: $old_delivery_date,
            new_delivery_date: $updated_subscription->ready_at
        ));

        return $updated_subscription;
    }

    public function rescheduleTo(Carbon $new_delivery_date): RecurringOrder
    {
        $this->skip_count++;
        $this->save();

        $old_delivery_date = $this->ready_at->copy();

        $updated_subscription = app(SyncSubscriptionDatetimes::class)
            ->handle($this, $new_delivery_date);

        event(new SubscriptionWasSkipped(
            subscription: $this,
            old_delivery_date: $old_delivery_date,
            new_delivery_date: $updated_subscription->ready_at
        ));

        return $updated_subscription;
    }

    /**
     *
     * @param  Builder<RecurringOrder>  $query
     * @param  Carbon|null  $starts_at
     * @param  Carbon|null  $ends_at
     * @return Builder<RecurringOrder>
     */
    public function scopeDeliveryDateBetween(Builder $query, ?Carbon $starts_at, ?Carbon $ends_at): Builder
    {
        if (is_null($starts_at)) {
            $starts_at = today()->startOfDay();
        }

        if (is_null($ends_at)) {
            $ends_at = today()->addYears(2)->endOfDay();
        }

        return $query
            ->whereBetween('ready_at', [$starts_at->format('Y-m-d'), $ends_at->format('Y-m-d')]);
    }

    public function getShippingInfo(): array
    {
        $default_address = $this->customer?->defaultShippingAttributes();

        return [
            'address_id' => $default_address['address_id'] ?? null,
            'street' => $default_address['street'] ?? '',
            'street_2' => $default_address['street_2'] ?? '',
            'city' => $default_address['city'] ?? '',
            'state' => $default_address['state'] ?? '',
            'zip' => $default_address['postal_code'] ?? '',
            'country' => $default_address['country'] ?? '',
            'save_for_later' => true,
        ];
    }

    public function weight(): float
    {
        return DB::table('recurring_order_items')
            ->selectRaw('SUM(recurring_order_items.qty * products.weight) AS weight')
            ->join('products', 'products.id', '=', 'recurring_order_items.product_id')
            ->where('order_id', $this->id)
            ->first()
            ->weight
            ?? 0.0;
    }

    protected function casts(): array
    {
        return [
            'paused_at' => 'datetime',
            'next_deadline' => 'date',
            'next_delivery' => 'date',
            'ready_at' => 'datetime',
            'generate_at' => 'datetime',
        ];
    }
}

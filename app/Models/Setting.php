<?php

namespace App\Models;

use App\Services\SettingsService;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Once;

/**
 * App\Models\Setting
 *
 * @property string $key
 * @property string|null $value
 * @property int|null $updated_by
 * @property string $updated_at
 * @method static Builder|Setting farmAddress()
 * @method static Builder|Setting farmSettings()
 * @method static Builder|Setting newModelQuery()
 * @method static Builder|Setting newQuery()
 * @method static Builder|Setting processOrderSettings()
 * @method static Builder|Setting query()
 * @method static Builder|Setting themeSettings()
 * @method static Builder|Setting whereKey($value)
 * @method static Builder|Setting whereUpdatedAt($value)
 * @method static Builder|Setting whereUpdatedBy($value)
 * @method static Builder|Setting whereValue($value)
 * @method static \Database\Factories\SettingFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class Setting extends Model
{
    use HasFactory;

    public $incrementing = false;
    public $timestamps = false;
    protected $guarded = [];
    protected $primaryKey = 'key';
    protected $keyType = 'string';

    public static function updateAll(array $settings = []): void
    {
        foreach ($settings as $key => $value) {
            Setting::updateOrCreate(['key' => $key], [
                'value' => is_array($value) ? json_encode($value) : $value
            ]);
        }

        Cache::tags(['setting', 'settings'])->flush();
    }

    public static function retrieveJson(string $key, mixed $default = null): array
    {
        return (array) self::retrieve($key, $default, true);
    }

    public static function retrieve(string|array $key, mixed $default = null, bool $jsonDecode = false): mixed
    {
        // If an array is passed then update or create the setting.
        if (is_array($key)) {
            self::updateOrCreate(
                ['key' => key($key)],
                ['value' => current($key)]
            );

            Cache::tags('setting')->flush();

            return current($key);
        }

        $settings = app(SettingsService::class)->all();

        if (!isset($settings[$key]) || !strlen($settings[$key])) {
            return $default;
        }

        return $jsonDecode ? json_decode($settings[$key]) : $settings[$key];
    }

    protected static function booted(): void
    {
        static::saving(function ($setting) {
            $setting->updated_at = now();
            $setting->updated_by = auth()->check() ? auth()->user()->id : null;
        });

        static::saved(function ($setting) {
            static::flushCache();
        });

        static::deleted(function ($setting) {
            static::flushCache();
        });
    }

    public static function flushCache(): void
    {
        Cache::tags('setting')->flush();
        Once::flush();
    }

    /**
     * @param  Builder<Setting>  $query
     * @return Builder<Setting>
     */
    public function scopeFarmSettings(Builder $query): Builder
    {
        return $query->whereIn('key', [
            'farm_name', 'farm_phone', 'farm_street',
            'farm_city', 'farm_state', 'farm_zip'
        ]);
    }

    /**
     * @param  Builder<Setting>  $query
     * @return Builder<Setting>
     */
    public function scopeProcessOrderSettings(Builder $query): Builder
    {
        return $query->whereIn('key', [
            'process_order_email', 'process_order_status',
            'process_order_charge', 'process_order_packed_by'
        ]);
    }

    /**
     * @param  Builder<Setting>  $query
     * @return Builder<Setting>
     */
    public function scopeFarmAddress(Builder $query): Builder
    {
        return $query->whereIn('key', [
            'farm_street', 'farm_city', 'farm_state', 'farm_zip'
        ]);
    }

    /**
     * @param  Builder<Setting>  $query
     * @return Builder<Setting>
     */
    public function scopeThemeSettings(Builder $query): Builder
    {
        return $query->whereIn('key', [
            'layout_bg',
            'layout_button_background',
            'layout_button_hover_background',
            'layout_button_hover_text',
            'layout_button_text',
            'layout_cta_background',
            'layout_cta_hover_background',
            'layout_cta_hover_text',
            'layout_cta_text',
            'layout_footer_background',
            'layout_header_bg',
            'layout_header_width',
            'layout_link_color',
            'layout_link_hover_color',
            'layout_logo_height',
            'layout_logo_padding',
            'layout_logo_path',
            'layout_logo_url',
            'layout_menu_align',
            'layout_menu_bg',
            'layout_menu_link_bg',
            'layout_menu_link_color',
            'layout_menu_link_hover_bg',
            'layout_menu_link_hover_color',
            'layout_menu_link_size',
            'layout_menu_padding',
            'layout_menu_position',
            'layout_text_color',
        ]);
    }
}

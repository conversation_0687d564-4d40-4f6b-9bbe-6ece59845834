<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\Request;

/**
 * App\Models\MenuItem
 *
 * @property int $id
 * @property string $title
 * @property string $path
 * @property int $menu_id
 * @property int $submenu_id
 * @property string $type
 * @property int $resource_id
 * @property string $resource_type
 * @property int $sort
 * @property int $visible
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read string $type_formatted
 * @property-read \App\Models\Menu $menu
 * @property-read \App\Models\Menu|null $submenu
 * @method static \Illuminate\Database\Eloquent\Builder|MenuItem newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MenuItem newQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|MenuItem query()
 * @method static \Illuminate\Database\Eloquent\Builder|MenuItem whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MenuItem whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MenuItem whereMenuId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MenuItem wherePath($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MenuItem whereResourceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MenuItem whereResourceType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MenuItem whereSort($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MenuItem whereSubmenuId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MenuItem whereTitle($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MenuItem whereType($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MenuItem whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|MenuItem whereVisible($value)
 * @mixin \Eloquent
 */
class MenuItem extends Model
{
    use HasFactory;

    protected $guarded = [];

    protected $appends = ['type_formatted'];

    public function items(): iterable
    {
        if ($this->isSubmenu()) {
            return $this->submenu->items ?? [];
        }

        return [];
    }

    public function isSubmenu(): bool
    {
        return $this->type === 'menu';
    }

    public function menu(): BelongsTo
    {
        return $this->belongsTo(Menu::class, 'menu_id');
    }

    public function submenu(): BelongsTo
    {
        return $this->belongsTo(Menu::class, 'submenu_id');
    }

    /**
     * Return the formatted type.
     */
    public function getTypeFormattedAttribute(): string
    {
        $types = [
            'link' => 'Custom Link',
            'page' => 'Page',
            'menu' => 'Submenu',
            'collection' => 'Collection',
            'category' => 'Category',
            'tag' => 'Tag'
        ];

        return $types[$this->type] ?? '';
    }

    public function isActive(): bool
    {
        return Request::url() === $this->getUrl();
    }

    public function getUrl(): string
    {
        return url($this->path);
    }

    public function getLabel(): string
    {
        return $this->title;
    }
}

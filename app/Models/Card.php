<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Str;

/**
 * App\Models\Card
 *
 * @property int $id
 * @property int $user_id
 * @property string|null $source_id
 * @property int $default
 * @property \Illuminate\Support\Carbon|null $created_at
 * @property \Illuminate\Support\Carbon|null $updated_at
 * @property-read \App\Models\User $user
 * @method static \Illuminate\Database\Eloquent\Builder|Card forCustomer(int $customerId)
 * @method static \Illuminate\Database\Eloquent\Builder|Card newModelQuery()
 * @method static \Illuminate\Database\Eloquent\Builder|Card newQuery()
 * @method static \Illuminate\Database\Query\Builder|Card onlyTrashed()
 * @method static \Illuminate\Database\Eloquent\Builder|Card query()
 * @method static \Illuminate\Database\Eloquent\Builder|Card whereCreatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Card whereDefault($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Card whereId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Card whereSourceId($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Card whereUpdatedAt($value)
 * @method static \Illuminate\Database\Eloquent\Builder|Card whereUserId($value)
 * @method static \Database\Factories\CardFactory factory($count = null, $state = [])
 * @mixin \Eloquent
 */
class Card extends Model
{
    use HasFactory;

    const STRIPE = 1;
    const GRAVITY = 2;

    const STAX = 3;

    const PAY_FAC = 4;

    const PAY_FAC_STRIPE = 5;

    protected $table = 'user_cards';

    protected $guarded = [];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function scopeForCustomer($query, int $customerId)
    {
        return $query->where('user_id', $customerId);
    }

    public function isPaymentMethod(): bool
    {
        return Str::startsWith($this->source_id, 'pm_');
    }

    public function idempotencyKey(int $amount): string
    {
        return $this->id . '_' . now()->format('y-m-d-h-i-s') . '_' . $amount;
    }

    protected function casts(): array
    {
        return [
            'default' => 'boolean',
        ];
    }
}

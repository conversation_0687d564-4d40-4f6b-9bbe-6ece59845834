<?php

namespace App\Integrations\Drip\Jobs;

use App\Integrations\Drip\DripIntegration;
use App\Models\Order;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class RecordCanceledOrder implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public int $order_id
    ) {}

    public function handle()
    {
        $drip = DripIntegration::getInstance();

        if (is_null($drip)) return;

        /** @var Order|null $order */
        $order = Order::with('items', 'customer')
            ->find($this->order_id);

        if (is_null($order?->customer)) return;

        $drip->recordEvent($order->customer->email, 'Order canceled', [
            'order_id' => $order->id,
            'order_total' => $order->total,
            'schedule_id' => $order->schedule_id,
            'fulfillment_id' => $order->pickup_id,
            'pickup_date' => $order->pickup_date?->format('m/d/y')
        ]);

        $drip->shopperActivity()?->cancelOrder($order);
    }

    public function tags()
    {
        return ['integration', 'drip'];
    }
}

<?php

namespace App\Jobs\User;

use App\Models\RecurringOrder;
use App\Models\User;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class DeleteCustomer implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(public int $userId)
    {}

    public function handle(): void
    {
        $user = User::findOrFail($this->userId);

        $deletedUser = User::deletedCustomer();

        // Re-associate the customer_id record on the orders table
        $user->orders()->update(['customer_id' => $deletedUser->id]);

        // Re-associate the customer_id record on the recurring_orders table
        RecurringOrder::withTrashed()
            ->where('customer_id', $user->id)
            ->update(['customer_id' => $deletedUser->id]);

        $user->delete();
    }
}

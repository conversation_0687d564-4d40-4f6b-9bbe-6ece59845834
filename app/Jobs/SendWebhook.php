<?php

namespace App\Jobs;

use App\Models\Webhook;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;

class SendWebhook implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public Webhook $webhook,
        public array $payload = []
    ) { }

    public function handle(): array
    {
        return Http::retry(3, 100)
            ->post($this->webhook->target_url, [
                'topic' => $this->webhook->topic,
                'data' => $this->payload
            ])
            ->throw()
            ->json();
    }

    public function backoff(): array
    {
        return [1, 5, 10];
    }
}
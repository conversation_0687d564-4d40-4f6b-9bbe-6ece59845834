<?php

namespace App\Jobs;

use App\Models\Address;
use App\Models\User;
use App\Services\SettingsService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;

class MigrateUserAddress implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public function __construct(
        public int $user_id
    ) {}

    public function handle(): void
    {
        $user = User::find($this->user_id);

        if (is_null($user) || ! $user->hasAddressAttributes() || $user->addresses()->exists()) return;

        $address = Address::firstOrCreate([
            'street' => $user->street,
            'city' => $user->city,
            'state' => $user->state,
            'postal_code' => $user->zip,
            'country' => app(SettingsService::class)->farmCountry(),
        ]);

        $user->addresses()->attach($address->id, [
            'name' => Str::limit( "{$user->street}, {$user->city}, {$user->state}, {$user->zip}"),
            'street_2' => $user->street_2 ?? '',
            'is_default' => true,
        ]);
    }

    public function tags(): array
    {
        return ['user-address-migration'];
    }
}

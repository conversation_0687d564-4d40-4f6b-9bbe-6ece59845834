<?php

namespace App\Http\Requests\API;

use App\Models\Card;
use App\Models\Cart;
use App\Models\Date;
use App\Models\Order;
use App\Models\Payment;
use App\Models\Pickup;
use App\Models\Product;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class CartConfirmationRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        return [
            'type' => ['required', Rule::in(['order', 'database'])],
            'id' => [
                'required',
                Rule::when(request('type') === 'order', [
                    Rule::exists(Order::class, 'id')->where('customer_id', auth()->id())
                ]),
                Rule::when(request('type') === 'database', [
                    Rule::exists(Cart::class, 'id')->where('shopper_id', auth()->id())
                ])
            ],
            'purchase_type' => ['required', Rule::in(['one_time_purchase', 'recurring'])],
            'date_id' => ['nullable', Rule::exists(Date::class, 'id')->where('active', true)],
            'delivery_method_id' => ['required', Rule::exists(Pickup::class, 'id')->where('status_id', 1)],
            'items' => ['required', 'array'],
            'items.*.product_id' => ['required', Rule::exists(Product::class, 'id')],
            'items.*.quantity' => ['required', 'int', 'min:1'],
            'subscription' => ['nullable', 'required_if:purchase_type,recurring', 'array'],
            'subscription.frequency' => ['required_if:purchase_type,recurring', 'int', Rule::in([7,14,28,42,56])],
            'subscription.product_incentive_id' => ['nullable', Rule::exists(Product::class, 'id')],
            'notes' => ['nullable', 'string', 'max:1000'],
            'is_gift' => ['nullable', 'boolean'],
            'recipient_email' => ['required_if_accepted:is_gift', 'nullable', 'email', 'max:255'],
            'recipient_notes' => ['nullable', 'string', 'max:500'],
            'customer' => ['required', 'array'],
            'customer.first_name' => ['required', 'string', 'max:255'],
            'customer.last_name' => ['required', 'string', 'max:255'],
            'customer.email' => ['required', 'email', 'max:255'],
            'customer.phone' => ['required', 'max:20'],
            'customer.save_for_later' => ['boolean'],
            'customer.opt_in_to_sms' => ['boolean'],
            'shipping' => ['array'],
            'shipping.street' => ['nullable', 'required_if:shipping.save_for_later,true', 'string', 'max:255'],
            'shipping.street_2' => ['nullable', 'string', 'max:255'],
            'shipping.city' => ['nullable', 'required_if:shipping.save_for_later,true', 'string', 'max:255'],
            'shipping.state' => ['nullable', 'required_if:shipping.save_for_later,true', 'string', 'max:255'],
            'shipping.zip' => ['nullable', 'required_if:shipping.save_for_later,true', 'string', 'max:15'],
            'shipping.country' => ['nullable', 'required_if:shipping.save_for_later,true', 'string', 'max:255'],
            'shipping.save_for_later' => ['boolean'],
            'billing' => ['required', 'array'],
            'billing.method' => [
                'required',
                Rule::exists(Payment::class, 'key')
                    ->where('enabled', true)
            ],
            'billing.source_id' => [
                'nullable',
                'required_if:billing.method,card',
                Rule::exists(Card::class, 'source_id')
                    ->where('user_id', auth()->id())
            ],
            'billing.save_for_later' => ['boolean'],
            'discounts' => ['array'],
            'discounts.coupons' => ['array'],
            'discounts.coupons.*.name' => ['nullable', 'string'],
            'discounts.coupons.*.code' => ['nullable', 'string'],
            'discounts.coupons.*.amount' => ['nullable', 'integer'],
            'discounts.gift_card' => ['array'],
            'discounts.gift_card.name' => ['nullable', 'string'],
            'discounts.gift_card.code' => ['nullable', 'string'],
            'discounts.gift_card.amount' => ['nullable', 'integer'],
            'discounts.store_credit' => ['array'],
            'discounts.store_credit.amount' => ['nullable', 'integer'],
        ];
    }
}

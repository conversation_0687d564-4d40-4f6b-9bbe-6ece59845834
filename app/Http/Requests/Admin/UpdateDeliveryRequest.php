<?php

namespace App\Http\Requests\Admin;

use App\Http\Requests\Request;
use App\Models\Pickup;
use App\Models\Schedule;
use App\Rules\CheckForExistingActiveRecurringOrders;
use Illuminate\Validation\Rule;

class UpdateDeliveryRequest extends Request
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        /** @var Pickup $pickup */
        $pickup = $this->route()->parameter('delivery');

        return [
            // Description
            'title' => ['sometimes', 'required', 'string', 'max:255'],
            'display_name' => ['nullable', 'string', 'max:255'],

            // Fees
            'settings' => ['array'],
            'settings.delivery_fee_type' => ['in:1,2'],
            'delivery_rate' => ['numeric', 'min:0'],
            'tax_delivery_fee' => ['boolean'],
            'apply_limit' => ['boolean'], // cap delivery fee
            'delivery_total_threshold' => ['numeric', 'min:0'],
            'delivery_fee_cap' => ['numeric', 'min:0'],
            'display_cart_shipping_calculator' => ['boolean'],

            // Settings
            'status_id' => [
                'sometimes',
                Rule::in([1,3,4]),
                new CheckForExistingActiveRecurringOrders($pickup)
            ],
            'visible' => ['boolean'],
            'tax_rate' => ['numeric', 'min:0', 'max:100'],
            'min_customer_orders' => ['numeric', 'min:0'],
            // TODO: restructure how form gets submitted so validation can be performed on each id in payment_methods array
            'payment_methods' => ['array'],
            'settings.sales_channel' => ['integer'],
            'pricing_group_id' => ['nullable', 'exists:product_price_groups,id'],
            'pickup_times' => ['nullable', 'string'],
            'settings.email_order_confirmation_template' => ['nullable', 'exists:templates,id'],
            'settings.email_order_packed_template' => ['nullable', 'exists:templates,id'],
            'settings.process_order_email' => ['nullable', 'exists:templates,id'],
            'settings.sms_subscription_reorder_template' => ['nullable', 'exists:templates,id'],
            'settings.recurring_orders_welcome_email_template_id' => ['nullable', 'exists:templates,id'],
            'settings.recurring_orders_reorder_email_template_id' => ['nullable', 'exists:templates,id'],
            'settings.ltv_conversion_multiplier' => ['nullable', 'numeric'],

            // Display Messages
            'settings.checkout_notes' => ['nullable', 'string'],

            // Other
            'schedule_id' => ['sometimes',
                function ($attribute, $value, $fail) {
                    /** @var Pickup|null $delivery */
                    $delivery = request()->route('delivery');

                    if ($delivery?->subscriptions()->exists() ?? false) {
                        if (is_null($value)) {
                            $fail('The schedule cannot be removed from delivery zones with active subscriptions.');
                        } else if (Schedule::where(['id' => $value, 'type_id' => Schedule::TYPE_CUSTOM])->exists()) {
                            $fail('Custom schedules cannot be assigned to delivery zones with active subscriptions.');
                        }
                    }

                    if ( ! is_null($value) && Schedule::where('id', $value)->doesntExist()) {
                        $fail('The selected schedule id is invalid.');
                    }
                }
            ]
        ];
    }

    public function messages(): array
    {
        return [
            'delivery_rate.min' => 'The Delivery Fee price field cannot be negative.',
            'delivery_rate.numeric' => 'The Delivery Fee price field must be a number.',
            'delivery_total_threshold.numeric' => 'The Cap Threshold field must be a number.',
            'delivery_total_threshold.min' => 'The Cap Threshold field cannot be negative.',
            'delivery_fee_cap.numeric' => 'The Capped Delivery Fee Total field must be a number.',
            'delivery_fee_cap.min' => 'The Capped Delivery Fee Total field cannot be negative.',
            'slug.unique' => 'The URL field slug is already being used.',
            'apply_limit.boolean' => 'The cap delivery fee field must be true or false.',
        ];
    }

    protected function prepareForValidation()
    {
        if ($this->has('tax_rate') && is_numeric($this->tax_rate)) {
            $this->merge(['tax_rate' => $this->tax_rate / 100]);
        }
    }
}

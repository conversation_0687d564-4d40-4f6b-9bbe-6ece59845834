<?php

namespace App\Http\Controllers\API;

use Illuminate\Http\JsonResponse;
use App\Http\Controllers\Controller;
use App\Models\Product;
use App\Models\ProductPrice;
use App\Models\ProductPriceGroup;
use Illuminate\Http\Request;

class ProductPriceGroupController extends Controller
{
    public function index()
    {
        return ProductPriceGroup::all();
    }

    public function store(Request $request): JsonResponse
    {
        $request->validate([
            'title' => ['required', 'unique:product_price_groups,title'],
            'type' => ['integer'],
            'amount' => ['sometimes', 'integer', 'min:0'],
        ]);

        $productPriceGroup = ProductPriceGroup::create([
            'title' => $request->get('title'),
            'type' => $request->get('type'),
            'amount' => $request->get('amount'),
        ]);

        if ($request->input('auto_assign')) {
            Product::withoutTrashed()->chunk(200, function ($products) use ($productPriceGroup) {
                foreach ($products as $product) {
                    ProductPrice::create([
                        'group_id' => $productPriceGroup->id,
                        'product_id' => $product->id,
                        'unit_price' => $product->unit_price,
                        'sale_unit_price' => $product->sale_unit_price,
                    ]);
                }
            });
        }

        return response()->json($productPriceGroup);
    }

    public function update(Request $request, int $id): JsonResponse
    {
        $request->validate([
            'title' => ['required', 'unique:product_price_groups,title,' . $id],
            'type' => ['integer'],
            'amount' => ['sometimes', 'integer', 'min:0'],
        ]);

        $product = ProductPriceGroup::findOrFail($id)
            ->update($request->all());

        return response()->json($product);
    }

    public function destroy(int $id): JsonResponse
    {
        ProductPriceGroup::findOrFail($id)->delete();

        return response()->json('Price group deleted');
    }
}

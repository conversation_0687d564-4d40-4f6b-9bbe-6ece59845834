<?php

namespace App\Http\Controllers\API;

use App\Billing\Gateway\GatewayException;
use App\Billing\Gateway\PaymentMethod;
use App\Contracts\Billing;
use App\Exceptions\OrderChargeException;
use App\Http\Controllers\Controller;
use App\Models\User;
use Illuminate\Http\JsonResponse;

class UserPaymentMethodsController extends Controller
{
    public function index(User $user): JsonResponse
    {
        $default_source_id = $user->defaultCard()->value('source_id');

        return response()->json(
            $user->stripePaymentMethods()
                ->map(function (PaymentMethod $payment_method) use ($default_source_id) {
                    $payment_method_array = $payment_method->toArray();
                    $payment_method_array['default'] = $payment_method->id === $default_source_id;
                    return $payment_method_array;
                })
        );
    }

    public function store(User $user, Billing $billing): JsonResponse
    {
        $validated = request()->validate([
            'make_default' => ['nullable'],
            'setup_intent_id' => ['required']
        ]);

        try {
            /** @var Billing $billing */
            $setup_intent = $billing->fetchSetupIntent($validated['setup_intent_id']);
            $payment_method = $billing->fetchPaymentMethod($setup_intent->payment_method);
        } catch (GatewayException|OrderChargeException $exception) {
            return response()->json(
                ['message' => $exception->getMessage()],
                400
            );
        }
        $user->cards()->create([
            'default' => false,
            'source_id' => $payment_method->id,
        ]);

        return response()->json([
            'id' => $payment_method->id,
            'brand' => $payment_method->card->brand,
            'last_four' => $payment_method->card->last4,
            'exp_month' => $payment_method->card->exp_month,
            'exp_year' => $payment_method->card->exp_year
        ], 201);
    }
}

<?php

namespace App\Http\Controllers\API\Theme;

use App\Actions\Product\ConfirmGiftCardPurchase;
use App\Actions\Product\ConfirmPreorderPurchase;
use App\Events\Order\OrderWasConfirmed;
use App\Http\Controllers\Controller;
use App\Http\Requests\API\ProductCheckoutRequest;
use App\Models\Order;
use App\Models\Product;
use Exception;

class ProductCheckoutController extends Controller
{
    /**
     * @throws Exception
     */
    public function __invoke(ProductCheckoutRequest $request, Product $product)
    {
        if ($request->get('type') === 'gift-card') {
            return $this->handleGiftCardPurchase($product, $request);
        }

        $order = app(ConfirmPreorderPurchase::class)->handle(auth()->user(), $request->validated());
        return $this->handleSuccessfulPurchase($order);
    }

    private function handleGiftCardPurchase(Product $product, ProductCheckoutRequest $request)
    {
        try {
            $order = app(ConfirmGiftCardPurchase::class)->handle(auth()->user(), $request->validated());
        } catch (Exception $exception) {
            return response()->json([
                'error' => [ 'message' => $exception->getMessage() ]
            ], 400);
        }

        return $this->handleSuccessfulPurchase($order);
    }

    private function handleSuccessfulPurchase(Order $order)
    {
        session(['cart' => null]);

        event(new OrderWasConfirmed($order));

        session()->flash('orderWasConfirmed');

        return response()->json(compact('order'), 201);
    }
}

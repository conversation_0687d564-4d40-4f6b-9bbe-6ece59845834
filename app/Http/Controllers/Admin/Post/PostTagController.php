<?php

namespace App\Http\Controllers\Admin\Post;

use App\Http\Controllers\Controller;
use App\Models\Post;
use App\Models\Tag;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PostTagController extends Controller
{
    public function index(int $postId)
    {
        $postTags = Post::findOrFail($postId)->tags()->pluck('id')->toArray();
        $tags = Tag::select(['id', 'title', 'slug'])->get();
        foreach ($tags as $index => $tag) {
            if (in_array($tag->id, $postTags)) {
                $tags[$index]['active'] = true;
            } else {
                $tags[$index]['active'] = false;
            }
        }

        return $tags->toJson();
    }

    public function store(Request $request, int $postId): JsonResponse
    {
        if (! is_null(Tag::where(['title' => $request->get('title')])->where('type', 'post')->first())) {
            return response()->json(false);
        }

        $tag = Tag::firstOrCreate([
            'title' => $request->get('title'),
            'type' => Tag::type('post'),
        ]);
        $post = Post::findOrFail($postId);
        $post->tags()->attach($tag->id);

        return response()->json([
            'id' => $tag->id,
            'title' => $tag->title,
            'slug' => $tag->slug,
            'active' => true,
        ]);
    }

    public function update(int $postId, int $tagId): JsonResponse
    {
        $post = Post::findOrFail($postId);
        $post->tags()->attach($tagId);

        return response()->json(['responseText' => 'Tag assigned to post']);
    }

    public function destroy(int $postId, int $tagId): JsonResponse
    {
        $post = Post::findOrFail($postId);
        $post->tags()->detach($tagId);

        return response()->json(['responseText' => 'Tag dissociated from post.']);
    }
}

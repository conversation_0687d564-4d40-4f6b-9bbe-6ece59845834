<?php

namespace App\Http\Controllers\Admin\Post;

use App\Http\Controllers\Controller;
use App\Models\Post;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class PostPhotoController extends Controller
{
    public function update(Request $request, int $postId): JsonResponse
    {
        $post = Post::findOrFail($postId);
        $post->cover_photo = $request->get('cover_photo');
        $post->save();

        return response()->json([
            'responseText' => 'Cover photo set.',
        ]);
    }
}

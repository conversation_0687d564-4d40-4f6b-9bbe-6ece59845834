<?php

namespace App\Http\Controllers\Admin\Post;

use App\Http\Controllers\Controller;
use App\Models\Post;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;
use Illuminate\View\View;

class PostController extends Controller
{
    public function index(Request $request): View
    {
        $posts = Post::with('author')
            ->filter($request->all())
            ->orderBy('published_at', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(50);

        return view('posts.index')
            ->with(compact('posts'));
    }

    public function store(Request $request)
    {
        $validated = $request->validate([
            'title' => ['required', 'max:255'],
            'user_id' => ['nullable', 'exists:users,id'],
        ]);

        $now = now();

        $post = Post::create(array_merge($validated, [
            'published_year' => $now->year,
            'published_month' => $now->month,
            'published_day' => $now->day,
            'published_at' => $now,
        ]));

        if ($request->expectsJson()) {
            return response()->json(['redirect' => route('admin.posts.edit', $post->slug)]);
        }

        return to_route('admin.posts.edit', $post->slug);
    }

    public function show(int $id): RedirectResponse
    {
        return redirect('/blog/'.Post::findOrFail($id)->slug);
    }

    public function edit(string $slug)
    {
        try {
            $post = Post::where('slug', $slug)->firstOrFail();
        } catch (ModelNotFoundException $e) {
            error('Blog post could not be found.');

            return redirect('/admin/posts');
        }

        return view('posts.edit')
            ->with(compact('post'));
    }

    public function update(Request $request, $id)
    {
        $request->validate([
            'title' => ['required', 'max:255'],
            'published_at' => ['required', 'date'],
        ]);

        $request['published_at'] = Carbon::createFromFormat('m/d/Y', $request->get('published_at'));
        Post::findOrFail($id)->update($request->all());

        return back();
    }

    public function destroy(int $id)
    {
        Post::findOrFail($id)->delete();

        return to_route('admin.posts.index');
    }
}

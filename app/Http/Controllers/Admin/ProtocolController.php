<?php

namespace App\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use App\Models\Protocol;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Illuminate\View\View;

class ProtocolController extends Controller
{
    public function index(Request $request): View
    {
        return view('protocols.index')->with([
            'protocols' => Protocol::orderBy(
                $request->input('orderBy', 'title'),
                $request->input('sort', 'asc')
            )->get(),
        ]);
    }

    public function store(Request $request)
    {
        $request->validate([
            'title' => 'required',
        ]);
        $protocol = Protocol::create($request->all());

        return to_route('admin.protocols.edit', $protocol->id);
    }

    public function show(Request $request, Protocol $protocol)
    {
        return to_route('admin.protocols.edit', $protocol);
    }

    public function edit(int $id): View
    {
        $protocol = Protocol::findOrFail($id);

        return view('protocols.edit')
            ->with(compact('protocol'));
    }

    public function update(Request $request, int $id)
    {
        $slug = Str::slug($request->get('slug'));
        $request['slug'] = $slug;

        $request->validate([
            'title' => ['required'],
            'slug' => ['unique:protocols,slug,'.$id],
        ], [
            'slug.unique' => 'That protocol url is already being used.',
        ]);

        Protocol::findOrFail($id)->update($request->all());

        return back();
    }

    public function destroy(int $id)
    {
        Protocol::findOrFail($id)->delete();

        return to_route('admin.protocols.index');
    }
}

<?php

namespace App\Http\Controllers\Admin\Order;

use App\Http\Controllers\Controller;
use App\Models\Event;
use App\Models\Order;
use App\Models\OrderItem;
use Carbon\Carbon;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class OrderItemWeightController extends Controller
{
    public function update(Request $request, int $orderId, int $itemId): JsonResponse
    {
        $request->validate([
            'weight' => ['required', 'min:0', 'numeric'],
        ]);

        $order = Order::findOrFail($orderId);

        if ($order->is_paid) {
            return response()->json('The weight could not be updated since the order has already been marked as paid.', 422);
        }

        $item = OrderItem::findOrFail($itemId);

        $oldWeight = $item->weight;
        $newWeight = $request->get('weight');

        // Alert the user if the new weight they entered is significantly different.
        $weightDifferenceToCheck = setting('estimated_weight_check', 10);
        if ($weightDifferenceToCheck > 0) {
            $differenceAmt = abs($item->weight - $request->get('weight'));
            $difference = $differenceAmt >= $weightDifferenceToCheck ? $differenceAmt : false;
        } else {
            $difference = false;
        }

        $item->weight = $newWeight;
        $item->updateSubtotal();
        $item->save();

        $order->updateTotals();

        $this->recordEvent($order, $item, number_format($oldWeight, 2), number_format($newWeight, 3));

        return response()->json([
            'item' => $item,
            'order' => $order,
            'weight_difference' => $difference,
            'responseText' => 'Item weight updated.',
        ]);
    }

    private function recordEvent(Order $order, OrderItem $item, string $oldWeight, string $newWeight)
    {
        Event::create([
            'model_type' => Order::class,
            'model_id' => $order->id,
            'description' => "{$item->title} weight changed from {$oldWeight} to {$newWeight}",
            'event_id' => 'order_item_weight_updated',
            'user_id' => auth()->id(), // So we can know who did this...
            'created_at' => Carbon::now(),
            'metadata' => json_encode([
                'item_id' => $item->id,
                'old_weight' => $oldWeight,
                'new_weight' => $newWeight,
            ]),
        ]);
    }
}

<?php

namespace App\Http\Controllers\Theme;

use App\Http\Controllers\Controller;
use App\Models\Recipe;
use App\Models\Tag;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class RecipeController extends Controller
{
    public function index(Request $request)
    {
        $recipes = Recipe::with('author')->where('published', true);
        if ($request->filled('tag')) {
            $recipes = $recipes->whereHas('tags', function ($q) use ($request) {
                return $q->where('slug', $request->get('tag'));
            });
        }

        if ($request->filled('q')) {
            $recipes = $recipes->where('title', 'LIKE', '%' . $request->get('q') . '%');
        }

        $tags = Tag::whereIn('id', DB::table('recipe_tag')->pluck('tag_id'))->with('recipes')->get();

        $recipes = $recipes->orderBy('published_at', 'desc')
            ->orderBy('created_at', 'desc')
            ->paginate(12);

        return view('theme::recipes.index')
            ->with(compact('tags', 'recipes'));
    }

    public function show(Recipe $recipe)
    {
        if (!$recipe->isDraft()) {
            return $this->renderRecipe($recipe);
        }

        if (auth()->check() && ! auth()->user()?->isCustomer()) {
            return $this->renderRecipe($recipe);
        }

        error('The item(s) you are requesting could not be found.');
        return redirect()->route('recipes.index');
    }

    private function renderRecipe(Recipe $recipe)
    {
        $recipe->load('ingredients.product', 'author');

        return view('theme::recipes.show')
            ->with([
                'recipe' => $recipe,
                'recent' => Recipe::recent($recipe)->get()
            ]);
    }
}

<?php

namespace App\Http\Controllers\Theme\Pages;

use App\Http\Controllers\Controller;
use App\Http\Controllers\Theme\Concerns\FetchesCustomPage;
use App\Models\Vendor;
use Illuminate\Database\Eloquent\ModelNotFoundException;
use Illuminate\View\View;

class VendorPageController extends Controller
{
    use FetchesCustomPage;

    public function index(): View
    {
        try {
            [$page, $html] = $this->fetchCustomPage('vendors');
        } catch (ModelNotFoundException $exception) {
            return view('theme::vendor.index')
                ->with(['vendors' => Vendor::orderBy('title')->get()]);
        }

        return view('theme::pages.show')
            ->with(compact('page', 'html'));
    }

    public function show(string $vendorId)
    {
        try {
            $vendor = Vendor::where('slug', $vendorId)->firstOrFail();
        } catch (ModelNotFoundException $exception) {
            return back(301);
        }

        return view('theme::vendor.show')
            ->with(compact('vendor'));
    }
}

<?php

namespace App\Http\Controllers\Theme\Checkout;

use App\Actions\CreateRecurringOrderBlueprint;
use App\Billing\Gateway\PaymentMethod;
use App\Cart\Validators\CartValidationException;
use App\Cart\Validators\DeadlineHasPassed;
use App\Cart\Validators\HasAvailableProductQuantities;
use App\Cart\Validators\HasValidSubscription;
use App\Cart\Validators\OnePageCheckoutValidation;
use App\Contracts\Cartable;
use App\Events\Cart\CartUpdated;
use App\Events\Checkout\CheckoutInitiated;
use App\Events\Order\OrderWasConfirmed;
use App\Events\Subscription\RecurringOrderCreated;
use App\Exceptions\BackOrderException;
use App\Exceptions\ExclusivityException;
use App\Exceptions\OrderDoesNotMeetRequirementsException;
use App\Exceptions\OrderWindowClosedException;
use App\Http\Controllers\Controller;
use App\Http\Middleware\HandleThemeInertiaRequests;
use App\Models\Coupon;
use App\Models\Date;
use App\Models\Integration;
use App\OrderWindow;
use App\Services\SettingsService;
use Illuminate\Foundation\Http\Middleware\ConvertEmptyStringsToNull;
use Illuminate\Http\Request;
use Inertia\Inertia;

class CheckoutConfirmationController extends Controller
{
    public function __construct()
    {
        $this->middleware([ConvertEmptyStringsToNull::class, HandleThemeInertiaRequests::class]);
    }

    public function show(SettingsService $settings)
    {
        $cart = $this->shopperCart();

        if (is_null($cart)) {
            error('You have not added any items to your cart yet.');
            return redirect()->to(session('store_url', route('store.index')));
        }

        // This ensures an expired date does not get validated
        if ($cart->cartDate()?->toOrderWindows()->deadlineDatetime() < now()) {
            $available_date = $cart->cartLocation()?->activeOrderWindow()?->originalDate();
            if ( ! is_null($available_date)) {
                $cart->updateCartDate($available_date);
                event(new CartUpdated($cart));
            }
        }


        try {
            app(OnePageCheckoutValidation::class)->validate($cart);

        } catch (CartValidationException $exception) {
            if ($exception->rule !== HasAvailableProductQuantities::class) {
                return $this->handleFailedCartValidation($exception);
            }

            session()->flash('show_out_of_stock_message', $exception->data['items']);
        }

        return $this->handleOnePageCheckout($cart, $settings->requiresAddressAtCheckout());
    }

    protected function handleFailedCartValidation(CartValidationException $exception)
    {
        if ( ! empty($exception->getMessage())) {
            error($exception->getMessage());
        }

        if ($exception->rule === DeadlineHasPassed::class) {
            $cart = $this->shopperCart();
            $available_date = $cart->cartLocation()?->activeOrderWindow()?->originalDate();
            $this->shopperCart()->updateCartDate($available_date);
        }

        $redirect = match($exception->rule) {
            DeadlineHasPassed::class => route('checkout.confirm.show'),
            HasValidSubscription::class => route('checkout.offers.show'),
            default => route('cart.show')
        };

        return redirect()->to($redirect);
    }

    protected function handleOnePageCheckout(Cartable $cart, bool $require_shipping_address)
    {
        $delivery_method = $cart->cartLocation();
        $cart_customer = $cart->cartCustomer();

        $customer = $cart_customer?->only([
            'street', 'street_2', 'city', 'state', 'zip', 'country', 'subscribed_to_sms_marketing_at',
            'checkout_card_id', 'exempt_from_fees', 'exempt_from_taxes'
        ]);

        $customer['cards'] = $cart_customer?->stripePaymentMethods()
            ->map(fn(PaymentMethod $card) => [
                'id' => $card->id,
                'brand' => $card->brand,
                'last_four' => $card->last_four,
                'exp_month' => $card->exp_month,
                'exp_year' => $card->exp_year,
            ]) ?? collect();

        $customer['addresses'] = $cart_customer?->addresses()->get() ?? collect();

        $notes_placeholder = getMessage('checkout_step4');
        $delivery_date_tba_message = getMessage('order_status_bar_tba_unconfirmed');
        $sms_opt_in_settings = (new Integration)->smsOptInSettings('during_checkout');
        $available_payment_options = $delivery_method->paymentMethods();
        $available_dates = $delivery_method->activeOrderWindowCollection($delivery_method->schedule?->ordering_in_advance)
            ->map(fn(OrderWindow $window) => [
                'id' => $window->dateId(),
                'date' => $window->deliveryDatetime()->format('Y-m-d H:i:s')
            ])
            ->values();

        $delivery_method->load('fees');

        if ($cart->hasCouponApplied(config('grazecart.welcome_coupon_code')) && $cart->cartSubtotal() < 17500) {
            $cart->removeCouponFromCart(config('grazecart.welcome_coupon_code'));
        }

        if ( ! $cart->hasCouponApplied(config('grazecart.welcome_coupon_code')) && $cart->hasConditionalCouponApplied(config('grazecart.welcome_coupon_code')) && $cart->cartSubtotal() >= 17500) {
            $cart->applyCouponToCart(Coupon::welcomeCoupon());
        }

        event(new CheckoutInitiated($cart));

        $cart = $cart->toCartArray();
        $cart['billing']['source_id'] = $cart_customer?->defaultCard()
            ->value('source_id');

        $checkout_settings = [
            'payment_gateway' => [
                'public_key' => app(SettingsService::class)->stripePublicKey(),
                'environment' => app()->isProduction() ? 'live' : 'test',
            ],
            'available_dates' => $available_dates,
            'available_payment_options' => $available_payment_options,
            'sms_opt_in_settings' => $sms_opt_in_settings,
            'delivery_method' => $delivery_method,
            'product' => null,
            'notes_placeholder' => $notes_placeholder,
            'quantity_limit' => null,
            'require_shipping_address' => $require_shipping_address,
            'delivery_date_tba_message' => $delivery_date_tba_message,
            'google_places_js_api_key' => config('services.google.places_js_api_key'),
        ];

        return Inertia::render('Checkout/Standard', compact(
            'cart',
            'customer',
            'checkout_settings'
        ));
    }

    public function store(Request $request, SettingsService $settings)
    {
        $cart = $this->shopperCart();

        if (is_null($cart)) {
            return redirect()->route('cart.show');
        }

        if ($request->get('pickup_date') || $request->get('date_id')) {
            $date = Date::find($request->get('pickup_date', $request->get('date_id')));

            if ( ! is_null($date)) {
                $cart->updateCartDate($date);
            }
        }

        try {
            app(OnePageCheckoutValidation::class)->validate(cart: $cart, validate_delivery_zone: false);
        } catch (CartValidationException $exception) {
            return $this->handleFailedCartValidation($exception);
        }

        if ($cart->cartLocation()->isDeliveryZone() && ! $cart->belongsInDeliveryZone()) {
            error(__('messages.checkout.address_error'));
            return redirect(route('checkout.address.show'));
        }

        try {
            $order = $cart->confirm($request->all());

        } catch (OrderDoesNotMeetRequirementsException | BackOrderException $e) {
            error($e->getMessage() . '<br/> You will need to edit your cart before you can continue.');
            return redirect(route('cart.show'));
        } catch (ExclusivityException | OrderWindowClosedException $e) {
            error($e->defaultMessage());
            return redirect(route('cart.show'));
        }

        // Save recurring order
        $subscription = $cart->cartSubscription();

        if ( ! is_null($subscription) && $cart->cartIsEligibleForSubscription()) {
            $recurringOrder = app(CreateRecurringOrderBlueprint::class)->execute(
                $order,
                $subscription->frequency ?? $request->get('reorder_frequency'),
                $subscription->product_incentive_id ?? $request->get('promo_item_id')
            );

            if ($order->pickup->isDeliveryZone()) {
                $order->customer->updateDefaultShippingAddress($cart->getShippingInfo());
            }

            event(new RecurringOrderCreated($recurringOrder));
        }

        event(new OrderWasConfirmed($order));

        session(['last_confirmed_order' => $order->id]);

        session()->flash('orderWasConfirmed');

        return redirect(route('checkout.complete.show'));
    }
}

<?php

namespace App\Services;

use Illuminate\Http\Client\RequestException;
use libphonenumber\PhoneNumberFormat;
use libphonenumber\PhoneNumberUtil;

class PhoneNumberService
{
    public function __construct(
        protected Twilio $twilio
    ) {}

    /**
     * @return array{valid: bool, type: string|null}
     */
    public function lookup(string $phone_number): array
    {
        try {
            $formatted_phone_number = $this->format($phone_number);
        } catch (\InvalidArgumentException $exception) {
            return ['valid' => false, 'type' => null];
        }

        try {
            $response = $this->twilio->lookup($formatted_phone_number);
        } catch (RequestException $exception) {
            return ['valid' => false, 'type' => null];
        }

        return [
            'valid' => $response['valid'] ?? false,
            'type' => $response['line_type_intelligence']['type'] ?? null,
        ];
    }

    /**
     * @throws \InvalidArgumentException
     */
    public function format(string $phone_number, int $format = PhoneNumberFormat::E164): string
    {
        $util = PhoneNumberUtil::getInstance();

        try {
            $number = $util->parse($phone_number, 'US');
        } catch (\Exception $exception) {
            throw new \InvalidArgumentException('The phone number could not be parsed.');
        }

        if ( ! $util->isValidNumber($number)) {
            throw new \InvalidArgumentException('The phone number is not valid.');
        }

        return $util->format($number, $format);
    }
}

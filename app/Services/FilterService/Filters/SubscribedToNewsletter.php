<?php

namespace App\Services\FilterService\Filters;

use Illuminate\Http\Request;

class SubscribedToNewsletter extends Filter
{
    public static string $query_param = 'newsletter';

    protected string $label = 'Newsletter:';

    public function setValue(Request $request): void
    {
        if ( ! $request->has(static::$query_param)) return;

        $this->value = $request->get(static::$query_param) ? 'Subscribed' : 'Not Subscribed';
    }
}
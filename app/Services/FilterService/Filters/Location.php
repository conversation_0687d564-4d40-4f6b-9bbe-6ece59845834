<?php

namespace App\Services\FilterService\Filters;

use Illuminate\Http\Request;

class Location extends Filter
{
    public static string $query_param = 'pickup_id';

    protected string $label = 'Location:';

    public function setValue(Request $request): void
    {
        $id = $request->get(static::$query_param);

        if (is_null($id)) return;

        $this->value = \App\Models\Pickup::whereIn('id', \Arr::wrap($id))
            ->pluck('title')
            ->implode(', ');
    }
}
<?php

namespace App\Services;

use App\Models\Payment;
use App\Models\Setting;
use Illuminate\Support\Carbon;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Cache;

class SettingsService
{
    public function all(): array
    {
        if (app()->runningInConsole()) {
            return Setting::pluck('value', 'key')->toArray();
        }
        
        return Cache::tags(['setting'])->remember('settings', now()->addMinutes(120), function () {
            return Setting::pluck('value', 'key')->toArray();
        });
    }

    public function preventsSiteGuests(): bool
    {
        return $this->registrationWall() === 'site';
    }

    /**
     * registrationWall - returns one of the following strings: site,  store,  cart,  price,  checkout
     */
    public function registrationWall(): string
    {
        if ( ! is_null(setting('registration_wall',null) )) {
            return setting('registration_wall');
        }

        if (((bool) setting('require_visitors_have_account', false)) === true) {
            return 'site';
        }

        if (((bool) setting('prevent_guest_shopper', false)) === true) {
            return 'store';
        }

        if (setting('product_price_gate', 'allow_any') === 'require_account') {
            return 'prices';
        }

        if (setting('registration_wall', 'cart') === 'checkout') {
            return 'checkout';
        }

        return 'cart';
    }

    public function preventsStoreGuests(): bool
    {
        return in_array($this->registrationWall(), ['site', 'store']);
    }

    public function requiresLocationOnStoreView(): bool
    {
        return $this->customerLocationWall() === 'store';
    }

    /**
     * customerLocationWall - returns one of the following strings: store, cart, off
     */
    public function customerLocationWall(): string
    {
        if ( ! is_null(setting('customer_location_wall',null) )) {
            return setting('customer_location_wall');
        }

        if (((bool) setting('require_shopper_local', false)) === true) {
            return 'store';
        }

        return 'cart';
    }

    public function pricesRequireAccount(): bool
    {
        return $this->registrationWall() === 'prices';
    }

    public function preventsMultipleCouponsPerOrder(): bool
    {
        return (bool) setting('prevent_multiple_coupons', false);
    }

    public function deadlineEndTime(): string
    {
        $hour = $this->deadlineHour();

        if ($hour === 24) return '23:59:59';

        $hour = str((string) $hour)->padLeft(2, '0');

        return "{$hour}:00:00";
    }

    public function deadlineHour(): int
    {
        return (int) setting('order_deadline_hour', 24);
    }

    public function formattedDeadlineEndTime(): string
    {
        $deadline_hour = $this->deadlineHour();

        return ($deadline_hour === 24)
            ? '11:59 PM'
            : Carbon::createFromTime($deadline_hour)->format('g:i A');
    }

    public function requiresAddressAtCheckout(): bool
    {
        return (bool) setting('require_address_at_checkout', true);
    }

    public function allowsPOBoxShipping(): bool
    {
        return (bool) setting('ship_to_pobox', true);
    }

    public function farmStreet(): ?string
    {
        return setting('farm_street');
    }

    public function farmCity(): ?string
    {
        return setting('farm_city');
    }

    public function farmState(): ?string
    {
        return setting('farm_state');
    }

    public function farmPostalCode(): ?string
    {
        return setting('farm_zip');
    }

    public function stripeSecretKey(): string
    {
        return $this->isStandardStripeAccount()
            ? decrypt(setting('stripe_secret_key'))
            : config('services.stripe.secret');
    }

    public function isStandardStripeAccount(): bool
    {
        return $this->stripeAccountType() === 'standard';
    }

    public function stripeAccountType(): string
    {
        return setting('stripe_account_type', 'connect');
    }

    public function stripePublicKey(): string
    {
        return $this->isStandardStripeAccount()
            ? decrypt(setting('stripe_public_key'))
            : config('services.stripe.key');
    }

    public function defaultParcelCount(): int
    {
        return (int) setting('default_parcel_count', 1);
    }

    public function businessAddress(): array
    {
        return [
            'street' => setting('farm_street'),
            'city' => setting('farm_city'),
            'state' => setting('farm_state'),
            'postal_code' => setting('farm_zip'),
            'country' => $this->farmCountry(),
        ];
    }

    public function farmCountry(): string
    {
        return setting('farm_country', 'USA');
    }

    public function timezone(): ?string
    {
        return setting('timezone');
    }

    public function cartService(): string
    {
        return setting('cart_service', 'order');
    }

    public function requiresAgreementToTermsAtCheckout(): bool
    {
        return (bool) setting('require_checkout_agreement', false);
    }

    public function allowsOrderModification(): bool
    {
        return (bool) setting('ordering_mode', false);
    }

    public function startingStoreCreditBalance(): int
    {
        return formatCurrencyForDB(setting('user_registration_credit', 0));
    }

    public function newCustomerReferralBonus(): int
    {
        return setting('referral_payout', 0);
    }

    public function existingCustomerReferralBonus(): int
    {
        return setting('referral_bonus', 0);
    }

    public function creditCardPaymentsAreEnabled(): bool
    {
        return Payment::query()
            ->enabled()
            ->where('key', 'card')
            ->exists();
    }

    public function geocoder(): ?string
    {
        return setting('geocoder');
    }

    public function blogPostsPerPage(): int
    {
        return setting('blog_posts_per_page', 12);
    }

    public function outOfStockSmsNotificationsEnabled(): bool
    {
        return setting('out_of_stock_sms_notifications', false);
    }

    public function outOfStockSmsNotificationsContacts(): Collection
    {
        return collect(explode(',', setting('out_of_stock_sms_notifications_contacts', '')));
    }

    public function registrationCredit(): int
    {
        return setting('user_registration_credit', 0);
    }
}

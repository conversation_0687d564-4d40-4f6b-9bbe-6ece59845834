<?php

namespace App\Services;

use App\Models\Order;
use App\Models\RecurringOrder;
use App\Models\User;
use Illuminate\Http\Client\RequestException;
use Illuminate\Support\Facades\Http;

class MetaConversionService
{
    /**
     * @throws RequestException
     */
    public function trackCompleteRegistration(User $user): void
    {
        $this->trackEvent([
            'event_name' => 'CompleteRegistration',
            'event_time' => now()->timestamp,
            'user_data' => [
                'em' => hash('sha256', strtolower(trim($user->email))),
            ],
            'action_source' => 'website',
        ]);
    }

    /**
     * @throws RequestException
     */
    private function trackEvent(array $event): ?array
    {
        $pixel_id = config('services.facebook.pixel_id');
        $access_token = config('services.facebook.access_token');

        if (is_null($pixel_id) || is_null($access_token)) return null;

        $payload = [['name' => 'data', 'contents' => json_encode([$event])]];

        if (!app()->environment('production')) {
            $payload['test_event_code'] = config('services.facebook.test_event_code');
        }

        return Http::asMultipart()
            ->post("https://graph.facebook.com/v23.0/{$pixel_id}/events?access_token={$access_token}", $payload)
            ->throw()
            ->json();
    }

    /**
     * @throws RequestException
     */
    public function trackPurchase(Order $order): void
    {
        $this->trackEvent([
            'event_name' => 'Purchase',
            'event_time' => now()->timestamp,
            'user_data' => [
                'em' => hash('sha256', strtolower(trim($order->customer->email))),
            ],
            'custom_data' => [
                'value' => $order->total,
                'currency' => 'USD',
                'first_time_order' => $order->first_time_order ? 'Yes' : 'No',
                'delivery_method_id' => $order->pickup_id,
                'delivery_method_name' => $order->pickup->title,
            ],
            'action_source' => 'website',
        ]);
    }

    /**
     * @throws RequestException
     */
    public function trackSubscription(RecurringOrder $subscription): void
    {
        $this->trackEvent([
            'event_name' => 'Subscribe',
            'event_time' => now()->timestamp,
            'user_data' => [
                'em' => hash('sha256', strtolower(trim($subscription->customer->email))),
            ],
            'custom_data' => [
                'value' => $subscription->subtotal(),
                'currency' => 'USD',
                'reorder_frequency' => $subscription->reorder_frequency,
                'delivery_method_id' => $subscription->fulfillment_id,
                'delivery_method_name' => $subscription->fulfillment->title,
            ],
            'action_source' => 'website',
        ]);
    }
}

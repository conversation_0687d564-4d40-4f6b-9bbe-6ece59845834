<?php

namespace App\Exports;

use App\Models\RecurringOrder;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithMapping;

class FirstTimeRenewalExport implements FromCollection, WithMapping, WithHeadings
{
    public function __construct(
        public Collection $subscriptions
    ) {}

    public function collection(): Collection
    {
        return $this->subscriptions;
    }

    public function map($row): array
    {
        /** @var RecurringOrder $row */

        $order_window = $row->pickupSchedule()->closestAvailableOrderWindow($row->ready_at);

        return [
            'id' => $row->id,
            'first_name' => $row->customer->first_name,
            'last_name' => $row->customer->last_name,
            'phone' => $row->customer->phone,
            'email' => $row->customer->email,
            'delivery_method' => $row->fulfillment->title,
            'generates_at' => $row->generate_at->format('Y-m-d H:i:s'),
            'deadline_at' => $order_window->deadlineDatetime()->format('Y-m-d H:i:s'),
            'ready_at' => $order_window->deliveryDatetime()->format('Y-m-d'),
        ];
    }

    public function headings(): array
    {
        return [
            'subscription_id',
            'first_name',
            'last_name',
            'phone',
            'email',
            'delivery_method',
            'generates_at',
            'deadline_at',
            'delivery_date',
        ];
    }
}

<?php

namespace App\Observers;

use App\Models\Protocol;
use Illuminate\Support\Facades\Cache;

class ProtocolObserver
{
    /**
     * Listen to the Protocol saving event.
     */
    public function saving(Protocol $protocol): void
    {
        Cache::tags('protocol')->flush();
    }

    /**
     * Listen to the Protocol deleting event.
     */
    public function deleting(Protocol $protocol): void
    {
        Cache::tags('protocol')->flush();
    }
}

<?php

namespace App\Livewire\Theme\Modals;

use App\Events\User\UserUpdated;
use App\Exceptions\NoGeocodeResultsException;
use App\Livewire\Theme\AddsProduct;
use App\Livewire\Theme\FetchesCart;
use App\Models\Pickup;
use App\Models\User;
use App\Services\DeliveryMethodService;
use App\Services\Geocoding\GeocodedAddress;
use App\Support\FetchesGeocodedAddress;
use Illuminate\Support\Facades\Cookie;
use Illuminate\Support\Once;
use Livewire\Attributes\On;
use Livewire\Component;

class ConfirmDeliveryMethod extends Component
{
    use ModalAttributes, FetchesGeocodedAddress, FetchesCart, AddsProduct;

    public ?int $product_id = null;
    public array $product_metadata = [];

    public string $postal_code = '';

    public ?string $geocoded_city = null;


    public function render()
    {
        return view('theme::livewire.modals.confirm-delivery-method');
    }

    public function submit()
    {
        $validated = $this->validate([
            'postal_code' => ['required', 'string', 'max:10'],
        ]);

        $this->geocoded_city = null;
        $this->postal_code = trim($this->postal_code);

        try {
            $geocoded_address = $this->fetchGeocodedAddressFromPostalCode($this->postal_code);
        } catch (NoGeocodeResultsException $e) {
            $this->addError('postal_code', __('Postal code could not be found.'));
            return;
        }

        /** @var Pickup|null $delivery_method */
        $delivery_method = $this->deliveryMethodForAddress($geocoded_address);

        if (is_null($delivery_method)) {
            $this->addError('postal_code', __('Delivery is not available in this postal code.'));
            return;
        }

        $this->geocoded_city = $geocoded_address->city;

        Cookie::queue(Cookie::make('shopping_city', $this->geocoded_city, 259200));
        Cookie::queue(Cookie::make('shopping_postal_code', $this->postal_code, 259200));
        Cookie::queue(Cookie::make('shopping_delivery_method_id', (string) $delivery_method->id, 259200));

        /** @var User $user */
        $user = auth()->user();

        $user->update(['pickup_point' => $delivery_method->id, 'zip' => $this->postal_code]);

        event(new UserUpdated($user));

        $cart = $this->fetchShopperCart(should_stub: true);
        $cart->updateCartLocation($delivery_method);
        $cart->setShippingInfo([
            'address_id' => null,
            'street' => '',
            'street_2' => '',
            'city' => $this->geocoded_city,
            'state' => $geocoded_address->state,
            'zip' => $this->postal_code,
            'postal_code' => $this->postal_code,
            'country' => 'USA',
            'save_for_later' => true,
        ]);

        if (!is_null($this->product_id)) {
            $this->addToCart($this->product_id, $this->product_metadata);
            $this->dispatch('openPanel', title: 'Shopping cart', component: 'theme.cart-side-panel');
        }

        Once::flush();

        $this->dispatch('cartDeliveryMethodUpdated');
        $this->close();
    }

    private function deliveryMethodForAddress(GeocodedAddress $address): ?Pickup
    {
        return app(DeliveryMethodService::class)
            ->deliveryZones()
            ->find($address)
            ->first();
    }

    #[On('close-modal-confirm-delivery-method')]
    public function close(): void
    {
        $this->reset();
        $this->closeModal();
    }

    #[On('open-modal-confirm-delivery-method')]
    public function open(?int $product_id = null, ?array $product_metadata = []): void
    {
        $this->product_id = $product_id;
        $this->product_metadata = $product_metadata;

        $this->openModal();
    }
}

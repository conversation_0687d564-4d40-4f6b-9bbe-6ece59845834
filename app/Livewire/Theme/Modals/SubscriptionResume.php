<?php

namespace App\Livewire\Theme\Modals;

use App\Events\Subscription\SubscriptionWasResumed;
use App\Models\Pickup;
use App\Models\Product;
use App\Models\RecurringOrder;
use App\Models\RecurringOrderItem;
use App\OrderWindow;
use App\Services\SubscriptionSettingsService;
use Illuminate\Support\Collection;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Locked;
use Livewire\Attributes\On;
use Livewire\Component;

class SubscriptionResume extends Component
{
    use ModalAttributes;

    #[Locked]
    public int $subscription_id;

    public ?int $promo_product_id = null;

    public ?int $frequency = null;

    public ?int $delivery_date_id = null;

    public ?int $previous_promo_product_id = null;
    public ?string $previous_promo_product_title = null;

    public function mount()
    {
        /** @var RecurringOrder $subscription */
        $subscription = $this->findSubscription();
        $this->frequency = $subscription->reorder_frequency;

        $settings = app(SubscriptionSettingsService::class);

        $previous_promo_item = $this->previousPromoItem();

        $this->promo_product_id = $previous_promo_item->product_id
            ?? $settings->defaultProductIncentiveId();

        $current_promo_product_ids = $settings->productIncentiveIds();

        $product_incentives = Product::query()
            ->whereIn('id', $current_promo_product_ids)
            ->pluck('title', 'id');

        if (
            ! is_null($previous_promo_item?->product_id)
            && $product_incentives->keys()->doesntContain($previous_promo_item->product_id)
        ) {
            $previous_promo_product = Product::query()
                ->select(['id', 'title'])
                ->find($previous_promo_item->product_id);

            if (!is_null($previous_promo_product)) {
                $this->promo_product_id = $previous_promo_product->id;
                $this->previous_promo_product_id = $previous_promo_product->id;
                $this->previous_promo_product_title = $previous_promo_product->title;
            }
        }
    }

    private function findSubscription(): ?RecurringOrder
    {
        return RecurringOrder::query()
            ->withTrashed()
            ->with(['fulfillment.schedule'])
            ->find($this->subscription_id);
    }

    private function previousPromoItem(): ?RecurringOrderItem
    {
        return RecurringOrderItem::query()
            ->where('order_id', $this->subscription_id)
            ->select(['product_id'])
            ->firstWhere('type', 'promo');
    }

    public function render()
    {
        /** @var RecurringOrder $subscription */
        $subscription = $this->findSubscription();

        $settings = app(SubscriptionSettingsService::class);

        $product_incentives = Product::query()
            ->whereIn('id', $settings->productIncentiveIds())
            ->pluck('title', 'id');

        if (!is_null($this->previous_promo_product_id)) {
            $product_incentives->prepend(
                $this->previous_promo_product_title,
                $this->previous_promo_product_id
            );
        }

        $delivery_dates = $this->availableOrderWindows($subscription->fulfillment)
            ->mapWithKeys(fn(OrderWindow $order_window) => [$order_window->dateId() => $order_window->deliveryDatetime()]);

        return view('theme::livewire.modals.subscription-resume', compact('product_incentives', 'delivery_dates'));
    }

    private function availableOrderWindows(Pickup $delivery_method): Collection
    {
        return $delivery_method
            ->orderWindowCollection()
            ->filter(fn(OrderWindow $window) =>
                $window->deliveryDatetime()
                    ->lessThanOrEqualTo(today()->addDays(28)->endOfDay())
            );
    }

    public function submit()
    {
        /** @var RecurringOrder $subscription */
        $subscription = $this->findSubscription();

        $previous_promo_item = $this->previousPromoItem();

        $available_promo_ids = app(SubscriptionSettingsService::class)->productIncentiveIds();

        if (!is_null($previous_promo_item) && $available_promo_ids->doesntContain($previous_promo_item->product_id)) {
            $available_promo_ids->push($previous_promo_item->product_id);
        }

        $validated = $this->validate([
            'delivery_date_id' => [
                'required',
                Rule::in(
                    $this->availableOrderWindows($subscription->fulfillment)
                        ->map(fn(OrderWindow $window) => $window->dateId())
                )
            ],
            'frequency' => ['required', Rule::in([7, 14, 28, 42, 56])],
            'promo_product_id' => ['required', Rule::in($available_promo_ids)],
        ]);

        $subscription = $this->findSubscription();

        $available_order_windows = $subscription->fulfillment->orderWindowCollection();
        /** @var OrderWindow $delivery_order_window */
        $delivery_order_window = $available_order_windows->firstWhere(fn(OrderWindow $window) => $window->dateId() === $validated['delivery_date_id'])
            ?? $available_order_windows->first();

        $old_deleted_at = $subscription->deleted_at;
        $old_reorder_frequency = $subscription->reorder_frequency;

        $subscription->generate_at = $delivery_order_window->generatesAtDatetime();
        $subscription->ready_at = $delivery_order_window->deliveryDatetime();
        $subscription->reorder_frequency = $validated['frequency'];

        $subscription->save();
        $subscription->restore();

        $subscription->items()->updateOrCreate(['type' => 'promo'], [
            'product_id' => $validated['promo_product_id'],
            'qty' => 1,
            'customer_id' => $subscription->customer_id
        ]);

        event(new SubscriptionWasResumed(
            subscription: $subscription,
            old_deleted_at: $old_deleted_at,
            old_frequency: $old_reorder_frequency,
            old_promo_item_id: $previous_promo_item?->product_id,
            new_delivery_date: $delivery_order_window->deliveryDatetime(),
            new_frequency: $validated['frequency'],
            new_promo_item_id: $validated['promo_product_id'],
        ));

        flash('Your subscription has been resumed.');

        session()->flash('livewire_dispatch', [
            'event' => 'openPanel',
            'params' => ['title' => 'Subscription', 'component' => 'theme.subscription-side-panel']
        ]);

        $this->redirect(route('store.index'));
    }

    #[On('open-modal-subscription-resume')]
    public function open(): void
    {
        $this->openModal();
    }

    #[On('close-modal-subscription-resume')]
    public function close(): void
    {
        $this->closeModal();
    }
}

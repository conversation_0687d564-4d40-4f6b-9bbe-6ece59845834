<?php

namespace App\Livewire\Theme;

use App\Cart\Item;
use App\Contracts\Cartable;
use App\Models\Order;
use Livewire\Component;

class AddProductCounterButton extends Component
{
    use AddsProduct, DecrementsProduct;

    public string $style;

    public ?string $cta_label = null;

    public ?string $cta_classes = null;

    public array $metadata = [];

    public function mount()
    {
        $this->style = 'btn-brand';
    }

    public function render()
    {
        return view('theme::livewire.add-product-counter-button', [
            'count' => $this->count(),
        ]);
    }

    public function count(): int
    {
        return match(true) {
            $this->has_subscription => $this->subscriptionCount($this->product->id),
            $this->has_order => $this->orderCount($this->product->id),
            default => $this->cartCount($this->product->id),
        };
    }

    public function subscriptionCount(int $product_id): int
    {
        $subscription = $this->fetchCustomerSubscription();

        if (is_null($subscription)) {
            return 0;
        }

        $current_order = $subscription->currentOrder;

        if ( ! is_null($current_order)) {
            return $current_order->items()
                ->select(['qty'])
                ->firstWhere('product_id', $product_id)
                ->qty
                ?? 0;
        }

        return $subscription->items()
            ->select(['qty'])
            ->firstWhere('product_id', $product_id)
            ->qty
            ?? 0;
    }

    public function orderCount(int $product_id): int
    {
        /** @var Order|null $order */
        $order = $this->fetchCustomerOrder();

        if (is_null($order)) {
            return 0;
        }

        return $order->items()
            ->select(['qty'])
            ->firstWhere('product_id', $product_id)
            ->qty
            ?? 0;
    }

    public function cartCount(int $product_id): int
    {
        /** @var Cartable|null $cart */
        $cart = $this->fetchShopperCart(should_stub: false);

        if (is_null($cart)) {
            return 0;
        }

        return $cart->itemsInCart()
            ->firstWhere(fn(Item $item) => $item->product->id === $product_id)
            ->quantity
            ?? 0;
    }

    public function add()
    {
        match(true) {
            $this->has_subscription => $this->addToSubscription($this->product->id),
            $this->has_order => $this->addToOrder($this->product->id),
            default => $this->addAndOpenCartPanel()
        };
    }

    private function addAndOpenCartPanel()
    {
        $this->addToCart($this->product->id, $this->metadata);
        $this->dispatch('openPanel', title: 'Shopping cart', component: 'theme.cart-side-panel');
    }

    public function decrement()
    {
        match(true) {
            $this->has_subscription => $this->decrementFromSubscription($this->product->id),
            $this->has_order => $this->decrementFromOrder($this->product->id),
            default => $this->decrementFromCart($this->product->id, $this->metadata),
        };
    }
}

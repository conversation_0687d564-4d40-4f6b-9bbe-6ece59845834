<?php

namespace App\Livewire\Theme\Offers;

use App\Contracts\CartService;
use App\Events\User\UserSubscribedToNewsletter;
use App\Events\User\UserWasRegistered;
use App\Models\Coupon;
use App\Models\Lead;
use App\Models\User;
use App\Rules\AllowedEmailDomain;
use App\Rules\HoneyPot;
use App\Rules\HoneyTime;
use Illuminate\Support\Facades\Auth;
use Illuminate\Validation\Rule;
use Livewire\Attributes\Locked;
use Livewire\Component;

class Tripwire extends Component
{
    public string $email = '';

    public string $username = '';

    public ?int $timestamp = null;

    #[Locked]
    public string $page = 'tripwire';

    public function mount()
    {
        $this->timestamp = time();
        $this->page = request()->fullUrl();
    }

    public function render()
    {
        return view('theme::livewire.offers.tripwire');
    }

    public function submit()
    {
        $validated = $this->validate([
            'username' => [new HoneyPot],
            'timestamp' => [new HoneyTime],
            'email' => ['required', 'email:filter', 'indisposable', new AllowedEmailDomain, 'max:255', Rule::unique(User::class, 'email')],
        ], [
            'email.unique' => 'The offer is available to new customers only.',
        ]);

        $user = User::register(collect([
            'email' => $validated['email'],
            'password' => str()->random(20),
        ]));

        Auth::login($user);

        event(new UserSubscribedToNewsletter($user));
        event(new UserWasRegistered($user));

        Lead::where('email', $user->email)->delete();

        $cart = app(CartService::class)
            ->create(shopper_type: User::class, shopper_id: (string) $user->id);

        $cart->applyConditionalCouponToCart(Coupon::welcomeCoupon());

        session()->flash('userWasCreated');
        session()->flash('open-cart-side-panel');

        $this->js("
            if (typeof gtag !== 'undefined') {
                gtag('event', 'select_promotion', {
                    creative_name: 'first_order_discount',
                    creative_slot: 'tripwire_footer',
                    promotion_name: '20% Off First $175 Order',
                    promotion_id: '20%_off_first_$175_order'
                });
            }
        ");

        return $this->redirect($this->page);
    }
}

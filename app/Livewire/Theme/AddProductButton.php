<?php

namespace App\Livewire\Theme;

use Livewire\Component;

class AddProductButton extends Component
{
    use AddsProduct;

    public string $style;

    public ?string $cta_label = null;

    public ?string $cta_classes = null;

    public array $metadata = [];

    public function mount()
    {
        $this->style = theme('store_button_style', 'btn-brand');
    }

    public function render()
    {
        return view('theme::livewire.add-product-button');
    }

    public function add()
    {
        match(true) {
            $this->has_subscription => $this->addToSubscription($this->product->id),
            $this->has_order => $this->addToOrder($this->product->id),
            default => $this->addAndOpenCartPanel()
        };
    }

    private function addAndOpenCartPanel()
    {
        $this->addToCart($this->product->id, $this->metadata);
        $this->dispatch('openPanel', title: 'Shopping cart', component: 'theme.cart-side-panel');
    }
}

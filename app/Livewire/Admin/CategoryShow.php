<?php

namespace App\Livewire\Admin;

use App\Models\Category;
use App\Models\Product;
use Illuminate\Database\Query\Builder;
use Illuminate\Validation\Rule;
use Livewire\Component;

class CategoryShow extends Component
{
    use SendsAdminNotifications;

    public Category $category;

    public string $tab = 'settings';
    public ?string $editing = null;
    public ?int $category_id = null;

    public string $name;
    public ?string $display_name = null;
    public string $slug;
    public ?string $summary = null;
    public ?string $subheading = null;
    public ?string $description = null;
    public ?string $cover_photo = null;

    public ?string $seo_meta_title = null;
    public ?string $seo_meta_description = null;
    public ?bool $seo_visible = null;

    public function mount(Category $category)
    {
        $this->category = $category;
        $this->category_id = $category->category_id;

        $this->name = $category->name;
        $this->display_name = $category->extra_attributes->get('display_name');
        $this->slug = $category->slug;
        $this->summary = $category->extra_attributes->get('summary');
        $this->subheading = $category->extra_attributes->get('subheading');
        $this->description = $category->description;
        $this->cover_photo = $category->extra_attributes->get('cover_photo');

        $this->seo_meta_title = $category->extra_attributes->get('seo_meta_title');
        $this->seo_meta_description = $category->extra_attributes->get('seo_meta_description');
        $this->seo_visible = $category->extra_attributes->get('seo_visible');

        $this->tab = request()->query('tab', 'settings');
    }

    public function render()
    {
        return view('livewire.category-show');
    }

    public function save()
    {
        $validated = $this->validate([
            'name' => ['required', 'string'],
            'display_name' => ['nullable', 'string'],
            'slug' => [
                'required',
                'string',
                Rule::unique('categories')
                    ->ignore($this->category->id)
                    ->where(fn(Builder $query) =>
                        $query->where('category_id', $this->category->category_id)
                    )
            ],
            'summary' => ['nullable', 'string'],
            'subheading' => ['nullable', 'string'],
            'description' => ['nullable', 'string'],
            'cover_photo' => ['nullable', 'url'],
            'seo_meta_title' => ['nullable', 'string'],
            'seo_meta_description' => ['nullable', 'string'],
            'seo_visible' => ['nullable', 'boolean'],
        ]);

        $this->category->fill([
            'name' => $validated['name'],
            'slug' => $validated['slug'],
            'description' => $validated['description'],
        ]);

        $this->category->extra_attributes->set([
            'display_name' => $validated['display_name'],
            'summary' => $validated['summary'],
            'subheading' => $validated['subheading'],
            'cover_photo' => $validated['cover_photo'],
            'seo_meta_title' => $validated['seo_meta_title'],
            'seo_meta_description' => $validated['seo_meta_description'],
            'seo_visible' => $validated['seo_visible'],
        ]);

        $this->category->save();

        $this->dispatch('categoryUpdated');

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Category updated!',
            'message' => 'The category has been updated!',
        ]);
    }

    public function updateSubcategoryOrder($items)
    {
        foreach($items as $item) {
            Category::whereNotNull('category_id')
                ->find($item['value'])
                ->update(['position' => $item['order']]);
        }

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Subcategories sorted!',
            'message' => 'The subcategory sort order has been updated!',
        ]);
    }

    public function updateProductCategoryOrder($products)
    {
        foreach($products as $product) {
            Product::query()
                ->where('id', $product['value'])
                ->update(['category_position' => $product['order']]);
        }

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Products sorted!',
            'message' => 'The product sort order has been updated!',
        ]);
    }
}

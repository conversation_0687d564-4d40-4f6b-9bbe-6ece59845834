<?php

namespace App\Livewire\Admin\Pages\Widgets;

use Illuminate\Validation\Rule;

trait HasBackground
{
    public string $background_color = '';
    public string $background_image = '';
    public string $background_attachment = '';

    public string $background_vertical_position = '';
    public string $background_overlay = '';

    protected function initializeBackground(array $settings)
    {
        $this->background_color = $settings['background']['color'] ?? '';
        $this->background_image = $settings['background']['image'] ?? '';
        $this->background_attachment = $settings['background']['attachment'] ?? '';
        $this->background_vertical_position = $settings['background']['vertical_position'] ?? 'center';
        $this->background_overlay = $settings['background']['overlay'] ?? '';
    }

    protected function backgroundRules()
    {
        return [
            'background_color' => ['nullable', 'string'],
            'background_image' => ['nullable', 'string'],
            'background_attachment' => ['nullable', 'string', Rule::in(['scroll', 'fixed'])],
            'background_vertical_position' => ['nullable', 'string', Rule::in(['top', 'center', 'bottom'])],
            'background_overlay' => ['nullable', 'string'],
        ];
    }
}

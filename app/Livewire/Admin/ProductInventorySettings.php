<?php

namespace App\Livewire\Admin;

use App\Events\InventoryAdjusted;
use App\Models\Product;
use Livewire\Attributes\On;
use Livewire\Component;

class ProductInventorySettings extends Component
{
    use SendsAdminNotifications;

    public Product $product;

    public bool $is_bundle;

    public string $track_inventory;

    public int $on_site_inventory;

    public int $reorder_threshold;

    public int $subscription_reserve;

    public int $off_site_inventory;

    protected $listeners = [
        'productUpdated' => 'productUpdated',
        'bundleUpdated' => 'productUpdated'
    ];

    public function mount(Product $product)
    {
        $this->product = $product;
        $this->fillProductAttributes($product);
    }

    private function fillProductAttributes(Product $product)
    {
        $this->fill([
            'is_bundle' => $product->is_bundle,
            'track_inventory' => $product->track_inventory,
            'on_site_inventory' => $product->onSiteInventory(),
            'reorder_threshold' => $product->reorderThreshold(),
            'subscription_reserve' => $product->subscriptionReserveInventory(),
            'off_site_inventory' => $product->offSiteInventory(),
        ]);
    }

    public function render()
    {
        return view('livewire.product-inventory-settings');
    }

    public function setInventoryTracking(string $tracking)
    {
        $this->resetValidation();

        match ($tracking) {
            'bundle' => $this->product->fill([
                'is_bundle' => true,
                'track_inventory' => 'bundle',
                'inventory' => 0
            ]),
            default => call_user_func(function () use ($tracking) {
                $this->product->fill([
                    'is_bundle' => false,
                    'track_inventory' => $tracking,
                    'inventory' => 0
                ]);

                $this->product->bundle()->detach();
            }),
        };

        $this->checkForInventoryAdjustments($this->product);

        $this->product->save();

        $this->fillProductAttributes($this->product);

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Product updated!',
            'message' => 'Product inventory tracking has been updated.',
        ]);
    }

    private function checkForInventoryAdjustments(Product $product)
    {
        if ( ! $product->isDirty('inventory')) {
            return;
        }

        event(new InventoryAdjusted(
            $product->id,
            $product->getOriginal('inventory'),
            $product->inventory
        ));
    }

    #[On('inventory-settings-saved')]
    public function saveStandardInventorySettings()
    {
        if ($this->track_inventory !== 'yes') {
            $this->sendAdminNotification([
                'level' => 'success',
                'title' => 'Product updated!',
                'message' => 'Product inventory settings has been updated.',
            ]);
            return;
        }

        $this->resetValidation();
        
       $this->validate([
            'on_site_inventory' => ['sometimes', 'required', 'integer'],
            'reorder_threshold' => ['sometimes', 'required', 'integer', 'min:0'],
            'subscription_reserve' => ['sometimes', 'required', 'integer'],
            'off_site_inventory' => ['nullable', 'integer', 'min:0'],
        ]);

        $this->product->fill([
            'inventory' => $this->on_site_inventory,
            'stock_out_inventory' => $this->reorder_threshold,
            'oos_threshold_inventory' => $this->subscription_reserve,
            'other_inventory' => $this->off_site_inventory,
        ]);

        $this->checkForInventoryAdjustments($this->product);

        $this->product->save();

        $this->sendAdminNotification([
            'level' => 'success',
            'title' => 'Product updated!',
            'message' => 'Product inventory settings has been updated.',
        ]);
    }

    public function productUpdated()
    {
        $this->product = $this->product->fresh();
        $this->fillProductAttributes($this->product);
    }
}

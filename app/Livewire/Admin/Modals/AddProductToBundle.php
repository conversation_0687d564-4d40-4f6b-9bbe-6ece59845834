<?php

namespace App\Livewire\Admin\Modals;

use App\Livewire\Theme\Modals\ModalAttributes;
use App\Models\Product;
use Closure;
use Illuminate\Validation\Rule;
use Livewire\Attributes\On;
use Livewire\Component;

class AddProductToBundle extends Component
{
    use ModalAttributes;

    public ?int $bundle_id = null;

    public int $quantity = 1;

    public ?int $product_id = null;

    protected $listeners = [
        'productSelected'
    ];

    public function render()
    {
        return view('livewire.modals.add-product-to-bundle');
    }

    public function submit()
    {
        $bundle = Product::find($this->bundle_id);

        $validated = $this->validate([
            'product_id' => [
                'required',
                'int',
                Rule::exists('products', 'id')->where(fn ($query) => $query->where('is_bundle', false)),
                function (string $attribute, mixed $value, Closure $fail) use ($bundle) {
                    if ($bundle?->bundle()->where('product_id', $value)->exists()) {
                        $fail("The product is already assigned to this bundle.");
                    }
                }],
            'quantity' => ['required', 'int', 'min:1'],
        ], [
            'product_id.exists' => 'The product cannot be a bundle.',
        ]);

        $bundle->bundle()->attach($this->product_id, ['qty' => $this->quantity]);

        $this->dispatch('productUpdated');
        $this->close();
    }

    #[On('close-modal-add-product-to-bundle')]
    public function close(): void
    {
        $this->closeModal();
    }

    #[On('open-modal-add-product-to-bundle')]
    public function open(int $bundle_id): void
    {
        $this->bundle_id = $bundle_id;

        $this->openModal();
    }

    public function productSelected(int $product_id): void
    {
        $this->product_id = $product_id;
    }
}

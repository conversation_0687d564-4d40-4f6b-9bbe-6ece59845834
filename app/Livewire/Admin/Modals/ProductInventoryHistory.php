<?php

namespace App\Livewire\Admin\Modals;

use App\Livewire\Theme\Modals\ModalAttributes;
use App\Models\Product;
use Livewire\Attributes\On;
use Livewire\Component;

class ProductInventoryHistory extends Component
{
    use ModalAttributes;

    public ?int $product_id = null;

    public function render()
    {
        $adjustments = Product::find($this->product_id)
            ?->inventoryAdjustments()
            ->with(['user'])
            ->limit(15)
            ->get()
            ?? collect();

        return view('livewire.modals.product-inventory-history', compact('adjustments'));
    }

    #[On('open-modal-product-inventory-history')]
    public function open(int $product_id): void
    {
        $this->product_id = $product_id;
        $this->openModal();
    }

    #[On('close-modal-product-inventory-history')]
    public function close(): void
    {
        $this->closeModal();
    }
}

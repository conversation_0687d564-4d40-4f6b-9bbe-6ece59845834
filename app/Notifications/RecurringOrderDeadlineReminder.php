<?php

namespace App\Notifications;

use App\Mail\RecurringOrderDeadline;
use App\Models\Order;
use App\Models\Schedule;
use App\Models\Template;
use App\Services\EmailTemplateBuilder;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Str;
use NotificationChannels\Twilio\TwilioChannel;
use NotificationChannels\Twilio\TwilioMessage;
use NotificationChannels\Twilio\TwilioSmsMessage;

class RecurringOrderDeadlineReminder extends TenantAwareNotification implements ShouldQueue
{
    use Queueable;

    public function __construct(
        public int $schedule_id
    ) {}

    public function via(Order $notifiable): array
    {
        $channels = [];

        if ($this->isEnabledViaMail($notifiable)) {
            $channels[] = 'mail';
        }

        if ( ! is_null($notifiable->routeNotificationForTwilio()) && $this->isEnabledViaSms($notifiable)) {
            $channels[] = TwilioChannel::class;
        }

        return $channels;
    }

    public function isEnabledViaMail(Order $notifiable): bool
    {
        $schedule = $notifiable->schedule;

        if (is_null($schedule)) {
            return (bool) setting('recurring_orders_deadline_email_enabled', false);
        }

        return $schedule->notifiesDeadlineReminderViaMail();
    }

    public function isEnabledViaSms(Order $notifiable): bool
    {
        $schedule = $notifiable->schedule;

        if (is_null($schedule)) {
            return (bool) setting('recurring_orders_deadline_sms_enabled', false);
        }

        return $schedule->notifiesDeadlineReminderViaSms();
    }

    public function toMail(Order $notifiable): RecurringOrderDeadline
    {
        $schedule = Schedule::findOrFail($this->schedule_id);

        $template_id = null;

        // Attempt to get the template for the schedule
        if ($schedule->subscription_template_id) {
            $template_id = Template::find($schedule->subscription_template_id)?->id;
        }

        // Attempt to get the template from the subscription settings
        if (is_null($template_id) && setting('recurring_orders_deadline_email_template_id')) {
            $template_id = Template::find(setting('recurring_orders_deadline_email_template_id'))?->id;
        }

        return (new RecurringOrderDeadline($notifiable->id, $template_id))
            ->to($notifiable->routeNotificationForMail());
    }

    public function toTwilio(Order $notifiable): TwilioMessage
    {
        return (new TwilioSmsMessage)
            ->content($this->getTwilioMessage($notifiable))
            ->from(config('twilio-notification-channel.from'));
    }

    private function getTwilioMessage(Order $notifiable): string
    {
        $order = Order::forEmail()->findOrFail($notifiable->id);

        return Str::limit(
            app(EmailTemplateBuilder::class)
                ->order($notifiable)
                ->mergeCustomMessage($this->defaultTwilioMessage($order)),
            config('twilio-notification-channel.message_size_limit')
        );
    }

    private function defaultTwilioMessage(Order $order): string
    {
        return "Seven Sons Farm: Reminder! {order_deadline} at {order_deadline_end_time} is the deadline for making changes to your subscription order at {site_url}/account/recurring-orders.";
    }

    public function tags(): array
    {
        return ['notification'];
    }
}

<?php

namespace App\Exceptions;

use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\RedirectResponse;
use Illuminate\Http\Request;

class ProductNotFoundException extends Exception
{
    /**
     * @return JsonResponse|RedirectResponse
     */
    public function render(Request $request)
    {
        if ($request->ajax()) {
            return response()->json($this->getMessage(), 400);
        }

        error($this->getMessage());
        return back(301);
    }
}

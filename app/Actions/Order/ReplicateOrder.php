<?php

namespace App\Actions\Order;

use App\Models\Order;
use App\Models\OrderItem;

class ReplicateOrder
{
    protected $message = '';
    protected $itemsAdded = [];

    protected $errors = [];

    protected ?Order $new_order = null;

    public function handle(Order $from, Order $to): self
    {
        $from->load([
            'pickup',
            'items.product.price' => function ($q) use ($to) {
                return $q->where('group_id', $to->cartPricingGroupId());
            }
        ]);

        [$deleted_items, $available_items] = $from->items->partition(function (OrderItem $item) {
            return $item->product->trashed();
        });

        $available_items->each(function (OrderItem $item) use ($to) {
            try {
                $to->addItem($item->product, $item->qty);
                $this->itemsAdded[] = $item->title;
            } catch (\Exception $e) {
                $this->errors[] = "{$item->title}: {$e->getMessage()}";
            }
        });

        $to->updateTotals();

        $this->new_order = $to;

        $deleted_items->each(function (OrderItem $item) {
            $this->errors[] = "{$item->title} is no longer available.";
        });

        return $this;
    }

    public function getMessage(): string
    {
        $this->message = 'No items were added to your cart.<br><br>';

        if (count($this->itemsAdded) > 0) {
            $this->message = '<strong>Added to your cart:</strong><br>' . implode('<br/>', $this->itemsAdded) . '<br><br>';
        }

        if ($this->hasErrors()) {
            $this->message .= '<strong class="text-action">Could not be added:</strong><br>' . $this->getErrorMessage();
        }

        return $this->message;
    }

    public function getErrorMessage(): ?string
    {
        if (count($this->errors) === 0) {
            return null;
        }

        return implode('<br>', $this->errors);
    }

    public function hasErrors(): bool
    {
        return count($this->errors) > 0;
    }

    public function newOrder(): ?Order
    {
        return $this->new_order;
    }
}

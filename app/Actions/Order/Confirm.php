<?php

namespace App\Actions\Order;

use App\Models\Date;
use App\Models\Order;
use App\Models\OrderFee;
use App\Models\OrderItem;
use App\Services\SettingsService;

class Confirm
{
    public function handle(Order $order, array $params = []): Order
    {
        $order->updateTotals();
        $order->refresh();

        $this->applyTags($order);

        $order = $this->updateOrderAttributes($order);

        if ( ! empty($params)) {
            $order = $this->updateOrderAttributesFromParams($order, $params);
        }

        if ($order->isDirty()) {
            $order->save();
        }

        $this->applyFees($order);
        $this->deductInventory($order);
        $this->updateCustomerAttributes($order);

        $order->updateTotals();
        $order->refresh();

        return $order;
    }

    protected function applyTags(Order $order): void
    {
        // intentionally left empty so tags are not set on one-time orders
    }

    protected function updateOrderAttributes(Order $order): Order
    {
        $order->type_id = $order->pickup->setting('sales_channel', 1);
        $order->delivery_rate = $order->pickup->delivery_rate;
        $order->delivery_fee_type = $order->pickup->setting('delivery_fee_type', 1);
        $order->accounting_id = $order->customer->accounting_id;
        $order->confirmed = true;
        $order->confirmed_date = today();
        $order->created_year = today()->year;
        $order->created_month = today()->month;
        $order->created_day = today()->day;
        $order->containers = app(SettingsService::class)->defaultParcelCount();
        $order->first_time_order = (int) Order::query()
            ->where([
                'customer_id' => $order->customer_id,
                'confirmed' => true,
                'canceled' => false
            ])
            ->doesntExist();

        // only generate dates if they are not already set and a date can be found
        if (is_null($order->pickup_date) || is_null($order->deadline_date)) {
            $order_window = $order->pickup->activeOrderWindow();

            $order->pickup_date = $order_window?->deliveryDatetime();
            $order->original_pickup_date = $order_window?->deliveryDatetime();
            $order->deadline_date = $order_window?->deadlineDatetime();
            $order->schedule_id = $order_window?->scheduleId();
        }

        if ($order->customer->hasSecondaryEmail()) {
            $order->customer_email_alt = $order->customer->secondaryEmail();
        }

        return $order;
    }

    protected function updateOrderAttributesFromParams(Order $order, array $params): Order
    {
        $params = collect($params);

        $order->type_id = $params->get('type_id', $order->pickup->setting('sales_channel', 1));
        $order->payment_source_id = $params['payment_source_id'] ?? $order->payment_source_id ?? null;
        $order->payment_id = $params->get('payment_id', $order->payment_id);
        $order->customer_first_name = e($params->get('customer_first_name', $order->customer_first_name));
        $order->customer_last_name = e($params->get('customer_last_name', $order->customer_last_name));
        $order->customer_phone = e($params->get('customer_phone', $order->customer_phone));
        $order->customer_email = $params->get('customer_email', $order->customer_email);
        $order->shipping_street = e($params->get('shipping_street', $order->shipping_street));
        $order->shipping_street_2 = e($params->get('shipping_street_2', $order->shipping_street_2));
        $order->shipping_city = e($params->get('shipping_city', $order->shipping_city));
        $order->shipping_state = e($params->get('shipping_state', $order->shipping_state));
        $order->shipping_zip = e($params->get('shipping_zip', $order->shipping_zip));
        $order->customer_notes = e($params->get('customer_notes', $order->customer_notes));

        if ($params->get('pickup_date')) {
            /** @var Date|null $date */
            $date = Date::find($params->get('pickup_date'));

            if ($date) {
                $order_window = $date->toOrderWindows();
                $order->pickup_date = $order_window->deliveryDatetime();
                $order->original_pickup_date = $order_window->deliveryDatetime();
                $order->deadline_date = $order_window->deadlineDatetime();
                $order->schedule_id = $order_window->scheduleId();
            }
        }

        return $order;
    }

    protected function applyFees(Order $order): void
    {
        if ($order->hasNoPickupPoint() || $order->customer->exemptFromFees()) {
            return;
        }

        $order->fees()->where('user_id', false)->delete();

        $order->pickup->fees->each(function ($fee) use ($order) {
            OrderFee::create([
                'order_id' => $order->id,
                'title' => $fee->title,
                'qty' => 1,
                'amount' => $fee->amount,
                'taxable' => $fee->taxable,
                'apply_limit' => $fee->apply_limit,
                'threshold' => $fee->threshold,
                'cap' => $fee->cap,
                'subtotal' => $fee->amount,
            ]);
        });
    }

    protected function deductInventory(Order $order): void
    {
        $order->items->each(fn(OrderItem $item) => $item->fulfillInventory());
    }

    protected function updateCustomerAttributes(Order $order): Order
    {
        $order->customer->last_purchase = now();
        $order->customer->order_count++;

        if ($order->isRecurring()) {
            $order->customer->recurring_order_count++;
        }

        $order->customer->save();

        return $order;
    }
}

<?php

namespace App\Actions;

use App\Models\Schedule;
use Carbon\Carbon;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\DB;

class ActivateSchedule extends BaseAction
{
    public function execute(array $params, Schedule $schedule)
    {
        $firstDeliveryDate = Carbon::parse(Arr::get($params,'first_delivery_date'));

        $orderCutoff = intval(Arr::get($params,'order_cutoff'));

        $orderingInAdvance = intval(Arr::get($params,'ordering_in_advance'));

        $deliveryDeadlineDate = $firstDeliveryDate->copy()->subDays($orderCutoff);

        if ($firstDeliveryDate->lt(today())) {
            throw new \Exception('The first delivery date must be in the future.');
        }

        if ($deliveryDeadlineDate >= $firstDeliveryDate) {
            throw new \Exception('The delivery deadline date must come before the first delivery date.');
        }

        $dates = (new CreateRepeatingDates)->execute(
            startDate: today(),
            endDate: $deliveryDeadlineDate,
            deliveryDate: $firstDeliveryDate,
            scheduleId: $schedule->id,
            stopDate: null,
            increments: $schedule->delivery_frequency ?? 7,
            format: 'Y-m-d',
            hasSharedStartDate: $schedule->isRepeating()
        );

        DB::transaction(function () use ($dates, $schedule, $firstDeliveryDate, $deliveryDeadlineDate, $orderingInAdvance) {
            DB::table('dates')->insert($dates);
            $schedule->active = true;
            $schedule->ordering_in_advance = $orderingInAdvance;
            $schedule->first_delivery_date = $firstDeliveryDate;
            $schedule->first_delivery_deadline = $deliveryDeadlineDate;
            $schedule->extra_attributes->set([
                'days_of_week' => [$firstDeliveryDate->dayOfWeek]
            ]);
            $schedule->save();
        }, 5);
    }
}

<?php

namespace App\Actions\Billing;

use App\Billing\Gateway\GatewayException;
use App\Contracts\Billing;
use App\Exceptions\OrderChargeException;
use App\Models\Card;
use App\Models\Order;
use App\Models\OrderPayment;

class ProcessOrderPayment
{
    /**
     * @throws OrderChargeException
     */
    public function handle(Order $order, ?Card $card = null, ?int $amount = null, string $description_suffix = ''): OrderPayment
    {
        if ( ! $order->customer || ! $order->customer->hasCard()) {
            throw new OrderChargeException('There is no card on file for the customer.');
        }

        $amount = ! is_null($amount) ? $amount : $order->total_due;

        if ($amount < 50) {
            throw new OrderChargeException('Payment cannot be processed for less than 50 cents.');
        }

        dd(is_null($card) && $order->payment_source_id);
        if (is_null($card) && $order->payment_source_id) {
            /** @var Card|null $card */
            $card = Card::find($order->payment_source_id);
        }

        if (is_null($card)) {
            /** @var Card|null $card */
            $card = $order->customer->cards()->where(['default' => true])->first();
        }

        if (is_null($card)) {
            /** @var Card|null $card */
            $card = $order->customer->cards()->first();
        }

        $billing = app(Billing::class);

        $description = 'Order #' . $order->id;
        if ($description_suffix) $description .= " ($description_suffix)";

        try {
            $charge = $billing->chargeOrderWithCard($order, $card, $amount, $description);
        } catch (GatewayException $exception) {
            throw new OrderChargeException($exception->getMessage());
        }

        $payment = OrderPayment::create([
            'order_id' => $order->id,
            'payment_type_id' => 2, // Credit card
            'payment_type' => $charge->payment_type ?? null,
            'customer_id' => $order->customer_id,
            'admin_id' => auth()->id() ?? 0,
            'amount' => $charge->amount,
            'payment_id' => $charge->id,
            'source_id' => $charge->source_id,
            'description' => $charge->description
        ]);

        $order->refresh()->updateTotals();

        if ($order->total_due <= 0) {
            $order->markAsPaid();
        }

        return $payment;
    }
}

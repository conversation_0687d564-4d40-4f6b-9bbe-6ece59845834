<?php

namespace App\Actions\Billing;

use App\Billing\Gateway\PaymentMethod;
use App\Models\Card;
use App\Models\User;

class AddPaymentMethodToCustomer
{
    public function handle(User $user, PaymentMethod $payment_method, bool $set_as_default = false): Card
    {
        if ( ! $user->hasCustomerId()) {
            $user->customer_id = $payment_method->customer_id;
            $user->save();
        }

        $card = $this->saveSourceToUser($user, $payment_method);

        if ($set_as_default) {
            app(SetDefaultCard::class)->handle($card);
        }

        return $card;
    }

    private function saveSourceToUser(User $user, PaymentMethod $source): Card
    {
        return Card::updateOrCreate([
            'user_id' => $user->id,
            'source_id' => $source->id,
        ], [
            'default' => false,
        ]);
    }
}